# SRS 5.0 流媒体服务器部署指南

## 目录
1. [系统环境要求](#系统环境要求)
2. [依赖安装](#依赖安装)
3. [源码获取](#源码获取)
4. [编译配置](#编译配置)
5. [系统安装](#系统安装)
6. [配置文件详解](#配置文件详解)
7. [systemd服务配置](#systemd服务配置)
8. [防火墙配置](#防火墙配置)
9. [服务启动验证](#服务启动验证)
10. [常见问题排查](#常见问题排查)
11. [性能优化](#性能优化)

## 系统环境要求

### 硬件要求
- **CPU**: 2核心以上（推荐4核心）
- **内存**: 4GB以上（推荐8GB）
- **存储**: 20GB以上可用空间
- **网络**: 稳定的网络连接，支持UDP和TCP

### 操作系统支持
- Ubuntu 18.04/20.04/22.04 LTS
- Debian 9/10/11
- CentOS 7/8
- RHEL 7/8
- Rocky Linux 8/9

### 软件版本要求
- GCC 4.8+ 或 Clang 3.5+
- Make 3.81+
- Git 2.0+
- OpenSSL 1.0+（用于HTTPS和WebRTC）

## 依赖安装

### Ubuntu/Debian 系统

```bash
# 更新包管理器
sudo apt update && sudo apt upgrade -y

# 安装基础编译工具
sudo apt install -y build-essential git wget curl

# 安装开发依赖
sudo apt install -y gcc g++ make autoconf automake libtool

# 安装SSL支持
sudo apt install -y libssl-dev

# 安装其他依赖
sudo apt install -y pkg-config zlib1g-dev

# 安装systemd开发包（用于服务管理）
sudo apt install -y libsystemd-dev

# 可选：安装调试工具
sudo apt install -y gdb valgrind strace
```

### CentOS/RHEL 系统

```bash
# 更新系统
sudo yum update -y

# 安装开发工具组
sudo yum groupinstall -y "Development Tools"

# 安装基础依赖
sudo yum install -y git wget curl

# 安装编译工具
sudo yum install -y gcc gcc-c++ make autoconf automake libtool

# 安装SSL支持
sudo yum install -y openssl-devel

# 安装其他依赖
sudo yum install -y pkgconfig zlib-devel

# CentOS 8/Rocky Linux 使用 dnf
# sudo dnf install -y gcc gcc-c++ make git openssl-devel
```

## 源码获取

### 1. 克隆SRS仓库

```bash

# 克隆SRS源码仓库
sudo git clone https://github.com/ossrs/srs.git
cd srs

# 查看可用版本
git tag | grep "v5.0"

# 切换到SRS 5.0稳定版本
sudo git checkout v5.0-r3

# 验证版本
git describe --tags
```

```

## 编译配置

### 1. 配置编译选项

```bash
cd /srs/trunk

# 查看所有配置选项
./configure --help

# 推荐的医疗录制系统配置
sudo ./configure \
    --prefix=/usr/local/srs \
    --with-ssl \
    --with-webrtc \
    --with-rtc \
    --with-hls \
    --with-dvr \
    --with-http-callback \
    --with-http-server \
    --with-http-api \
    --with-stream-caster \
    --with-gb28181 \
    --jobs=4
```

### 2. 编译选项详解

| 选项 | 说明 | 医疗系统重要性 |
|------|------|----------------|
| `--with-ssl` | 启用SSL/TLS支持 | ⭐⭐⭐ 安全传输必需 |
| `--with-webrtc` | 启用WebRTC功能 | ⭐⭐⭐ 核心功能 |
| `--with-rtc` | 启用RTC服务器 | ⭐⭐⭐ WebRTC必需 |
| `--with-hls` | 启用HLS流媒体 | ⭐⭐ 回放功能 |
| `--with-dvr` | 启用录制功能 | ⭐⭐⭐ 录制必需 |
| `--with-http-callback` | HTTP回调支持 | ⭐⭐ 集成必需 |
| `--with-http-server` | 内置HTTP服务器 | ⭐⭐ 管理界面 |
| `--with-http-api` | HTTP API接口 | ⭐⭐⭐ 系统集成 |

### 3. 开始编译

```bash
# 编译（使用4个并行任务）
sudo make -j4

# 编译时间约5-15分钟，取决于硬件性能
```

## 系统安装

### 1. 安装SRS

```bash
# 安装到系统目录
sudo make install

# 验证安装
/usr/local/srs/objs/srs -v
```

### 2. 创建目录结构

```bash
# 创建必要的目录
sudo mkdir -p /usr/local/srs/{conf,logs,records,www}
sudo mkdir -p /var/log/srs
sudo mkdir -p /var/lib/srs

# 设置权限
sudo chown -R root:root /usr/local/srs
sudo chmod -R 755 /usr/local/srs

# 创建运行用户（安全考虑）
sudo useradd -r -s /bin/false -d /usr/local/srs srs
sudo chown -R srs:srs /var/log/srs /var/lib/srs
```

### 3. 复制配置文件

```bash
# 复制默认配置文件
sudo cp /opt/srs/srs/conf/srs.conf /usr/local/srs/conf/
sudo cp /opt/srs/srs/conf/console.conf /usr/local/srs/conf/

# 备份原始配置
sudo cp /usr/local/srs/conf/srs.conf /usr/local/srs/conf/srs.conf.backup
```

## 配置文件详解

### 1. 主配置文件 (/usr/local/srs/conf/srs.conf)

```nginx
# SRS 5.0 医疗录制系统配置

# 监听端口配置
listen              1935;
max_connections     1000;
srs_log_tank        file;
srs_log_file        /var/log/srs/srs.log;
srs_log_level       info;

# HTTP服务器配置
http_server {
    enabled         on;
    listen          8080;
    dir             /usr/local/srs/www;
}

# HTTP API配置
http_api {
    enabled         on;
    listen          1985;
    crossdomain     on;
}

# WebRTC配置（核心功能）
rtc_server {
    enabled         on;
    listen          8000;
    # 候选地址配置
    candidate       $CANDIDATE;
}

# VHOST配置
vhost __defaultVhost__ {
    # WebRTC推流配置
    rtc {
        enabled     on;
        rtmp        on;
        # 启用NACK重传
        nack        on;
        # 启用PLI关键帧请求
        pli         on;
        # 启用TWCC拥塞控制
        twcc        on;
    }
    
    # RTMP配置
    rtmp {
        enabled     on;
    }
    
    # HLS配置
    hls {
        enabled         on;
        hls_fragment    10;
        hls_window      60;
        hls_path        /var/lib/srs/hls;
        hls_m3u8_file   [app]/[stream].m3u8;
        hls_ts_file     [app]/[stream]-[seq].ts;
    }
    
    # DVR录制配置
    dvr {
        enabled         on;
        dvr_path        /var/lib/srs/records/[app]/[stream]/[2006]/[01]/[02]/[stream]-[15].[04].[05].flv;
        dvr_plan        session;
        dvr_duration    30;
        dvr_wait_keyframe   on;
    }
    
    # HTTP回调配置
    http_hooks {
        enabled         on;
        on_connect      http://127.0.0.1:5410/api/srs/on_connect;
        on_close        http://127.0.0.1:5410/api/srs/on_close;
        on_publish      http://127.0.0.1:5410/api/srs/on_publish;
        on_unpublish    http://127.0.0.1:5410/api/srs/on_unpublish;
        on_play         http://127.0.0.1:5410/api/srs/on_play;
        on_stop         http://127.0.0.1:5410/api/srs/on_stop;
        on_dvr          http://127.0.0.1:5410/api/srs/on_dvr;
    }
}
```

### 2. 环境变量配置

```bash
# 创建环境配置文件
sudo tee /usr/local/srs/conf/srs.env << 'EOF'
# SRS环境变量配置

# 服务器外网IP（重要：WebRTC需要）
CANDIDATE=**************

# 日志级别
SRS_LOG_LEVEL=info

# 最大连接数
SRS_MAX_CONNECTIONS=1000
EOF
```

## systemd服务配置

### 1. 创建systemd服务文件

```bash
sudo tee /etc/systemd/system/srs.service << 'EOF'
[Unit]
Description=SRS Media Server
Documentation=https://github.com/ossrs/srs
After=network.target

[Service]
Type=forking
User=srs
Group=srs
WorkingDirectory=/usr/local/srs
EnvironmentFile=/usr/local/srs/conf/srs.env
ExecStartPre=/bin/mkdir -p /var/log/srs /var/lib/srs/hls /var/lib/srs/records
ExecStartPre=/bin/chown -R srs:srs /var/log/srs /var/lib/srs
ExecStart=/usr/local/srs/objs/srs -c /usr/local/srs/conf/srs.conf -d
ExecReload=/bin/kill -HUP $MAINPID
ExecStop=/bin/kill -TERM $MAINPID
PIDFile=/usr/local/srs/objs/srs.pid
Restart=always
RestartSec=3
LimitNOFILE=65536

[Install]
WantedBy=multi-user.target
EOF
```

### 2. 启用和管理服务

```bash
# 重新加载systemd配置
sudo systemctl daemon-reload

# 启用开机自启
sudo systemctl enable srs

# 启动服务
sudo systemctl start srs

# 查看服务状态
sudo systemctl status srs

# 查看日志
sudo journalctl -u srs -f
```

## 防火墙配置

### 1. Ubuntu/Debian (UFW)

```bash
# 启用UFW
sudo ufw enable

# 允许SSH（重要：避免锁定）
sudo ufw allow ssh

# SRS核心端口
sudo ufw allow 1935/tcp    # RTMP推流端口
sudo ufw allow 8080/tcp    # HTTP服务器端口
sudo ufw allow 1985/tcp    # HTTP API端口
sudo ufw allow 8000/udp    # WebRTC端口

# HLS端口（如果单独配置）
sudo ufw allow 8081/tcp

# 查看防火墙状态
sudo ufw status verbose
```

### 2. CentOS/RHEL (firewalld)

```bash
# 启动firewalld
sudo systemctl start firewalld
sudo systemctl enable firewalld

# 添加SRS端口
sudo firewall-cmd --permanent --add-port=1935/tcp
sudo firewall-cmd --permanent --add-port=8080/tcp
sudo firewall-cmd --permanent --add-port=1985/tcp
sudo firewall-cmd --permanent --add-port=8000/udp

# 重新加载配置
sudo firewall-cmd --reload

# 查看开放端口
sudo firewall-cmd --list-ports
```

### 3. 端口说明表

| 端口 | 协议 | 用途 | 必需性 |
|------|------|------|--------|
| 1935 | TCP | RTMP推流 | ⭐⭐⭐ |
| 8000 | UDP | WebRTC | ⭐⭐⭐ |
| 1985 | TCP | HTTP API | ⭐⭐⭐ |
| 8080 | TCP | HTTP服务器 | ⭐⭐ |
| 8081 | TCP | HLS播放 | ⭐ |

## 服务启动验证

### 1. 基础验证

```bash
# 检查进程是否运行
ps aux | grep srs

# 检查端口监听
sudo netstat -tlnp | grep srs
# 或使用ss命令
sudo ss -tlnp | grep srs

# 检查日志
tail -f /var/log/srs/srs.log
```

### 2. API接口测试

```bash
# 测试HTTP API
curl http://localhost:1985/api/v1/summaries

# 预期返回JSON格式的服务器信息
```

### 3. WebRTC功能测试

```bash
# 测试WebRTC API
curl -X POST http://localhost:1985/rtc/v1/publish/ \
  -H "Content-Type: application/json" \
  -d '{
    "api": "http://localhost:1985/rtc/v1/publish/",
    "streamurl": "webrtc://localhost:8000/live/test",
    "sdp": "test"
  }'
```

### 4. 推流测试

使用FFmpeg进行RTMP推流测试：

```bash
# 安装FFmpeg（如果未安装）
sudo apt install ffmpeg  # Ubuntu/Debian
sudo yum install ffmpeg  # CentOS/RHEL

# 测试推流（使用测试视频）
ffmpeg -re -i /dev/video0 -c:v libx264 -preset veryfast -tune zerolatency \
  -c:a aac -ar 44100 -f flv rtmp://localhost:1935/live/test

# 或使用测试模式
ffmpeg -f lavfi -i testsrc=duration=10:size=320x240:rate=30 \
  -f lavfi -i sine=frequency=1000:duration=10 \
  -c:v libx264 -c:a aac -f flv rtmp://localhost:1935/live/test
```

## 常见问题排查

### 1. 服务启动失败

**问题**: SRS服务无法启动

**排查步骤**:
```bash
# 检查配置文件语法
/usr/local/srs/objs/srs -t -c /usr/local/srs/conf/srs.conf

# 检查端口占用
sudo netstat -tlnp | grep -E "(1935|8000|1985|8080)"

# 检查权限
ls -la /usr/local/srs/objs/srs
sudo chown srs:srs /usr/local/srs/objs/srs
sudo chmod +x /usr/local/srs/objs/srs

# 手动启动调试
sudo -u srs /usr/local/srs/objs/srs -c /usr/local/srs/conf/srs.conf
```

### 2. WebRTC连接失败

**问题**: WebRTC推流无法建立连接

**排查步骤**:
```bash
# 检查CANDIDATE配置
grep CANDIDATE /usr/local/srs/conf/srs.env

# 检查UDP端口8000
sudo netstat -unp | grep 8000

# 检查防火墙
sudo ufw status | grep 8000
sudo firewall-cmd --list-ports | grep 8000

# 测试网络连通性
nc -u localhost 8000
```

**解决方案**:
- 确保CANDIDATE设置为正确的外网IP
- 检查NAT和防火墙配置
- 验证UDP端口8000可访问

### 3. 录制文件无法生成

**问题**: DVR录制功能不工作

**排查步骤**:
```bash
# 检查录制目录权限
ls -la /var/lib/srs/records/
sudo chown -R srs:srs /var/lib/srs/

# 检查磁盘空间
df -h /var/lib/srs/

# 检查DVR配置
grep -A 10 "dvr {" /usr/local/srs/conf/srs.conf

# 查看DVR相关日志
grep -i dvr /var/log/srs/srs.log
```

### 4. HTTP回调失败

**问题**: HTTP回调无法连接到录制服务

**排查步骤**:
```bash
# 测试回调地址连通性
curl http://127.0.0.1:5410/api/health

# 检查回调配置
grep -A 10 "http_hooks" /usr/local/srs/conf/srs.conf

# 查看回调日志
grep -i "http hook" /var/log/srs/srs.log
```

### 5. 性能问题

**问题**: 高并发时性能下降

**排查步骤**:
```bash
# 检查系统资源
top
htop
iotop

# 检查网络连接数
ss -s
netstat -an | grep :1935 | wc -l

# 检查文件描述符限制
ulimit -n
cat /proc/$(pgrep srs)/limits | grep "Max open files"
```

## 性能优化

### 1. 系统级优化

```bash
# 增加文件描述符限制
echo "* soft nofile 65536" | sudo tee -a /etc/security/limits.conf
echo "* hard nofile 65536" | sudo tee -a /etc/security/limits.conf

# 优化网络参数
sudo tee -a /etc/sysctl.conf << 'EOF'
# SRS网络优化
net.core.rmem_max = 134217728
net.core.wmem_max = 134217728
net.ipv4.tcp_rmem = 4096 87380 134217728
net.ipv4.tcp_wmem = 4096 65536 134217728
net.core.netdev_max_backlog = 5000
net.ipv4.tcp_congestion_control = bbr
EOF

# 应用配置
sudo sysctl -p
```

### 2. SRS配置优化

```nginx
# 高性能配置示例
listen              1935;
max_connections     2000;
daemon              on;
utc_time            off;
work_dir            /usr/local/srs;
pid                 /usr/local/srs/objs/srs.pid;
ff_log_dir          /tmp;

# 优化日志级别（生产环境）
srs_log_level       warn;

# 优化缓冲区
tcp_nodelay         on;
send_min_interval   10.0;
reduce_sequence_header on;

vhost __defaultVhost__ {
    # WebRTC优化
    rtc {
        enabled     on;
        rtmp        on;
        nack        on;
        pli         on;
        twcc        on;
        # 优化GOP缓存
        gop_cache   on;
        # 队列长度优化
        queue_length 10;
    }

    # 播放优化
    play {
        # 减少延迟
        gop_cache       on;
        queue_length    10;
        mw_latency      100;
    }

    # 发布优化
    publish {
        # 关键帧检测
        parse_sps       on;
        mr              off;
        # 第一个GOP缓存
        firstgop        on;
        # 正常播放超时
        normal_timeout  5000;
    }
}
```

### 3. 监控和维护

```bash
# 创建监控脚本
sudo tee /usr/local/srs/scripts/monitor.sh << 'EOF'
#!/bin/bash
# SRS监控脚本

LOG_FILE="/var/log/srs/monitor.log"
DATE=$(date '+%Y-%m-%d %H:%M:%S')

# 检查进程
if ! pgrep srs > /dev/null; then
    echo "[$DATE] SRS进程未运行，尝试重启" >> $LOG_FILE
    systemctl restart srs
fi

# 检查内存使用
MEM_USAGE=$(ps -o pid,ppid,cmd,%mem,%cpu --sort=-%mem -C srs | tail -n +2)
echo "[$DATE] 内存使用情况: $MEM_USAGE" >> $LOG_FILE

# 检查连接数
CONN_COUNT=$(ss -an | grep :1935 | wc -l)
echo "[$DATE] 当前连接数: $CONN_COUNT" >> $LOG_FILE

# 检查磁盘空间
DISK_USAGE=$(df -h /var/lib/srs | tail -1 | awk '{print $5}')
echo "[$DATE] 录制目录磁盘使用: $DISK_USAGE" >> $LOG_FILE
EOF

sudo chmod +x /usr/local/srs/scripts/monitor.sh

# 添加到crontab（每5分钟检查一次）
echo "*/5 * * * * /usr/local/srs/scripts/monitor.sh" | sudo crontab -
```

### 4. 日志轮转配置

```bash
# 配置logrotate
sudo tee /etc/logrotate.d/srs << 'EOF'
/var/log/srs/*.log {
    daily
    missingok
    rotate 30
    compress
    delaycompress
    notifempty
    create 644 srs srs
    postrotate
        systemctl reload srs > /dev/null 2>&1 || true
    endscript
}
EOF
```

## 部署验证清单

完成部署后，请按以下清单验证：

- [ ] SRS服务正常启动 (`systemctl status srs`)
- [ ] 所有必需端口正常监听 (`netstat -tlnp`)
- [ ] WebRTC API响应正常 (`curl测试`)
- [ ] 防火墙规则配置正确
- [ ] 录制目录权限正确
- [ ] 日志文件正常生成
- [ ] HTTP回调连接正常（如果配置）
- [ ] 推流测试成功
- [ ] 录制功能测试成功

## 总结

本文档提供了SRS 5.0流媒体服务器的完整部署指南，特别针对医疗录制系统的需求进行了优化配置。关键要点：

1. **WebRTC配置**是核心，确保CANDIDATE正确设置
2. **防火墙配置**必须开放UDP 8000端口
3. **录制功能**需要正确的目录权限和磁盘空间
4. **性能优化**对于高并发场景至关重要
5. **监控维护**确保系统稳定运行

按照本指南操作，您应该能够成功部署一个稳定、高性能的SRS流媒体服务器，满足医疗录制系统的需求。
