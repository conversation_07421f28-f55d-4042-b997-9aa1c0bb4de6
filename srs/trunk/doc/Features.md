# Features

The features of SRS.

- [x] System: Support coroutine [state-threads](https://github.com/ossrs/state-threads) for high performance.
- [x] System: Support native HTTP server([CN](https://ossrs.net/lts/zh-cn/docs/v4/doc/sample-http), [EN](https://ossrs.io/lts/en-us/docs/v4/doc/sample-http)) for http api and http live streaming.
- [x] System: Support DVR([CN](https://ossrs.net/lts/zh-cn/docs/v4/doc/dvr), [EN](https://ossrs.io/lts/en-us/docs/v4/doc/dvr)) to record live streaming to FLV file.
- [x] System: Support security strategy including allow/deny publish/play IP([CN](https://ossrs.net/lts/zh-cn/docs/v4/doc/security), [EN](https://ossrs.io/lts/en-us/docs/v4/doc/security)).
- [x] System: Security: Enable CIDR for allow/deny play/publish, [#2914](https://github.com/ossrs/srs/pull/2914). v4.0.248+
- [x] System: Support Vhost([CN](https://ossrs.net/lts/zh-cn/docs/v4/doc/rtmp-url-vhost), [EN](https://ossrs.io/lts/en-us/docs/v4/doc/rtmp-url-vhost)) and \_\_defaultVhost\_\_.
- [x] System: Support reloading([CN](https://ossrs.net/lts/zh-cn/docs/v4/doc/reload), [EN](https://ossrs.io/lts/en-us/docs/v4/doc/reload)) to apply changes of config.
- [x] System: Support traceable and session-based log([CN](https://ossrs.net/lts/zh-cn/docs/v4/doc/log), [EN](https://ossrs.io/lts/en-us/docs/v4/doc/log)).
- [x] System: Support listen at IPv4 and IPv6, read [#460](https://github.com/ossrs/srs/issues/460).
- [x] System: Support docker by [srs-docker](https://hub.docker.com/r/ossrs/srs/tags).
- [x] System: Support multiple processes by ReusePort([CN](https://ossrs.net/lts/zh-cn/docs/v4/doc/reuse-port), [EN](https://ossrs.io/lts/en-us/docs/v4/doc/reuse-port)), [#775](https://github.com/ossrs/srs/issues/775).
- [x] System: Support include directive for config file, [#2878](https://github.com/ossrs/srs/pull/2878).
- [x] System: Support x86_64, armv7 and aarch64 docker image, [#3058](https://github.com/ossrs/srs/pull/3058). v5.0.29+
- [x] System: [Experimental] Enhance HTTP Stream Server for HTTP-FLV, HTTPS, HLS etc. [#1657](https://github.com/ossrs/srs/issues/1657).
- [x] System: [Experimental] Support DVR in MP4 format, read [#738](https://github.com/ossrs/srs/issues/738).
- [x] System: [Experimental] Support Cygwin64 and MIPS cpu. v5.0.13+
- [x] System: [Experimental] Support RISCV cpu, [#3115](https://github.com/ossrs/srs/pull/3115). v5.0.33+
- [x] System: [Experimental] Support loongarch, loongson CPU, [#2689](https://github.com/ossrs/srs/issues/2689).  v5.0.38+
- [x] System: [Experimental] Support Apple Silicon M1(aarch64), [#2747](https://github.com/ossrs/srs/issues/2747). v5.0.41+
- [x] System: [Experimental] Support distributed tracing by Tencent Cloud APM. v5.0.64+
- [x] System: [Experimental] Support grab backtrace stack when assert fail. v5.0.80+
- [x] System: [Experimental] Support Google Address Sanitizer, [#3216](https://github.com/ossrs/srs/issues/3216). v5.0.81+
- [x] System: [Experimental] Windows: Support cygwin pipline and packager, [#2532](https://github.com/ossrs/srs/issues/2532). v5.0.89+
- [x] API: Support HTTP API([CN](https://ossrs.net/lts/zh-cn/docs/v4/doc/http-api), [EN](https://ossrs.io/lts/en-us/docs/v4/doc/http-api)) for system management.
- [x] API: Support HTTP callback([CN](https://ossrs.net/lts/zh-cn/docs/v4/doc/http-callback), [EN](https://ossrs.io/lts/en-us/docs/v4/doc/http-callback)) for authentication and integration.
- [x] API: Support reuse HTTP Stream port for HTTP API, [#2881](https://github.com/ossrs/srs/issues/2881).
- [x] API: [Experimental] Support Prometheus exporter, [#2899](https://github.com/ossrs/srs/issues/2899). v5.0.67+
- [x] Live: Support Edge Cluster for live streaming, see ([CN](https://ossrs.net/lts/zh-cn/docs/v4/doc/edge), [EN](https://ossrs.io/lts/en-us/docs/v4/doc/edge)).
- [x] Live: Support Origin server for converting RTMP to HTTP-FLV([CN](https://ossrs.net/lts/zh-cn/docs/v4/doc/sample-http-flv), [EN](https://ossrs.io/lts/en-us/docs/v4/doc/sample-http-flv)) and HLS([CN](https://ossrs.net/lts/zh-cn/docs/v4/doc/delivery-hls), [EN](https://ossrs.io/lts/en-us/docs/v4/doc/delivery-hls)).
- [x] Live: Support Edge server for converting RTMP to HTTP-FLV([CN](https://ossrs.net/lts/zh-cn/docs/v4/doc/sample-http-flv), [EN](https://ossrs.io/lts/en-us/docs/v4/doc/sample-http-flv)).
- [x] Live: Support HLS with aac(h.264+aac) and mp3(h.264+mp3) codec, please read [bug #301](https://github.com/ossrs/srs/issues/301).
- [x] Live: Support transmux RTMP to HTTP-FLV/MP3/AAC/TS, please read wiki([CN](https://ossrs.net/lts/zh-cn/docs/v4/doc/delivery-http-flv), [EN](https://ossrs.net/lts/zh-cn/docs/v4/doc/delivery-http-flv)).
- [x] Live: Support timestamp correcting for long time(>4.6hours) publishing/playing.
- [x] Live: Support gop-cache([CN](https://ossrs.net/lts/zh-cn/docs/v4/doc/low-latency#gop-cache), [EN](https://ossrs.io/lts/en-us/docs/v4/doc/low-latency#gop-cache)) for player fast startup.
- [x] Live: High performance([CN](https://ossrs.net/lts/zh-cn/docs/v4/doc/performance), [EN](https://ossrs.io/lts/en-us/docs/v4/doc/performance)) RTMP/HTTP-FLV, 6000+ connections.
- [x] Live: Enhanced RTMP url which supports vhost in stream, read [#1059](https://github.com/ossrs/srs/issues/1059).
- [x] Live: Support origin cluster, please read [#464](https://github.com/ossrs/srs/issues/464), [RTMP 302](https://github.com/ossrs/srs/issues/92).
- [x] Live: Support NGINX HLS Cluster, see [CN](https://ossrs.net/lts/zh-cn/docs/v4/doc/sample-hls-cluster) or [EN](https://ossrs.io/lts/en-us/docs/v4/doc/sample-hls-cluster).
- [x] Live: SRT: Support PUSH SRT by IP and optional port, see [#3198](https://github.com/ossrs/srs/issues/3198). v5.0.76+
- [x] Live: Kickoff publisher when stream is idle, which means no players. v5.0.144+
- [x] Live: [Experimental] Support SRT server, read [#1147](https://github.com/ossrs/srs/issues/1147). v4.0.143+
- [x] Live: [Experimental] Support Coroutine Native SRT over ST, [#3010](https://github.com/ossrs/srs/pull/3010). v5.0.30+
- [x] RTC: Support playing stream by WebRTC, [#307](https://github.com/ossrs/srs/issues/307).
- [x] RTC: Support publishing stream by WebRTC, [#307](https://github.com/ossrs/srs/issues/307).
- [x] RTC: Support mux RTP/RTCP/DTLS/SRTP on one port for WebRTC, [#307](https://github.com/ossrs/srs/issues/307).
- [x] RTC: Support client address changing for WebRTC, [#307](https://github.com/ossrs/srs/issues/307).
- [x] RTC: Support transcode RTMP/AAC to WebRTC/Opus, [#307](https://github.com/ossrs/srs/issues/307).
- [x] RTC: Support [Unity](https://github.com/ossrs/srs-unity) to publish or play stream. v5.0.62+
- [x] RTC: [Experimental] Support AV1 codec for WebRTC, [#2324](https://github.com/ossrs/srs/issues/2324).
- [x] RTC: [Experimental] Support transmux RTC to RTMP, [#2093](https://github.com/ossrs/srs/issues/2093).
- [x] RTC: [Experimental] Support WebRTC over TCP directly, [#2852](https://github.com/ossrs/srs/issues/2852). v5.0.60+
- [x] RTC: [Experimental] Support WHIP(WebRTC-HTTP ingestion protocol), [#3170](https://github.com/ossrs/srs/issues/3170). v5.0.61+
- [x] RTC: [Experimental] Support [Larix Broadcaster](https://softvelum.com/larix/), [#3476](https://github.com/ossrs/srs/issues/3476). v5.0.148+
- [x] Other: Support ingesting([CN](https://ossrs.net/lts/zh-cn/docs/v4/doc/ingest), [EN](https://ossrs.io/lts/en-us/docs/v4/doc/ingest)) other protocols to SRS by FFMPEG. v1.0.0+
- [x] Other: Support forwarding([CN](https://ossrs.net/lts/zh-cn/docs/v4/doc/forward), [EN](https://ossrs.io/lts/en-us/docs/v4/doc/forward)) to other RTMP servers. v1.0.0+
- [x] Other: Support transcoding([CN](https://ossrs.net/lts/zh-cn/docs/v4/doc/ffmpeg), [EN](https://ossrs.io/lts/en-us/docs/v4/doc/ffmpeg)) by FFMPEG. v1.0.0+
- [x] Other: All wikis are writen in [Chinese](https://ossrs.net) and [English](https://ossrs.io). v2.0.23+
- [x] Other: Support valgrind and latest ARM by patching ST, read [ST#1](https://github.com/ossrs/state-threads/issues/1) and [ST#2](https://github.com/ossrs/state-threads/issues/2). v3.0.11+
- [x] Other: Enhanced complex error code with description and stack, read [#913](https://github.com/ossrs/srs/issues/913). v3.0.26+
- [x] Other: Support test coverage for core/kernel/protocol/service. v3.0.91+
- [x] Other: Support a simple [mgmt console](http://ossrs.net/console/), please read [srs-console](https://github.com/ossrs/srs-console). v3.0.43+
- [x] Other: Support dynamic forwarding by backend api, [#2799](https://github.com/ossrs/srs/pull/2799). v5.0.24+
- [x] Other: Support write log to tencent cloud CLS. v5.0.44+
- [x] Other: [Experimental] Support pushing MPEG-TS over UDP, please read [bug #250](https://github.com/ossrs/srs/issues/250). v2.0.111+
- [x] Other: [Experimental] Support pushing FLV over HTTP POST, please read wiki([CN](https://ossrs.net/lts/zh-cn/docs/v4/doc/streamer#push-http-flv-to-srs), [EN](https://ossrs.io/lts/en-us/docs/v4/doc/streamer#push-http-flv-to-srs)). v2.0.163+
- [x] Other: [Experimental] Support push stream by GB28181, [#3176](https://github.com/ossrs/srs/issues/3176). v5.0.74+
- [x] Other: Support WHIP/WHEP player, [#3460](https://github.com/ossrs/srs/pull/3460). v5.0.147+
- [ ] System: Support Windows/Cygwin 64bits, [#2532](https://github.com/ossrs/srs/issues/2532).
- [ ] System: Support H.265 over RTMP and HLS, [#465](https://github.com/ossrs/srs/issues/465).
- [ ] System: Proxy to extend origin servers, [#3138](https://github.com/ossrs/srs/issues/3138).
- [ ] System: Support source cleanup for idle streams, [#413](https://github.com/ossrs/srs/issues/413).
- [ ] Live: Support HLS variant, [#463](https://github.com/ossrs/srs/issues/463).
- [ ] RTC: Support IETF-QUIC for WebRTC Cluster, [#2091](https://github.com/ossrs/srs/issues/2091).
- [ ] RTC: Improve RTC performance to 5K by multiple threading, [#2188](https://github.com/ossrs/srs/issues/2188).
- [ ] Other: Support change user to run SRS, [#1111](https://github.com/ossrs/srs/issues/1111).
- [x] [Deprecated] Live: Support Adobe HDS(f4m), please read wiki([CN](https://ossrs.net/lts/zh-cn/docs/v4/doc/delivery-hds), [EN](https://ossrs.io/lts/en-us/docs/v4/doc/delivery-hds)) and [#1535](https://github.com/ossrs/srs/issues/1535).
- [x] [Deprecated] Other: Support bandwidth testing, please read [#1535](https://github.com/ossrs/srs/issues/1535).
- [x] [Deprecated] Other: Support Adobe FMS/AMS token traverse([CN](https://ossrs.net/lts/zh-cn/docs/v4/doc/drm#tokentraverse), [EN](https://ossrs.io/lts/en-us/docs/v4/doc/drm#tokentraverse)) authentication, please read [#1535](https://github.com/ossrs/srs/issues/1535).
- [x] [Removed] Other: Support pushing RTSP, please read [#2304](https://github.com/ossrs/srs/issues/2304#issuecomment-826009290).
- [x] [Removed] Other: Support HTTP RAW API, please read [#2653](https://github.com/ossrs/srs/issues/2653).
- [x] [Removed] Other: Support RTMP client library: [srs-librtmp](https://github.com/ossrs/srs-librtmp).

> Remark: About the milestone and product plan, please read ([CN](https://ossrs.net/lts/zh-cn/product), [EN](https://ossrs.io/lts/en-us/product)) wiki.

