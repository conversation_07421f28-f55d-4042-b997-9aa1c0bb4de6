# This isn't a library ".num" file but is a list of documented items
# that don't appear in lib*.num -- because they are define's, in
# assembly language, etc.
#
OPENSSL_ia32cap                         environment
OPENSSL_MALLOC_FD                       environment
OPENSSL_MALLOC_FAILURES                 environment
OPENSSL_instrument_bus                  assembler
OPENSSL_instrument_bus2                 assembler
#
ADMISSION_SYNTAX                        datatype
ADMISSIONS                              datatype
ASN1_STRING_TABLE                       datatype
BIO_ADDR                                datatype
BIO_ADDRINFO                            datatype
BIO_callback_fn                         datatype
BIO_callback_fn_ex                      datatype
BIO_hostserv_priorities                 datatype
BIO_lookup_type                         datatype
CRYPTO_EX_dup                           datatype
CRYPTO_EX_free                          datatype
CRYPTO_EX_new                           datatype
DTLS_timer_cb                           datatype
EVP_PKEY_gen_cb                         datatype
EVP_PKEY_METHOD                         datatype
EVP_PKEY_ASN1_METHOD                    datatype
GEN_SESSION_CB                          datatype
OPENSSL_Applink                         external
NAMING_AUTHORITY                        datatype
OSSL_STORE_CTX                          datatype
OSSL_STORE_INFO                         datatype
OSSL_STORE_LOADER                       datatype
OSSL_STORE_LOADER_CTX                   datatype
OSSL_STORE_SEARCH                       datatype
OSSL_STORE_close_fn                     datatype
OSSL_STORE_ctrl_fn                      datatype
OSSL_STORE_expect_fn                    datatype
OSSL_STORE_find_fn                      datatype
OSSL_STORE_eof_fn                       datatype
OSSL_STORE_error_fn                     datatype
OSSL_STORE_load_fn                      datatype
OSSL_STORE_open_fn                      datatype
OSSL_STORE_post_process_info_fn         datatype
PROFESSION_INFO                         datatype
PROFESSION_INFOS                        datatype
RAND_DRBG_cleanup_entropy_fn            datatype
RAND_DRBG_cleanup_nonce_fn              datatype
RAND_DRBG_get_entropy_fn                datatype
RAND_DRBG_get_nonce_fn                  datatype
RAND_poll_cb                            datatype
SSL_CTX_allow_early_data_cb_fn          datatype
SSL_CTX_keylog_cb_func                  datatype
SSL_allow_early_data_cb_fn              datatype
SSL_client_hello_cb_fn                  datatype
SSL_psk_client_cb_func                  datatype
SSL_psk_find_session_cb_func            datatype
SSL_psk_server_cb_func                  datatype
SSL_psk_use_session_cb_func             datatype
SSL_verify_cb                           datatype
UI                                      datatype
UI_METHOD                               datatype
UI_STRING                               datatype
UI_string_types                         datatype
UI_string_types                         datatype
X509_STORE_CTX_cert_crl_fn              datatype
X509_STORE_CTX_check_crl_fn             datatype
X509_STORE_CTX_check_issued_fn          datatype
X509_STORE_CTX_check_policy_fn          datatype
X509_STORE_CTX_check_revocation_fn      datatype
X509_STORE_CTX_cleanup_fn               datatype
X509_STORE_CTX_get_crl_fn               datatype
X509_STORE_CTX_get_issuer_fn            datatype
X509_STORE_CTX_lookup_certs_fn          datatype
X509_STORE_CTX_lookup_crls_fn           datatype
X509_STORE_CTX_verify_cb                datatype
X509_STORE_CTX_verify_fn                datatype
X509_STORE_set_verify_cb_func           datatype
X509_LOOKUP                             datatype
X509_LOOKUP_METHOD                      datatype
X509_LOOKUP_TYPE                        datatype
X509_LOOKUP_get_by_alias_fn             datatype
X509_LOOKUP_get_by_subject_fn           datatype
X509_LOOKUP_get_by_fingerprint_fn       datatype
X509_LOOKUP_ctrl_fn                     datatype
X509_LOOKUP_get_by_issuer_serial_fn     datatype
X509_STORE                              datatype
bio_info_cb                             datatype
BIO_info_cb                             datatype
custom_ext_add_cb                       datatype
custom_ext_free_cb                      datatype
custom_ext_parse_cb                     datatype
pem_password_cb                         datatype
ssl_ct_validation_cb                    datatype
#
BIO_append_filename                     define
BIO_destroy_bio_pair                    define
BIO_do_accept                           define
BIO_do_connect                          define
BIO_do_handshake                        define
BIO_eof                                 define
BIO_flush                               define
BIO_get_accept_name                     define
BIO_get_accept_port                     define
BIO_get_accept_ip_family                define
BIO_get_peer_name                       define
BIO_get_peer_port                       define
BIO_get_bind_mode                       define
BIO_get_buffer_num_lines                define
BIO_get_cipher_ctx                      define
BIO_get_cipher_status                   define
BIO_get_close                           define
BIO_get_conn_address                    define
BIO_get_conn_hostname                   define
BIO_get_conn_port                       define
BIO_get_conn_ip_family                  define
BIO_get_fd                              define
BIO_get_fp                              define
BIO_get_info_callback                   define
BIO_get_md                              define
BIO_get_md_ctx                          define
BIO_get_mem_data                        define
BIO_get_mem_ptr                         define
BIO_get_num_renegotiates                define
BIO_get_read_request                    define
BIO_get_ssl                             define
BIO_get_write_buf_size                  define
BIO_get_write_guarantee                 define
BIO_make_bio_pair                       define
BIO_pending                             define
BIO_read_filename                       define
BIO_reset                               define
BIO_retry_type                          define
BIO_rw_filename                         define
BIO_seek                                define
BIO_set_accept_bios                     define
BIO_set_accept_name                     define
BIO_set_accept_port                     define
BIO_set_accept_ip_family                define
BIO_set_bind_mode                       define
BIO_set_buffer_read_data                define
BIO_set_buffer_size                     define
BIO_set_close                           define
BIO_set_conn_address                    define
BIO_set_conn_hostname                   define
BIO_set_conn_port                       define
BIO_set_conn_ip_family                  define
BIO_set_fd                              define
BIO_set_fp                              define
BIO_set_info_callback                   define
BIO_set_md                              define
BIO_set_mem_buf                         define
BIO_set_mem_eof_return                  define
BIO_set_nbio                            define
BIO_set_nbio_accept                     define
BIO_set_read_buffer_size                define
BIO_set_ssl                             define
BIO_set_ssl_mode                        define
BIO_set_ssl_renegotiate_bytes           define
BIO_set_ssl_renegotiate_timeout         define
BIO_set_write_buf_size                  define
BIO_set_write_buffer_size               define
BIO_should_io_special                   define
BIO_should_read                         define
BIO_should_retry                        define
BIO_should_write                        define
BIO_shutdown_wr                         define
BIO_tell                                define
BIO_wpending                            define
BIO_write_filename                      define
BN_mod                                  define
BN_num_bytes                            define
BN_one                                  define
BN_zero                                 define deprecated 0.9.8
CONF_modules_free                       define deprecated 1.1.0
DES_ecb2_encrypt                        define
DES_ede2_cbc_encrypt                    define
DES_ede2_cfb64_encrypt                  define
DES_ede2_ofb64_encrypt                  define
DTLS_get_link_min_mtu                   define
DTLS_set_link_mtu                       define
ENGINE_cleanup                          define deprecated 1.1.0
ERR_FATAL_ERROR                         define
ERR_GET_FUNC                            define
ERR_GET_LIB                             define
ERR_GET_REASON                          define
ERR_PACK                                define
ERR_free_strings                        define deprecated 1.1.0
ERR_load_crypto_strings                 define deprecated 1.1.0
EVP_DigestSignUpdate                    define
EVP_DigestVerifyUpdate                  define
EVP_MD_CTX_block_size                   define
EVP_MD_CTX_size                         define
EVP_MD_CTX_type                         define
EVP_OpenUpdate                          define
EVP_PKEY_CTX_add1_hkdf_info             define
EVP_PKEY_CTX_add1_tls1_prf_seed         define
EVP_PKEY_CTX_get0_dh_kdf_oid            define
EVP_PKEY_CTX_get0_dh_kdf_ukm            define
EVP_PKEY_CTX_get0_ecdh_kdf_ukm          define
EVP_PKEY_CTX_get0_rsa_oaep_label        define
EVP_PKEY_CTX_get_dh_kdf_md              define
EVP_PKEY_CTX_get_dh_kdf_outlen          define
EVP_PKEY_CTX_get_dh_kdf_type            define
EVP_PKEY_CTX_get_ecdh_cofactor_mode     define
EVP_PKEY_CTX_get_ecdh_kdf_md            define
EVP_PKEY_CTX_get_ecdh_kdf_outlen        define
EVP_PKEY_CTX_get_ecdh_kdf_type          define
EVP_PKEY_CTX_get_rsa_mgf1_md            define
EVP_PKEY_CTX_get_rsa_oaep_md            define
EVP_PKEY_CTX_get_rsa_padding            define
EVP_PKEY_CTX_get_rsa_pss_saltlen        define
EVP_PKEY_CTX_get_signature_md           define
EVP_PKEY_CTX_hkdf_mode                  define
EVP_PKEY_CTX_set0_dh_kdf_oid            define
EVP_PKEY_CTX_set0_dh_kdf_ukm            define
EVP_PKEY_CTX_set0_ecdh_kdf_ukm          define
EVP_PKEY_CTX_set0_rsa_oaep_label        define
EVP_PKEY_CTX_set1_hkdf_key              define
EVP_PKEY_CTX_set1_hkdf_salt             define
EVP_PKEY_CTX_set1_pbe_pass              define
EVP_PKEY_CTX_set1_scrypt_salt           define
EVP_PKEY_CTX_set1_tls1_prf_secret       define
EVP_PKEY_CTX_set_dh_paramgen_generator  define
EVP_PKEY_CTX_set_dh_paramgen_prime_len  define
EVP_PKEY_CTX_set_dh_paramgen_subprime_len     define
EVP_PKEY_CTX_set_dh_paramgen_type       define
EVP_PKEY_CTX_set_dh_kdf_md              define
EVP_PKEY_CTX_set_dh_kdf_outlen          define
EVP_PKEY_CTX_set_dh_kdf_type            define
EVP_PKEY_CTX_set_dh_nid                 define
EVP_PKEY_CTX_set_dh_pad                 define
EVP_PKEY_CTX_set_dh_rfc5114             define
EVP_PKEY_CTX_set_dhx_rfc5114            define
EVP_PKEY_CTX_set_dsa_paramgen_bits      define
EVP_PKEY_CTX_set_dsa_paramgen_q_bits    define
EVP_PKEY_CTX_set_dsa_paramgen_md        define
EVP_PKEY_CTX_set_ec_param_enc           define
EVP_PKEY_CTX_set_ec_paramgen_curve_nid  define
EVP_PKEY_CTX_set_ecdh_cofactor_mode     define
EVP_PKEY_CTX_set_ecdh_kdf_md            define
EVP_PKEY_CTX_set_ecdh_kdf_outlen        define
EVP_PKEY_CTX_set_ecdh_kdf_type          define
EVP_PKEY_CTX_set_hkdf_md                define
EVP_PKEY_CTX_set_mac_key                define
EVP_PKEY_CTX_set_rsa_keygen_bits        define
EVP_PKEY_CTX_set_rsa_keygen_pubexp      define
EVP_PKEY_CTX_set_rsa_keygen_primes      define
EVP_PKEY_CTX_set_rsa_mgf1_md            define
EVP_PKEY_CTX_set_rsa_oaep_md            define
EVP_PKEY_CTX_set_rsa_padding            define
EVP_PKEY_CTX_set_rsa_pss_keygen_mgf1_md define
EVP_PKEY_CTX_set_rsa_pss_keygen_saltlen define
EVP_PKEY_CTX_set_rsa_pss_keygen_md      define
EVP_PKEY_CTX_set_rsa_pss_saltlen        define
EVP_PKEY_CTX_set_scrypt_N               define
EVP_PKEY_CTX_set_scrypt_r               define
EVP_PKEY_CTX_set_scrypt_maxmem_bytes    define
EVP_PKEY_CTX_set_scrypt_p               define
EVP_PKEY_CTX_set_signature_md           define
EVP_PKEY_CTX_set_tls1_prf_md            define
EVP_PKEY_assign_DH                      define
EVP_PKEY_assign_DSA                     define
EVP_PKEY_assign_EC_KEY                  define
EVP_PKEY_assign_POLY1305                define
EVP_PKEY_assign_RSA                     define
EVP_PKEY_assign_SIPHASH                 define
EVP_SealUpdate                          define
EVP_SignInit                            define
EVP_SignInit_ex                         define
EVP_SignUpdate                          define
EVP_VerifyInit                          define
EVP_VerifyInit_ex                       define
EVP_VerifyUpdate                        define
EVP_bf_cfb                              define
EVP_cast5_cfb                           define
EVP_cleanup                             define deprecated 1.1.0
EVP_get_digestbynid                     define
EVP_get_digestbyobj                     define
EVP_idea_cfb                            define
EVP_rc2_cfb                             define
EVP_rc5_32_12_16_cfb                    define
EVP_seed_cfb                            define
EVP_sm4_cfb                             define
OBJ_cleanup                             define deprecated 1.1.0
OPENSSL_VERSION_NUMBER                  define
OPENSSL_VERSION_TEXT                    define
OPENSSL_clear_free                      define
OPENSSL_clear_realloc                   define
OPENSSL_free                            define
OPENSSL_malloc                          define
OPENSSL_malloc_init                     define
OPENSSL_mem_debug_pop                   define
OPENSSL_mem_debug_push                  define
OPENSSL_memdup                          define
OPENSSL_no_config                       define deprecated 1.1.0
OPENSSL_realloc                         define
OPENSSL_secure_actual_size              define
OPENSSL_secure_clear_free               define
OPENSSL_secure_free                     define
OPENSSL_secure_malloc                   define
OPENSSL_secure_zalloc                   define
OPENSSL_strdup                          define
OPENSSL_strndup                         define
OPENSSL_zalloc                          define
OpenSSL_add_all_algorithms              define deprecated 1.1.0
OpenSSL_add_all_ciphers                 define deprecated 1.1.0
OpenSSL_add_all_digests                 define deprecated 1.1.0
OpenSSL_add_ssl_algorithms              define
PEM_FLAG_EAY_COMPATIBLE                 define
PEM_FLAG_ONLY_B64                       define
PEM_FLAG_SECURE                         define
RAND_cleanup                            define deprecated 1.1.0
RAND_DRBG_get_ex_new_index              define
SSL_COMP_free_compression_methods       define deprecated 1.1.0
SSL_CTX_add0_chain_cert                 define
SSL_CTX_add1_chain_cert                 define
SSL_CTX_add_extra_chain_cert            define
SSL_CTX_build_cert_chain                define
SSL_CTX_clear_chain_certs               define
SSL_CTX_clear_extra_chain_certs         define
SSL_CTX_clear_mode                      define
SSL_CTX_decrypt_session_ticket_fn       define
SSL_CTX_disable_ct                      define
SSL_CTX_generate_session_ticket_fn      define
SSL_CTX_get0_chain_certs                define
SSL_CTX_get_default_read_ahead          define
SSL_CTX_get_max_cert_list               define
SSL_CTX_get_max_proto_version           define
SSL_CTX_get_min_proto_version           define
SSL_CTX_get_mode                        define
SSL_CTX_get_read_ahead                  define
SSL_CTX_get_session_cache_mode          define
SSL_CTX_get_tlsext_status_arg           define
SSL_CTX_get_tlsext_status_cb            define
SSL_CTX_get_tlsext_status_type          define
SSL_CTX_select_current_cert             define
SSL_CTX_sess_accept                     define
SSL_CTX_sess_accept_good                define
SSL_CTX_sess_accept_renegotiate         define
SSL_CTX_sess_cache_full                 define
SSL_CTX_sess_cb_hits                    define
SSL_CTX_sess_connect                    define
SSL_CTX_sess_connect_good               define
SSL_CTX_sess_connect_renegotiate        define
SSL_CTX_sess_get_cache_size             define
SSL_CTX_sess_hits                       define
SSL_CTX_sess_misses                     define
SSL_CTX_sess_number                     define
SSL_CTX_sess_set_cache_size             define
SSL_CTX_sess_timeouts                   define
SSL_CTX_set0_chain                      define
SSL_CTX_set0_chain_cert_store           define
SSL_CTX_set0_verify_cert_store          define
SSL_CTX_set1_chain                      define
SSL_CTX_set1_chain_cert_store           define
SSL_CTX_set1_client_sigalgs             define
SSL_CTX_set1_client_sigalgs_list        define
SSL_CTX_set1_curves                     define
SSL_CTX_set1_curves_list                define
SSL_CTX_set1_groups                     define
SSL_CTX_set1_groups_list                define
SSL_CTX_set1_sigalgs                    define
SSL_CTX_set1_sigalgs_list               define
SSL_CTX_set1_verify_cert_store          define
SSL_CTX_set_current_cert                define
SSL_CTX_set_max_cert_list               define
SSL_CTX_set_max_pipelines               define
SSL_CTX_set_max_proto_version           define
SSL_CTX_set_max_send_fragment           define
SSL_CTX_set_min_proto_version           define
SSL_CTX_set_mode                        define
SSL_CTX_set_msg_callback_arg            define
SSL_CTX_set_read_ahead                  define
SSL_CTX_set_session_cache_mode          define
SSL_CTX_set_split_send_fragment         define
SSL_CTX_set_tlsext_servername_arg       define
SSL_CTX_set_tlsext_servername_callback  define
SSL_CTX_set_tlsext_status_arg           define
SSL_CTX_set_tlsext_status_cb            define
SSL_CTX_set_tlsext_status_type          define
SSL_CTX_set_tlsext_ticket_key_cb        define
SSL_CTX_set_tmp_dh                      define
SSL_add0_chain_cert                     define
SSL_add1_chain_cert                     define
SSL_build_cert_chain                    define
SSL_clear_chain_certs                   define
SSL_clear_mode                          define
SSL_disable_ct                          define
SSL_get0_chain_certs                    define
SSL_get0_session                        define
SSL_get1_curves                         define
SSL_get1_groups                         define
SSL_get_cipher                          define
SSL_get_cipher_bits                     define
SSL_get_cipher_name                     define
SSL_get_cipher_version                  define
SSL_get_extms_support                   define
SSL_get_max_cert_list                   define
SSL_get_max_proto_version               define
SSL_get_min_proto_version               define
SSL_get_mode                            define
SSL_get_peer_signature_nid              define
SSL_get_peer_tmp_key                    define
SSL_get_secure_renegotiation_support    define
SSL_get_server_tmp_key                  define
SSL_get_shared_curve                    define
SSL_get_shared_group                    define
SSL_get_signature_nid                   define
SSL_get_time                            define
SSL_get_timeout                         define
SSL_get_tlsext_status_ocsp_resp         define
SSL_get_tlsext_status_type              define
SSL_get_tmp_key                         define
SSL_in_accept_init                      define
SSL_in_connect_init                     define
SSL_library_init                        define
SSL_load_error_strings                  define deprecated 1.1.0
SSL_select_current_cert                 define
SSL_set0_chain                          define
SSL_set0_chain_cert_store               define
SSL_set0_verify_cert_store              define
SSL_set1_chain                          define
SSL_set1_chain_cert_store               define
SSL_set1_client_sigalgs                 define
SSL_set1_client_sigalgs_list            define
SSL_set1_curves                         define
SSL_set1_curves_list                    define
SSL_set1_groups                         define
SSL_set1_groups_list                    define
SSL_set1_sigalgs                        define
SSL_set1_sigalgs_list                   define
SSL_set1_verify_cert_store              define
SSL_set_current_cert                    define
SSL_set_max_cert_list                   define
SSL_set_max_pipelines                   define
SSL_set_max_proto_version               define
SSL_set_max_send_fragment               define
SSL_set_min_proto_version               define
SSL_set_mode                            define
SSL_set_msg_callback_arg                define
SSL_set_mtu                             define
SSL_set_split_send_fragment             define
SSL_set_time                            define
SSL_set_timeout                         define
SSL_set_tlsext_host_name                define
SSL_set_tlsext_status_ocsp_resp         define
SSL_set_tlsext_status_type              define
SSL_set_tmp_dh                          define
SSL_want_async                          define
SSL_want_async_job                      define
SSL_want_client_hello_cb                define
SSL_want_nothing                        define
SSL_want_read                           define
SSL_want_write                          define
SSL_want_x509_lookup                    define
SSLv23_client_method                    define
SSLv23_method                           define
SSLv23_server_method                    define
X509_LOOKUP_add_dir                     define
X509_LOOKUP_load_file                   define
X509_STORE_set_lookup_crls_cb           define
X509_STORE_set_verify_func              define
EVP_PKEY_CTX_set1_id                    define
EVP_PKEY_CTX_get1_id                    define
EVP_PKEY_CTX_get1_id_len                define
