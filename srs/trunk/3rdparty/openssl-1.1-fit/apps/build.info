{- our @apps_openssl_src =
       qw(openssl.c
          asn1pars.c ca.c ciphers.c cms.c crl.c crl2p7.c dgst.c
          enc.c errstr.c
          genpkey.c nseq.c passwd.c pkcs7.c pkcs8.c
          pkey.c pkeyparam.c pkeyutl.c prime.c rand.c req.c
          s_client.c s_server.c s_time.c sess_id.c smime.c speed.c spkac.c
          verify.c version.c x509.c rehash.c storeutl.c);
   our @apps_lib_src =
       ( qw(apps.c opt.c s_cb.c s_socket.c app_rand.c bf_prefix.c),
         split(/\s+/, $target{apps_aux_src}) );
   our @apps_init_src = split(/\s+/, $target{apps_init_src});
   "" -}

IF[{- !$disabled{apps} -}]
  LIBS_NO_INST=libapps.a
  SOURCE[libapps.a]={- join(" ", @apps_lib_src) -}
  INCLUDE[libapps.a]=.. ../include

  PROGRAMS=openssl
  SOURCE[openssl]={- join(" ", @apps_init_src) -}
  SOURCE[openssl]={- join(" ", @apps_openssl_src) -}
  INCLUDE[openssl]=.. ../include
  DEPEND[openssl]=libapps.a ../libssl
  IF[{- !$disabled{'des'} -}]
    SOURCE[openssl]=pkcs12.c
    DEPEND[pkcs12.o]=progs.h
  ENDIF
  IF[{- !$disabled{'ec'} -}]
    SOURCE[openssl]=ec.c ecparam.c
    DEPEND[ec.o]=progs.h
    DEPEND[ecparam.o]=progs.h
  ENDIF
  IF[{- !$disabled{'ocsp'} -}]
    SOURCE[openssl]=ocsp.c
    DEPEND[ocsp.o]=progs.h
  ENDIF
  IF[{- !$disabled{'srp'} -}]
    SOURCE[openssl]=srp.c
    DEPEND[srp.o]=progs.h
  ENDIF
  IF[{- !$disabled{'ts'} -}]
    SOURCE[openssl]=ts.c
    DEPEND[ts.o]=progs.h
  ENDIF
  IF[{- !$disabled{'dh'} -}]
    SOURCE[openssl]=dhparam.c
    DEPEND[dhparam.o]=progs.h
  ENDIF
  IF[{- !$disabled{'dsa'} -}]
    SOURCE[openssl]=dsa.c dsaparam.c gendsa.c
    DEPEND[dsa.o]=progs.h
    DEPEND[dsaparam.o]=progs.h
    DEPEND[gendsa.o]=progs.h
  ENDIF
  IF[{- !$disabled{'engine'} -}]
    SOURCE[openssl]=engine.c
    DEPEND[engine.o]=progs.h
  ENDIF
  IF[{- !$disabled{'rsa'} -}]
    SOURCE[openssl]=rsa.c rsautl.c genrsa.c
    DEPEND[rsa.o]=progs.h
    DEPEND[rsautl.o]=progs.h
    DEPEND[genrsa.o]=progs.h
  ENDIF
  IF[{- $config{target} =~ /^(?:Cygwin|mingw|VC-)/ -}]
    GENERATE[openssl.rc]=../util/mkrc.pl openssl
    SOURCE[openssl]=openssl.rc
  ENDIF

  {- join("\n  ", map { (my $x = $_) =~ s|\.c$|.o|; "DEPEND[$x]=progs.h" }
                  @apps_openssl_src) -}
  GENERATE[progs.h]=progs.pl $(APPS_OPENSSL)
  DEPEND[progs.h]=../configdata.pm

  SCRIPTS=CA.pl tsget.pl
  SOURCE[CA.pl]=CA.pl.in
  SOURCE[tsget.pl]=tsget.in
ENDIF
