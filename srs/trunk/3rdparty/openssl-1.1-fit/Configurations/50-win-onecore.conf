# Windows OneCore targets.
#
# OneCore is new API stability "contract" that transcends Desktop, IoT and
# Mobile[?] Windows editions. It's a set up "umbrella" libraries that
# export subset of Win32 API that are common to all Windows 10 devices.
#
# OneCore Configuration temporarily dedicated for console applications
# due to disabled event logging, which is incompatible with one core.
# Error messages are provided via standard error only.
# TODO: extend error handling to use ETW based eventing
# (Or rework whole error messaging)

my %targets = (
    "VC-WIN32-ONECORE" => {
        inherit_from    => [ "VC-WIN32" ],
        # /NODEFAULTLIB:kernel32.lib is needed, because MSVCRT.LIB has
        # hidden reference to kernel32.lib, but we don't actually want
        # it in "onecore" build.
        lflags          => add("/NODEFAULTLIB:kernel32.lib"),
        defines         => add("OPENSSL_SYS_WIN_CORE"),
        ex_libs         => "onecore.lib",
    },
    "VC-WIN64A-ONECORE" => {
        inherit_from    => [ "VC-WIN64A" ],
        lflags          => add("/NODEFAULTLIB:kernel32.lib"),
        defines         => add("OPENSSL_SYS_WIN_CORE"),
        ex_libs         => "onecore.lib",
    },

    # Windows on ARM targets. ARM compilers are additional components in
    # VS2017, i.e. they are not installed by default. And when installed,
    # there are no "ARM Tool Command Prompt"s on Start menu, you have
    # to locate vcvarsall.bat and act accordingly. VC-WIN32-ARM has
    # received limited testing with evp_test.exe on Windows 10 IoT Core,
    # but not VC-WIN64-ARM, no hardware... In other words they are not
    # actually supported...
    #
    # Another thing to keep in mind [in cross-compilation scenario such
    # as this one] is that target's file system has nothing to do with
    # compilation system's one. This means that you're are likely to use
    # --prefix and --openssldir with target-specific values. 'nmake install'
    # step is effectively meaningless in cross-compilation case, though
    # it might be useful to 'nmake install DESTDIR=S:\ome\where' where you
    # can point Visual Studio to when compiling custom application code.

    "VC-WIN32-ARM" => {
        inherit_from    => [ "VC-noCE-common" ],
        defines         => add("_ARM_WINAPI_PARTITION_DESKTOP_SDK_AVAILABLE",
                               "OPENSSL_SYS_WIN_CORE"),
        bn_ops          => "BN_LLONG RC4_CHAR EXPORT_VAR_AS_FN",
        lflags          => add("/NODEFAULTLIB:kernel32.lib"),
        ex_libs         => "onecore.lib",
        multilib        => "-arm",
    },
    "VC-WIN64-ARM" => {
        inherit_from    => [ "VC-noCE-common" ],
        defines         => add("_ARM_WINAPI_PARTITION_DESKTOP_SDK_AVAILABLE",
                               "OPENSSL_SYS_WIN_CORE"),
        bn_ops          => "SIXTY_FOUR_BIT RC4_CHAR EXPORT_VAR_AS_FN",
        lflags          => add("/NODEFAULTLIB:kernel32.lib"),
        ex_libs         => "onecore.lib",
        multilib        => "-arm64",
    },
);
