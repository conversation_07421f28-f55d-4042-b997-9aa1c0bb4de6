##
## Makefile for OpenSSL
##
## {- join("\n## ", @autowarntext) -}
{-
     our $objext = $target{obj_extension} || ".o";
     our $depext = $target{dep_extension} || ".d";
     our $exeext = $target{exe_extension} || "";
     our $libext = $target{lib_extension} || ".a";
     our $shlibext = $target{shared_extension} || ".so";
     our $shlibvariant = $target{shlib_variant} || "";
     our $shlibextsimple = $target{shared_extension_simple} || ".so";
     our $shlibextimport = $target{shared_import_extension} || "";
     our $dsoext = $target{dso_extension} || ".so";
     our $makedepprog = $disabled{makedepend} ? undef : $config{makedepprog};

     # $mingw_installroot and $mingw_commonroot is relevant for mingw only.
     my $build_scheme = $target{build_scheme};
     my $install_flavour = $build_scheme->[$#$build_scheme]; # last element
     my $mingw_installenv = $install_flavour eq "WOW" ? "ProgramFiles(x86)"
                                                      : "ProgramW6432";
     my $mingw_commonenv = $install_flavour eq "WOW" ? "CommonProgramFiles(x86)"
                                                     : "CommonProgramW6432";
     our $mingw_installroot =
         defined($ENV{$mingw_installenv}) ? $mingw_installenv : 'ProgramFiles';
     our $mingw_commonroot =
         defined($ENV{$mingw_commonenv}) ? $mingw_commonenv : 'CommonProgramFiles';
     my $mingw_installdflt =
         $install_flavour eq "WOW" ? "C:/Program Files (x86)"
                                   : "C:/Program Files";
     my $mingw_commondflt = "$mingw_installdflt/Common Files";

     # expand variables early
     $mingw_installroot = $ENV{$mingw_installroot} // $mingw_installdflt;
     $mingw_commonroot = $ENV{$mingw_commonroot} // $mingw_commondflt;

     sub windowsdll { $config{target} =~ /^(?:Cygwin|mingw)/ }

     # Shared AIX support is special. We put libcrypto[64].so.ver into
     # libcrypto.a and use libcrypto_a.a as static one.
     sub sharedaix  { !$disabled{shared} && $config{target} =~ /^aix/ }

     our $sover_dirname = $config{shlib_version_number};
     $sover_dirname =~ s|\.|_|g
         if $config{target} =~ /^mingw/;

     # shlib and shlib_simple both take a static library name and figure
     # out what the shlib name should be.
     #
     # When OpenSSL is configured "no-shared", these functions will just
     # return empty lists, making them suitable to join().
     #
     # With Windows DLL producers, shlib($libname) will return the shared
     # library name (which usually is different from the static library
     # name) with the default shared extension appended to it, while
     # shlib_simple($libname) will return the static library name with
     # the shared extension followed by ".a" appended to it.  The former
     # result is used as the runtime shared library while the latter is
     # used as the DLL import library.
     #
     # On all Unix systems, shlib($libname) will return the library name
     # with the default shared extension, while shlib_simple($libname)
     # will return the name from shlib($libname) with any SO version number
     # removed.  On some systems, they may therefore return the exact same
     # string.
     sub shlib {
         my $lib = shift;
         return () if $disabled{shared} || $lib =~ /\.a$/;
         return $unified_info{sharednames}->{$lib}. $shlibvariant. '$(SHLIB_EXT)';
     }
     sub shlib_simple {
         my $lib = shift;
         return () if $disabled{shared} || $lib =~ /\.a$/;

         if (windowsdll()) {
             return $lib . '$(SHLIB_EXT_IMPORT)';
         }
         return $lib .  '$(SHLIB_EXT_SIMPLE)';
     }

     # Easy fixing of static library names
     sub lib {
         (my $lib = shift) =~ s/\.a$//;
         return $lib . $libext;
     }

     # dso is a complement to shlib / shlib_simple that returns the
     # given libname with the simple shared extension (possible SO version
     # removed).  This differs from shlib_simple() by being unconditional.
     sub dso {
         my $engine = shift;

         return $engine . $dsoext;
     }
     # This makes sure things get built in the order they need
     # to. You're welcome.
     sub dependmagic {
         my $target = shift;

         return "$target: build_generated\n\t\$(MAKE) depend && \$(MAKE) _$target\n_$target";
     }
     '';
-}
PLATFORM={- $config{target} -}
OPTIONS={- $config{options} -}
CONFIGURE_ARGS=({- join(", ",quotify_l(@{$config{perlargv}})) -})
SRCDIR={- $config{sourcedir} -}
BLDDIR={- $config{builddir} -}

VERSION={- $config{version} -}
MAJOR={- $config{major} -}
MINOR={- $config{minor} -}
SHLIB_VERSION_NUMBER={- $config{shlib_version_number} -}
SHLIB_VERSION_HISTORY={- $config{shlib_version_history} -}
SHLIB_MAJOR={- $config{shlib_major} -}
SHLIB_MINOR={- $config{shlib_minor} -}
SHLIB_TARGET={- $target{shared_target} -}
SHLIB_EXT={- $shlibext -}
SHLIB_EXT_SIMPLE={- $shlibextsimple -}
SHLIB_EXT_IMPORT={- $shlibextimport -}

LIBS={- join(" ", map { lib($_) } @{$unified_info{libraries}}) -}
SHLIBS={- join(" ", map { shlib($_) } @{$unified_info{libraries}}) -}
SHLIB_INFO={- join(" ", map { "\"".shlib($_).";".shlib_simple($_)."\"" } @{$unified_info{libraries}}) -}
ENGINES={- join(" ", map { dso($_) } @{$unified_info{engines}}) -}
PROGRAMS={- join(" ", map { $_.$exeext } @{$unified_info{programs}}) -}
SCRIPTS={- join(" ", @{$unified_info{scripts}}) -}
{- output_off() if $disabled{makedepend}; "" -}
DEPS={- join(" ", map { (my $x = $_) =~ s|\.o$|$depext|; $x; }
                  grep { $unified_info{sources}->{$_}->[0] =~ /\.c$/ }
                  keys %{$unified_info{sources}}); -}
{- output_on() if $disabled{makedepend}; "" -}
GENERATED_MANDATORY={- join(" ", @{$unified_info{depends}->{""}}) -}
GENERATED={- # common0.tmpl provides @generated
             join(" ", @generated ) -}

INSTALL_LIBS={- join(" ", map { lib($_) } @{$unified_info{install}->{libraries}}) -}
INSTALL_SHLIBS={- join(" ", map { shlib($_) } @{$unified_info{install}->{libraries}}) -}
INSTALL_SHLIB_INFO={- join(" ", map { "\"".shlib($_).";".shlib_simple($_)."\"" } @{$unified_info{install}->{libraries}}) -}
INSTALL_ENGINES={- join(" ", map { dso($_) } @{$unified_info{install}->{engines}}) -}
INSTALL_PROGRAMS={- join(" ", map { $_.$exeext } @{$unified_info{install}->{programs}}) -}
{- output_off() if $disabled{apps}; "" -}
BIN_SCRIPTS=$(BLDDIR)/tools/c_rehash
MISC_SCRIPTS=$(BLDDIR)/apps/CA.pl $(BLDDIR)/apps/tsget.pl:tsget
{- output_on() if $disabled{apps}; "" -}

APPS_OPENSSL={- use File::Spec::Functions;
                catfile("apps","openssl") -}

# DESTDIR is for package builders so that they can configure for, say,
# /usr/ and yet have everything installed to /tmp/somedir/usr/.
# Normally it is left empty.
DESTDIR=

{- output_off() if $config{target} =~ /^mingw/; "" -}
# Do not edit these manually. Use Configure with --prefix or --openssldir
# to change this!  Short explanation in the top comment in Configure
INSTALLTOP={- # $prefix is used in the OPENSSLDIR perl snippet
	      #
	      our $prefix = $config{prefix} || "/usr/local";
              $prefix -}
OPENSSLDIR={- #
	      # The logic here is that if no --openssldir was given,
	      # OPENSSLDIR will get the value from $prefix plus "/ssl".
	      # If --openssldir was given and the value is an absolute
	      # path, OPENSSLDIR will get its value without change.
	      # If the value from --openssldir is a relative path,
	      # OPENSSLDIR will get $prefix with the --openssldir
	      # value appended as a subdirectory.
	      #
              use File::Spec::Functions;
              our $openssldir =
                  $config{openssldir} ?
                      (file_name_is_absolute($config{openssldir}) ?
                           $config{openssldir}
                           : catdir($prefix, $config{openssldir}))
                      : catdir($prefix, "ssl");
              $openssldir -}
LIBDIR={- our $libdir = $config{libdir};
          unless ($libdir) {
              #
              # if $prefix/lib$target{multilib} is not an existing
              # directory, then assume that it's not searched by linker
              # automatically, in which case adding $target{multilib} suffix
              # causes more grief than we're ready to tolerate, so don't...
              our $multilib =
                  -d "$prefix/lib$target{multilib}" ? $target{multilib} : "";
              $libdir = "lib$multilib";
          }
          file_name_is_absolute($libdir) ? "" : $libdir -}
# $(libdir) is chosen to be compatible with the GNU coding standards
libdir={- file_name_is_absolute($libdir)
          ? $libdir : '$(INSTALLTOP)/$(LIBDIR)' -}
ENGINESDIR=$(libdir)/engines-{- $sover_dirname -}

# Convenience variable for those who want to set the rpath in shared
# libraries and applications
LIBRPATH=$(libdir)
{- output_on() if $config{target} =~ /^mingw/;
   output_off() if $config{target} !~ /^mingw/;
   "" -}
# Do not edit these manually. Use Configure with --prefix or --openssldir
# to change this!  Short explanation in the top comment in Configure
INSTALLTOP_dev={- # $prefix is used in the OPENSSLDIR perl snippet
                  #
                  use File::Spec::Win32;
                  my $prefix_default = "$mingw_installroot/OpenSSL";
                  our $prefix =
                      File::Spec::Win32->canonpath($config{prefix}
                                                  || $prefix_default);
                  our ($prefix_dev, $prefix_dir, $prefix_file) =
                      File::Spec::Win32->splitpath($prefix, 1);
                  $prefix =~ s|\\|/|g;
                  $prefix_dir =~ s|\\|/|g;
                  $prefix_dev -}
INSTALLTOP_dir={- my $x = File::Spec::Win32->canonpath($prefix_dir);
                  $x =~ s|\\|/|g;
                  $x -}
OPENSSLDIR_dev={- #
                  # The logic here is that if no --openssldir was given,
                  # OPENSSLDIR will get the value "$mingw_commonroot/SSL".
                  # If --openssldir was given and the value is an absolute
                  # path, OPENSSLDIR will get its value without change.
                  # If the value from --openssldir is a relative path,
                  # OPENSSLDIR will get $prefix with the --openssldir
                  # value appended as a subdirectory.
                  #
                  use File::Spec::Win32;
                  our $openssldir =
                      $config{openssldir} ?
                          (File::Spec::Win32->file_name_is_absolute($config{openssldir}) ?
                               File::Spec::Win32->canonpath($config{openssldir})
                               : File::Spec::Win32->catdir($prefix, $config{openssldir}))
                          : File::Spec::Win32->canonpath("$mingw_commonroot/SSL");
                  our ($openssldir_dev, $openssldir_dir, $openssldir_file) =
                      File::Spec::Win32->splitpath($openssldir, 1);
                  $openssldir =~ s|\\|/|g;
                  $openssldir_dir =~ s|\\|/|g;
                  $openssldir_dev -}
OPENSSLDIR_dir={- my $x = File::Spec::Win32->canonpath($openssldir_dir);
                  $x =~ s|\\|/|g;
                  $x -}
LIBDIR={- our $libdir = $config{libdir} || "lib";
          File::Spec::Win32->file_name_is_absolute($libdir) ? "" : $libdir -}
ENGINESDIR_dev={- use File::Spec::Win32;
                  our $enginesdir =
                      File::Spec::Win32->catdir($prefix,$libdir,
                                                "engines-$sover_dirname");
                  our ($enginesdir_dev, $enginesdir_dir, $enginesdir_file) =
                      File::Spec::Win32->splitpath($enginesdir, 1);
                  $enginesdir =~ s|\\|/|g;
                  $enginesdir_dir =~ s|\\|/|g;
                  $enginesdir_dev -}
ENGINESDIR_dir={- my $x = File::Spec::Win32->canonpath($enginesdir_dir);
                  $x =~ s|\\|/|g;
                  $x -}
# In a Windows environment, $(DESTDIR) is harder to contatenate with other
# directory variables, because both may contain devices.  What we do here is
# to adapt INSTALLTOP, OPENSSLDIR and ENGINESDIR depending on if $(DESTDIR)
# has a value or not, to ensure that concatenation will always work further
# down.
ifneq "$(DESTDIR)" ""
INSTALLTOP=$(INSTALLTOP_dir)
OPENSSLDIR=$(OPENSSLDIR_dir)
ENGINESDIR=$(ENGINESDIR_dir)
else
INSTALLTOP=$(INSTALLTOP_dev)$(INSTALLTOP_dir)
OPENSSLDIR=$(OPENSSLDIR_dev)$(OPENSSLDIR_dir)
ENGINESDIR=$(ENGINESDIR_dev)$(ENGINESDIR_dir)
endif

# $(libdir) is chosen to be compatible with the GNU coding standards
libdir={- File::Spec::Win32->file_name_is_absolute($libdir)
          ? $libdir : '$(INSTALLTOP)/$(LIBDIR)' -}
{- output_on() if $config{target} !~ /^mingw/; "" -}

MANDIR=$(INSTALLTOP)/share/man
DOCDIR=$(INSTALLTOP)/share/doc/$(BASENAME)
HTMLDIR=$(DOCDIR)/html

# MANSUFFIX is for the benefit of anyone who may want to have a suffix
# appended after the manpage file section number.  "ssl" is popular,
# resulting in files such as config.5ssl rather than config.5.
MANSUFFIX=
HTMLSUFFIX=html

# For "optional" echo messages, to get "real" silence
ECHO = echo

##### User defined commands and flags ################################

# We let the C compiler driver to take care of .s files. This is done in
# order to be excused from maintaining a separate set of architecture
# dependent assembler flags. E.g. if you throw -mcpu=ultrasparc at SPARC
# gcc, then the driver will automatically translate it to -xarch=v8plus
# and pass it down to assembler.  In any case, we do not define AS or
# ASFLAGS for this reason.

CROSS_COMPILE={- $config{CROSS_COMPILE} -}
CC=$(CROSS_COMPILE){- $config{CC} -}
CXX={- $config{CXX} ? "\$(CROSS_COMPILE)$config{CXX}" : '' -}
CPPFLAGS={- our $cppflags1 = join(" ",
                                  (map { "-D".$_} @{$config{CPPDEFINES}}),
                                  (map { "-I".$_} @{$config{CPPINCLUDES}}),
                                  @{$config{CPPFLAGS}}) -}
CFLAGS={- join(' ', @{$config{CFLAGS}}) -}
CXXFLAGS={- join(' ', @{$config{CXXFLAGS}}) -}
LDFLAGS= {- join(' ', @{$config{LDFLAGS}}) -}
EX_LIBS= {- join(' ', @{$config{LDLIBS}}) -}

MAKEDEPEND={- $config{makedepprog} -}

PERL={- $config{PERL} -}

AR=$(CROSS_COMPILE){- $config{AR} -}
ARFLAGS= {- join(' ', @{$config{ARFLAGS}}) -}
RANLIB={- $config{RANLIB} ? "\$(CROSS_COMPILE)$config{RANLIB}" : "true"; -}
RC= $(CROSS_COMPILE){- $config{RC} -}
RCFLAGS={- join(' ', @{$config{RCFLAGS}}) -} {- $target{shared_rcflag} -}

RM= rm -f
RMDIR= rmdir
TAR= {- $target{TAR} || "tar" -}
TARFLAGS= {- $target{TARFLAGS} -}

BASENAME=       openssl
NAME=           $(BASENAME)-$(VERSION)
# Relative to $(SRCDIR)
TARFILE=        ../$(NAME).tar

##### Project flags ##################################################

# Variables starting with CNF_ are common variables for all product types

CNF_CPPFLAGS={- our $cppflags2 =
                    join(' ', $target{cppflags} || (),
                              (map { "-D".$_} @{$target{defines}},
                                              @{$config{defines}}),
                              (map { "-I".$_} @{$target{includes}},
                                              @{$config{includes}}),
                              @{$config{cppflags}}) -}
CNF_CFLAGS={- join(' ', $target{cflags} || (),
                        @{$config{cflags}}) -}
CNF_CXXFLAGS={- join(' ', $target{cxxflags} || (),
                          @{$config{cxxflags}}) -}
CNF_LDFLAGS={- join(' ', $target{lflags} || (),
                         @{$config{lflags}}) -}
CNF_EX_LIBS={- join(' ', $target{ex_libs} || (),
                         @{$config{ex_libs}}) -}

# Variables starting with LIB_ are used to build library object files
# and shared libraries.
# Variables starting with DSO_ are used to build DSOs and their object files.
# Variables starting with BIN_ are used to build programs and their object
# files.

LIB_CPPFLAGS={- our $lib_cppflags =
                join(' ', $target{lib_cppflags} || (),
                          $target{shared_cppflag} || (),
                          (map { '-D'.$_ }
                               @{$config{lib_defines} || ()},
                               @{$config{shared_defines} || ()}),
                          @{$config{lib_cppflags}},
                          @{$config{shared_cppflag}});
                join(' ', $lib_cppflags,
                          (map { '-D'.$_ }
                               'OPENSSLDIR="\"$(OPENSSLDIR)\""',
                               'ENGINESDIR="\"$(ENGINESDIR)\""'),
                          '$(CNF_CPPFLAGS)', '$(CPPFLAGS)') -}
LIB_CFLAGS={- join(' ', $target{lib_cflags} || (),
                        $target{shared_cflag} || (),
                        @{$config{lib_cflags}},
                        @{$config{shared_cflag}},
                        '$(CNF_CFLAGS)', '$(CFLAGS)') -}
LIB_CXXFLAGS={- join(' ', $target{lib_cxxflags} || (),
                          $target{shared_cxxflag} || (),
                          @{$config{lib_cxxflags}},
                          @{$config{shared_cxxflag}},
                          '$(CNF_CXXFLAGS)', '$(CXXFLAGS)') -}
LIB_LDFLAGS={- join(' ', $target{shared_ldflag} || (),
                         $config{shared_ldflag} || (),
                         '$(CNF_LDFLAGS)', '$(LDFLAGS)') -}
LIB_EX_LIBS=$(CNF_EX_LIBS) $(EX_LIBS)
DSO_CPPFLAGS={- join(' ', $target{dso_cppflags} || (),
                          $target{module_cppflags} || (),
                          (map { '-D'.$_ }
                               @{$config{dso_defines} || ()},
                               @{$config{module_defines} || ()}),
                          @{$config{dso_cppflags}},
                          @{$config{module_cppflags}},
                          '$(CNF_CPPFLAGS)', '$(CPPFLAGS)') -}
DSO_CFLAGS={- join(' ', $target{dso_cflags} || (),
                        $target{module_cflags} || (),
                        @{$config{dso_cflags}},
                        @{$config{module_cflags}},
                        '$(CNF_CFLAGS)', '$(CFLAGS)') -}
DSO_CXXFLAGS={- join(' ', $target{dso_cxxflags} || (),
                          $target{module_cxxflags} || (),
                          @{$config{dso_cxxflags}},
                          @{$config{module_cxxflag}},
                          '$(CNF_CXXFLAGS)', '$(CXXFLAGS)') -}
DSO_LDFLAGS={- join(' ', $target{dso_ldflags} || (),
                         $target{module_ldflags} || (),
                         @{$config{dso_ldflags}},
                         @{$config{module_ldflags}},
                         '$(CNF_LDFLAGS)', '$(LDFLAGS)') -}
DSO_EX_LIBS=$(CNF_EX_LIBS) $(EX_LIBS)
BIN_CPPFLAGS={- join(' ', $target{bin_cppflags} || (),
                          (map { '-D'.$_ } @{$config{bin_defines} || ()}),
                          @{$config{bin_cppflags}},
                          '$(CNF_CPPFLAGS)', '$(CPPFLAGS)') -}
BIN_CFLAGS={- join(' ', $target{bin_cflags} || (),
                        @{$config{bin_cflags}},
                        '$(CNF_CFLAGS)', '$(CFLAGS)') -}
BIN_CXXFLAGS={- join(' ', $target{bin_cxxflags} || (),
                          @{$config{bin_cxxflags}},
                          '$(CNF_CXXFLAGS)', '$(CXXFLAGS)') -}
BIN_LDFLAGS={- join(' ', $target{bin_lflags} || (),
                         @{$config{bin_lflags}},
                         '$(CNF_LDFLAGS)', '$(LDFLAGS)') -}
BIN_EX_LIBS=$(CNF_EX_LIBS) $(EX_LIBS)

# CPPFLAGS_Q is used for one thing only: to build up buildinf.h
CPPFLAGS_Q={- $cppflags1 =~ s|([\\"])|\\$1|g;
              $cppflags2 =~ s|([\\"])|\\$1|g;
              $lib_cppflags =~ s|([\\"])|\\$1|g;
              join(' ', $lib_cppflags || (), $cppflags2 || (),
                        $cppflags1 || ()) -}

PERLASM_SCHEME= {- $target{perlasm_scheme} -}

# For x86 assembler: Set PROCESSOR to 386 if you want to support
# the 80386.
PROCESSOR= {- $config{processor} -}

# We want error [and other] messages in English. Trouble is that make(1)
# doesn't pass macros down as environment variables unless there already
# was corresponding variable originally set. In other words we can only
# reassign environment variables, but not set new ones, not in portable
# manner that is. That's why we reassign several, just to be sure...
LC_ALL=C
LC_MESSAGES=C
LANG=C

# The main targets ###################################################

{- dependmagic('all'); -}: build_libs_nodep build_engines_nodep build_programs_nodep link-utils
{- dependmagic('build_libs'); -}: build_libs_nodep
{- dependmagic('build_engines'); -}: build_engines_nodep
{- dependmagic('build_programs'); -}: build_programs_nodep

build_generated: $(GENERATED_MANDATORY)
build_libs_nodep: libcrypto.pc libssl.pc openssl.pc
build_engines_nodep: $(ENGINES)
build_programs_nodep: $(PROGRAMS) $(SCRIPTS)

# Kept around for backward compatibility
build_apps build_tests: build_programs

# Convenience target to prebuild all generated files, not just the mandatory
# ones
build_all_generated: $(GENERATED_MANDATORY) $(GENERATED)
	@ : {- output_off() if $disabled{makedepend}; "" -}
	@echo "Warning: consider configuring with no-makedepend, because if"
	@echo "         target system doesn't have $(PERL),"
	@echo "         then make will fail..."
	@ : {- output_on() if $disabled{makedepend}; "" -}

test: tests
{- dependmagic('tests'); -}: build_programs_nodep build_engines_nodep link-utils
	@ : {- output_off() if $disabled{tests}; "" -}
	( cd test; \
	  mkdir -p test-runs; \
	  SRCTOP=../$(SRCDIR) \
	  BLDTOP=../$(BLDDIR) \
	  RESULT_D=test-runs \
	  PERL="$(PERL)" \
	  EXE_EXT={- $exeext -} \
	  OPENSSL_ENGINES=`cd ../$(BLDDIR)/engines 2>/dev/null && pwd` \
	  OPENSSL_DEBUG_MEMORY=on \
	    $(PERL) ../$(SRCDIR)/test/run_tests.pl $(TESTS) )
	@ : {- if ($disabled{tests}) { output_on(); } else { output_off(); } "" -}
	@echo "Tests are not supported with your chosen Configure options"
	@ : {- output_on() if !$disabled{tests}; "" -}

list-tests:
	@ : {- output_off() if $disabled{tests}; "" -}
	@SRCTOP="$(SRCDIR)" \
	 $(PERL) $(SRCDIR)/test/run_tests.pl list
	@ : {- if ($disabled{tests}) { output_on(); } else { output_off(); } "" -}
	@echo "Tests are not supported with your chosen Configure options"
	@ : {- output_on() if !$disabled{tests}; "" -}

install: install_sw install_ssldirs install_docs

uninstall: uninstall_docs uninstall_sw

libclean:
	@set -e; for s in $(SHLIB_INFO); do \
		if [ "$$s" = ";" ]; then continue; fi; \
		s1=`echo "$$s" | cut -f1 -d";"`; \
		s2=`echo "$$s" | cut -f2 -d";"`; \
		$(ECHO) $(RM) $$s1; {- output_off() unless windowsdll(); "" -}\
		$(RM) apps/$$s1; \
		$(RM) test/$$s1; \
		$(RM) fuzz/$$s1; {- output_on() unless windowsdll(); "" -}\
		$(RM) $$s1; \
		if [ "$$s1" != "$$s2" ]; then \
			$(ECHO) $(RM) $$s2; \
			$(RM) $$s2; \
		fi; \
	done
	$(RM) $(LIBS)
	$(RM) *.map

clean: libclean
	$(RM) $(PROGRAMS) $(TESTPROGS) $(ENGINES) $(SCRIPTS)
	$(RM) $(GENERATED_MANDATORY) $(GENERATED)
	-$(RM) `find . -name '*{- $depext -}' \! -name '.*' \! -type d -print`
	-$(RM) `find . -name '*{- $objext -}' \! -name '.*' \! -type d -print`
	$(RM) core
	$(RM) tags TAGS doc-nits
	$(RM) -r test/test-runs
	$(RM) openssl.pc libcrypto.pc libssl.pc
	-$(RM) `find . -type l \! -name '.*' -print`

distclean: clean
	$(RM) configdata.pm
	$(RM) Makefile

# We check if any depfile is newer than Makefile and decide to
# concatenate only if that is true.
depend:
	@: {- output_off() if $disabled{makedepend}; "" -}
	@$(PERL) $(SRCDIR)/util/add-depends.pl {-
		 defined $makedepprog  && $makedepprog =~ /\/makedepend/
                 ? 'makedepend' : 'gcc' -}
	@: {- output_on() if $disabled{makedepend}; "" -}

# Install helper targets #############################################

install_sw: install_dev install_engines install_runtime

uninstall_sw: uninstall_runtime uninstall_engines uninstall_dev

install_docs: install_man_docs install_html_docs

uninstall_docs: uninstall_man_docs uninstall_html_docs
	$(RM) -r "$(DESTDIR)$(DOCDIR)"

install_ssldirs:
	@$(PERL) $(SRCDIR)/util/mkdir-p.pl "$(DESTDIR)$(OPENSSLDIR)/certs"
	@$(PERL) $(SRCDIR)/util/mkdir-p.pl "$(DESTDIR)$(OPENSSLDIR)/private"
	@$(PERL) $(SRCDIR)/util/mkdir-p.pl "$(DESTDIR)$(OPENSSLDIR)/misc"
	@set -e; for x in dummy $(MISC_SCRIPTS); do \
		if [ "$$x" = "dummy" ]; then continue; fi; \
		x1=`echo "$$x" | cut -f1 -d:`; \
		x2=`echo "$$x" | cut -f2 -d:`; \
		fn=`basename $$x1`; \
		$(ECHO) "install $$x1 -> $(DESTDIR)$(OPENSSLDIR)/misc/$$fn"; \
		cp $$x1 "$(DESTDIR)$(OPENSSLDIR)/misc/$$fn.new"; \
		chmod 755 "$(DESTDIR)$(OPENSSLDIR)/misc/$$fn.new"; \
		mv -f "$(DESTDIR)$(OPENSSLDIR)/misc/$$fn.new" \
		      "$(DESTDIR)$(OPENSSLDIR)/misc/$$fn"; \
		if [ "$$x1" != "$$x2" ]; then \
			ln=`basename "$$x2"`; \
			: {- output_off() unless windowsdll(); "" -}; \
			$(ECHO) "copy $(DESTDIR)$(OPENSSLDIR)/misc/$$ln -> $(DESTDIR)$(OPENSSLDIR)/misc/$$fn"; \
			cp "$(DESTDIR)$(OPENSSLDIR)/misc/$$fn" "$(DESTDIR)$(OPENSSLDIR)/misc/$$ln"; \
			: {- output_on() unless windowsdll();
			     output_off() if windowsdll(); "" -}; \
			$(ECHO) "link $(DESTDIR)$(OPENSSLDIR)/misc/$$ln -> $(DESTDIR)$(OPENSSLDIR)/misc/$$fn"; \
			ln -sf $$fn "$(DESTDIR)$(OPENSSLDIR)/misc/$$ln"; \
			: {- output_on() if windowsdll(); "" -}; \
		fi; \
	done
	@$(ECHO) "install $(SRCDIR)/apps/openssl.cnf -> $(DESTDIR)$(OPENSSLDIR)/openssl.cnf.dist"
	@cp $(SRCDIR)/apps/openssl.cnf "$(DESTDIR)$(OPENSSLDIR)/openssl.cnf.new"
	@chmod 644 "$(DESTDIR)$(OPENSSLDIR)/openssl.cnf.new"
	@mv -f "$(DESTDIR)$(OPENSSLDIR)/openssl.cnf.new" "$(DESTDIR)$(OPENSSLDIR)/openssl.cnf.dist"
	@if [ ! -f "$(DESTDIR)$(OPENSSLDIR)/openssl.cnf" ]; then \
		$(ECHO) "install $(SRCDIR)/apps/openssl.cnf -> $(DESTDIR)$(OPENSSLDIR)/openssl.cnf"; \
		cp $(SRCDIR)/apps/openssl.cnf "$(DESTDIR)$(OPENSSLDIR)/openssl.cnf"; \
		chmod 644 "$(DESTDIR)$(OPENSSLDIR)/openssl.cnf"; \
	fi
	@$(ECHO) "install $(SRCDIR)/apps/ct_log_list.cnf -> $(DESTDIR)$(OPENSSLDIR)/ct_log_list.cnf.dist"
	@cp $(SRCDIR)/apps/ct_log_list.cnf "$(DESTDIR)$(OPENSSLDIR)/ct_log_list.cnf.new"
	@chmod 644 "$(DESTDIR)$(OPENSSLDIR)/ct_log_list.cnf.new"
	@mv -f "$(DESTDIR)$(OPENSSLDIR)/ct_log_list.cnf.new" "$(DESTDIR)$(OPENSSLDIR)/ct_log_list.cnf.dist"
	@if [ ! -f "$(DESTDIR)$(OPENSSLDIR)/ct_log_list.cnf" ]; then \
		$(ECHO) "install $(SRCDIR)/apps/ct_log_list.cnf -> $(DESTDIR)$(OPENSSLDIR)/ct_log_list.cnf"; \
		cp $(SRCDIR)/apps/ct_log_list.cnf "$(DESTDIR)$(OPENSSLDIR)/ct_log_list.cnf"; \
		chmod 644 "$(DESTDIR)$(OPENSSLDIR)/ct_log_list.cnf"; \
	fi

install_dev: install_runtime_libs
	@[ -n "$(INSTALLTOP)" ] || (echo INSTALLTOP should not be empty; exit 1)
	@$(ECHO) "*** Installing development files"
	@$(PERL) $(SRCDIR)/util/mkdir-p.pl "$(DESTDIR)$(INSTALLTOP)/include/openssl"
	@ : {- output_off() unless grep { $_ eq "OPENSSL_USE_APPLINK" } (@{$target{defines}}, @{$config{defines}}); "" -}
	@$(ECHO) "install $(SRCDIR)/ms/applink.c -> $(DESTDIR)$(INSTALLTOP)/include/openssl/applink.c"
	@cp $(SRCDIR)/ms/applink.c "$(DESTDIR)$(INSTALLTOP)/include/openssl/applink.c"
	@chmod 644 "$(DESTDIR)$(INSTALLTOP)/include/openssl/applink.c"
	@ : {- output_on() unless grep { $_ eq "OPENSSL_USE_APPLINK" } (@{$target{defines}}, @{$config{defines}}); "" -}
	@set -e; for i in $(SRCDIR)/include/openssl/*.h \
			  $(BLDDIR)/include/openssl/*.h; do \
		fn=`basename $$i`; \
		$(ECHO) "install $$i -> $(DESTDIR)$(INSTALLTOP)/include/openssl/$$fn"; \
		cp $$i "$(DESTDIR)$(INSTALLTOP)/include/openssl/$$fn"; \
		chmod 644 "$(DESTDIR)$(INSTALLTOP)/include/openssl/$$fn"; \
	done
	@$(PERL) $(SRCDIR)/util/mkdir-p.pl "$(DESTDIR)$(libdir)"
	@set -e; for l in $(INSTALL_LIBS); do \
		fn=`basename $$l`; \
		$(ECHO) "install $$l -> $(DESTDIR)$(libdir)/$$fn"; \
		cp $$l "$(DESTDIR)$(libdir)/$$fn.new"; \
		$(RANLIB) "$(DESTDIR)$(libdir)/$$fn.new"; \
		chmod 644 "$(DESTDIR)$(libdir)/$$fn.new"; \
		mv -f "$(DESTDIR)$(libdir)/$$fn.new" \
		      "$(DESTDIR)$(libdir)/$$fn"; \
	done
	@ : {- output_off() if $disabled{shared}; "" -}
	@set -e; for s in $(INSTALL_SHLIB_INFO); do \
		s1=`echo "$$s" | cut -f1 -d";"`; \
		s2=`echo "$$s" | cut -f2 -d";"`; \
		fn1=`basename $$s1`; \
		fn2=`basename $$s2`; \
		: {- output_off(); output_on() unless windowsdll() or sharedaix(); "" -}; \
		if [ "$$fn1" != "$$fn2" ]; then \
			$(ECHO) "link $(DESTDIR)$(libdir)/$$fn2 -> $(DESTDIR)$(libdir)/$$fn1"; \
			ln -sf $$fn1 "$(DESTDIR)$(libdir)/$$fn2"; \
		fi; \
		: {- output_off() unless windowsdll() or sharedaix(); output_on() if windowsdll(); "" -}; \
		$(ECHO) "install $$s2 -> $(DESTDIR)$(libdir)/$$fn2"; \
		cp $$s2 "$(DESTDIR)$(libdir)/$$fn2.new"; \
		chmod 755 "$(DESTDIR)$(libdir)/$$fn2.new"; \
		mv -f "$(DESTDIR)$(libdir)/$$fn2.new" \
		      "$(DESTDIR)$(libdir)/$$fn2"; \
		: {- output_off() if windowsdll(); output_on() if sharedaix(); "" -}; \
		a="$(DESTDIR)$(libdir)/$$fn2"; \
		$(ECHO) "install $$s1 -> $$a"; \
		if [ -f "$$a" ]; then ( trap "rm -rf /tmp/ar.$$$$" INT 0; \
			mkdir /tmp/ar.$$$$; ( cd /tmp/ar.$$$$; \
			cp -f "$$a" "$$a.new"; \
			for so in `$(AR) t "$$a"`; do \
				$(AR) x "$$a" "$$so"; \
				chmod u+w "$$so"; \
				strip -X32_64 -e "$$so"; \
				$(AR) r "$$a.new" "$$so"; \
			done; \
		)); fi; \
		$(AR) r "$$a.new" "$$s1"; \
		mv -f "$$a.new" "$$a"; \
		: {- output_off() if sharedaix(); output_on(); "" -}; \
	done
	@ : {- output_on() if $disabled{shared}; "" -}
	@$(PERL) $(SRCDIR)/util/mkdir-p.pl "$(DESTDIR)$(libdir)/pkgconfig"
	@$(ECHO) "install libcrypto.pc -> $(DESTDIR)$(libdir)/pkgconfig/libcrypto.pc"
	@cp libcrypto.pc "$(DESTDIR)$(libdir)/pkgconfig"
	@chmod 644 "$(DESTDIR)$(libdir)/pkgconfig/libcrypto.pc"
	@$(ECHO) "install libssl.pc -> $(DESTDIR)$(libdir)/pkgconfig/libssl.pc"
	@cp libssl.pc "$(DESTDIR)$(libdir)/pkgconfig"
	@chmod 644 "$(DESTDIR)$(libdir)/pkgconfig/libssl.pc"
	@$(ECHO) "install openssl.pc -> $(DESTDIR)$(libdir)/pkgconfig/openssl.pc"
	@cp openssl.pc "$(DESTDIR)$(libdir)/pkgconfig"
	@chmod 644 "$(DESTDIR)$(libdir)/pkgconfig/openssl.pc"

uninstall_dev: uninstall_runtime_libs
	@$(ECHO) "*** Uninstalling development files"
	@ : {- output_off() unless grep { $_ eq "OPENSSL_USE_APPLINK" } (@{$target{defines}}, @{$config{defines}}); "" -}
	@$(ECHO) "$(RM) $(DESTDIR)$(INSTALLTOP)/include/openssl/applink.c"
	@$(RM) "$(DESTDIR)$(INSTALLTOP)/include/openssl/applink.c"
	@ : {- output_on() unless grep { $_ eq "OPENSSL_USE_APPLINK" } (@{$target{defines}}, @{$config{defines}}); "" -}
	@set -e; for i in $(SRCDIR)/include/openssl/*.h \
			  $(BLDDIR)/include/openssl/*.h; do \
		fn=`basename $$i`; \
		$(ECHO) "$(RM) $(DESTDIR)$(INSTALLTOP)/include/openssl/$$fn"; \
		$(RM) "$(DESTDIR)$(INSTALLTOP)/include/openssl/$$fn"; \
	done
	-$(RMDIR) "$(DESTDIR)$(INSTALLTOP)/include/openssl"
	-$(RMDIR) "$(DESTDIR)$(INSTALLTOP)/include"
	@set -e; for l in $(INSTALL_LIBS); do \
		fn=`basename $$l`; \
		$(ECHO) "$(RM) $(DESTDIR)$(libdir)/$$fn"; \
		$(RM) "$(DESTDIR)$(libdir)/$$fn"; \
	done
	@ : {- output_off() if $disabled{shared}; "" -}
	@set -e; for s in $(INSTALL_SHLIB_INFO); do \
		s1=`echo "$$s" | cut -f1 -d";"`; \
		s2=`echo "$$s" | cut -f2 -d";"`; \
		fn1=`basename $$s1`; \
		fn2=`basename $$s2`; \
		: {- output_off() if windowsdll(); "" -}; \
		$(ECHO) "$(RM) $(DESTDIR)$(libdir)/$$fn2"; \
		$(RM) "$(DESTDIR)$(libdir)/$$fn2"; \
		if [ "$$fn1" != "$$fn2" -a -f "$(DESTDIR)$(libdir)/$$fn1" ]; then \
			$(ECHO) "$(RM) $(DESTDIR)$(libdir)/$$fn1"; \
			$(RM) "$(DESTDIR)$(libdir)/$$fn1"; \
		fi; \
		: {- output_on() if windowsdll(); "" -}{- output_off() unless windowsdll(); "" -}; \
		$(ECHO) "$(RM) $(DESTDIR)$(libdir)/$$fn2"; \
		$(RM) "$(DESTDIR)$(libdir)/$$fn2"; \
		: {- output_on() unless windowsdll(); "" -}; \
	done
	@ : {- output_on() if $disabled{shared}; "" -}
	$(RM) "$(DESTDIR)$(libdir)/pkgconfig/libcrypto.pc"
	$(RM) "$(DESTDIR)$(libdir)/pkgconfig/libssl.pc"
	$(RM) "$(DESTDIR)$(libdir)/pkgconfig/openssl.pc"
	-$(RMDIR) "$(DESTDIR)$(libdir)/pkgconfig"
	-$(RMDIR) "$(DESTDIR)$(libdir)"

install_engines: install_runtime_libs build_engines
	@[ -n "$(INSTALLTOP)" ] || (echo INSTALLTOP should not be empty; exit 1)
	@$(PERL) $(SRCDIR)/util/mkdir-p.pl "$(DESTDIR)$(ENGINESDIR)/"
	@$(ECHO) "*** Installing engines"
	@set -e; for e in dummy $(INSTALL_ENGINES); do \
		if [ "$$e" = "dummy" ]; then continue; fi; \
		fn=`basename $$e`; \
		$(ECHO) "install $$e -> $(DESTDIR)$(ENGINESDIR)/$$fn"; \
		cp $$e "$(DESTDIR)$(ENGINESDIR)/$$fn.new"; \
		chmod 755 "$(DESTDIR)$(ENGINESDIR)/$$fn.new"; \
		mv -f "$(DESTDIR)$(ENGINESDIR)/$$fn.new" \
		      "$(DESTDIR)$(ENGINESDIR)/$$fn"; \
	done

uninstall_engines:
	@$(ECHO) "*** Uninstalling engines"
	@set -e; for e in dummy $(INSTALL_ENGINES); do \
		if [ "$$e" = "dummy" ]; then continue; fi; \
		fn=`basename $$e`; \
		if [ "$$fn" = '{- dso("ossltest") -}' ]; then \
			continue; \
		fi; \
		$(ECHO) "$(RM) $(DESTDIR)$(ENGINESDIR)/$$fn"; \
		$(RM) "$(DESTDIR)$(ENGINESDIR)/$$fn"; \
	done
	-$(RMDIR) "$(DESTDIR)$(ENGINESDIR)"

install_runtime: install_programs

install_runtime_libs: build_libs
	@[ -n "$(INSTALLTOP)" ] || (echo INSTALLTOP should not be empty; exit 1)
	@ : {- output_off() if windowsdll(); "" -}
	@$(PERL) $(SRCDIR)/util/mkdir-p.pl "$(DESTDIR)$(libdir)"
	@ : {- output_on() if windowsdll(); output_off() unless windowsdll(); "" -}
	@$(PERL) $(SRCDIR)/util/mkdir-p.pl "$(DESTDIR)$(INSTALLTOP)/bin"
	@ : {- output_on() unless windowsdll(); "" -}
	@$(ECHO) "*** Installing runtime libraries"
	@set -e; for s in dummy $(INSTALL_SHLIBS); do \
		if [ "$$s" = "dummy" ]; then continue; fi; \
		fn=`basename $$s`; \
		: {- output_off() unless windowsdll(); "" -}; \
		$(ECHO) "install $$s -> $(DESTDIR)$(INSTALLTOP)/bin/$$fn"; \
		cp $$s "$(DESTDIR)$(INSTALLTOP)/bin/$$fn.new"; \
		chmod 755 "$(DESTDIR)$(INSTALLTOP)/bin/$$fn.new"; \
		mv -f "$(DESTDIR)$(INSTALLTOP)/bin/$$fn.new" \
		      "$(DESTDIR)$(INSTALLTOP)/bin/$$fn"; \
		: {- output_on() unless windowsdll(); "" -}{- output_off() if windowsdll(); "" -}; \
		$(ECHO) "install $$s -> $(DESTDIR)$(libdir)/$$fn"; \
		cp $$s "$(DESTDIR)$(libdir)/$$fn.new"; \
		chmod 755 "$(DESTDIR)$(libdir)/$$fn.new"; \
		mv -f "$(DESTDIR)$(libdir)/$$fn.new" \
		      "$(DESTDIR)$(libdir)/$$fn"; \
		: {- output_on() if windowsdll(); "" -}; \
	done

install_programs: install_runtime_libs build_programs
	@[ -n "$(INSTALLTOP)" ] || (echo INSTALLTOP should not be empty; exit 1)
	@$(PERL) $(SRCDIR)/util/mkdir-p.pl "$(DESTDIR)$(INSTALLTOP)/bin"
	@$(ECHO) "*** Installing runtime programs"
	@set -e; for x in dummy $(INSTALL_PROGRAMS); do \
		if [ "$$x" = "dummy" ]; then continue; fi; \
		fn=`basename $$x`; \
		$(ECHO) "install $$x -> $(DESTDIR)$(INSTALLTOP)/bin/$$fn"; \
		cp $$x "$(DESTDIR)$(INSTALLTOP)/bin/$$fn.new"; \
		chmod 755 "$(DESTDIR)$(INSTALLTOP)/bin/$$fn.new"; \
		mv -f "$(DESTDIR)$(INSTALLTOP)/bin/$$fn.new" \
		      "$(DESTDIR)$(INSTALLTOP)/bin/$$fn"; \
	done
	@set -e; for x in dummy $(BIN_SCRIPTS); do \
		if [ "$$x" = "dummy" ]; then continue; fi; \
		fn=`basename $$x`; \
		$(ECHO) "install $$x -> $(DESTDIR)$(INSTALLTOP)/bin/$$fn"; \
		cp $$x "$(DESTDIR)$(INSTALLTOP)/bin/$$fn.new"; \
		chmod 755 "$(DESTDIR)$(INSTALLTOP)/bin/$$fn.new"; \
		mv -f "$(DESTDIR)$(INSTALLTOP)/bin/$$fn.new" \
		      "$(DESTDIR)$(INSTALLTOP)/bin/$$fn"; \
	done

uninstall_runtime: uninstall_programs uninstall_runtime_libs

uninstall_programs:
	@$(ECHO) "*** Uninstalling runtime programs"
	@set -e; for x in dummy $(INSTALL_PROGRAMS); \
	do  \
		if [ "$$x" = "dummy" ]; then continue; fi; \
		fn=`basename $$x`; \
		$(ECHO) "$(RM) $(DESTDIR)$(INSTALLTOP)/bin/$$fn"; \
		$(RM) "$(DESTDIR)$(INSTALLTOP)/bin/$$fn"; \
	done;
	@set -e; for x in dummy $(BIN_SCRIPTS); \
	do  \
		if [ "$$x" = "dummy" ]; then continue; fi; \
		fn=`basename $$x`; \
		$(ECHO) "$(RM) $(DESTDIR)$(INSTALLTOP)/bin/$$fn"; \
		$(RM) "$(DESTDIR)$(INSTALLTOP)/bin/$$fn"; \
	done
	-$(RMDIR) "$(DESTDIR)$(INSTALLTOP)/bin"

uninstall_runtime_libs:
	@$(ECHO) "*** Uninstalling runtime libraries"
	@ : {- output_off() unless windowsdll(); "" -}
	@set -e; for s in dummy $(INSTALL_SHLIBS); do \
		if [ "$$s" = "dummy" ]; then continue; fi; \
		fn=`basename $$s`; \
		$(ECHO) "$(RM) $(DESTDIR)$(INSTALLTOP)/bin/$$fn"; \
		$(RM) "$(DESTDIR)$(INSTALLTOP)/bin/$$fn"; \
	done
	@ : {- output_on() unless windowsdll(); "" -}


install_man_docs:
	@[ -n "$(INSTALLTOP)" ] || (echo INSTALLTOP should not be empty; exit 1)
	@$(ECHO) "*** Installing manpages"
	$(PERL) $(SRCDIR)/util/process_docs.pl \
		"--destdir=$(DESTDIR)$(MANDIR)" --type=man --suffix=$(MANSUFFIX)

uninstall_man_docs:
	@$(ECHO) "*** Uninstalling manpages"
	$(PERL) $(SRCDIR)/util/process_docs.pl \
		"--destdir=$(DESTDIR)$(MANDIR)" --type=man --suffix=$(MANSUFFIX) \
		--remove

install_html_docs:
	@[ -n "$(INSTALLTOP)" ] || (echo INSTALLTOP should not be empty; exit 1)
	@$(ECHO) "*** Installing HTML manpages"
	$(PERL) $(SRCDIR)/util/process_docs.pl \
		"--destdir=$(DESTDIR)$(HTMLDIR)" --type=html

uninstall_html_docs:
	@$(ECHO) "*** Uninstalling manpages"
	$(PERL) $(SRCDIR)/util/process_docs.pl \
		"--destdir=$(DESTDIR)$(HTMLDIR)" --type=html --remove


# Developer targets (note: these are only available on Unix) #########

update: generate errors ordinals

generate: generate_apps generate_crypto_bn generate_crypto_objects \
          generate_crypto_conf generate_crypto_asn1 generate_fuzz_oids

.PHONY: doc-nits
doc-nits:
	(cd $(SRCDIR); $(PERL) util/find-doc-nits -n -p ) >doc-nits
	@if [ -s doc-nits ] ; then cat doc-nits ; exit 1; \
	else echo 'doc-nits: no errors.'; rm doc-nits ; fi

# Test coverage is a good idea for the future
#coverage: $(PROGRAMS) $(TESTPROGRAMS)
#	...

lint:
	lint -DLINT $(INCLUDES) $(SRCS)

generate_apps:
	( cd $(SRCDIR); $(PERL) VMS/VMSify-conf.pl \
				< apps/openssl.cnf > apps/openssl-vms.cnf )

generate_crypto_bn:
	( cd $(SRCDIR); $(PERL) crypto/bn/bn_prime.pl > crypto/bn/bn_prime.h )

generate_crypto_objects:
	( cd $(SRCDIR); $(PERL) crypto/objects/objects.pl -n \
				crypto/objects/objects.txt \
				crypto/objects/obj_mac.num \
				> crypto/objects/obj_mac.new && \
	    mv crypto/objects/obj_mac.new crypto/objects/obj_mac.num )
	( cd $(SRCDIR); $(PERL) crypto/objects/objects.pl \
				crypto/objects/objects.txt \
				crypto/objects/obj_mac.num \
				> include/openssl/obj_mac.h )
	( cd $(SRCDIR); $(PERL) crypto/objects/obj_dat.pl \
				include/openssl/obj_mac.h \
				> crypto/objects/obj_dat.h )
	( cd $(SRCDIR); $(PERL) crypto/objects/objxref.pl \
				crypto/objects/obj_mac.num \
				crypto/objects/obj_xref.txt \
				> crypto/objects/obj_xref.h )

generate_crypto_conf:
	( cd $(SRCDIR); $(PERL) crypto/conf/keysets.pl \
			        > crypto/conf/conf_def.h )

generate_crypto_asn1:
	( cd $(SRCDIR); $(PERL) crypto/asn1/charmap.pl \
			        > crypto/asn1/charmap.h )

generate_fuzz_oids:
	( cd $(SRCDIR); $(PERL) fuzz/mkfuzzoids.pl \
				crypto/objects/obj_dat.h \
				> fuzz/oids.txt )

# Set to -force to force a rebuild
ERROR_REBUILD=
errors:
	( b=`pwd`; set -e; cd $(SRCDIR); \
          $(PERL) util/ck_errf.pl -strict -internal; \
          $(PERL) -I$$b util/mkerr.pl $(ERROR_REBUILD) -internal )
	( b=`pwd`; set -e; cd $(SRCDIR)/engines; \
          for E in *.ec ; do \
              $(PERL) ../util/ck_errf.pl -strict \
                -conf $$E `basename $$E .ec`.c; \
              $(PERL) -I$$b ../util/mkerr.pl $(ERROR_REBUILD) -static \
                -conf $$E `basename $$E .ec`.c ; \
          done )

ordinals:
	$(PERL) $(SRCDIR)/util/mkdef.pl crypto update
	$(PERL) $(SRCDIR)/util/mkdef.pl ssl update

test_ordinals:
	( cd test; \
	  SRCTOP=../$(SRCDIR) \
	  BLDTOP=../$(BLDDIR) \
	    $(PERL) ../$(SRCDIR)/test/run_tests.pl test_ordinals )

tags TAGS: FORCE
	rm -f TAGS tags
	-ctags -R .
	-etags `find . -name '*.[ch]' -o -name '*.pm'`

# Release targets (note: only available on Unix) #####################

tar:
	(cd $(SRCDIR); ./util/mktar.sh --name='$(NAME)' --tarfile='$(TARFILE)')

# Helper targets #####################################################

link-utils: $(BLDDIR)/util/opensslwrap.sh

$(BLDDIR)/util/opensslwrap.sh: configdata.pm
	@if [ "$(SRCDIR)" != "$(BLDDIR)" ]; then \
	    mkdir -p "$(BLDDIR)/util"; \
	    ln -sf "../$(SRCDIR)/util/opensslwrap.sh" "$(BLDDIR)/util"; \
	fi

FORCE:

# Building targets ###################################################

libcrypto.pc libssl.pc openssl.pc: configdata.pm $(LIBS) {- join(" ",map { shlib_simple($_) } @{$unified_info{libraries}}) -}
libcrypto.pc:
	@ ( echo 'prefix=$(INSTALLTOP)'; \
	    echo 'exec_prefix=$${prefix}'; \
	    if [ -n "$(LIBDIR)" ]; then \
	        echo 'libdir=$${exec_prefix}/$(LIBDIR)'; \
	    else \
	        echo 'libdir=$(libdir)'; \
	    fi; \
	    echo 'includedir=$${prefix}/include'; \
	    echo 'enginesdir=$${libdir}/engines-{- $sover_dirname -}'; \
	    echo ''; \
	    echo 'Name: OpenSSL-libcrypto'; \
	    echo 'Description: OpenSSL cryptography library'; \
	    echo 'Version: '$(VERSION); \
	    echo 'Libs: -L$${libdir} -lcrypto'; \
	    echo 'Libs.private: $(LIB_EX_LIBS)'; \
	    echo 'Cflags: -I$${includedir}' ) > libcrypto.pc

libssl.pc:
	@ ( echo 'prefix=$(INSTALLTOP)'; \
	    echo 'exec_prefix=$${prefix}'; \
	    if [ -n "$(LIBDIR)" ]; then \
	        echo 'libdir=$${exec_prefix}/$(LIBDIR)'; \
	    else \
	        echo 'libdir=$(libdir)'; \
	    fi; \
	    echo 'includedir=$${prefix}/include'; \
	    echo ''; \
	    echo 'Name: OpenSSL-libssl'; \
	    echo 'Description: Secure Sockets Layer and cryptography libraries'; \
	    echo 'Version: '$(VERSION); \
	    echo 'Requires.private: libcrypto'; \
	    echo 'Libs: -L$${libdir} -lssl'; \
	    echo 'Cflags: -I$${includedir}' ) > libssl.pc

openssl.pc:
	@ ( echo 'prefix=$(INSTALLTOP)'; \
	    echo 'exec_prefix=$${prefix}'; \
	    if [ -n "$(LIBDIR)" ]; then \
	        echo 'libdir=$${exec_prefix}/$(LIBDIR)'; \
	    else \
	        echo 'libdir=$(libdir)'; \
	    fi; \
	    echo 'includedir=$${prefix}/include'; \
	    echo ''; \
	    echo 'Name: OpenSSL'; \
	    echo 'Description: Secure Sockets Layer and cryptography libraries and tools'; \
	    echo 'Version: '$(VERSION); \
	    echo 'Requires: libssl libcrypto' ) > openssl.pc

configdata.pm: $(SRCDIR)/Configure $(SRCDIR)/config {- join(" ", @{$config{build_file_templates}}, @{$config{build_infos}}, @{$config{conf_files}}) -}
	@echo "Detected changed: $?"
	$(PERL) configdata.pm -r
	@echo "**************************************************"
	@echo "***                                            ***"
	@echo "***   Please run the same make command again   ***"
	@echo "***                                            ***"
	@echo "**************************************************"
	@false

reconfigure reconf:
	$(PERL) configdata.pm -r

{-
  use File::Basename;
  use File::Spec::Functions qw/:DEFAULT abs2rel rel2abs/;

  # Helper function to figure out dependencies on libraries
  # It takes a list of library names and outputs a list of dependencies
  sub compute_lib_depends {
      if ($disabled{shared}) {
          return map { lib($_) } @_;
      }

      # Depending on shared libraries:
      # On Windows POSIX layers, we depend on {libname}.dll.a
      # On Unix platforms, we depend on {shlibname}.so
      return map { $_ =~ /\.a$/ ? $`.$libext : shlib_simple($_) } @_;
  }

  sub generatesrc {
      my %args = @_;
      my $generator = join(" ", @{$args{generator}});
      my $generator_incs = join("", map { " -I".$_ } @{$args{generator_incs}});
      my $incs = join("", map { " -I".$_ } @{$args{incs}});
      my $deps = join(" ", @{$args{generator_deps}}, @{$args{deps}});

      if ($args{src} !~ /\.[sS]$/) {
          if ($args{generator}->[0] =~ m|^.*\.in$|) {
              my $dofile = abs2rel(rel2abs(catfile($config{sourcedir},
                                                   "util", "dofile.pl")),
                                   rel2abs($config{builddir}));
              return <<"EOF";
$args{src}: $args{generator}->[0] $deps
	\$(PERL) "-I\$(BLDDIR)" -Mconfigdata "$dofile" \\
	    "-o$target{build_file}" $generator > \$@
EOF
	  } else {
              return <<"EOF";
$args{src}: $args{generator}->[0] $deps
	\$(PERL)$generator_incs $generator > \$@
EOF
	  }
      } else {
          if ($args{generator}->[0] =~ /\.pl$/) {
              $generator = 'CC="$(CC)" $(PERL)'.$generator_incs.' '.$generator;
          } elsif ($args{generator}->[0] =~ /\.m4$/) {
              $generator = 'm4 -B 8192'.$generator_incs.' '.$generator.' >'
          } elsif ($args{generator}->[0] =~ /\.S$/) {
              $generator = undef;
          } else {
              die "Generator type for $args{src} unknown: $generator\n";
          }

          my $cppflags = {
              lib => '$(LIB_CFLAGS) $(LIB_CPPFLAGS)',
              dso => '$(DSO_CFLAGS) $(DSO_CPPFLAGS)',
              bin => '$(BIN_CFLAGS) $(BIN_CPPFLAGS)'
          } -> {$args{intent}};
          if (defined($generator)) {
              return <<"EOF";
$args{src}: $args{generator}->[0] $deps
	$generator \$@
EOF
          }
          return <<"EOF";
$args{src}: $args{generator}->[0] $deps
	\$(CC) $incs $cppflags -E $args{generator}->[0] | \\
	\$(PERL) -ne '/^#(line)?\\s*[0-9]+/ or print' > \$@
EOF
      }
  }

  # Should one wonder about the end of the Perl snippet, it's because this
  # second regexp eats up line endings as well, if the removed path is the
  # last in the line.  We may therefore need to put back a line ending.
  sub src2obj {
      my %args = @_;
      (my $obj = $args{obj}) =~ s|\.o$||;
      my @srcs = @{$args{srcs}};
      my $srcs = join(" ",  @srcs);
      my $deps = join(" ", @srcs, @{$args{deps}});
      my $incs = join("", map { " -I".$_ } @{$args{incs}});
      my $cmd;
      my $cmdflags;
      my $cmdcompile;
      if (grep /\.rc$/, @srcs) {
          $cmd = '$(RC)';
          $cmdflags = '$(RCFLAGS)';
          $cmdcompile = '';
      } elsif (grep /\.(cc|cpp)$/, @srcs) {
          $cmd = '$(CXX)';
          $cmdcompile = ' -c';
          $cmdflags = {
              lib => '$(LIB_CXXFLAGS) $(LIB_CPPFLAGS)',
              dso => '$(DSO_CXXFLAGS) $(DSO_CPPFLAGS)',
              bin => '$(BIN_CXXFLAGS) $(BIN_CPPFLAGS)'
          } -> {$args{intent}};
      } else {
          $cmd = '$(CC)';
          $cmdcompile = ' -c';
          $cmdflags = {
              lib => '$(LIB_CFLAGS) $(LIB_CPPFLAGS)',
              dso => '$(DSO_CFLAGS) $(DSO_CPPFLAGS)',
              bin => '$(BIN_CFLAGS) $(BIN_CPPFLAGS)'
          } -> {$args{intent}};
      }
      my $recipe;
      # extension-specific rules
      if (grep /\.s$/, @srcs) {
          $recipe .= <<"EOF";
$obj$objext: $deps
	$cmd $cmdflags -c -o \$\@ $srcs
EOF
      } elsif (grep /\.S$/, @srcs) {
          # Originally there was mutli-step rule with $(CC) -E file.S
          # followed by $(CC) -c file.s. It compensated for one of
          # legacy platform compiler's inability to handle .S files.
          # The platform is long discontinued by vendor so there is
          # hardly a point to drag it along...
          $recipe .= <<"EOF";
$obj$objext: $deps
	$cmd $incs $cmdflags -c -o \$\@ $srcs
EOF
      } elsif (defined $makedepprog && $makedepprog !~ /\/makedepend/
               && !grep /\.rc$/, @srcs) {
          $recipe .= <<"EOF";
$obj$objext: $deps
	$cmd $incs $cmdflags -MMD -MF $obj$depext.tmp -MT \$\@ -c -o \$\@ $srcs
	\@touch $obj$depext.tmp
	\@if cmp $obj$depext.tmp $obj$depext > /dev/null 2> /dev/null; then \\
		rm -f $obj$depext.tmp; \\
	else \\
		mv $obj$depext.tmp $obj$depext; \\
	fi
EOF
      } else {
          $recipe .= <<"EOF";
$obj$objext: $deps
	$cmd $incs $cmdflags $cmdcompile -o \$\@ $srcs
EOF
          if (defined $makedepprog  && $makedepprog =~ /\/makedepend/) {
              $recipe .= <<"EOF";
	\$(MAKEDEPEND) -f- -Y -- $incs $cmdflags -- $srcs 2>/dev/null \\
	    > $obj$depext
EOF
          }
      }
      return $recipe;
  }
  # We *know* this routine is only called when we've configure 'shared'.
  sub libobj2shlib {
      my %args = @_;
      my $lib = $args{lib};
      my $shlib = $args{shlib};
      my $libd = dirname($lib);
      my $libn = basename($lib);
      (my $libname = $libn) =~ s/^lib//;
      my @linkdirs = ();
      foreach (@{args{deps}}) {
          my $d = dirname($_);
          push @linkdirs, $d unless grep { $d eq $_ } @linkdirs;
      }
      my $linkflags = join("", map { "-L$_ " } @linkdirs);
      my $linklibs = join("", map { my $f = basename($_);
                                    (my $l = $f) =~ s/^lib//;
                                    " -l$l" } @{$args{deps}});
      my @objs = map { (my $x = $_) =~ s|\.o$||; "$x$objext" }
                 grep { $_ !~ m/\.(?:def|map)$/ }
                 @{$args{objs}};
      my @defs = grep { $_ =~ /\.(?:def|map)$/ } @{$args{objs}};
      my @deps = compute_lib_depends(@{$args{deps}});
      die "More than one exported symbol map" if scalar @defs > 1;
      my $objs = join(" ", @objs);
      my $deps = join(" ", @objs, @defs, @deps);
      my $simple = shlib_simple($lib);
      my $full = shlib($lib);
      my $target = "$simple $full";
      my $shared_soname = "";
      $shared_soname .= ' '.$target{shared_sonameflag}.basename($full)
          if defined $target{shared_sonameflag};
      my $shared_imp = "";
      $shared_imp .= ' '.$target{shared_impflag}.basename($simple)
          if defined $target{shared_impflag};
      my $shared_def = join("", map { ' '.$target{shared_defflag}.$_ } @defs);
      my $recipe = <<"EOF";
$target: $deps
	\$(CC) \$(LIB_CFLAGS) $linkflags\$(LIB_LDFLAGS)$shared_soname$shared_imp \\
		-o $full$shared_def $objs \\
                $linklibs \$(LIB_EX_LIBS)
EOF
      if (windowsdll()) {
          $recipe .= <<"EOF";
	rm -f apps/$shlib'\$(SHLIB_EXT)'
	rm -f test/$shlib'\$(SHLIB_EXT)'
	rm -f fuzz/$shlib'\$(SHLIB_EXT)'
	cp -p $shlib'\$(SHLIB_EXT)' apps/
	cp -p $shlib'\$(SHLIB_EXT)' test/
	cp -p $shlib'\$(SHLIB_EXT)' fuzz/
EOF
      } elsif (sharedaix()) {
          $recipe .= <<"EOF";
	rm -f $simple && \\
	\$(AR) r $simple $full
EOF
      } else {
          $recipe .= <<"EOF";
	if [ '$simple' != '$full' ]; then \\
		rm -f $simple; \\
		ln -s $full $simple; \\
	fi
EOF
      }
  }
  sub obj2dso {
      my %args = @_;
      my $dso = $args{lib};
      my $dsod = dirname($dso);
      my $dson = basename($dso);
      my @linkdirs = ();
      foreach (@{args{deps}}) {
          my $d = dirname($_);
          push @linkdirs, $d unless grep { $d eq $_ } @linkdirs;
      }
      my $linkflags = join("", map { "-L$_ " } @linkdirs);
      my $linklibs = join("", map { my $f = basename($_);
                                    (my $l = $f) =~ s/^lib//;
                                    " -l$l" } @{$args{deps}});
      my @objs = map { (my $x = $_) =~ s|\.o$||; "$x$objext" }
                 grep { $_ !~ m/\.(?:def|map)$/ }
                 @{$args{objs}};
      my @deps = compute_lib_depends(@{$args{deps}});
      my $objs = join(" ", @objs);
      my $deps = join(" ", @deps);
      my $target = dso($dso);
      return <<"EOF";
$target: $objs $deps
	\$(CC) \$(DSO_CFLAGS) $linkflags\$(DSO_LDFLAGS) \\
		-o $target $objs \\
                $linklibs \$(DSO_EX_LIBS)
EOF
  }
  sub obj2lib {
      my %args = @_;
      (my $lib = $args{lib}) =~ s/\.a$//;
      my @objs = map { (my $x = $_) =~ s|\.o$|$objext|; $x } @{$args{objs}};
      my $objs = join(" ", @objs);
      return <<"EOF";
$lib$libext: $objs
	\$(AR) \$(ARFLAGS) \$\@ \$\?
	\$(RANLIB) \$\@ || echo Never mind.
EOF
  }
  sub obj2bin {
      my %args = @_;
      my $bin = $args{bin};
      my $bind = dirname($bin);
      my $binn = basename($bin);
      my $objs = join(" ", map { (my $x = $_) =~ s|\.o$||; "$x$objext" }
                           @{$args{objs}});
      my $deps = join(" ",compute_lib_depends(@{$args{deps}}));
      my @linkdirs = ();
      foreach (@{args{deps}}) {
          next if $_ =~ /\.a$/;
          my $d = dirname($_);
          push @linkdirs, $d unless grep { $d eq $_ } @linkdirs;
      }
      my $linkflags = join("", map { "-L$_ " } @linkdirs);
      my $linklibs = join("", map { if ($_ =~ s/\.a$//) {
                                        " $_$libext";
                                    } else {
                                        my $f = basename($_);
                                        (my $l = $f) =~ s/^lib//;
                                        " -l$l"
                                    }
                                  } @{$args{deps}});
      my $cmd = '$(CC)';
      my $cmdflags = '$(BIN_CFLAGS)';
      if (grep /_cc\.o$/, @{$args{objs}}) {
          $cmd = '$(CXX)';
          $cmdflags = '$(BIN_CXXFLAGS)';
      }
      return <<"EOF";
$bin$exeext: $objs $deps
	rm -f $bin$exeext
	\$\${LDCMD:-$cmd} $cmdflags $linkflags\$(BIN_LDFLAGS) \\
		-o $bin$exeext $objs \\
		$linklibs \$(BIN_EX_LIBS)
EOF
  }
  sub in2script {
      my %args = @_;
      my $script = $args{script};
      my $sources = join(" ", @{$args{sources}});
      my $dofile = abs2rel(rel2abs(catfile($config{sourcedir},
                                           "util", "dofile.pl")),
                           rel2abs($config{builddir}));
      return <<"EOF";
$script: $sources
	\$(PERL) "-I\$(BLDDIR)" -Mconfigdata "$dofile" \\
	    "-o$target{build_file}" $sources > "$script"
	chmod a+x $script
EOF
  }
  sub generatedir {
      my %args = @_;
      my $dir = $args{dir};
      my @deps = map { s|\.o$|$objext|; $_ } @{$args{deps}};
      my @actions = ();
      my %extinfo = ( dso => $dsoext,
                      lib => $libext,
                      bin => $exeext );

      # We already have a 'test' target, and the top directory is just plain
      # silly
      return if $dir eq "test" || $dir eq ".";

      foreach my $type (("dso", "lib", "bin", "script")) {
          next unless defined($unified_info{dirinfo}->{$dir}->{products}->{$type});
          # For lib object files, we could update the library.  However, it
          # was decided that it's enough to build the directory local object
          # files, so we don't need to add any actions, and the dependencies
          # are already taken care of.
          if ($type ne "lib") {
              foreach my $prod (@{$unified_info{dirinfo}->{$dir}->{products}->{$type}}) {
                  if (dirname($prod) eq $dir) {
                      push @deps, $prod.$extinfo{$type};
                  } else {
                      push @actions, "\t@ : No support to produce $type ".join(", ", @{$unified_info{dirinfo}->{$dir}->{products}->{$type}});
                  }
              }
          }
      }

      my $deps = join(" ", @deps);
      my $actions = join("\n", "", @actions);
      return <<"EOF";
$dir $dir/: $deps$actions
EOF
  }
  ""    # Important!  This becomes part of the template result.
-}
