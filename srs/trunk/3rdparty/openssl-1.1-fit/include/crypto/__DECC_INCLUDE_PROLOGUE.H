/*
 * Copyright 2016 The OpenSSL Project Authors. All Rights Reserved.
 *
 * Licensed under the OpenSSL license (the "License").  You may not use
 * this file except in compliance with the License.  You can obtain a copy
 * in the file LICENSE in the source distribution or at
 * https://www.openssl.org/source/license.html
 */

/*
 * This file is only used by HP C on VMS, and is included automatically
 * after each header file from this directory
 */

/* save state */
#pragma names save
/* have the compiler shorten symbols larger than 31 chars to 23 chars
 * followed by a 8 hex char CRC
 */
#pragma names as_is,shortened
