LIBS=../../libcrypto
SOURCE[../../libcrypto]=\
        cmll_ecb.c cmll_ofb.c cmll_cfb.c cmll_ctr.c \
        {- $target{cmll_asm_src} -}

GENERATE[cmll-x86.s]=asm/cmll-x86.pl \
        $(PERLASM_SCHEME) $(LIB_CFLAGS) $(LIB_CPPFLAGS) \
        $(PROCESSOR)
DEPEND[cmll-x86.s]=../perlasm/x86asm.pl
GENERATE[cmll-x86_64.s]=asm/cmll-x86_64.pl $(PERLASM_SCHEME)
GENERATE[cmllt4-sparcv9.S]=asm/cmllt4-sparcv9.pl $(PERLASM_SCHEME)
INCLUDE[cmllt4-sparcv9.o]=..
DEPEND[cmllt4-sparcv9.S]=../perlasm/sparcv9_modes.pl
