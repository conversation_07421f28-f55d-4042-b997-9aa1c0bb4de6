image:
  - Visual Studio 2017

platform:
    - x64
    - x86

environment:
    fast_finish: true
    matrix:
        - VSVER: 15

configuration:
    - shared
    - plain
    - minimal

before_build:
    - ps: >-
        Install-Module VSSetup -Scope CurrentUser
    - ps: >-
        Get-VSSetupInstance -All
    - ps: >-
        gci env:* | sort-object name
    - ps: >-
        If ($env:Platform -Match "x86") {
            $env:VCVARS_PLATFORM="x86"
            $env:TARGET="VC-WIN32 no-asm --strict-warnings"
        } Else {
            $env:VCVARS_PLATFORM="amd64"
            $env:TARGET="VC-WIN64A-masm"
        }
    - ps: >-
        If ($env:Configuration -Match "shared") {
            $env:SHARED="no-makedepend"
        } ElseIf ($env:Configuration -Match "minimal") {
            $env:SHARED="no-shared no-dso no-makedepend no-aria no-async no-autoload-config no-blake2 no-bf no-camellia no-cast no-chacha no-cmac no-cms no-comp no-ct no-des no-dgram no-dh no-dsa no-dtls no-ec2m no-engine no-filenames no-gost no-idea no-mdc2 no-md4 no-multiblock no-nextprotoneg no-ocsp no-ocb no-poly1305 no-psk no-rc2 no-rc4 no-rmd160 no-seed no-siphash no-sm2 no-sm3 no-sm4 no-srp no-srtp no-ssl3 no-ssl3-method no-ts no-ui-console no-whirlpool no-asm -DOPENSSL_SMALL_FOOTPRINT"
        } Else {
            $env:SHARED="no-shared no-makedepend"
        }
    - call "C:\Program Files (x86)\Microsoft Visual Studio\2017\Community\VC\Auxiliary\Build\vcvarsall.bat" %VCVARS_PLATFORM%
    - mkdir _build
    - cd _build
    - perl ..\Configure %TARGET% %SHARED%
    - perl configdata.pm --dump
    - cd ..
    - ps: >-
        if (-not $env:APPVEYOR_PULL_REQUEST_NUMBER`
            -or (&git log -1 $env:APPVEYOR_PULL_REQUEST_HEAD_COMMIT |
                 Select-String "\[extended tests\]") ) {
            $env:EXTENDED_TESTS="yes"
        }

build_script:
    - cd _build
    - ps: >-
        If ($env:Configuration -Match "shared" -or $env:EXTENDED_TESTS) {
            cmd /c "nmake build_all_generated 2>&1"
            cmd /c "nmake PERL=no-perl 2>&1"
        }
    - cd ..

test_script:
    - cd _build
    - ps: >-
        If ($env:Configuration -Match "shared" -or $env:EXTENDED_TESTS) {
            if ($env:EXTENDED_TESTS) {
                cmd /c "nmake test V=1 2>&1"
            } Else {
                cmd /c "nmake test V=1 TESTS=-test_fuzz 2>&1"
            }
        }
    - ps: >-
        if ($env:EXTENDED_TESTS) {
            mkdir ..\_install
            cmd /c "nmake install DESTDIR=..\_install 2>&1"
        }
    - cd ..
