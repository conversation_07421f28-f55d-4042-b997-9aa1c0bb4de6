/*
 * AAC data
 * Copyright (c) 2005-2006 O<PERSON> ( ods15 ods15 dyndns org )
 * Copyright (c) 2006-2007 <PERSON> Gavrilov ( maxim.gavrilov gmail com )
 *
 * This file is part of FFmpeg.
 *
 * FFmpeg is free software; you can redistribute it and/or
 * modify it under the terms of the GNU Lesser General Public
 * License as published by the Free Software Foundation; either
 * version 2.1 of the License, or (at your option) any later version.
 *
 * FFmpeg is distributed in the hope that it will be useful,
 * but WITHOUT ANY WARRANTY; without even the implied warranty of
 * MERCHANTABILITY or FITNESS FOR A PARTICULAR PURPOSE.  See the GNU
 * Lesser General Public License for more details.
 *
 * You should have received a copy of the GNU Lesser General Public
 * License along with FFmpeg; if not, write to the Free Software
 * Foundation, Inc., 51 Franklin Street, Fifth Floor, Boston, MA 02110-1301 USA
 */

/**
 * @file
 * AAC data
 * <AUTHOR> <PERSON>  ( ods15 ods15 dyndns org )
 * <AUTHOR> ( maxim.gavrilov gmail com )
 */

#include "libavutil/mem.h"
#include "aac.h"

#include <stdint.h>

float ff_aac_pow2sf_tab[428];
float ff_aac_pow34sf_tab[428];

DECLARE_ALIGNED(32, float,  ff_aac_kbd_long_1024)[1024];
DECLARE_ALIGNED(32, float,  ff_aac_kbd_short_128)[128];
DECLARE_ALIGNED(32, float,  ff_aac_kbd_long_960)[960];
DECLARE_ALIGNED(32, float,  ff_aac_kbd_short_120)[120];
DECLARE_ALIGNED(32, int,    ff_aac_kbd_long_1024_fixed)[1024];
DECLARE_ALIGNED(32, int,    ff_aac_kbd_short_128_fixed)[128];

const uint8_t ff_aac_num_swb_1024[] = {
    41, 41, 47, 49, 49, 51, 47, 47, 43, 43, 43, 40, 40
};

const uint8_t ff_aac_num_swb_960[] = {
    40, 40, 46, 49, 49, 49, 46, 46, 42, 42, 42, 40, 40
};

const uint8_t ff_aac_num_swb_512[] = {
     0,  0,  0, 36, 36, 37, 31, 31,  0,  0,  0,  0,  0
};

const uint8_t ff_aac_num_swb_480[] = {
     0,  0,  0, 35, 35, 37, 30, 30,  0,  0,  0,  0,  0
};

const uint8_t ff_aac_num_swb_128[] = {
    12, 12, 12, 14, 14, 14, 15, 15, 15, 15, 15, 15, 15
};

const uint8_t ff_aac_num_swb_120[] = {
    12, 12, 12, 14, 14, 14, 15, 15, 15, 15, 15, 15, 15
};

const uint8_t ff_aac_pred_sfb_max[] = {
    33, 33, 38, 40, 40, 40, 41, 41, 37, 37, 37, 34, 34
};

const uint32_t ff_aac_scalefactor_code[121] = {
    0x3ffe8, 0x3ffe6, 0x3ffe7, 0x3ffe5, 0x7fff5, 0x7fff1, 0x7ffed, 0x7fff6,
    0x7ffee, 0x7ffef, 0x7fff0, 0x7fffc, 0x7fffd, 0x7ffff, 0x7fffe, 0x7fff7,
    0x7fff8, 0x7fffb, 0x7fff9, 0x3ffe4, 0x7fffa, 0x3ffe3, 0x1ffef, 0x1fff0,
    0x0fff5, 0x1ffee, 0x0fff2, 0x0fff3, 0x0fff4, 0x0fff1, 0x07ff6, 0x07ff7,
    0x03ff9, 0x03ff5, 0x03ff7, 0x03ff3, 0x03ff6, 0x03ff2, 0x01ff7, 0x01ff5,
    0x00ff9, 0x00ff7, 0x00ff6, 0x007f9, 0x00ff4, 0x007f8, 0x003f9, 0x003f7,
    0x003f5, 0x001f8, 0x001f7, 0x000fa, 0x000f8, 0x000f6, 0x00079, 0x0003a,
    0x00038, 0x0001a, 0x0000b, 0x00004, 0x00000, 0x0000a, 0x0000c, 0x0001b,
    0x00039, 0x0003b, 0x00078, 0x0007a, 0x000f7, 0x000f9, 0x001f6, 0x001f9,
    0x003f4, 0x003f6, 0x003f8, 0x007f5, 0x007f4, 0x007f6, 0x007f7, 0x00ff5,
    0x00ff8, 0x01ff4, 0x01ff6, 0x01ff8, 0x03ff8, 0x03ff4, 0x0fff0, 0x07ff4,
    0x0fff6, 0x07ff5, 0x3ffe2, 0x7ffd9, 0x7ffda, 0x7ffdb, 0x7ffdc, 0x7ffdd,
    0x7ffde, 0x7ffd8, 0x7ffd2, 0x7ffd3, 0x7ffd4, 0x7ffd5, 0x7ffd6, 0x7fff2,
    0x7ffdf, 0x7ffe7, 0x7ffe8, 0x7ffe9, 0x7ffea, 0x7ffeb, 0x7ffe6, 0x7ffe0,
    0x7ffe1, 0x7ffe2, 0x7ffe3, 0x7ffe4, 0x7ffe5, 0x7ffd7, 0x7ffec, 0x7fff4,
    0x7fff3,
};

const uint8_t ff_aac_scalefactor_bits[121] = {
    18, 18, 18, 18, 19, 19, 19, 19, 19, 19, 19, 19, 19, 19, 19, 19,
    19, 19, 19, 18, 19, 18, 17, 17, 16, 17, 16, 16, 16, 16, 15, 15,
    14, 14, 14, 14, 14, 14, 13, 13, 12, 12, 12, 11, 12, 11, 10, 10,
    10,  9,  9,  8,  8,  8,  7,  6,  6,  5,  4,  3,  1,  4,  4,  5,
     6,  6,  7,  7,  8,  8,  9,  9, 10, 10, 10, 11, 11, 11, 11, 12,
    12, 13, 13, 13, 14, 14, 16, 15, 16, 15, 18, 19, 19, 19, 19, 19,
    19, 19, 19, 19, 19, 19, 19, 19, 19, 19, 19, 19, 19, 19, 19, 19,
    19, 19, 19, 19, 19, 19, 19, 19, 19,
};

static const uint16_t codes1[81] = {
    0x7f8, 0x1f1, 0x7fd, 0x3f5, 0x068, 0x3f0, 0x7f7, 0x1ec,
    0x7f5, 0x3f1, 0x072, 0x3f4, 0x074, 0x011, 0x076, 0x1eb,
    0x06c, 0x3f6, 0x7fc, 0x1e1, 0x7f1, 0x1f0, 0x061, 0x1f6,
    0x7f2, 0x1ea, 0x7fb, 0x1f2, 0x069, 0x1ed, 0x077, 0x017,
    0x06f, 0x1e6, 0x064, 0x1e5, 0x067, 0x015, 0x062, 0x012,
    0x000, 0x014, 0x065, 0x016, 0x06d, 0x1e9, 0x063, 0x1e4,
    0x06b, 0x013, 0x071, 0x1e3, 0x070, 0x1f3, 0x7fe, 0x1e7,
    0x7f3, 0x1ef, 0x060, 0x1ee, 0x7f0, 0x1e2, 0x7fa, 0x3f3,
    0x06a, 0x1e8, 0x075, 0x010, 0x073, 0x1f4, 0x06e, 0x3f7,
    0x7f6, 0x1e0, 0x7f9, 0x3f2, 0x066, 0x1f5, 0x7ff, 0x1f7,
    0x7f4,
};

static const uint8_t bits1[81] = {
    11,  9, 11, 10,  7, 10, 11,  9, 11, 10,  7, 10,  7,  5,  7,  9,
     7, 10, 11,  9, 11,  9,  7,  9, 11,  9, 11,  9,  7,  9,  7,  5,
     7,  9,  7,  9,  7,  5,  7,  5,  1,  5,  7,  5,  7,  9,  7,  9,
     7,  5,  7,  9,  7,  9, 11,  9, 11,  9,  7,  9, 11,  9, 11, 10,
     7,  9,  7,  5,  7,  9,  7, 10, 11,  9, 11, 10,  7,  9, 11,  9,
    11,
};

static const uint16_t codes2[81] = {
    0x1f3, 0x06f, 0x1fd, 0x0eb, 0x023, 0x0ea, 0x1f7, 0x0e8,
    0x1fa, 0x0f2, 0x02d, 0x070, 0x020, 0x006, 0x02b, 0x06e,
    0x028, 0x0e9, 0x1f9, 0x066, 0x0f8, 0x0e7, 0x01b, 0x0f1,
    0x1f4, 0x06b, 0x1f5, 0x0ec, 0x02a, 0x06c, 0x02c, 0x00a,
    0x027, 0x067, 0x01a, 0x0f5, 0x024, 0x008, 0x01f, 0x009,
    0x000, 0x007, 0x01d, 0x00b, 0x030, 0x0ef, 0x01c, 0x064,
    0x01e, 0x00c, 0x029, 0x0f3, 0x02f, 0x0f0, 0x1fc, 0x071,
    0x1f2, 0x0f4, 0x021, 0x0e6, 0x0f7, 0x068, 0x1f8, 0x0ee,
    0x022, 0x065, 0x031, 0x002, 0x026, 0x0ed, 0x025, 0x06a,
    0x1fb, 0x072, 0x1fe, 0x069, 0x02e, 0x0f6, 0x1ff, 0x06d,
    0x1f6,
};

static const uint8_t bits2[81] = {
    9, 7, 9, 8, 6, 8, 9, 8, 9, 8, 6, 7, 6, 5, 6, 7,
    6, 8, 9, 7, 8, 8, 6, 8, 9, 7, 9, 8, 6, 7, 6, 5,
    6, 7, 6, 8, 6, 5, 6, 5, 3, 5, 6, 5, 6, 8, 6, 7,
    6, 5, 6, 8, 6, 8, 9, 7, 9, 8, 6, 8, 8, 7, 9, 8,
    6, 7, 6, 4, 6, 8, 6, 7, 9, 7, 9, 7, 6, 8, 9, 7,
    9,
};

static const uint16_t codes3[81] = {
    0x0000, 0x0009, 0x00ef, 0x000b, 0x0019, 0x00f0, 0x01eb, 0x01e6,
    0x03f2, 0x000a, 0x0035, 0x01ef, 0x0034, 0x0037, 0x01e9, 0x01ed,
    0x01e7, 0x03f3, 0x01ee, 0x03ed, 0x1ffa, 0x01ec, 0x01f2, 0x07f9,
    0x07f8, 0x03f8, 0x0ff8, 0x0008, 0x0038, 0x03f6, 0x0036, 0x0075,
    0x03f1, 0x03eb, 0x03ec, 0x0ff4, 0x0018, 0x0076, 0x07f4, 0x0039,
    0x0074, 0x03ef, 0x01f3, 0x01f4, 0x07f6, 0x01e8, 0x03ea, 0x1ffc,
    0x00f2, 0x01f1, 0x0ffb, 0x03f5, 0x07f3, 0x0ffc, 0x00ee, 0x03f7,
    0x7ffe, 0x01f0, 0x07f5, 0x7ffd, 0x1ffb, 0x3ffa, 0xffff, 0x00f1,
    0x03f0, 0x3ffc, 0x01ea, 0x03ee, 0x3ffb, 0x0ff6, 0x0ffa, 0x7ffc,
    0x07f2, 0x0ff5, 0xfffe, 0x03f4, 0x07f7, 0x7ffb, 0x0ff7, 0x0ff9,
    0x7ffa,
};

static const uint8_t bits3[81] = {
     1,  4,  8,  4,  5,  8,  9,  9, 10,  4,  6,  9,  6,  6,  9,  9,
     9, 10,  9, 10, 13,  9,  9, 11, 11, 10, 12,  4,  6, 10,  6,  7,
    10, 10, 10, 12,  5,  7, 11,  6,  7, 10,  9,  9, 11,  9, 10, 13,
     8,  9, 12, 10, 11, 12,  8, 10, 15,  9, 11, 15, 13, 14, 16,  8,
    10, 14,  9, 10, 14, 12, 12, 15, 11, 12, 16, 10, 11, 15, 12, 12,
    15,
};

static const uint16_t codes4[81] = {
    0x007, 0x016, 0x0f6, 0x018, 0x008, 0x0ef, 0x1ef, 0x0f3,
    0x7f8, 0x019, 0x017, 0x0ed, 0x015, 0x001, 0x0e2, 0x0f0,
    0x070, 0x3f0, 0x1ee, 0x0f1, 0x7fa, 0x0ee, 0x0e4, 0x3f2,
    0x7f6, 0x3ef, 0x7fd, 0x005, 0x014, 0x0f2, 0x009, 0x004,
    0x0e5, 0x0f4, 0x0e8, 0x3f4, 0x006, 0x002, 0x0e7, 0x003,
    0x000, 0x06b, 0x0e3, 0x069, 0x1f3, 0x0eb, 0x0e6, 0x3f6,
    0x06e, 0x06a, 0x1f4, 0x3ec, 0x1f0, 0x3f9, 0x0f5, 0x0ec,
    0x7fb, 0x0ea, 0x06f, 0x3f7, 0x7f9, 0x3f3, 0xfff, 0x0e9,
    0x06d, 0x3f8, 0x06c, 0x068, 0x1f5, 0x3ee, 0x1f2, 0x7f4,
    0x7f7, 0x3f1, 0xffe, 0x3ed, 0x1f1, 0x7f5, 0x7fe, 0x3f5,
    0x7fc,
};

static const uint8_t bits4[81] = {
     4,  5,  8,  5,  4,  8,  9,  8, 11,  5,  5,  8,  5,  4,  8,  8,
     7, 10,  9,  8, 11,  8,  8, 10, 11, 10, 11,  4,  5,  8,  4,  4,
     8,  8,  8, 10,  4,  4,  8,  4,  4,  7,  8,  7,  9,  8,  8, 10,
     7,  7,  9, 10,  9, 10,  8,  8, 11,  8,  7, 10, 11, 10, 12,  8,
     7, 10,  7,  7,  9, 10,  9, 11, 11, 10, 12, 10,  9, 11, 11, 10,
    11,
};

static const uint16_t codes5[81] = {
    0x1fff, 0x0ff7, 0x07f4, 0x07e8, 0x03f1, 0x07ee, 0x07f9, 0x0ff8,
    0x1ffd, 0x0ffd, 0x07f1, 0x03e8, 0x01e8, 0x00f0, 0x01ec, 0x03ee,
    0x07f2, 0x0ffa, 0x0ff4, 0x03ef, 0x01f2, 0x00e8, 0x0070, 0x00ec,
    0x01f0, 0x03ea, 0x07f3, 0x07eb, 0x01eb, 0x00ea, 0x001a, 0x0008,
    0x0019, 0x00ee, 0x01ef, 0x07ed, 0x03f0, 0x00f2, 0x0073, 0x000b,
    0x0000, 0x000a, 0x0071, 0x00f3, 0x07e9, 0x07ef, 0x01ee, 0x00ef,
    0x0018, 0x0009, 0x001b, 0x00eb, 0x01e9, 0x07ec, 0x07f6, 0x03eb,
    0x01f3, 0x00ed, 0x0072, 0x00e9, 0x01f1, 0x03ed, 0x07f7, 0x0ff6,
    0x07f0, 0x03e9, 0x01ed, 0x00f1, 0x01ea, 0x03ec, 0x07f8, 0x0ff9,
    0x1ffc, 0x0ffc, 0x0ff5, 0x07ea, 0x03f3, 0x03f2, 0x07f5, 0x0ffb,
    0x1ffe,
};

static const uint8_t bits5[81] = {
    13, 12, 11, 11, 10, 11, 11, 12, 13, 12, 11, 10,  9,  8,  9, 10,
    11, 12, 12, 10,  9,  8,  7,  8,  9, 10, 11, 11,  9,  8,  5,  4,
     5,  8,  9, 11, 10,  8,  7,  4,  1,  4,  7,  8, 11, 11,  9,  8,
     5,  4,  5,  8,  9, 11, 11, 10,  9,  8,  7,  8,  9, 10, 11, 12,
    11, 10,  9,  8,  9, 10, 11, 12, 13, 12, 12, 11, 10, 10, 11, 12,
    13,
};

static const uint16_t codes6[81] = {
    0x7fe, 0x3fd, 0x1f1, 0x1eb, 0x1f4, 0x1ea, 0x1f0, 0x3fc,
    0x7fd, 0x3f6, 0x1e5, 0x0ea, 0x06c, 0x071, 0x068, 0x0f0,
    0x1e6, 0x3f7, 0x1f3, 0x0ef, 0x032, 0x027, 0x028, 0x026,
    0x031, 0x0eb, 0x1f7, 0x1e8, 0x06f, 0x02e, 0x008, 0x004,
    0x006, 0x029, 0x06b, 0x1ee, 0x1ef, 0x072, 0x02d, 0x002,
    0x000, 0x003, 0x02f, 0x073, 0x1fa, 0x1e7, 0x06e, 0x02b,
    0x007, 0x001, 0x005, 0x02c, 0x06d, 0x1ec, 0x1f9, 0x0ee,
    0x030, 0x024, 0x02a, 0x025, 0x033, 0x0ec, 0x1f2, 0x3f8,
    0x1e4, 0x0ed, 0x06a, 0x070, 0x069, 0x074, 0x0f1, 0x3fa,
    0x7ff, 0x3f9, 0x1f6, 0x1ed, 0x1f8, 0x1e9, 0x1f5, 0x3fb,
    0x7fc,
};

static const uint8_t bits6[81] = {
    11, 10,  9,  9,  9,  9,  9, 10, 11, 10,  9,  8,  7,  7,  7,  8,
     9, 10,  9,  8,  6,  6,  6,  6,  6,  8,  9,  9,  7,  6,  4,  4,
     4,  6,  7,  9,  9,  7,  6,  4,  4,  4,  6,  7,  9,  9,  7,  6,
     4,  4,  4,  6,  7,  9,  9,  8,  6,  6,  6,  6,  6,  8,  9, 10,
     9,  8,  7,  7,  7,  7,  8, 10, 11, 10,  9,  9,  9,  9,  9, 10,
    11,
};

static const uint16_t codes7[64] = {
    0x000, 0x005, 0x037, 0x074, 0x0f2, 0x1eb, 0x3ed, 0x7f7,
    0x004, 0x00c, 0x035, 0x071, 0x0ec, 0x0ee, 0x1ee, 0x1f5,
    0x036, 0x034, 0x072, 0x0ea, 0x0f1, 0x1e9, 0x1f3, 0x3f5,
    0x073, 0x070, 0x0eb, 0x0f0, 0x1f1, 0x1f0, 0x3ec, 0x3fa,
    0x0f3, 0x0ed, 0x1e8, 0x1ef, 0x3ef, 0x3f1, 0x3f9, 0x7fb,
    0x1ed, 0x0ef, 0x1ea, 0x1f2, 0x3f3, 0x3f8, 0x7f9, 0x7fc,
    0x3ee, 0x1ec, 0x1f4, 0x3f4, 0x3f7, 0x7f8, 0xffd, 0xffe,
    0x7f6, 0x3f0, 0x3f2, 0x3f6, 0x7fa, 0x7fd, 0xffc, 0xfff,
};

static const uint8_t bits7[64] = {
     1,  3,  6,  7,  8,  9, 10, 11,  3,  4,  6,  7,  8,  8,  9,  9,
     6,  6,  7,  8,  8,  9,  9, 10,  7,  7,  8,  8,  9,  9, 10, 10,
     8,  8,  9,  9, 10, 10, 10, 11,  9,  8,  9,  9, 10, 10, 11, 11,
    10,  9,  9, 10, 10, 11, 12, 12, 11, 10, 10, 10, 11, 11, 12, 12,
};

static const uint16_t codes8[64] = {
    0x00e, 0x005, 0x010, 0x030, 0x06f, 0x0f1, 0x1fa, 0x3fe,
    0x003, 0x000, 0x004, 0x012, 0x02c, 0x06a, 0x075, 0x0f8,
    0x00f, 0x002, 0x006, 0x014, 0x02e, 0x069, 0x072, 0x0f5,
    0x02f, 0x011, 0x013, 0x02a, 0x032, 0x06c, 0x0ec, 0x0fa,
    0x071, 0x02b, 0x02d, 0x031, 0x06d, 0x070, 0x0f2, 0x1f9,
    0x0ef, 0x068, 0x033, 0x06b, 0x06e, 0x0ee, 0x0f9, 0x3fc,
    0x1f8, 0x074, 0x073, 0x0ed, 0x0f0, 0x0f6, 0x1f6, 0x1fd,
    0x3fd, 0x0f3, 0x0f4, 0x0f7, 0x1f7, 0x1fb, 0x1fc, 0x3ff,
};

static const uint8_t bits8[64] = {
     5,  4,  5,  6,  7,  8,  9, 10,  4,  3,  4,  5,  6,  7,  7,  8,
     5,  4,  4,  5,  6,  7,  7,  8,  6,  5,  5,  6,  6,  7,  8,  8,
     7,  6,  6,  6,  7,  7,  8,  9,  8,  7,  6,  7,  7,  8,  8, 10,
     9,  7,  7,  8,  8,  8,  9,  9, 10,  8,  8,  8,  9,  9,  9, 10,
};

static const uint16_t codes9[169] = {
    0x0000, 0x0005, 0x0037, 0x00e7, 0x01de, 0x03ce, 0x03d9, 0x07c8,
    0x07cd, 0x0fc8, 0x0fdd, 0x1fe4, 0x1fec, 0x0004, 0x000c, 0x0035,
    0x0072, 0x00ea, 0x00ed, 0x01e2, 0x03d1, 0x03d3, 0x03e0, 0x07d8,
    0x0fcf, 0x0fd5, 0x0036, 0x0034, 0x0071, 0x00e8, 0x00ec, 0x01e1,
    0x03cf, 0x03dd, 0x03db, 0x07d0, 0x0fc7, 0x0fd4, 0x0fe4, 0x00e6,
    0x0070, 0x00e9, 0x01dd, 0x01e3, 0x03d2, 0x03dc, 0x07cc, 0x07ca,
    0x07de, 0x0fd8, 0x0fea, 0x1fdb, 0x01df, 0x00eb, 0x01dc, 0x01e6,
    0x03d5, 0x03de, 0x07cb, 0x07dd, 0x07dc, 0x0fcd, 0x0fe2, 0x0fe7,
    0x1fe1, 0x03d0, 0x01e0, 0x01e4, 0x03d6, 0x07c5, 0x07d1, 0x07db,
    0x0fd2, 0x07e0, 0x0fd9, 0x0feb, 0x1fe3, 0x1fe9, 0x07c4, 0x01e5,
    0x03d7, 0x07c6, 0x07cf, 0x07da, 0x0fcb, 0x0fda, 0x0fe3, 0x0fe9,
    0x1fe6, 0x1ff3, 0x1ff7, 0x07d3, 0x03d8, 0x03e1, 0x07d4, 0x07d9,
    0x0fd3, 0x0fde, 0x1fdd, 0x1fd9, 0x1fe2, 0x1fea, 0x1ff1, 0x1ff6,
    0x07d2, 0x03d4, 0x03da, 0x07c7, 0x07d7, 0x07e2, 0x0fce, 0x0fdb,
    0x1fd8, 0x1fee, 0x3ff0, 0x1ff4, 0x3ff2, 0x07e1, 0x03df, 0x07c9,
    0x07d6, 0x0fca, 0x0fd0, 0x0fe5, 0x0fe6, 0x1feb, 0x1fef, 0x3ff3,
    0x3ff4, 0x3ff5, 0x0fe0, 0x07ce, 0x07d5, 0x0fc6, 0x0fd1, 0x0fe1,
    0x1fe0, 0x1fe8, 0x1ff0, 0x3ff1, 0x3ff8, 0x3ff6, 0x7ffc, 0x0fe8,
    0x07df, 0x0fc9, 0x0fd7, 0x0fdc, 0x1fdc, 0x1fdf, 0x1fed, 0x1ff5,
    0x3ff9, 0x3ffb, 0x7ffd, 0x7ffe, 0x1fe7, 0x0fcc, 0x0fd6, 0x0fdf,
    0x1fde, 0x1fda, 0x1fe5, 0x1ff2, 0x3ffa, 0x3ff7, 0x3ffc, 0x3ffd,
    0x7fff,
};

static const uint8_t bits9[169] = {
     1,  3,  6,  8,  9, 10, 10, 11, 11, 12, 12, 13, 13,  3,  4,  6,
     7,  8,  8,  9, 10, 10, 10, 11, 12, 12,  6,  6,  7,  8,  8,  9,
    10, 10, 10, 11, 12, 12, 12,  8,  7,  8,  9,  9, 10, 10, 11, 11,
    11, 12, 12, 13,  9,  8,  9,  9, 10, 10, 11, 11, 11, 12, 12, 12,
    13, 10,  9,  9, 10, 11, 11, 11, 12, 11, 12, 12, 13, 13, 11,  9,
    10, 11, 11, 11, 12, 12, 12, 12, 13, 13, 13, 11, 10, 10, 11, 11,
    12, 12, 13, 13, 13, 13, 13, 13, 11, 10, 10, 11, 11, 11, 12, 12,
    13, 13, 14, 13, 14, 11, 10, 11, 11, 12, 12, 12, 12, 13, 13, 14,
    14, 14, 12, 11, 11, 12, 12, 12, 13, 13, 13, 14, 14, 14, 15, 12,
    11, 12, 12, 12, 13, 13, 13, 13, 14, 14, 15, 15, 13, 12, 12, 12,
    13, 13, 13, 13, 14, 14, 14, 14, 15,
};

static const uint16_t codes10[169] = {
    0x022, 0x008, 0x01d, 0x026, 0x05f, 0x0d3, 0x1cf, 0x3d0,
    0x3d7, 0x3ed, 0x7f0, 0x7f6, 0xffd, 0x007, 0x000, 0x001,
    0x009, 0x020, 0x054, 0x060, 0x0d5, 0x0dc, 0x1d4, 0x3cd,
    0x3de, 0x7e7, 0x01c, 0x002, 0x006, 0x00c, 0x01e, 0x028,
    0x05b, 0x0cd, 0x0d9, 0x1ce, 0x1dc, 0x3d9, 0x3f1, 0x025,
    0x00b, 0x00a, 0x00d, 0x024, 0x057, 0x061, 0x0cc, 0x0dd,
    0x1cc, 0x1de, 0x3d3, 0x3e7, 0x05d, 0x021, 0x01f, 0x023,
    0x027, 0x059, 0x064, 0x0d8, 0x0df, 0x1d2, 0x1e2, 0x3dd,
    0x3ee, 0x0d1, 0x055, 0x029, 0x056, 0x058, 0x062, 0x0ce,
    0x0e0, 0x0e2, 0x1da, 0x3d4, 0x3e3, 0x7eb, 0x1c9, 0x05e,
    0x05a, 0x05c, 0x063, 0x0ca, 0x0da, 0x1c7, 0x1ca, 0x1e0,
    0x3db, 0x3e8, 0x7ec, 0x1e3, 0x0d2, 0x0cb, 0x0d0, 0x0d7,
    0x0db, 0x1c6, 0x1d5, 0x1d8, 0x3ca, 0x3da, 0x7ea, 0x7f1,
    0x1e1, 0x0d4, 0x0cf, 0x0d6, 0x0de, 0x0e1, 0x1d0, 0x1d6,
    0x3d1, 0x3d5, 0x3f2, 0x7ee, 0x7fb, 0x3e9, 0x1cd, 0x1c8,
    0x1cb, 0x1d1, 0x1d7, 0x1df, 0x3cf, 0x3e0, 0x3ef, 0x7e6,
    0x7f8, 0xffa, 0x3eb, 0x1dd, 0x1d3, 0x1d9, 0x1db, 0x3d2,
    0x3cc, 0x3dc, 0x3ea, 0x7ed, 0x7f3, 0x7f9, 0xff9, 0x7f2,
    0x3ce, 0x1e4, 0x3cb, 0x3d8, 0x3d6, 0x3e2, 0x3e5, 0x7e8,
    0x7f4, 0x7f5, 0x7f7, 0xffb, 0x7fa, 0x3ec, 0x3df, 0x3e1,
    0x3e4, 0x3e6, 0x3f0, 0x7e9, 0x7ef, 0xff8, 0xffe, 0xffc,
    0xfff,
};

static const uint8_t bits10[169] = {
     6,  5,  6,  6,  7,  8,  9, 10, 10, 10, 11, 11, 12,  5,  4,  4,
     5,  6,  7,  7,  8,  8,  9, 10, 10, 11,  6,  4,  5,  5,  6,  6,
     7,  8,  8,  9,  9, 10, 10,  6,  5,  5,  5,  6,  7,  7,  8,  8,
     9,  9, 10, 10,  7,  6,  6,  6,  6,  7,  7,  8,  8,  9,  9, 10,
    10,  8,  7,  6,  7,  7,  7,  8,  8,  8,  9, 10, 10, 11,  9,  7,
     7,  7,  7,  8,  8,  9,  9,  9, 10, 10, 11,  9,  8,  8,  8,  8,
     8,  9,  9,  9, 10, 10, 11, 11,  9,  8,  8,  8,  8,  8,  9,  9,
    10, 10, 10, 11, 11, 10,  9,  9,  9,  9,  9,  9, 10, 10, 10, 11,
    11, 12, 10,  9,  9,  9,  9, 10, 10, 10, 10, 11, 11, 11, 12, 11,
    10,  9, 10, 10, 10, 10, 10, 11, 11, 11, 11, 12, 11, 10, 10, 10,
    10, 10, 10, 11, 11, 12, 12, 12, 12,
};

static const uint16_t codes11[289] = {
    0x000, 0x006, 0x019, 0x03d, 0x09c, 0x0c6, 0x1a7, 0x390,
    0x3c2, 0x3df, 0x7e6, 0x7f3, 0xffb, 0x7ec, 0xffa, 0xffe,
    0x38e, 0x005, 0x001, 0x008, 0x014, 0x037, 0x042, 0x092,
    0x0af, 0x191, 0x1a5, 0x1b5, 0x39e, 0x3c0, 0x3a2, 0x3cd,
    0x7d6, 0x0ae, 0x017, 0x007, 0x009, 0x018, 0x039, 0x040,
    0x08e, 0x0a3, 0x0b8, 0x199, 0x1ac, 0x1c1, 0x3b1, 0x396,
    0x3be, 0x3ca, 0x09d, 0x03c, 0x015, 0x016, 0x01a, 0x03b,
    0x044, 0x091, 0x0a5, 0x0be, 0x196, 0x1ae, 0x1b9, 0x3a1,
    0x391, 0x3a5, 0x3d5, 0x094, 0x09a, 0x036, 0x038, 0x03a,
    0x041, 0x08c, 0x09b, 0x0b0, 0x0c3, 0x19e, 0x1ab, 0x1bc,
    0x39f, 0x38f, 0x3a9, 0x3cf, 0x093, 0x0bf, 0x03e, 0x03f,
    0x043, 0x045, 0x09e, 0x0a7, 0x0b9, 0x194, 0x1a2, 0x1ba,
    0x1c3, 0x3a6, 0x3a7, 0x3bb, 0x3d4, 0x09f, 0x1a0, 0x08f,
    0x08d, 0x090, 0x098, 0x0a6, 0x0b6, 0x0c4, 0x19f, 0x1af,
    0x1bf, 0x399, 0x3bf, 0x3b4, 0x3c9, 0x3e7, 0x0a8, 0x1b6,
    0x0ab, 0x0a4, 0x0aa, 0x0b2, 0x0c2, 0x0c5, 0x198, 0x1a4,
    0x1b8, 0x38c, 0x3a4, 0x3c4, 0x3c6, 0x3dd, 0x3e8, 0x0ad,
    0x3af, 0x192, 0x0bd, 0x0bc, 0x18e, 0x197, 0x19a, 0x1a3,
    0x1b1, 0x38d, 0x398, 0x3b7, 0x3d3, 0x3d1, 0x3db, 0x7dd,
    0x0b4, 0x3de, 0x1a9, 0x19b, 0x19c, 0x1a1, 0x1aa, 0x1ad,
    0x1b3, 0x38b, 0x3b2, 0x3b8, 0x3ce, 0x3e1, 0x3e0, 0x7d2,
    0x7e5, 0x0b7, 0x7e3, 0x1bb, 0x1a8, 0x1a6, 0x1b0, 0x1b2,
    0x1b7, 0x39b, 0x39a, 0x3ba, 0x3b5, 0x3d6, 0x7d7, 0x3e4,
    0x7d8, 0x7ea, 0x0ba, 0x7e8, 0x3a0, 0x1bd, 0x1b4, 0x38a,
    0x1c4, 0x392, 0x3aa, 0x3b0, 0x3bc, 0x3d7, 0x7d4, 0x7dc,
    0x7db, 0x7d5, 0x7f0, 0x0c1, 0x7fb, 0x3c8, 0x3a3, 0x395,
    0x39d, 0x3ac, 0x3ae, 0x3c5, 0x3d8, 0x3e2, 0x3e6, 0x7e4,
    0x7e7, 0x7e0, 0x7e9, 0x7f7, 0x190, 0x7f2, 0x393, 0x1be,
    0x1c0, 0x394, 0x397, 0x3ad, 0x3c3, 0x3c1, 0x3d2, 0x7da,
    0x7d9, 0x7df, 0x7eb, 0x7f4, 0x7fa, 0x195, 0x7f8, 0x3bd,
    0x39c, 0x3ab, 0x3a8, 0x3b3, 0x3b9, 0x3d0, 0x3e3, 0x3e5,
    0x7e2, 0x7de, 0x7ed, 0x7f1, 0x7f9, 0x7fc, 0x193, 0xffd,
    0x3dc, 0x3b6, 0x3c7, 0x3cc, 0x3cb, 0x3d9, 0x3da, 0x7d3,
    0x7e1, 0x7ee, 0x7ef, 0x7f5, 0x7f6, 0xffc, 0xfff, 0x19d,
    0x1c2, 0x0b5, 0x0a1, 0x096, 0x097, 0x095, 0x099, 0x0a0,
    0x0a2, 0x0ac, 0x0a9, 0x0b1, 0x0b3, 0x0bb, 0x0c0, 0x18f,
    0x004,
};

static const uint8_t bits11[289] = {
     4,  5,  6,  7,  8,  8,  9, 10, 10, 10, 11, 11, 12, 11, 12, 12,
    10,  5,  4,  5,  6,  7,  7,  8,  8,  9,  9,  9, 10, 10, 10, 10,
    11,  8,  6,  5,  5,  6,  7,  7,  8,  8,  8,  9,  9,  9, 10, 10,
    10, 10,  8,  7,  6,  6,  6,  7,  7,  8,  8,  8,  9,  9,  9, 10,
    10, 10, 10,  8,  8,  7,  7,  7,  7,  8,  8,  8,  8,  9,  9,  9,
    10, 10, 10, 10,  8,  8,  7,  7,  7,  7,  8,  8,  8,  9,  9,  9,
     9, 10, 10, 10, 10,  8,  9,  8,  8,  8,  8,  8,  8,  8,  9,  9,
     9, 10, 10, 10, 10, 10,  8,  9,  8,  8,  8,  8,  8,  8,  9,  9,
     9, 10, 10, 10, 10, 10, 10,  8, 10,  9,  8,  8,  9,  9,  9,  9,
     9, 10, 10, 10, 10, 10, 10, 11,  8, 10,  9,  9,  9,  9,  9,  9,
     9, 10, 10, 10, 10, 10, 10, 11, 11,  8, 11,  9,  9,  9,  9,  9,
     9, 10, 10, 10, 10, 10, 11, 10, 11, 11,  8, 11, 10,  9,  9, 10,
     9, 10, 10, 10, 10, 10, 11, 11, 11, 11, 11,  8, 11, 10, 10, 10,
    10, 10, 10, 10, 10, 10, 10, 11, 11, 11, 11, 11,  9, 11, 10,  9,
     9, 10, 10, 10, 10, 10, 10, 11, 11, 11, 11, 11, 11,  9, 11, 10,
    10, 10, 10, 10, 10, 10, 10, 10, 11, 11, 11, 11, 11, 11,  9, 12,
    10, 10, 10, 10, 10, 10, 10, 11, 11, 11, 11, 11, 11, 12, 12,  9,
     9,  8,  8,  8,  8,  8,  8,  8,  8,  8,  8,  8,  8,  8,  8,  9,
     5,
};

const uint16_t * const ff_aac_spectral_codes[11] = {
    codes1,  codes2,  codes3, codes4, codes5, codes6, codes7, codes8,
    codes9, codes10, codes11,
};

const uint8_t * const ff_aac_spectral_bits[11] = {
    bits1,  bits2,  bits3, bits4, bits5, bits6, bits7, bits8,
    bits9, bits10, bits11,
};

const uint16_t ff_aac_spectral_sizes[11] = {
    81, 81, 81, 81, 81, 81, 64, 64, 169, 169, 289,
};

/* NOTE:
 * 64.0f is a special value indicating the existence of an escape code in the
 * bitstream.
 */
static const DECLARE_ALIGNED(16, float, codebook_vector0)[324] = {
 -1.0000000, -1.0000000, -1.0000000, -1.0000000,
 -1.0000000, -1.0000000, -1.0000000,  0.0000000,
 -1.0000000, -1.0000000, -1.0000000,  1.0000000,
 -1.0000000, -1.0000000,  0.0000000, -1.0000000,
 -1.0000000, -1.0000000,  0.0000000,  0.0000000,
 -1.0000000, -1.0000000,  0.0000000,  1.0000000,
 -1.0000000, -1.0000000,  1.0000000, -1.0000000,
 -1.0000000, -1.0000000,  1.0000000,  0.0000000,
 -1.0000000, -1.0000000,  1.0000000,  1.0000000,
 -1.0000000,  0.0000000, -1.0000000, -1.0000000,
 -1.0000000,  0.0000000, -1.0000000,  0.0000000,
 -1.0000000,  0.0000000, -1.0000000,  1.0000000,
 -1.0000000,  0.0000000,  0.0000000, -1.0000000,
 -1.0000000,  0.0000000,  0.0000000,  0.0000000,
 -1.0000000,  0.0000000,  0.0000000,  1.0000000,
 -1.0000000,  0.0000000,  1.0000000, -1.0000000,
 -1.0000000,  0.0000000,  1.0000000,  0.0000000,
 -1.0000000,  0.0000000,  1.0000000,  1.0000000,
 -1.0000000,  1.0000000, -1.0000000, -1.0000000,
 -1.0000000,  1.0000000, -1.0000000,  0.0000000,
 -1.0000000,  1.0000000, -1.0000000,  1.0000000,
 -1.0000000,  1.0000000,  0.0000000, -1.0000000,
 -1.0000000,  1.0000000,  0.0000000,  0.0000000,
 -1.0000000,  1.0000000,  0.0000000,  1.0000000,
 -1.0000000,  1.0000000,  1.0000000, -1.0000000,
 -1.0000000,  1.0000000,  1.0000000,  0.0000000,
 -1.0000000,  1.0000000,  1.0000000,  1.0000000,
  0.0000000, -1.0000000, -1.0000000, -1.0000000,
  0.0000000, -1.0000000, -1.0000000,  0.0000000,
  0.0000000, -1.0000000, -1.0000000,  1.0000000,
  0.0000000, -1.0000000,  0.0000000, -1.0000000,
  0.0000000, -1.0000000,  0.0000000,  0.0000000,
  0.0000000, -1.0000000,  0.0000000,  1.0000000,
  0.0000000, -1.0000000,  1.0000000, -1.0000000,
  0.0000000, -1.0000000,  1.0000000,  0.0000000,
  0.0000000, -1.0000000,  1.0000000,  1.0000000,
  0.0000000,  0.0000000, -1.0000000, -1.0000000,
  0.0000000,  0.0000000, -1.0000000,  0.0000000,
  0.0000000,  0.0000000, -1.0000000,  1.0000000,
  0.0000000,  0.0000000,  0.0000000, -1.0000000,
  0.0000000,  0.0000000,  0.0000000,  0.0000000,
  0.0000000,  0.0000000,  0.0000000,  1.0000000,
  0.0000000,  0.0000000,  1.0000000, -1.0000000,
  0.0000000,  0.0000000,  1.0000000,  0.0000000,
  0.0000000,  0.0000000,  1.0000000,  1.0000000,
  0.0000000,  1.0000000, -1.0000000, -1.0000000,
  0.0000000,  1.0000000, -1.0000000,  0.0000000,
  0.0000000,  1.0000000, -1.0000000,  1.0000000,
  0.0000000,  1.0000000,  0.0000000, -1.0000000,
  0.0000000,  1.0000000,  0.0000000,  0.0000000,
  0.0000000,  1.0000000,  0.0000000,  1.0000000,
  0.0000000,  1.0000000,  1.0000000, -1.0000000,
  0.0000000,  1.0000000,  1.0000000,  0.0000000,
  0.0000000,  1.0000000,  1.0000000,  1.0000000,
  1.0000000, -1.0000000, -1.0000000, -1.0000000,
  1.0000000, -1.0000000, -1.0000000,  0.0000000,
  1.0000000, -1.0000000, -1.0000000,  1.0000000,
  1.0000000, -1.0000000,  0.0000000, -1.0000000,
  1.0000000, -1.0000000,  0.0000000,  0.0000000,
  1.0000000, -1.0000000,  0.0000000,  1.0000000,
  1.0000000, -1.0000000,  1.0000000, -1.0000000,
  1.0000000, -1.0000000,  1.0000000,  0.0000000,
  1.0000000, -1.0000000,  1.0000000,  1.0000000,
  1.0000000,  0.0000000, -1.0000000, -1.0000000,
  1.0000000,  0.0000000, -1.0000000,  0.0000000,
  1.0000000,  0.0000000, -1.0000000,  1.0000000,
  1.0000000,  0.0000000,  0.0000000, -1.0000000,
  1.0000000,  0.0000000,  0.0000000,  0.0000000,
  1.0000000,  0.0000000,  0.0000000,  1.0000000,
  1.0000000,  0.0000000,  1.0000000, -1.0000000,
  1.0000000,  0.0000000,  1.0000000,  0.0000000,
  1.0000000,  0.0000000,  1.0000000,  1.0000000,
  1.0000000,  1.0000000, -1.0000000, -1.0000000,
  1.0000000,  1.0000000, -1.0000000,  0.0000000,
  1.0000000,  1.0000000, -1.0000000,  1.0000000,
  1.0000000,  1.0000000,  0.0000000, -1.0000000,
  1.0000000,  1.0000000,  0.0000000,  0.0000000,
  1.0000000,  1.0000000,  0.0000000,  1.0000000,
  1.0000000,  1.0000000,  1.0000000, -1.0000000,
  1.0000000,  1.0000000,  1.0000000,  0.0000000,
  1.0000000,  1.0000000,  1.0000000,  1.0000000,
};

static const DECLARE_ALIGNED(16, float, codebook_vector2)[324] = {
  0.0000000,  0.0000000,  0.0000000,  0.0000000,
  0.0000000,  0.0000000,  0.0000000,  1.0000000,
  0.0000000,  0.0000000,  0.0000000,  2.5198421,
  0.0000000,  0.0000000,  1.0000000,  0.0000000,
  0.0000000,  0.0000000,  1.0000000,  1.0000000,
  0.0000000,  0.0000000,  1.0000000,  2.5198421,
  0.0000000,  0.0000000,  2.5198421,  0.0000000,
  0.0000000,  0.0000000,  2.5198421,  1.0000000,
  0.0000000,  0.0000000,  2.5198421,  2.5198421,
  0.0000000,  1.0000000,  0.0000000,  0.0000000,
  0.0000000,  1.0000000,  0.0000000,  1.0000000,
  0.0000000,  1.0000000,  0.0000000,  2.5198421,
  0.0000000,  1.0000000,  1.0000000,  0.0000000,
  0.0000000,  1.0000000,  1.0000000,  1.0000000,
  0.0000000,  1.0000000,  1.0000000,  2.5198421,
  0.0000000,  1.0000000,  2.5198421,  0.0000000,
  0.0000000,  1.0000000,  2.5198421,  1.0000000,
  0.0000000,  1.0000000,  2.5198421,  2.5198421,
  0.0000000,  2.5198421,  0.0000000,  0.0000000,
  0.0000000,  2.5198421,  0.0000000,  1.0000000,
  0.0000000,  2.5198421,  0.0000000,  2.5198421,
  0.0000000,  2.5198421,  1.0000000,  0.0000000,
  0.0000000,  2.5198421,  1.0000000,  1.0000000,
  0.0000000,  2.5198421,  1.0000000,  2.5198421,
  0.0000000,  2.5198421,  2.5198421,  0.0000000,
  0.0000000,  2.5198421,  2.5198421,  1.0000000,
  0.0000000,  2.5198421,  2.5198421,  2.5198421,
  1.0000000,  0.0000000,  0.0000000,  0.0000000,
  1.0000000,  0.0000000,  0.0000000,  1.0000000,
  1.0000000,  0.0000000,  0.0000000,  2.5198421,
  1.0000000,  0.0000000,  1.0000000,  0.0000000,
  1.0000000,  0.0000000,  1.0000000,  1.0000000,
  1.0000000,  0.0000000,  1.0000000,  2.5198421,
  1.0000000,  0.0000000,  2.5198421,  0.0000000,
  1.0000000,  0.0000000,  2.5198421,  1.0000000,
  1.0000000,  0.0000000,  2.5198421,  2.5198421,
  1.0000000,  1.0000000,  0.0000000,  0.0000000,
  1.0000000,  1.0000000,  0.0000000,  1.0000000,
  1.0000000,  1.0000000,  0.0000000,  2.5198421,
  1.0000000,  1.0000000,  1.0000000,  0.0000000,
  1.0000000,  1.0000000,  1.0000000,  1.0000000,
  1.0000000,  1.0000000,  1.0000000,  2.5198421,
  1.0000000,  1.0000000,  2.5198421,  0.0000000,
  1.0000000,  1.0000000,  2.5198421,  1.0000000,
  1.0000000,  1.0000000,  2.5198421,  2.5198421,
  1.0000000,  2.5198421,  0.0000000,  0.0000000,
  1.0000000,  2.5198421,  0.0000000,  1.0000000,
  1.0000000,  2.5198421,  0.0000000,  2.5198421,
  1.0000000,  2.5198421,  1.0000000,  0.0000000,
  1.0000000,  2.5198421,  1.0000000,  1.0000000,
  1.0000000,  2.5198421,  1.0000000,  2.5198421,
  1.0000000,  2.5198421,  2.5198421,  0.0000000,
  1.0000000,  2.5198421,  2.5198421,  1.0000000,
  1.0000000,  2.5198421,  2.5198421,  2.5198421,
  2.5198421,  0.0000000,  0.0000000,  0.0000000,
  2.5198421,  0.0000000,  0.0000000,  1.0000000,
  2.5198421,  0.0000000,  0.0000000,  2.5198421,
  2.5198421,  0.0000000,  1.0000000,  0.0000000,
  2.5198421,  0.0000000,  1.0000000,  1.0000000,
  2.5198421,  0.0000000,  1.0000000,  2.5198421,
  2.5198421,  0.0000000,  2.5198421,  0.0000000,
  2.5198421,  0.0000000,  2.5198421,  1.0000000,
  2.5198421,  0.0000000,  2.5198421,  2.5198421,
  2.5198421,  1.0000000,  0.0000000,  0.0000000,
  2.5198421,  1.0000000,  0.0000000,  1.0000000,
  2.5198421,  1.0000000,  0.0000000,  2.5198421,
  2.5198421,  1.0000000,  1.0000000,  0.0000000,
  2.5198421,  1.0000000,  1.0000000,  1.0000000,
  2.5198421,  1.0000000,  1.0000000,  2.5198421,
  2.5198421,  1.0000000,  2.5198421,  0.0000000,
  2.5198421,  1.0000000,  2.5198421,  1.0000000,
  2.5198421,  1.0000000,  2.5198421,  2.5198421,
  2.5198421,  2.5198421,  0.0000000,  0.0000000,
  2.5198421,  2.5198421,  0.0000000,  1.0000000,
  2.5198421,  2.5198421,  0.0000000,  2.5198421,
  2.5198421,  2.5198421,  1.0000000,  0.0000000,
  2.5198421,  2.5198421,  1.0000000,  1.0000000,
  2.5198421,  2.5198421,  1.0000000,  2.5198421,
  2.5198421,  2.5198421,  2.5198421,  0.0000000,
  2.5198421,  2.5198421,  2.5198421,  1.0000000,
  2.5198421,  2.5198421,  2.5198421,  2.5198421,
};

static const DECLARE_ALIGNED(16, float, codebook_vector4)[162] = {
 -6.3496042, -6.3496042, -6.3496042, -4.3267487,
 -6.3496042, -2.5198421, -6.3496042, -1.0000000,
 -6.3496042,  0.0000000, -6.3496042,  1.0000000,
 -6.3496042,  2.5198421, -6.3496042,  4.3267487,
 -6.3496042,  6.3496042, -4.3267487, -6.3496042,
 -4.3267487, -4.3267487, -4.3267487, -2.5198421,
 -4.3267487, -1.0000000, -4.3267487,  0.0000000,
 -4.3267487,  1.0000000, -4.3267487,  2.5198421,
 -4.3267487,  4.3267487, -4.3267487,  6.3496042,
 -2.5198421, -6.3496042, -2.5198421, -4.3267487,
 -2.5198421, -2.5198421, -2.5198421, -1.0000000,
 -2.5198421,  0.0000000, -2.5198421,  1.0000000,
 -2.5198421,  2.5198421, -2.5198421,  4.3267487,
 -2.5198421,  6.3496042, -1.0000000, -6.3496042,
 -1.0000000, -4.3267487, -1.0000000, -2.5198421,
 -1.0000000, -1.0000000, -1.0000000,  0.0000000,
 -1.0000000,  1.0000000, -1.0000000,  2.5198421,
 -1.0000000,  4.3267487, -1.0000000,  6.3496042,
  0.0000000, -6.3496042,  0.0000000, -4.3267487,
  0.0000000, -2.5198421,  0.0000000, -1.0000000,
  0.0000000,  0.0000000,  0.0000000,  1.0000000,
  0.0000000,  2.5198421,  0.0000000,  4.3267487,
  0.0000000,  6.3496042,  1.0000000, -6.3496042,
  1.0000000, -4.3267487,  1.0000000, -2.5198421,
  1.0000000, -1.0000000,  1.0000000,  0.0000000,
  1.0000000,  1.0000000,  1.0000000,  2.5198421,
  1.0000000,  4.3267487,  1.0000000,  6.3496042,
  2.5198421, -6.3496042,  2.5198421, -4.3267487,
  2.5198421, -2.5198421,  2.5198421, -1.0000000,
  2.5198421,  0.0000000,  2.5198421,  1.0000000,
  2.5198421,  2.5198421,  2.5198421,  4.3267487,
  2.5198421,  6.3496042,  4.3267487, -6.3496042,
  4.3267487, -4.3267487,  4.3267487, -2.5198421,
  4.3267487, -1.0000000,  4.3267487,  0.0000000,
  4.3267487,  1.0000000,  4.3267487,  2.5198421,
  4.3267487,  4.3267487,  4.3267487,  6.3496042,
  6.3496042, -6.3496042,  6.3496042, -4.3267487,
  6.3496042, -2.5198421,  6.3496042, -1.0000000,
  6.3496042,  0.0000000,  6.3496042,  1.0000000,
  6.3496042,  2.5198421,  6.3496042,  4.3267487,
  6.3496042,  6.3496042,
};

static const DECLARE_ALIGNED(16, float, codebook_vector6)[128] = {
  0.0000000,  0.0000000,  0.0000000,  1.0000000,
  0.0000000,  2.5198421,  0.0000000,  4.3267487,
  0.0000000,  6.3496042,  0.0000000,  8.5498797,
  0.0000000, 10.9027236,  0.0000000, 13.3905183,
  1.0000000,  0.0000000,  1.0000000,  1.0000000,
  1.0000000,  2.5198421,  1.0000000,  4.3267487,
  1.0000000,  6.3496042,  1.0000000,  8.5498797,
  1.0000000, 10.9027236,  1.0000000, 13.3905183,
  2.5198421,  0.0000000,  2.5198421,  1.0000000,
  2.5198421,  2.5198421,  2.5198421,  4.3267487,
  2.5198421,  6.3496042,  2.5198421,  8.5498797,
  2.5198421, 10.9027236,  2.5198421, 13.3905183,
  4.3267487,  0.0000000,  4.3267487,  1.0000000,
  4.3267487,  2.5198421,  4.3267487,  4.3267487,
  4.3267487,  6.3496042,  4.3267487,  8.5498797,
  4.3267487, 10.9027236,  4.3267487, 13.3905183,
  6.3496042,  0.0000000,  6.3496042,  1.0000000,
  6.3496042,  2.5198421,  6.3496042,  4.3267487,
  6.3496042,  6.3496042,  6.3496042,  8.5498797,
  6.3496042, 10.9027236,  6.3496042, 13.3905183,
  8.5498797,  0.0000000,  8.5498797,  1.0000000,
  8.5498797,  2.5198421,  8.5498797,  4.3267487,
  8.5498797,  6.3496042,  8.5498797,  8.5498797,
  8.5498797, 10.9027236,  8.5498797, 13.3905183,
 10.9027236,  0.0000000, 10.9027236,  1.0000000,
 10.9027236,  2.5198421, 10.9027236,  4.3267487,
 10.9027236,  6.3496042, 10.9027236,  8.5498797,
 10.9027236, 10.9027236, 10.9027236, 13.3905183,
 13.3905183,  0.0000000, 13.3905183,  1.0000000,
 13.3905183,  2.5198421, 13.3905183,  4.3267487,
 13.3905183,  6.3496042, 13.3905183,  8.5498797,
 13.3905183, 10.9027236, 13.3905183, 13.3905183,
};

static const DECLARE_ALIGNED(16, float, codebook_vector8)[338] = {
  0.0000000,  0.0000000,  0.0000000,  1.0000000,
  0.0000000,  2.5198421,  0.0000000,  4.3267487,
  0.0000000,  6.3496042,  0.0000000,  8.5498797,
  0.0000000, 10.9027236,  0.0000000, 13.3905183,
  0.0000000, 16.0000000,  0.0000000, 18.7207544,
  0.0000000, 21.5443469,  0.0000000, 24.4637810,
  0.0000000, 27.4731418,  1.0000000,  0.0000000,
  1.0000000,  1.0000000,  1.0000000,  2.5198421,
  1.0000000,  4.3267487,  1.0000000,  6.3496042,
  1.0000000,  8.5498797,  1.0000000, 10.9027236,
  1.0000000, 13.3905183,  1.0000000, 16.0000000,
  1.0000000, 18.7207544,  1.0000000, 21.5443469,
  1.0000000, 24.4637810,  1.0000000, 27.4731418,
  2.5198421,  0.0000000,  2.5198421,  1.0000000,
  2.5198421,  2.5198421,  2.5198421,  4.3267487,
  2.5198421,  6.3496042,  2.5198421,  8.5498797,
  2.5198421, 10.9027236,  2.5198421, 13.3905183,
  2.5198421, 16.0000000,  2.5198421, 18.7207544,
  2.5198421, 21.5443469,  2.5198421, 24.4637810,
  2.5198421, 27.4731418,  4.3267487,  0.0000000,
  4.3267487,  1.0000000,  4.3267487,  2.5198421,
  4.3267487,  4.3267487,  4.3267487,  6.3496042,
  4.3267487,  8.5498797,  4.3267487, 10.9027236,
  4.3267487, 13.3905183,  4.3267487, 16.0000000,
  4.3267487, 18.7207544,  4.3267487, 21.5443469,
  4.3267487, 24.4637810,  4.3267487, 27.4731418,
  6.3496042,  0.0000000,  6.3496042,  1.0000000,
  6.3496042,  2.5198421,  6.3496042,  4.3267487,
  6.3496042,  6.3496042,  6.3496042,  8.5498797,
  6.3496042, 10.9027236,  6.3496042, 13.3905183,
  6.3496042, 16.0000000,  6.3496042, 18.7207544,
  6.3496042, 21.5443469,  6.3496042, 24.4637810,
  6.3496042, 27.4731418,  8.5498797,  0.0000000,
  8.5498797,  1.0000000,  8.5498797,  2.5198421,
  8.5498797,  4.3267487,  8.5498797,  6.3496042,
  8.5498797,  8.5498797,  8.5498797, 10.9027236,
  8.5498797, 13.3905183,  8.5498797, 16.0000000,
  8.5498797, 18.7207544,  8.5498797, 21.5443469,
  8.5498797, 24.4637810,  8.5498797, 27.4731418,
 10.9027236,  0.0000000, 10.9027236,  1.0000000,
 10.9027236,  2.5198421, 10.9027236,  4.3267487,
 10.9027236,  6.3496042, 10.9027236,  8.5498797,
 10.9027236, 10.9027236, 10.9027236, 13.3905183,
 10.9027236, 16.0000000, 10.9027236, 18.7207544,
 10.9027236, 21.5443469, 10.9027236, 24.4637810,
 10.9027236, 27.4731418, 13.3905183,  0.0000000,
 13.3905183,  1.0000000, 13.3905183,  2.5198421,
 13.3905183,  4.3267487, 13.3905183,  6.3496042,
 13.3905183,  8.5498797, 13.3905183, 10.9027236,
 13.3905183, 13.3905183, 13.3905183, 16.0000000,
 13.3905183, 18.7207544, 13.3905183, 21.5443469,
 13.3905183, 24.4637810, 13.3905183, 27.4731418,
 16.0000000,  0.0000000, 16.0000000,  1.0000000,
 16.0000000,  2.5198421, 16.0000000,  4.3267487,
 16.0000000,  6.3496042, 16.0000000,  8.5498797,
 16.0000000, 10.9027236, 16.0000000, 13.3905183,
 16.0000000, 16.0000000, 16.0000000, 18.7207544,
 16.0000000, 21.5443469, 16.0000000, 24.4637810,
 16.0000000, 27.4731418, 18.7207544,  0.0000000,
 18.7207544,  1.0000000, 18.7207544,  2.5198421,
 18.7207544,  4.3267487, 18.7207544,  6.3496042,
 18.7207544,  8.5498797, 18.7207544, 10.9027236,
 18.7207544, 13.3905183, 18.7207544, 16.0000000,
 18.7207544, 18.7207544, 18.7207544, 21.5443469,
 18.7207544, 24.4637810, 18.7207544, 27.4731418,
 21.5443469,  0.0000000, 21.5443469,  1.0000000,
 21.5443469,  2.5198421, 21.5443469,  4.3267487,
 21.5443469,  6.3496042, 21.5443469,  8.5498797,
 21.5443469, 10.9027236, 21.5443469, 13.3905183,
 21.5443469, 16.0000000, 21.5443469, 18.7207544,
 21.5443469, 21.5443469, 21.5443469, 24.4637810,
 21.5443469, 27.4731418, 24.4637810,  0.0000000,
 24.4637810,  1.0000000, 24.4637810,  2.5198421,
 24.4637810,  4.3267487, 24.4637810,  6.3496042,
 24.4637810,  8.5498797, 24.4637810, 10.9027236,
 24.4637810, 13.3905183, 24.4637810, 16.0000000,
 24.4637810, 18.7207544, 24.4637810, 21.5443469,
 24.4637810, 24.4637810, 24.4637810, 27.4731418,
 27.4731418,  0.0000000, 27.4731418,  1.0000000,
 27.4731418,  2.5198421, 27.4731418,  4.3267487,
 27.4731418,  6.3496042, 27.4731418,  8.5498797,
 27.4731418, 10.9027236, 27.4731418, 13.3905183,
 27.4731418, 16.0000000, 27.4731418, 18.7207544,
 27.4731418, 21.5443469, 27.4731418, 24.4637810,
 27.4731418, 27.4731418,
};

static const DECLARE_ALIGNED(16, float, codebook_vector10)[578] = {
  0.0000000,  0.0000000,  0.0000000,  1.0000000,
  0.0000000,  2.5198421,  0.0000000,  4.3267487,
  0.0000000,  6.3496042,  0.0000000,  8.5498797,
  0.0000000, 10.9027236,  0.0000000, 13.3905183,
  0.0000000, 16.0000000,  0.0000000, 18.7207544,
  0.0000000, 21.5443469,  0.0000000, 24.4637810,
  0.0000000, 27.4731418,  0.0000000, 30.5673509,
  0.0000000, 33.7419917,  0.0000000, 36.9931811,
  0.0000000,      64.0f,  1.0000000,  0.0000000,
  1.0000000,  1.0000000,  1.0000000,  2.5198421,
  1.0000000,  4.3267487,  1.0000000,  6.3496042,
  1.0000000,  8.5498797,  1.0000000, 10.9027236,
  1.0000000, 13.3905183,  1.0000000, 16.0000000,
  1.0000000, 18.7207544,  1.0000000, 21.5443469,
  1.0000000, 24.4637810,  1.0000000, 27.4731418,
  1.0000000, 30.5673509,  1.0000000, 33.7419917,
  1.0000000, 36.9931811,  1.0000000,      64.0f,
  2.5198421,  0.0000000,  2.5198421,  1.0000000,
  2.5198421,  2.5198421,  2.5198421,  4.3267487,
  2.5198421,  6.3496042,  2.5198421,  8.5498797,
  2.5198421, 10.9027236,  2.5198421, 13.3905183,
  2.5198421, 16.0000000,  2.5198421, 18.7207544,
  2.5198421, 21.5443469,  2.5198421, 24.4637810,
  2.5198421, 27.4731418,  2.5198421, 30.5673509,
  2.5198421, 33.7419917,  2.5198421, 36.9931811,
  2.5198421,      64.0f,  4.3267487,  0.0000000,
  4.3267487,  1.0000000,  4.3267487,  2.5198421,
  4.3267487,  4.3267487,  4.3267487,  6.3496042,
  4.3267487,  8.5498797,  4.3267487, 10.9027236,
  4.3267487, 13.3905183,  4.3267487, 16.0000000,
  4.3267487, 18.7207544,  4.3267487, 21.5443469,
  4.3267487, 24.4637810,  4.3267487, 27.4731418,
  4.3267487, 30.5673509,  4.3267487, 33.7419917,
  4.3267487, 36.9931811,  4.3267487,      64.0f,
  6.3496042,  0.0000000,  6.3496042,  1.0000000,
  6.3496042,  2.5198421,  6.3496042,  4.3267487,
  6.3496042,  6.3496042,  6.3496042,  8.5498797,
  6.3496042, 10.9027236,  6.3496042, 13.3905183,
  6.3496042, 16.0000000,  6.3496042, 18.7207544,
  6.3496042, 21.5443469,  6.3496042, 24.4637810,
  6.3496042, 27.4731418,  6.3496042, 30.5673509,
  6.3496042, 33.7419917,  6.3496042, 36.9931811,
  6.3496042,      64.0f,  8.5498797,  0.0000000,
  8.5498797,  1.0000000,  8.5498797,  2.5198421,
  8.5498797,  4.3267487,  8.5498797,  6.3496042,
  8.5498797,  8.5498797,  8.5498797, 10.9027236,
  8.5498797, 13.3905183,  8.5498797, 16.0000000,
  8.5498797, 18.7207544,  8.5498797, 21.5443469,
  8.5498797, 24.4637810,  8.5498797, 27.4731418,
  8.5498797, 30.5673509,  8.5498797, 33.7419917,
  8.5498797, 36.9931811,  8.5498797,      64.0f,
 10.9027236,  0.0000000, 10.9027236,  1.0000000,
 10.9027236,  2.5198421, 10.9027236,  4.3267487,
 10.9027236,  6.3496042, 10.9027236,  8.5498797,
 10.9027236, 10.9027236, 10.9027236, 13.3905183,
 10.9027236, 16.0000000, 10.9027236, 18.7207544,
 10.9027236, 21.5443469, 10.9027236, 24.4637810,
 10.9027236, 27.4731418, 10.9027236, 30.5673509,
 10.9027236, 33.7419917, 10.9027236, 36.9931811,
 10.9027236,      64.0f, 13.3905183,  0.0000000,
 13.3905183,  1.0000000, 13.3905183,  2.5198421,
 13.3905183,  4.3267487, 13.3905183,  6.3496042,
 13.3905183,  8.5498797, 13.3905183, 10.9027236,
 13.3905183, 13.3905183, 13.3905183, 16.0000000,
 13.3905183, 18.7207544, 13.3905183, 21.5443469,
 13.3905183, 24.4637810, 13.3905183, 27.4731418,
 13.3905183, 30.5673509, 13.3905183, 33.7419917,
 13.3905183, 36.9931811, 13.3905183,      64.0f,
 16.0000000,  0.0000000, 16.0000000,  1.0000000,
 16.0000000,  2.5198421, 16.0000000,  4.3267487,
 16.0000000,  6.3496042, 16.0000000,  8.5498797,
 16.0000000, 10.9027236, 16.0000000, 13.3905183,
 16.0000000, 16.0000000, 16.0000000, 18.7207544,
 16.0000000, 21.5443469, 16.0000000, 24.4637810,
 16.0000000, 27.4731418, 16.0000000, 30.5673509,
 16.0000000, 33.7419917, 16.0000000, 36.9931811,
 16.0000000,      64.0f, 18.7207544,  0.0000000,
 18.7207544,  1.0000000, 18.7207544,  2.5198421,
 18.7207544,  4.3267487, 18.7207544,  6.3496042,
 18.7207544,  8.5498797, 18.7207544, 10.9027236,
 18.7207544, 13.3905183, 18.7207544, 16.0000000,
 18.7207544, 18.7207544, 18.7207544, 21.5443469,
 18.7207544, 24.4637810, 18.7207544, 27.4731418,
 18.7207544, 30.5673509, 18.7207544, 33.7419917,
 18.7207544, 36.9931811, 18.7207544,      64.0f,
 21.5443469,  0.0000000, 21.5443469,  1.0000000,
 21.5443469,  2.5198421, 21.5443469,  4.3267487,
 21.5443469,  6.3496042, 21.5443469,  8.5498797,
 21.5443469, 10.9027236, 21.5443469, 13.3905183,
 21.5443469, 16.0000000, 21.5443469, 18.7207544,
 21.5443469, 21.5443469, 21.5443469, 24.4637810,
 21.5443469, 27.4731418, 21.5443469, 30.5673509,
 21.5443469, 33.7419917, 21.5443469, 36.9931811,
 21.5443469,      64.0f, 24.4637810,  0.0000000,
 24.4637810,  1.0000000, 24.4637810,  2.5198421,
 24.4637810,  4.3267487, 24.4637810,  6.3496042,
 24.4637810,  8.5498797, 24.4637810, 10.9027236,
 24.4637810, 13.3905183, 24.4637810, 16.0000000,
 24.4637810, 18.7207544, 24.4637810, 21.5443469,
 24.4637810, 24.4637810, 24.4637810, 27.4731418,
 24.4637810, 30.5673509, 24.4637810, 33.7419917,
 24.4637810, 36.9931811, 24.4637810,      64.0f,
 27.4731418,  0.0000000, 27.4731418,  1.0000000,
 27.4731418,  2.5198421, 27.4731418,  4.3267487,
 27.4731418,  6.3496042, 27.4731418,  8.5498797,
 27.4731418, 10.9027236, 27.4731418, 13.3905183,
 27.4731418, 16.0000000, 27.4731418, 18.7207544,
 27.4731418, 21.5443469, 27.4731418, 24.4637810,
 27.4731418, 27.4731418, 27.4731418, 30.5673509,
 27.4731418, 33.7419917, 27.4731418, 36.9931811,
 27.4731418,      64.0f, 30.5673509,  0.0000000,
 30.5673509,  1.0000000, 30.5673509,  2.5198421,
 30.5673509,  4.3267487, 30.5673509,  6.3496042,
 30.5673509,  8.5498797, 30.5673509, 10.9027236,
 30.5673509, 13.3905183, 30.5673509, 16.0000000,
 30.5673509, 18.7207544, 30.5673509, 21.5443469,
 30.5673509, 24.4637810, 30.5673509, 27.4731418,
 30.5673509, 30.5673509, 30.5673509, 33.7419917,
 30.5673509, 36.9931811, 30.5673509,      64.0f,
 33.7419917,  0.0000000, 33.7419917,  1.0000000,
 33.7419917,  2.5198421, 33.7419917,  4.3267487,
 33.7419917,  6.3496042, 33.7419917,  8.5498797,
 33.7419917, 10.9027236, 33.7419917, 13.3905183,
 33.7419917, 16.0000000, 33.7419917, 18.7207544,
 33.7419917, 21.5443469, 33.7419917, 24.4637810,
 33.7419917, 27.4731418, 33.7419917, 30.5673509,
 33.7419917, 33.7419917, 33.7419917, 36.9931811,
 33.7419917,      64.0f, 36.9931811,  0.0000000,
 36.9931811,  1.0000000, 36.9931811,  2.5198421,
 36.9931811,  4.3267487, 36.9931811,  6.3496042,
 36.9931811,  8.5498797, 36.9931811, 10.9027236,
 36.9931811, 13.3905183, 36.9931811, 16.0000000,
 36.9931811, 18.7207544, 36.9931811, 21.5443469,
 36.9931811, 24.4637810, 36.9931811, 27.4731418,
 36.9931811, 30.5673509, 36.9931811, 33.7419917,
 36.9931811, 36.9931811, 36.9931811,      64.0f,
      64.0f,  0.0000000,      64.0f,  1.0000000,
      64.0f,  2.5198421,      64.0f,  4.3267487,
      64.0f,  6.3496042,      64.0f,  8.5498797,
      64.0f, 10.9027236,      64.0f, 13.3905183,
      64.0f, 16.0000000,      64.0f, 18.7207544,
      64.0f, 21.5443469,      64.0f, 24.4637810,
      64.0f, 27.4731418,      64.0f, 30.5673509,
      64.0f, 33.7419917,      64.0f, 36.9931811,
      64.0f,      64.0f,
};

const float * const ff_aac_codebook_vectors[] = {
    codebook_vector0, codebook_vector0, codebook_vector2,
    codebook_vector2, codebook_vector4, codebook_vector4,
    codebook_vector6, codebook_vector6, codebook_vector8,
    codebook_vector8, codebook_vector10,
};

static const float codebook_vector0_vals[] = {
   -1.0000000,  0.0000000,  1.0000000
};

/*
 * bits  0:1, 2:3, 4:5, 6:7  index into _vals array
 *       8:11                number of non-zero values
 *      12:15                bit mask of non-zero values
 */
static const uint16_t codebook_vector02_idx[] = {
    0x0000, 0x8140, 0x8180, 0x4110, 0xc250, 0xc290, 0x4120, 0xc260, 0xc2a0,
    0x2104, 0xa244, 0xa284, 0x6214, 0xe354, 0xe394, 0x6224, 0xe364, 0xe3a4,
    0x2108, 0xa248, 0xa288, 0x6218, 0xe358, 0xe398, 0x6228, 0xe368, 0xe3a8,
    0x1101, 0x9241, 0x9281, 0x5211, 0xd351, 0xd391, 0x5221, 0xd361, 0xd3a1,
    0x3205, 0xb345, 0xb385, 0x7315, 0xf455, 0xf495, 0x7325, 0xf465, 0xf4a5,
    0x3209, 0xb349, 0xb389, 0x7319, 0xf459, 0xf499, 0x7329, 0xf469, 0xf4a9,
    0x1102, 0x9242, 0x9282, 0x5212, 0xd352, 0xd392, 0x5222, 0xd362, 0xd3a2,
    0x3206, 0xb346, 0xb386, 0x7316, 0xf456, 0xf496, 0x7326, 0xf466, 0xf4a6,
    0x320a, 0xb34a, 0xb38a, 0x731a, 0xf45a, 0xf49a, 0x732a, 0xf46a, 0xf4aa,
};

static const float codebook_vector4_vals[] = {
   -6.3496042, -4.3267487,
   -2.5198421, -1.0000000,
    0.0000000,  1.0000000,
    2.5198421,  4.3267487,
    6.3496042,
};

/*
 * bits  0:3, 4:7  index into _vals array
 */
static const uint16_t codebook_vector4_idx[] = {
    0x0000, 0x0010, 0x0020, 0x0030, 0x0040, 0x0050, 0x0060, 0x0070, 0x0080,
    0x0001, 0x0011, 0x0021, 0x0031, 0x0041, 0x0051, 0x0061, 0x0071, 0x0081,
    0x0002, 0x0012, 0x0022, 0x0032, 0x0042, 0x0052, 0x0062, 0x0072, 0x0082,
    0x0003, 0x0013, 0x0023, 0x0033, 0x0043, 0x0053, 0x0063, 0x0073, 0x0083,
    0x0004, 0x0014, 0x0024, 0x0034, 0x0044, 0x0054, 0x0064, 0x0074, 0x0084,
    0x0005, 0x0015, 0x0025, 0x0035, 0x0045, 0x0055, 0x0065, 0x0075, 0x0085,
    0x0006, 0x0016, 0x0026, 0x0036, 0x0046, 0x0056, 0x0066, 0x0076, 0x0086,
    0x0007, 0x0017, 0x0027, 0x0037, 0x0047, 0x0057, 0x0067, 0x0077, 0x0087,
    0x0008, 0x0018, 0x0028, 0x0038, 0x0048, 0x0058, 0x0068, 0x0078, 0x0088,
};

/*
 * bits  0:3, 4:7  index into _vals array
 *       8:11      number of non-zero values
 *      12:15      1: only second value non-zero
 *                 0: other cases
 */
static const uint16_t codebook_vector6_idx[] = {
    0x0000, 0x0110, 0x0120, 0x0130, 0x0140, 0x0150, 0x0160, 0x0170,
    0x1101, 0x0211, 0x0221, 0x0231, 0x0241, 0x0251, 0x0261, 0x0271,
    0x1102, 0x0212, 0x0222, 0x0232, 0x0242, 0x0252, 0x0262, 0x0272,
    0x1103, 0x0213, 0x0223, 0x0233, 0x0243, 0x0253, 0x0263, 0x0273,
    0x1104, 0x0214, 0x0224, 0x0234, 0x0244, 0x0254, 0x0264, 0x0274,
    0x1105, 0x0215, 0x0225, 0x0235, 0x0245, 0x0255, 0x0265, 0x0275,
    0x1106, 0x0216, 0x0226, 0x0236, 0x0246, 0x0256, 0x0266, 0x0276,
    0x1107, 0x0217, 0x0227, 0x0237, 0x0247, 0x0257, 0x0267, 0x0277,
};

/*
 * bits  0:3, 4:7  index into _vals array
 *       8:11      number of non-zero values
 *      12:15      1: only second value non-zero
 *                 0: other cases
 */
static const uint16_t codebook_vector8_idx[] = {
  0x0000, 0x0110, 0x0120, 0x0130, 0x0140, 0x0150, 0x0160,
  0x0170, 0x0180, 0x0190, 0x01a0, 0x01b0, 0x01c0,
  0x1101, 0x0211, 0x0221, 0x0231, 0x0241, 0x0251, 0x0261,
  0x0271, 0x0281, 0x0291, 0x02a1, 0x02b1, 0x02c1,
  0x1102, 0x0212, 0x0222, 0x0232, 0x0242, 0x0252, 0x0262,
  0x0272, 0x0282, 0x0292, 0x02a2, 0x02b2, 0x02c2,
  0x1103, 0x0213, 0x0223, 0x0233, 0x0243, 0x0253, 0x0263,
  0x0273, 0x0283, 0x0293, 0x02a3, 0x02b3, 0x02c3,
  0x1104, 0x0214, 0x0224, 0x0234, 0x0244, 0x0254, 0x0264,
  0x0274, 0x0284, 0x0294, 0x02a4, 0x02b4, 0x02c4,
  0x1105, 0x0215, 0x0225, 0x0235, 0x0245, 0x0255, 0x0265,
  0x0275, 0x0285, 0x0295, 0x02a5, 0x02b5, 0x02c5,
  0x1106, 0x0216, 0x0226, 0x0236, 0x0246, 0x0256, 0x0266,
  0x0276, 0x0286, 0x0296, 0x02a6, 0x02b6, 0x02c6,
  0x1107, 0x0217, 0x0227, 0x0237, 0x0247, 0x0257, 0x0267,
  0x0277, 0x0287, 0x0297, 0x02a7, 0x02b7, 0x02c7,
  0x1108, 0x0218, 0x0228, 0x0238, 0x0248, 0x0258, 0x0268,
  0x0278, 0x0288, 0x0298, 0x02a8, 0x02b8, 0x02c8,
  0x1109, 0x0219, 0x0229, 0x0239, 0x0249, 0x0259, 0x0269,
  0x0279, 0x0289, 0x0299, 0x02a9, 0x02b9, 0x02c9,
  0x110a, 0x021a, 0x022a, 0x023a, 0x024a, 0x025a, 0x026a,
  0x027a, 0x028a, 0x029a, 0x02aa, 0x02ba, 0x02ca,
  0x110b, 0x021b, 0x022b, 0x023b, 0x024b, 0x025b, 0x026b,
  0x027b, 0x028b, 0x029b, 0x02ab, 0x02bb, 0x02cb,
  0x110c, 0x021c, 0x022c, 0x023c, 0x024c, 0x025c, 0x026c,
  0x027c, 0x028c, 0x029c, 0x02ac, 0x02bc, 0x02cc,
};

static const float codebook_vector10_vals[] = {
     0.0000000,  1.0000000,
     2.5198421,  4.3267487,
     6.3496042,  8.5498797,
    10.9027236, 13.3905183,
    16.0000000, 18.7207544,
    21.5443469, 24.4637810,
    27.4731418, 30.5673509,
    33.7419917, 36.9931811,
};

/*
 * bits  0:3, 4:7  index into _vals array
 *       8:9       bit mask of escape-coded entries
 *      12:15      number of non-zero values
 */
static const uint16_t codebook_vector10_idx[] = {
    0x0000, 0x1010, 0x1020, 0x1030, 0x1040, 0x1050, 0x1060, 0x1070,
    0x1080, 0x1090, 0x10a0, 0x10b0, 0x10c0, 0x10d0, 0x10e0, 0x10f0, 0x1200,
    0x1001, 0x2011, 0x2021, 0x2031, 0x2041, 0x2051, 0x2061, 0x2071,
    0x2081, 0x2091, 0x20a1, 0x20b1, 0x20c1, 0x20d1, 0x20e1, 0x20f1, 0x2201,
    0x1002, 0x2012, 0x2022, 0x2032, 0x2042, 0x2052, 0x2062, 0x2072,
    0x2082, 0x2092, 0x20a2, 0x20b2, 0x20c2, 0x20d2, 0x20e2, 0x20f2, 0x2202,
    0x1003, 0x2013, 0x2023, 0x2033, 0x2043, 0x2053, 0x2063, 0x2073,
    0x2083, 0x2093, 0x20a3, 0x20b3, 0x20c3, 0x20d3, 0x20e3, 0x20f3, 0x2203,
    0x1004, 0x2014, 0x2024, 0x2034, 0x2044, 0x2054, 0x2064, 0x2074,
    0x2084, 0x2094, 0x20a4, 0x20b4, 0x20c4, 0x20d4, 0x20e4, 0x20f4, 0x2204,
    0x1005, 0x2015, 0x2025, 0x2035, 0x2045, 0x2055, 0x2065, 0x2075,
    0x2085, 0x2095, 0x20a5, 0x20b5, 0x20c5, 0x20d5, 0x20e5, 0x20f5, 0x2205,
    0x1006, 0x2016, 0x2026, 0x2036, 0x2046, 0x2056, 0x2066, 0x2076,
    0x2086, 0x2096, 0x20a6, 0x20b6, 0x20c6, 0x20d6, 0x20e6, 0x20f6, 0x2206,
    0x1007, 0x2017, 0x2027, 0x2037, 0x2047, 0x2057, 0x2067, 0x2077,
    0x2087, 0x2097, 0x20a7, 0x20b7, 0x20c7, 0x20d7, 0x20e7, 0x20f7, 0x2207,
    0x1008, 0x2018, 0x2028, 0x2038, 0x2048, 0x2058, 0x2068, 0x2078,
    0x2088, 0x2098, 0x20a8, 0x20b8, 0x20c8, 0x20d8, 0x20e8, 0x20f8, 0x2208,
    0x1009, 0x2019, 0x2029, 0x2039, 0x2049, 0x2059, 0x2069, 0x2079,
    0x2089, 0x2099, 0x20a9, 0x20b9, 0x20c9, 0x20d9, 0x20e9, 0x20f9, 0x2209,
    0x100a, 0x201a, 0x202a, 0x203a, 0x204a, 0x205a, 0x206a, 0x207a,
    0x208a, 0x209a, 0x20aa, 0x20ba, 0x20ca, 0x20da, 0x20ea, 0x20fa, 0x220a,
    0x100b, 0x201b, 0x202b, 0x203b, 0x204b, 0x205b, 0x206b, 0x207b,
    0x208b, 0x209b, 0x20ab, 0x20bb, 0x20cb, 0x20db, 0x20eb, 0x20fb, 0x220b,
    0x100c, 0x201c, 0x202c, 0x203c, 0x204c, 0x205c, 0x206c, 0x207c,
    0x208c, 0x209c, 0x20ac, 0x20bc, 0x20cc, 0x20dc, 0x20ec, 0x20fc, 0x220c,
    0x100d, 0x201d, 0x202d, 0x203d, 0x204d, 0x205d, 0x206d, 0x207d,
    0x208d, 0x209d, 0x20ad, 0x20bd, 0x20cd, 0x20dd, 0x20ed, 0x20fd, 0x220d,
    0x100e, 0x201e, 0x202e, 0x203e, 0x204e, 0x205e, 0x206e, 0x207e,
    0x208e, 0x209e, 0x20ae, 0x20be, 0x20ce, 0x20de, 0x20ee, 0x20fe, 0x220e,
    0x100f, 0x201f, 0x202f, 0x203f, 0x204f, 0x205f, 0x206f, 0x207f,
    0x208f, 0x209f, 0x20af, 0x20bf, 0x20cf, 0x20df, 0x20ef, 0x20ff, 0x220f,
    0x1100, 0x2110, 0x2120, 0x2130, 0x2140, 0x2150, 0x2160, 0x2170,
    0x2180, 0x2190, 0x21a0, 0x21b0, 0x21c0, 0x21d0, 0x21e0, 0x21f0, 0x2300,
};

const float *const ff_aac_codebook_vector_vals[] = {
    codebook_vector0_vals,  codebook_vector0_vals,
    codebook_vector10_vals, codebook_vector10_vals,
    codebook_vector4_vals,  codebook_vector4_vals,
    codebook_vector10_vals, codebook_vector10_vals,
    codebook_vector10_vals, codebook_vector10_vals,
    codebook_vector10_vals,
};

const uint16_t *const ff_aac_codebook_vector_idx[] = {
    codebook_vector02_idx, codebook_vector02_idx,
    codebook_vector02_idx, codebook_vector02_idx,
    codebook_vector4_idx,  codebook_vector4_idx,
    codebook_vector6_idx,  codebook_vector6_idx,
    codebook_vector8_idx,  codebook_vector8_idx,
    codebook_vector10_idx,
};

/* @name swb_offsets
 * Sample offset into the window indicating the beginning of a scalefactor
 * window band
 *
 * scalefactor window band - term for scalefactor bands within a window,
 * given in Table 4.110 to Table 4.128.
 *
 * scalefactor band - a set of spectral coefficients which are scaled by one
 * scalefactor. In case of EIGHT_SHORT_SEQUENCE and grouping a scalefactor band
 * may contain several scalefactor window bands of corresponding frequency. For
 * all other window_sequences scalefactor bands and scalefactor window bands are
 * identical.
 * @{
 */

static const uint16_t swb_offset_1024_96[] = {
      0,   4,   8,  12,  16,  20,  24,  28,
     32,  36,  40,  44,  48,  52,  56,  64,
     72,  80,  88,  96, 108, 120, 132, 144,
    156, 172, 188, 212, 240, 276, 320, 384,
    448, 512, 576, 640, 704, 768, 832, 896,
    960, 1024
};

static const uint16_t swb_offset_128_96[] = {
    0, 4, 8, 12, 16, 20, 24, 32, 40, 48, 64, 92, 128
};

static const uint16_t swb_offset_1024_64[] = {
      0,   4,   8,  12,  16,  20,  24,  28,
     32,  36,  40,  44,  48,  52,  56,  64,
     72,  80,  88, 100, 112, 124, 140, 156,
    172, 192, 216, 240, 268, 304, 344, 384,
    424, 464, 504, 544, 584, 624, 664, 704,
    744, 784, 824, 864, 904, 944, 984, 1024
};

static const uint16_t swb_offset_1024_48[] = {
      0,   4,   8,  12,  16,  20,  24,  28,
     32,  36,  40,  48,  56,  64,  72,  80,
     88,  96, 108, 120, 132, 144, 160, 176,
    196, 216, 240, 264, 292, 320, 352, 384,
    416, 448, 480, 512, 544, 576, 608, 640,
    672, 704, 736, 768, 800, 832, 864, 896,
    928, 1024
};

static const uint16_t swb_offset_512_48[] = {
      0,   4,   8,  12,  16,  20,  24,  28,
     32,  36,  40,  44,  48,  52,  56,  60,
     68,  76,  84,  92, 100, 112, 124, 136,
    148, 164, 184, 208, 236, 268, 300, 332,
    364, 396, 428, 460, 512
};

static const uint16_t swb_offset_480_48[] = {
      0,   4,   8,  12,  16,  20,  24,  28,
     32,  36,  40,  44,  48,  52,  56,  64,
     72,  80,  88,  96, 108, 120, 132, 144,
    156, 172, 188, 212, 240, 272, 304, 336,
    368, 400, 432, 480
};

static const uint16_t swb_offset_128_48[] = {
     0,   4,   8,  12,  16,  20,  28,  36,
    44,  56,  68,  80,  96, 112, 128
};

static const uint16_t swb_offset_1024_32[] = {
      0,   4,   8,  12,  16,  20,  24,  28,
     32,  36,  40,  48,  56,  64,  72,  80,
     88,  96, 108, 120, 132, 144, 160, 176,
    196, 216, 240, 264, 292, 320, 352, 384,
    416, 448, 480, 512, 544, 576, 608, 640,
    672, 704, 736, 768, 800, 832, 864, 896,
    928, 960, 992, 1024
};

static const uint16_t swb_offset_512_32[] = {
      0,   4,   8,  12,  16,  20,  24,  28,
     32,  36,  40,  44,  48,  52,  56,  64,
     72,  80,  88,  96, 108, 120, 132, 144,
    160, 176, 192, 212, 236, 260, 288, 320,
    352, 384, 416, 448, 480, 512
};

static const uint16_t swb_offset_480_32[] = {
      0,   4,   8,  12,  16,  20,  24,  28,
     32,  36,  40,  44,  48,  52,  56,  60,
     64,  72,  80,  88,  96, 104, 112, 124,
    136, 148, 164, 180, 200, 224, 256, 288,
    320, 352, 384, 416, 448, 480
 };

static const uint16_t swb_offset_1024_24[] = {
      0,   4,   8,  12,  16,  20,  24,  28,
     32,  36,  40,  44,  52,  60,  68,  76,
     84,  92, 100, 108, 116, 124, 136, 148,
    160, 172, 188, 204, 220, 240, 260, 284,
    308, 336, 364, 396, 432, 468, 508, 552,
    600, 652, 704, 768, 832, 896, 960, 1024
};

static const uint16_t swb_offset_512_24[] = {
      0,   4,   8,  12,  16,  20,  24,  28,
     32,  36,  40,  44,  52,  60,  68,  80,
     92, 104, 120, 140, 164, 192, 224, 256,
    288, 320, 352, 384, 416, 448, 480, 512,
};

static const uint16_t swb_offset_480_24[] = {
      0,   4,   8,  12,  16,  20,  24,  28,
     32,  36,  40,  44,  52,  60,  68,  80,
     92, 104, 120, 140, 164, 192, 224, 256,
    288, 320, 352, 384, 416, 448, 480
};

static const uint16_t swb_offset_128_24[] = {
     0,   4,   8,  12,  16,  20,  24,  28,
    36,  44,  52,  64,  76,  92, 108, 128
};

static const uint16_t swb_offset_1024_16[] = {
      0,   8,  16,  24,  32,  40,  48,  56,
     64,  72,  80,  88, 100, 112, 124, 136,
    148, 160, 172, 184, 196, 212, 228, 244,
    260, 280, 300, 320, 344, 368, 396, 424,
    456, 492, 532, 572, 616, 664, 716, 772,
    832, 896, 960, 1024
};

static const uint16_t swb_offset_128_16[] = {
     0,   4,   8,  12,  16,  20,  24,  28,
    32,  40,  48,  60,  72,  88, 108, 128
};

static const uint16_t swb_offset_1024_8[] = {
      0,  12,  24,  36,  48,  60,  72,  84,
     96, 108, 120, 132, 144, 156, 172, 188,
    204, 220, 236, 252, 268, 288, 308, 328,
    348, 372, 396, 420, 448, 476, 508, 544,
    580, 620, 664, 712, 764, 820, 880, 944,
    1024
};

static const uint16_t swb_offset_128_8[] = {
     0,   4,   8,  12,  16,  20,  24,  28,
    36,  44,  52,  60,  72,  88, 108, 128
};

static const uint16_t swb_offset_960_96[] =
{
    0,   4,   8,   12,  16,  20,  24,  28,  32,  36,
    40,  44,  48,  52,  56,  64,  72,  80,  88,  96,
    108, 120, 132, 144, 156, 172, 188, 212, 240, 276,
    320, 384, 448, 512, 576, 640, 704, 768, 832, 896,
    960
};

static const uint16_t swb_offset_960_64[] =
{
    0,   4,   8,   12,  16,  20,  24,  28,  32,  36,
    40,  44,  48,  52,  56,  64,  72,  80,  88,  100,
    112, 124, 140, 156, 172, 192, 216, 240, 268, 304,
    344, 384, 424, 464, 504, 544, 584, 624, 664, 704,
    744, 784, 824, 864, 904, 944, 960
};

static const uint16_t swb_offset_960_48[] =
{
    0,   4,   8,   12,  16,  20,  24,  28,  32,  36,
    40,  48,  56,  64,  72,  80,  88,  96,  108, 120,
    132, 144, 160, 176, 196, 216, 240, 264, 292, 320,
    352, 384, 416, 448, 480, 512, 544, 576, 608, 640,
    672, 704, 736, 768, 800, 832, 864, 896, 928, 960
};

static const uint16_t swb_offset_960_32[] =
{
    0,   4,   8,   12,  16,  20,  24,  28,  32,  36,
    40,  48,  56,  64,  72,  80,  88,  96,  108, 120,
    132, 144, 160, 176, 196, 216, 240, 264, 292, 320,
    352, 384, 416, 448, 480, 512, 544, 576, 608, 640,
    672, 704, 736, 768, 800, 832, 864, 896, 928, 960
};

static const uint16_t swb_offset_960_24[] =
{
    0,   4,   8,   12,  16,  20,  24,  28,  32,  36,
    40,  44,  52,  60,  68,  76,  84,  92,  100, 108,
    116, 124, 136, 148, 160, 172, 188, 204, 220, 240,
    260, 284, 308, 336, 364, 396, 432, 468, 508, 552,
    600, 652, 704, 768, 832, 896, 960
};

static const uint16_t swb_offset_960_16[] =
{
    0,   8,   16,  24,  32,  40,  48,  56,  64,  72,
    80,  88,  100, 112, 124, 136, 148, 160, 172, 184,
    196, 212, 228, 244, 260, 280, 300, 320, 344, 368,
    396, 424, 456, 492, 532, 572, 616, 664, 716, 772,
    832, 896, 960
};

static const uint16_t swb_offset_960_8[] =
{
    0,   12,  24,  36,  48,  60,  72,  84,  96,  108,
    120, 132, 144, 156, 172, 188, 204, 220, 236, 252,
    268, 288, 308, 328, 348, 372, 396, 420, 448, 476,
    508, 544, 580, 620, 664, 712, 764, 820, 880, 944,
    960
};


static const uint16_t swb_offset_120_96[] =
{
    0, 4, 8, 12, 16, 20, 24, 32, 40, 48, 64, 92, 120
};

static const uint16_t swb_offset_120_64[] =
{
    0, 4, 8, 12, 16, 20, 24, 32, 40, 48, 64, 92, 120
};

static const uint16_t swb_offset_120_48[] =
{
    0,  4, 8, 12, 16, 20, 28, 36, 44, 56, 68, 80, 96, 112, 120
};

static const uint16_t swb_offset_120_24[] =
{
    0, 4, 8, 12, 16, 20, 24, 28, 36, 44, 52, 64, 76, 92, 108, 120
};

static const uint16_t swb_offset_120_16[] =
{
    0, 4, 8, 12, 16, 20, 24, 28, 32, 40, 48, 60, 72, 88, 108, 120
};

static const uint16_t swb_offset_120_8[] =
{
    0, 4, 8, 12, 16,  20, 24, 28, 36, 44, 52, 60, 72, 88, 108, 120
};

const uint16_t * const ff_swb_offset_1024[] = {
    swb_offset_1024_96, swb_offset_1024_96, swb_offset_1024_64,
    swb_offset_1024_48, swb_offset_1024_48, swb_offset_1024_32,
    swb_offset_1024_24, swb_offset_1024_24, swb_offset_1024_16,
    swb_offset_1024_16, swb_offset_1024_16, swb_offset_1024_8,
    swb_offset_1024_8
};

const uint16_t * const ff_swb_offset_960[] = {
    swb_offset_960_96, swb_offset_960_96, swb_offset_960_64,
    swb_offset_960_48, swb_offset_960_48, swb_offset_960_32,
    swb_offset_960_24, swb_offset_960_24, swb_offset_960_16,
    swb_offset_960_16, swb_offset_960_16, swb_offset_960_8,
    swb_offset_960_8
};

const uint16_t * const ff_swb_offset_512[] = {
    NULL,               NULL,               NULL,
    swb_offset_512_48,  swb_offset_512_48,  swb_offset_512_32,
    swb_offset_512_24,  swb_offset_512_24,  NULL,
    NULL,               NULL,               NULL,
    NULL
};

const uint16_t * const ff_swb_offset_480[] = {
    NULL,               NULL,               NULL,
    swb_offset_480_48,  swb_offset_480_48,  swb_offset_480_32,
    swb_offset_480_24,  swb_offset_480_24,  NULL,
    NULL,               NULL,               NULL,
    NULL
};

const uint16_t * const ff_swb_offset_128[] = {
    /* The last entry on the following row is swb_offset_128_64 but is a
       duplicate of swb_offset_128_96. */
    swb_offset_128_96, swb_offset_128_96, swb_offset_128_96,
    swb_offset_128_48, swb_offset_128_48, swb_offset_128_48,
    swb_offset_128_24, swb_offset_128_24, swb_offset_128_16,
    swb_offset_128_16, swb_offset_128_16, swb_offset_128_8,
    swb_offset_128_8
};

const uint16_t * const ff_swb_offset_120[] = {
    swb_offset_120_96, swb_offset_120_96, swb_offset_120_96,
    swb_offset_120_48, swb_offset_120_48, swb_offset_120_48,
    swb_offset_120_24, swb_offset_120_24, swb_offset_120_16,
    swb_offset_120_16, swb_offset_120_16, swb_offset_120_8,
    swb_offset_120_8
};

// @}

/* @name ff_tns_max_bands
 * The maximum number of scalefactor bands on which TNS can operate for the long
 * and short transforms respectively. The index to these tables is related to
 * the sample rate of the audio.
 * @{
 */
const uint8_t ff_tns_max_bands_1024[] = {
    31, 31, 34, 40, 42, 51, 46, 46, 42, 42, 42, 39, 39
};

const uint8_t ff_tns_max_bands_512[] = {
    0, 0, 0, 31, 32, 37, 31, 31, 0, 0, 0, 0, 0
};

const uint8_t ff_tns_max_bands_480[] = {
    0, 0, 0, 31, 32, 37, 30, 30, 0, 0, 0, 0, 0
};

const uint8_t ff_tns_max_bands_128[] = {
    9, 9, 10, 14, 14, 14, 14, 14, 14, 14, 14, 14, 14
};
// @}

const DECLARE_ALIGNED(32, float, ff_aac_eld_window_512)[1920] = {
     0.00338834,  0.00567745,  0.00847677,  0.01172641,
     0.01532555,  0.01917664,  0.02318809,  0.02729259,
     0.03144503,  0.03560261,  0.03972499,  0.04379783,
     0.04783094,  0.05183357,  0.05581342,  0.05977723,
     0.06373173,  0.06768364,  0.07163937,  0.07559976,
     0.07956096,  0.08352024,  0.08747623,  0.09143035,
     0.09538618,  0.09934771,  0.10331917,  0.10730456,
     0.11130697,  0.11532867,  0.11937133,  0.12343922,
     0.12753911,  0.13167705,  0.13585812,  0.14008529,
     0.14435986,  0.14868291,  0.15305531,  0.15747594,
     0.16194193,  0.16645070,  0.17099991,  0.17558633,
     0.18020600,  0.18485548,  0.18953191,  0.19423322,
     0.19895800,  0.20370512,  0.20847374,  0.21326312,
     0.21807244,  0.22290083,  0.22774742,  0.23261210,
     0.23749542,  0.24239767,  0.24731889,  0.25225887,
     0.25721719,  0.26219330,  0.26718648,  0.27219630,
     0.27722262,  0.28226514,  0.28732336,  0.29239628,
     0.29748247,  0.30258055,  0.30768914,  0.31280508,
     0.31792385,  0.32304172,  0.32815579,  0.33326397,
     0.33836470,  0.34345661,  0.34853868,  0.35361188,
     0.35867865,  0.36374072,  0.36879900,  0.37385347,
     0.37890349,  0.38394836,  0.38898730,  0.39401912,
     0.39904236,  0.40405575,  0.40905820,  0.41404819,
     0.41902398,  0.42398423,  0.42892805,  0.43385441,
     0.43876210,  0.44365014,  0.44851786,  0.45336632,
     0.45819759,  0.46301302,  0.46781309,  0.47259722,
     0.47736435,  0.48211365,  0.48684450,  0.49155594,
     0.49624679,  0.50091636,  0.50556440,  0.51019132,
     0.51479771,  0.51938391,  0.52394998,  0.52849587,
     0.53302151,  0.53752680,  0.54201160,  0.54647575,
     0.55091916,  0.55534181,  0.55974376,  0.56412513,
     0.56848615,  0.57282710,  0.57714834,  0.58145030,
     0.58492489,  0.58918511,  0.59342326,  0.59763936,
     0.60183347,  0.60600561,  0.61015581,  0.61428412,
     0.61839056,  0.62247517,  0.62653799,  0.63057912,
     0.63459872,  0.63859697,  0.64257403,  0.64653001,
     0.65046495,  0.65437887,  0.65827181,  0.66214383,
     0.66599499,  0.66982535,  0.67363499,  0.67742394,
     0.68119219,  0.68493972,  0.68866653,  0.69237258,
     0.69605778,  0.69972207,  0.70336537,  0.70698758,
     0.71058862,  0.71416837,  0.71772674,  0.72126361,
     0.72477889,  0.72827246,  0.73174419,  0.73519392,
     0.73862141,  0.74202643,  0.74540874,  0.74876817,
     0.75210458,  0.75541785,  0.75870785,  0.76197437,
     0.76521709,  0.76843570,  0.77162988,  0.77479939,
     0.77794403,  0.78106359,  0.78415789,  0.78722670,
     0.79026979,  0.79328694,  0.79627791,  0.79924244,
     0.80218027,  0.80509112,  0.80797472,  0.81083081,
     0.81365915,  0.81645949,  0.81923160,  0.82197528,
     0.82469037,  0.82737673,  0.83003419,  0.83266262,
     0.83526186,  0.83783176,  0.84037217,  0.84288297,
     0.84536401,  0.84781517,  0.85023632,  0.85262739,
     0.85498836,  0.85731921,  0.85961993,  0.86189052,
     0.86413101,  0.86634140,  0.86852173,  0.87067211,
     0.87279275,  0.87488384,  0.87694559,  0.87897824,
     0.88098206,  0.88295729,  0.88490423,  0.88682332,
     0.88871519,  0.89058048,  0.89241983,  0.89423391,
     0.89602338,  0.89778893,  0.89953126,  0.90125142,
     0.90295086,  0.90463104,  0.90629341,  0.90793946,
     0.90957067,  0.91118856,  0.91279464,  0.91439073,
     0.91597898,  0.91756153,  0.91914049,  0.92071690,
     0.92229070,  0.92386182,  0.92542993,  0.92698946,
     0.92852960,  0.93003929,  0.93150727,  0.93291739,
     0.93424863,  0.93547974,  0.93658982,  0.93756587,
     0.93894072,  0.93922780,  0.93955477,  0.93991290,
     0.94029104,  0.94067794,  0.94106258,  0.94144084,
     0.94181549,  0.94218963,  0.94256628,  0.94294662,
     0.94332998,  0.94371562,  0.94410280,  0.94449122,
     0.94488106,  0.94527249,  0.94566568,  0.94606074,
     0.94645772,  0.94685665,  0.94725759,  0.94766054,
     0.94806547,  0.94847234,  0.94888115,  0.94929190,
     0.94970469,  0.95011960,  0.95053672,  0.95095604,
     0.95137751,  0.95180105,  0.95222658,  0.95265413,
     0.95308380,  0.95351571,  0.95394994,  0.95438653,
     0.95482538,  0.95526643,  0.95570958,  0.95615486,
     0.95660234,  0.95705214,  0.95750433,  0.95795892,
     0.95841582,  0.95887493,  0.95933616,  0.95979949,
     0.96026500,  0.96073277,  0.96120286,  0.96167526,
     0.96214986,  0.96262655,  0.96310522,  0.96358586,
     0.96406853,  0.96455330,  0.96504026,  0.96552936,
     0.96602051,  0.96651360,  0.96700850,  0.96750520,
     0.96800376,  0.96850424,  0.96900670,  0.96951112,
     0.97001738,  0.97052533,  0.97103488,  0.97154597,
     0.97205867,  0.97257304,  0.97308915,  0.97360694,
     0.97412631,  0.97464711,  0.97516923,  0.97569262,
     0.97621735,  0.97674350,  0.97727111,  0.97780016,
     0.97833051,  0.97886205,  0.97939463,  0.97992823,
     0.98046291,  0.98099875,  0.98153580,  0.98207405,
     0.98261337,  0.98315364,  0.98369474,  0.98423664,
     0.98477941,  0.98532311,  0.98586780,  0.98641348,
     0.98696003,  0.98750734,  0.98805530,  0.98860389,
     0.98915320,  0.98970328,  0.99025423,  0.99080602,
     0.99135855,  0.99191171,  0.99246541,  0.99301962,
     0.99357443,  0.99412992,  0.99468617,  0.99524320,
     0.99580092,  0.99635926,  0.99691814,  0.99747748,
     0.99803721,  0.99859725,  0.99915752,  0.99971793,
     1.00028215,  1.00084319,  1.00140472,  1.00196665,
     1.00252889,  1.00309139,  1.00365404,  1.00421679,
     1.00477954,  1.00534221,  1.00590474,  1.00646713,
     1.00702945,  1.00759179,  1.00815424,  1.00871678,
     1.00927930,  1.00984169,  1.01040384,  1.01096575,
     1.01152747,  1.01208910,  1.01265070,  1.01321226,
     1.01377365,  1.01433478,  1.01489551,  1.01545584,
     1.01601582,  1.01657553,  1.01713502,  1.01769427,
     1.01825316,  1.01881154,  1.01936929,  1.01992639,
     1.02048289,  1.02103888,  1.02159441,  1.02214945,
     1.02270387,  1.02325751,  1.02381025,  1.02436204,
     1.02491295,  1.02546304,  1.02601238,  1.02656092,
     1.02710853,  1.02765508,  1.02820041,  1.02874449,
     1.02928737,  1.02982913,  1.03036981,  1.03090937,
     1.03144768,  1.03198460,  1.03252000,  1.03305384,
     1.03358617,  1.03411707,  1.03464659,  1.03517470,
     1.03570128,  1.03622620,  1.03674934,  1.03727066,
     1.03779024,  1.03830815,  1.03882446,  1.03933914,
     1.03985206,  1.04036312,  1.04087217,  1.04137920,
     1.04188428,  1.04238748,  1.04288888,  1.04338845,
     1.04388610,  1.04438170,  1.04487515,  1.04536645,
     1.04585569,  1.04634297,  1.04682838,  1.04731192,
     1.04779350,  1.04827303,  1.04875042,  1.04922568,
     1.04969891,  1.05017022,  1.05063974,  1.05110746,
     1.05157332,  1.05203721,  1.05249907,  1.05295889,
     1.05341676,  1.05387277,  1.05432700,  1.05477948,
     1.05523018,  1.05567906,  1.05612608,  1.05657124,
     1.05701459,  1.05745616,  1.05789601,  1.05833426,
     1.05877109,  1.05920669,  1.05964125,  1.06007444,
     1.06050542,  1.06093335,  1.06135746,  1.06177909,
     1.06220164,  1.06262858,  1.06306309,  1.06350050,
     1.06392837,  1.06433391,  1.06470443,  1.06502996,
     1.06481076,  1.06469765,  1.06445004,  1.06408002,
     1.06361382,  1.06307719,  1.06249453,  1.06188365,
     1.06125612,  1.06062291,  1.05999418,  1.05937132,
     1.05874726,  1.05811486,  1.05746728,  1.05680000,
     1.05611070,  1.05539715,  1.05465735,  1.05389329,
     1.05311083,  1.05231578,  1.05151372,  1.05070811,
     1.04990044,  1.04909210,  1.04828434,  1.04747647,
     1.04666590,  1.04585003,  1.04502628,  1.04419009,
     1.04333499,  1.04245452,  1.04154244,  1.04059452,
     1.03960846,  1.03858207,  1.03751326,  1.03640189,
     1.03524976,  1.03405868,  1.03283047,  1.03156812,
     1.03027574,  1.02895743,  1.02761717,  1.02625804,
     1.02488222,  1.02349184,  1.02208892,  1.02067450,
     1.01924861,  1.01781123,  1.01636229,  1.01490045,
     1.01342315,  1.01192778,  1.01041175,  1.00887284,
     1.00730915,  1.00571882,  1.00409996,  1.00245032,
     1.00076734,  0.99904842,  0.99729101,  0.99549380,
     0.99365664,  0.99177946,  0.98986234,  0.98791024,
     0.98593294,  0.98394037,  0.98194226,  0.97994532,
     0.97795324,  0.97596955,  0.97399748,  0.97203326,
     0.97006624,  0.96808546,  0.96608018,  0.96404416,
     0.96197556,  0.95987276,  0.95773420,  0.95556018,
     0.95335291,  0.95111462,  0.94884764,  0.94655663,
     0.94424858,  0.94193055,  0.93960953,  0.93729154,
     0.93498157,  0.93268456,  0.93040503,  0.92813771,
     0.92586755,  0.92357910,  0.92125731,  0.91889642,
     0.91649998,  0.91407191,  0.91161623,  0.90913975,
     0.90665202,  0.90416271,  0.90168115,  0.89920934,
     0.89674189,  0.89427312,  0.89179743,  0.88931147,
     0.88681415,  0.88430445,  0.88178141,  0.87924528,
     0.87669753,  0.87413966,  0.87157318,  0.86899958,
     0.86642037,  0.86383703,  0.86125106,  0.85866393,
     0.85604236,  0.85344385,  0.85083093,  0.84820550,
     0.84556943,  0.84292458,  0.84027278,  0.83761586,
     0.83495565,  0.83229393,  0.82963243,  0.82697135,
     0.82430933,  0.82164496,  0.81897669,  0.81630017,
     0.81360822,  0.81089355,  0.80814924,  0.80537741,
     0.80258920,  0.79979611,  0.79700954,  0.79423813,
     0.79148780,  0.78876432,  0.78607290,  0.78340590,
     0.78074288,  0.77806279,  0.77534514,  0.77258187,
     0.76977737,  0.76693654,  0.76406441,  0.76116851,
     0.75825892,  0.75534582,  0.75243924,  0.74954634,
     0.74667135,  0.74381840,  0.74099145,  0.73819147,
     0.73541641,  0.73266408,  0.72993193,  0.72720913,
     0.72447661,  0.72171494,  0.71890515,  0.71603932,
     0.71312056,  0.71015250,  0.70713900,  0.70409084,
     0.70102565,  0.69796137,  0.69491556,  0.69189772,
     0.68890931,  0.68595141,  0.68302498,  0.68012852,
     0.67725801,  0.67440936,  0.67157841,  0.66876081,
     0.66595195,  0.66314722,  0.66034194,  0.65753027,
     0.65470525,  0.65185984,  0.64898709,  0.64608214,
     0.64314221,  0.64016460,  0.63714680,  0.63409034,
     0.63100082,  0.62788400,  0.62474577,  0.62159473,
     0.61844225,  0.61529977,  0.61217866,  0.60908811,
     0.60603510,  0.60302654,  0.60006916,  0.59716588,
     0.59431580,  0.59151787,  0.58877068,  0.58606495,
     0.58338353,  0.58070891,  0.57802356,  0.57530864,
     0.57254404,  0.56970958,  0.56678577,  0.56376860,
     0.56066951,  0.55750064,  0.55427451,  0.55101301,
     0.54774732,  0.54450907,  0.54132936,  0.53822744,
     0.53521072,  0.53228613,  0.52945979,  0.52671997,
     0.52403708,  0.52138072,  0.51872085,  0.51603570,
     0.51331170,  0.51053560,  0.50769466,  0.50478931,
     0.50183308,  0.49884001,  0.49582406,  0.49279905,
     0.48985748,  0.48679641,  0.48379429,  0.48085363,
     0.47796576,  0.47512151,  0.47231151,  0.46952402,
     0.46674486,  0.46395978,  0.46115496,  0.45832607,
     0.45547830,  0.45261727,  0.44974866,  0.44688011,
     0.44402125,  0.44118178,  0.43837094,  0.43558772,
     0.43282082,  0.43005847,  0.42728913,  0.42450572,
     0.42170567,  0.41888658,  0.41604633,  0.41318897,
     0.41032472,  0.40746405,  0.40461724,  0.40178943,
     0.39898066,  0.39619073,  0.39341940,  0.39066519,
     0.38792536,  0.38519713,  0.38247773,  0.37976476,
     0.37705620,  0.37435006,  0.37164438,  0.36893869,
     0.36623396,  0.36353124,  0.36083153,  0.35813533,
     0.35544262,  0.35275338,  0.35006755,  0.34738530,
     0.34470699,  0.34203296,  0.33936359,  0.33669922,
     0.33404027,  0.33138711,  0.32874013,  0.32609944,
     0.32346493,  0.32083645,  0.31821388,  0.31559703,
     0.31298573,  0.31037987,  0.30777941,  0.30518446,
     0.30259525,  0.30001202,  0.29743499,  0.29486428,
     0.29229989,  0.28974179,  0.28718997,  0.28464452,
     0.28210562,  0.27957346,  0.27704820,  0.27452992,
     0.27201854,  0.26951399,  0.26701622,  0.26452533,
     0.26204158,  0.25956526,  0.25709662,  0.25463583,
     0.25218294,  0.24973798,  0.24730100,  0.24487207,
     0.24245133,  0.24003893,  0.23763500,  0.23523959,
     0.23285262,  0.23047401,  0.22810369,  0.22574170,
     0.22338818,  0.22104329,  0.21870719,  0.21637986,
     0.21406117,  0.21175095,  0.20944904,  0.20715535,
     0.20486987,  0.20259261,  0.20032356,  0.19806259,
     0.19580944,  0.19356385,  0.19132556,  0.18909442,
     0.18687040,  0.18465350,  0.18244372,  0.18024164,
     0.17804841,  0.17586521,  0.17369322,  0.17153360,
     0.16938755,  0.16725622,  0.16514081,  0.16304247,
     0.16098974,  0.15896561,  0.15696026,  0.15497259,
     0.15300151,  0.15104590,  0.14910466,  0.14717666,
     0.14526081,  0.14335599,  0.14146111,  0.13957570,
     0.13769993,  0.13583399,  0.13397806,  0.13213229,
     0.13029682,  0.12847178,  0.12665729,  0.12485353,
     0.12306074,  0.12127916,  0.11950900,  0.11775043,
     0.11600347,  0.11426820,  0.11254464,  0.11083292,
     0.10913318,  0.10744559,  0.10577028,  0.10410733,
     0.10245672,  0.10081842,  0.09919240,  0.09757872,
     0.09597750,  0.09438884,  0.09281288,  0.09124964,
     0.08969907,  0.08816111,  0.08663570,  0.08512288,
     0.08362274,  0.08213540,  0.08066096,  0.07919944,
     0.07775076,  0.07631484,  0.07489161,  0.07348108,
     0.07208335,  0.07069851,  0.06932666,  0.06796781,
     0.06662187,  0.06528874,  0.06396833,  0.06266065,
     0.06136578,  0.06008380,  0.05881480,  0.05755876,
     0.05631557,  0.05508511,  0.05386728,  0.05266206,
     0.05146951,  0.05028971,  0.04912272,  0.04796855,
     0.04682709,  0.04569825,  0.04458194,  0.04347817,
     0.04238704,  0.04130868,  0.04024318,  0.03919056,
     0.03815071,  0.03712352,  0.03610890,  0.03510679,
     0.03411720,  0.03314013,  0.03217560,  0.03122343,
     0.03028332,  0.02935494,  0.02843799,  0.02753230,
     0.02663788,  0.02575472,  0.02488283,  0.02402232,
     0.02317341,  0.02233631,  0.02151124,  0.02069866,
     0.01989922,  0.01911359,  0.01834241,  0.01758563,
     0.01684248,  0.01611219,  0.01539397,  0.01468726,
     0.01399167,  0.01330687,  0.01263250,  0.01196871,
     0.01131609,  0.01067527,  0.01004684,  0.00943077,
     0.00882641,  0.00823307,  0.00765011,  0.00707735,
     0.00651513,  0.00596377,  0.00542364,  0.00489514,
     0.00437884,  0.00387530,  0.00338509,  0.00290795,
     0.00244282,  0.00198860,  0.00154417,  0.00110825,
     0.00067934,  0.00025589, -0.00016357, -0.00057897,
    -0.00098865, -0.00139089, -0.00178397, -0.00216547,
    -0.00253230, -0.00288133, -0.00320955, -0.00351626,
    -0.00380315, -0.00407198, -0.00432457, -0.00456373,
    -0.00479326, -0.00501699, -0.00523871, -0.00546066,
    -0.00568360, -0.00590821, -0.00613508, -0.00636311,
    -0.00658944, -0.00681117, -0.00702540, -0.00722982,
    -0.00742268, -0.00760226, -0.00776687, -0.00791580,
    -0.00804933, -0.00816774, -0.00827139, -0.00836122,
    -0.00843882, -0.00850583, -0.00856383, -0.00861430,
    -0.00865853, -0.00869781, -0.00873344, -0.00876633,
    -0.00879707, -0.00882622, -0.00885433, -0.00888132,
    -0.00890652, -0.00892925, -0.00894881, -0.00896446,
    -0.00897541, -0.00898088, -0.00898010, -0.00897234,
    -0.00895696, -0.00893330, -0.00890076, -0.00885914,
    -0.00880875, -0.00874987, -0.00868282, -0.00860825,
    -0.00852716, -0.00844055, -0.00834941, -0.00825485,
    -0.00815807, -0.00806025, -0.00796253, -0.00786519,
    -0.00776767, -0.00766937, -0.00756971, -0.00746790,
    -0.00736305, -0.00725422, -0.00714055, -0.00702161,
    -0.00689746, -0.00676816, -0.00663381, -0.00649489,
    -0.00635230, -0.00620694, -0.00605969, -0.00591116,
    -0.00576167, -0.00561155, -0.00546110, -0.00531037,
    -0.00515917, -0.00500732, -0.00485462, -0.00470075,
    -0.00454530, -0.00438786, -0.00422805, -0.00406594,
    -0.00390204, -0.00373686, -0.00357091, -0.00340448,
    -0.00323770, -0.00307066, -0.00290344, -0.00273610,
    -0.00256867, -0.00240117, -0.00223365, -0.00206614,
    -0.00189866, -0.00173123, -0.00156390, -0.00139674,
    -0.00122989, -0.00106351, -0.00089772, -0.00073267,
    -0.00056849, -0.00040530, -0.00024324, -0.00008241,
     0.00008214,  0.00024102,  0.00039922,  0.00055660,
     0.00071299,  0.00086826,  0.00102224,  0.00117480,
     0.00132579,  0.00147507,  0.00162252,  0.00176804,
     0.00191161,  0.00205319,  0.00219277,  0.00233029,
     0.00246567,  0.00259886,  0.00272975,  0.00285832,
     0.00298453,  0.00310839,  0.00322990,  0.00334886,
     0.00346494,  0.00357778,  0.00368706,  0.00379273,
     0.00389501,  0.00399411,  0.00409020,  0.00418350,
     0.00427419,  0.00436249,  0.00444858,  0.00453250,
     0.00461411,  0.00469328,  0.00476988,  0.00484356,
     0.00491375,  0.00497987,  0.00504139,  0.00509806,
     0.00514990,  0.00519693,  0.00523920,  0.00527700,
     0.00531083,  0.00534122,  0.00536864,  0.00539357,
     0.00541649,  0.00543785,  0.00545809,  0.00547713,
     0.00549441,  0.00550936,  0.00552146,  0.00553017,
     0.00553494,  0.00553524,  0.00553058,  0.00552065,
     0.00550536,  0.00548459,  0.00545828,  0.00542662,
     0.00539007,  0.00534910,  0.00530415,  0.00525568,
     0.00520417,  0.00515009,  0.00509387,  0.00503595,
     0.00497674,  0.00491665,  0.00485605,  0.00479503,
     0.00473336,  0.00467082,  0.00460721,  0.00454216,
     0.00447517,  0.00440575,  0.00433344,  0.00425768,
     0.00417786,  0.00409336,  0.00400363,  0.00390837,
     0.00380759,  0.00370130,  0.00358952,  0.00347268,
     0.00335157,  0.00322699,  0.00309975,  0.00297088,
     0.00284164,  0.00271328,  0.00258700,  0.00246328,
     0.00234195,  0.00222281,  0.00210562,  0.00198958,
     0.00187331,  0.00175546,  0.00163474,  0.00151020,
     0.00138130,  0.00124750,  0.00110831,  0.00096411,
     0.00081611,  0.00066554,  0.00051363,  0.00036134,
     0.00020940,  0.00005853, -0.00009058, -0.00023783,
    -0.00038368, -0.00052861, -0.00067310, -0.00081757,
    -0.00096237, -0.00110786, -0.00125442, -0.00140210,
    -0.00155065, -0.00169984, -0.00184940, -0.00199910,
    -0.00214872, -0.00229798, -0.00244664, -0.00259462,
    -0.00274205, -0.00288912, -0.00303596, -0.00318259,
    -0.00332890, -0.00347480, -0.00362024, -0.00376519,
    -0.00390962, -0.00405345, -0.00419658, -0.00433902,
    -0.00448085, -0.00462219, -0.00476309, -0.00490357,
    -0.00504361, -0.00518321, -0.00532243, -0.00546132,
    -0.00559988, -0.00573811, -0.00587602, -0.00601363,
    -0.00615094, -0.00628795, -0.00642466, -0.00656111,
    -0.00669737, -0.00683352, -0.00696963, -0.00710578,
    -0.00724208, -0.00737862, -0.00751554, -0.00765295,
    -0.00779098, -0.00792976, -0.00806941, -0.00821006,
    -0.00835183, -0.00849485, -0.00863926, -0.00878522,
    -0.00893293, -0.00908260, -0.00923444, -0.00938864,
    -0.00954537, -0.00970482, -0.00986715, -0.01003173,
    -0.01019711, -0.01036164, -0.01052357, -0.01068184,
    -0.01083622, -0.01098652, -0.01113252, -0.01127409,
    -0.01141114, -0.01154358, -0.01167135, -0.01179439,
    -0.01191268, -0.01202619, -0.01213493, -0.01223891,
    -0.01233817, -0.01243275, -0.01252272, -0.01260815,
    -0.01268915, -0.01276583, -0.01283832, -0.01290685,
    -0.01297171, -0.01303320, -0.01309168, -0.01314722,
    -0.01319969, -0.01324889, -0.01329466, -0.01333693,
    -0.01337577, -0.01341125, -0.01344345, -0.01347243,
    -0.01349823, -0.01352089, -0.01354045, -0.01355700,
    -0.01357068, -0.01358164, -0.01359003, -0.01359587,
    -0.01359901, -0.01359931, -0.01359661, -0.01359087,
    -0.01358219, -0.01357065, -0.01355637, -0.01353935,
    -0.01351949, -0.01349670, -0.01347088, -0.01344214,
    -0.01341078, -0.01337715, -0.01334158, -0.01330442,
    -0.01326601, -0.01322671, -0.01318689, -0.01314692,
    -0.01310123, -0.01306470, -0.01302556, -0.01298381,
    -0.01293948, -0.01289255, -0.01284305, -0.01279095,
    -0.01273625, -0.01267893, -0.01261897, -0.01255632,
    -0.01249096, -0.01242283, -0.01235190, -0.01227827,
    -0.01220213, -0.01212366, -0.01204304, -0.01196032,
    -0.01187543, -0.01178829, -0.01169884, -0.01160718,
    -0.01151352, -0.01141809, -0.01132111, -0.01122272,
    -0.01112304, -0.01102217, -0.01092022, -0.01081730,
    -0.01071355, -0.01060912, -0.01050411, -0.01039854,
    -0.01029227, -0.01018521, -0.01007727, -0.00996859,
    -0.00985959, -0.00975063, -0.00964208, -0.00953420,
    -0.00942723, -0.00932135, -0.00921677, -0.00911364,
    -0.00901208, -0.00891220, -0.00881412, -0.00871792,
    -0.00862369, -0.00853153, -0.00844149, -0.00835360,
    -0.00826785, -0.00818422, -0.00810267, -0.00802312,
    -0.00794547, -0.00786959, -0.00779533, -0.00772165,
    -0.00764673, -0.00756886, -0.00748649, -0.00739905,
    -0.00730681, -0.00721006, -0.00710910, -0.00700419,
    -0.00689559, -0.00678354, -0.00666829, -0.00655007,
    -0.00642916, -0.00630579, -0.00618022, -0.00605267,
    -0.00592333, -0.00579240, -0.00566006, -0.00552651,
    -0.00539194, -0.00525653, -0.00512047, -0.00498390,
    -0.00484693, -0.00470969, -0.00457228, -0.00443482,
    -0.00429746, -0.00416034, -0.00402359, -0.00388738,
    -0.00375185, -0.00361718, -0.00348350, -0.00335100,
    -0.00321991, -0.00309043, -0.00296276, -0.00283698,
    -0.00271307, -0.00259098, -0.00247066, -0.00235210,
    -0.00223531, -0.00212030, -0.00200709, -0.00189576,
    -0.00178647, -0.00167936, -0.00157457, -0.00147216,
    -0.00137205, -0.00127418, -0.00117849, -0.00108498,
    -0.00099375, -0.00090486, -0.00081840, -0.00073444,
    -0.00065309, -0.00057445, -0.00049860, -0.00042551,
    -0.00035503, -0.00028700, -0.00022125, -0.00015761,
    -0.00009588, -0.00003583,  0.00002272,  0.00007975,
     0.00013501,  0.00018828,  0.00023933,  0.00028784,
     0.00033342,  0.00037572,  0.00041438,  0.00044939,
     0.00048103,  0.00050958,  0.00053533,  0.00055869,
     0.00058015,  0.00060022,  0.00061935,  0.00063781,
     0.00065568,  0.00067303,  0.00068991,  0.00070619,
     0.00072155,  0.00073567,  0.00074826,  0.00075912,
     0.00076811,  0.00077509,  0.00077997,  0.00078275,
     0.00078351,  0.00078237,  0.00077943,  0.00077484,
     0.00076884,  0.00076160,  0.00075335,  0.00074423,
     0.00073442,  0.00072404,  0.00071323,  0.00070209,
     0.00069068,  0.00067906,  0.00066728,  0.00065534,
     0.00064321,  0.00063086,  0.00061824,  0.00060534,
     0.00059211,  0.00057855,  0.00056462,  0.00055033,
     0.00053566,  0.00052063,  0.00050522,  0.00048949,
     0.00047349,  0.00045728,  0.00044092,  0.00042447,
     0.00040803,  0.00039166,  0.00037544,  0.00035943,
     0.00034371,  0.00032833,  0.00031333,  0.00029874,
     0.00028452,  0.00027067,  0.00025715,  0.00024395,
     0.00023104,  0.00021842,  0.00020606,  0.00019398,
     0.00018218,  0.00017069,  0.00015953,  0.00014871,
     0.00013827,  0.00012823,  0.00011861,  0.00010942,
     0.00010067,  0.00009236,  0.00008448,  0.00007703,
     0.00006999,  0.00006337,  0.00005714,  0.00005129,
     0.00004583,  0.00004072,  0.00003597,  0.00003157,
     0.00002752,  0.00002380,  0.00002042,  0.00001736,
     0.00001461,  0.00001215,  0.00000998,  0.00000807,
     0.00000641,  0.00000499,  0.00000378,  0.00000278,
     0.00000196,  0.00000132,  0.00000082,  0.00000046,
     0.00000020,  0.00000005, -0.00000003, -0.00000006,
    -0.00000004, -0.00000001,  0.00000001,  0.00000001,
     0.00000001,  0.00000001, -0.00000001, -0.00000004,
    -0.00000005, -0.00000003,  0.00000005,  0.00000020,
     0.00000043,  0.00000077,  0.00000123,  0.00000183,
     0.00000257,  0.00000348,  0.00000455,  0.00000581,
     0.00000727,  0.00000893,  0.00001080,  0.00001290,
     0.00001522,  0.00001778,  0.00002057,  0.00002362,
     0.00002691,  0.00003044,  0.00003422,  0.00003824,
     0.00004250,  0.00004701,  0.00005176,  0.00005676,
     0.00006200,  0.00006749,  0.00007322,  0.00007920,
     0.00008541,  0.00009186,  0.00009854,  0.00010543,
     0.00011251,  0.00011975,  0.00012714,  0.00013465,
     0.00014227,  0.00014997,  0.00015775,  0.00016558,
     0.00017348,  0.00018144,  0.00018947,  0.00019756,
     0.00020573,  0.00021399,  0.00022233,  0.00023076,
     0.00023924,  0.00024773,  0.00025621,  0.00026462,
     0.00027293,  0.00028108,  0.00028904,  0.00029675,
     0.00030419,  0.00031132,  0.00031810,  0.00032453,
     0.00033061,  0.00033632,  0.00034169,  0.00034672,
     0.00035142,  0.00035580,  0.00035988,  0.00036369,
     0.00036723,  0.00037053,  0.00037361,  0.00037647,
     0.00037909,  0.00038145,  0.00038352,  0.00038527,
     0.00038663,  0.00038757,  0.00038801,  0.00038790,
     0.00038717,  0.00038572,  0.00038350,  0.00038044,
     0.00037651,  0.00037170,  0.00036597,  0.00035936,
     0.00035191,  0.00034370,  0.00033480,  0.00032531,
     0.00031537,  0.00030512,  0.00029470,  0.00028417,
     0.00027354,  0.00026279,  0.00025191,  0.00024081,
     0.00022933,  0.00021731,  0.00020458,  0.00019101,
     0.00017654,  0.00016106,  0.00014452,  0.00012694,
     0.00010848,  0.00008929,  0.00006953,  0.00004935,
     0.00002884,  0.00000813, -0.00001268, -0.00003357,
    -0.00005457, -0.00007574, -0.00009714, -0.00011882,
    -0.00014082, -0.00016318, -0.00018595, -0.00020912,
    -0.00023265, -0.00025650, -0.00028060, -0.00030492,
    -0.00032941, -0.00035400, -0.00037865, -0.00040333,
    -0.00042804, -0.00045279, -0.00047759, -0.00050243,
    -0.00052728, -0.00055209, -0.00057685, -0.00060153,
    -0.00062611, -0.00065056, -0.00067485, -0.00069895,
    -0.00072287, -0.00074660, -0.00077013, -0.00079345,
    -0.00081653, -0.00083936, -0.00086192, -0.00088421,
    -0.00090619, -0.00092786, -0.00094919, -0.00097017,
    -0.00099077, -0.00101098, -0.00103077, -0.00105012,
    -0.00106904, -0.00108750, -0.00110549, -0.00112301,
    -0.00114005, -0.00115660, -0.00117265, -0.00118821,
    -0.00120325, -0.00121779, -0.00123180, -0.00124528,
    -0.00125822, -0.00127061, -0.00128243, -0.00129368,
    -0.00130435, -0.00131445, -0.00132395, -0.00133285,
    -0.00134113, -0.00134878, -0.00135577, -0.00136215,
    -0.00136797, -0.00137333, -0.00137834, -0.00138305,
    -0.00138748, -0.00139163, -0.00139551, -0.00139913,
    -0.00140249, -0.00140559, -0.00140844, -0.00141102,
    -0.00141334, -0.00141538, -0.00141714, -0.00141861,
    -0.00141978, -0.00142064, -0.00142117, -0.00142138,
    -0.00142125, -0.00142077, -0.00141992, -0.00141870,
    -0.00141710, -0.00141510, -0.00141268, -0.00140986,
    -0.00140663, -0.00140301, -0.00139900, -0.00139460,
    -0.00138981, -0.00138464, -0.00137908, -0.00137313,
    -0.00136680, -0.00136010, -0.00135301, -0.00134555,
    -0.00133772, -0.00132952, -0.00132095, -0.00131201,
    -0.00130272, -0.00129307, -0.00128309, -0.00127277,
    -0.00126211, -0.00125113, -0.00123981, -0.00122817,
    -0.00121622, -0.00120397, -0.00119141, -0.00117859,
    -0.00116552, -0.00115223, -0.00113877, -0.00112517,
    -0.00111144, -0.00109764, -0.00108377, -0.00106989,
};

/* Q30 representation of ff_aac_eld_window_512 table */
const DECLARE_ALIGNED(32, int, ff_aac_eld_window_512_fixed)[1920] = {
    0x003783ba, 0x005d04f4, 0x008ae226, 0x00c02021,
    0x00fb1804, 0x013a30a8, 0x017be9e6, 0x01bf296c,
    0x02033204, 0x0247502c, 0x028adab0, 0x02cd9568,
    0x030fa980, 0x03513dc0, 0x03927274, 0x03d363e0,
    0x04142e40, 0x0454edc0, 0x0495bd48, 0x04d6a060,
    0x051786d8, 0x05586548, 0x059935e8, 0x05d9feb0,
    0x061acea0, 0x065bb680, 0x069cc800, 0x06de13f0,
    0x071fa748, 0x07618b80, 0x07a3c7a8, 0x07e66da0,
    0x082999d0, 0x086d6590, 0x08b1e640, 0x08f72850,
    0x093d3120, 0x09840550, 0x09cba880, 0x0a1415f0,
    0x0a5d41b0, 0x0aa720d0, 0x0af1a9a0, 0x0b3cce70,
    0x0b887ec0, 0x0bd4ac10, 0x0c214a70, 0x0c6e5130,
    0x0cbbba50, 0x0d098130, 0x0d57a240, 0x0da61a60,
    0x0df4e620, 0x0e4401d0, 0x0e9369f0, 0x0ee31de0,
    0x0f332000, 0x0f837180, 0x0fd412a0, 0x10250260,
    0x10763f20, 0x10c7c660, 0x11199560, 0x116baa00,
    0x11be0400, 0x1210a1c0, 0x12638180, 0x12b69ee0,
    0x1309f3e0, 0x135d7ac0, 0x13b12dc0, 0x1404ffa0,
    0x1458dd40, 0x14acb720, 0x15008120, 0x15543260,
    0x15a7c460, 0x15fb3160, 0x164e7520, 0x16a193c0,
    0x16f49740, 0x17478720, 0x179a6720, 0x17ed3720,
    0x183ff460, 0x18929c20, 0x18e52b00, 0x19379c00,
    0x1989e900, 0x19dc0ca0, 0x1a2e0280, 0x1a7fc400,
    0x1ad14a00, 0x1b228ec0, 0x1b738ea0, 0x1bc44540,
    0x1c14ada0, 0x1c64c380, 0x1cb48440, 0x1d03f420,
    0x1d531c00, 0x1da20160, 0x1df0a660, 0x1e3f0860,
    0x1e8d2340, 0x1edaf340, 0x1f2875e0, 0x1f75a700,
    0x1fc281e0, 0x200f0380, 0x205b2ac0, 0x20a6f980,
    0x20f27200, 0x213d9600, 0x21886580, 0x21d2e040,
    0x221d0640, 0x2266d6c0, 0x22b05180, 0x22f97580,
    0x23424280, 0x238ab880, 0x23d2d780, 0x241aa040,
    0x246213c0, 0x24a93300, 0x24efff80, 0x25367b40,
    0x256f68c0, 0x25b53580, 0x25faa580, 0x263fb940,
    0x26847080, 0x26c8cbc0, 0x270ccb00, 0x27506e40,
    0x2793b600, 0x27d6a200, 0x281932c0, 0x285b6880,
    0x289d4400, 0x28dec5c0, 0x291feec0, 0x2960bf80,
    0x29a137c0, 0x29e15800, 0x2a212000, 0x2a609080,
    0x2a9fa980, 0x2ade6b40, 0x2b1cd600, 0x2b5aea00,
    0x2b98a740, 0x2bd60d80, 0x2c131cc0, 0x2c4fd500,
    0x2c8c3600, 0x2cc83f00, 0x2d03f040, 0x2d3f48c0,
    0x2d7a48c0, 0x2db4ef40, 0x2def3c40, 0x2e292ec0,
    0x2e62c700, 0x2e9c0400, 0x2ed4e580, 0x2f0d6ac0,
    0x2f4592c0, 0x2f7d5c80, 0x2fb4c6c0, 0x2febd140,
    0x30227b40, 0x3058c400, 0x308eab40, 0x30c43040,
    0x30f95100, 0x312e0d00, 0x31626240, 0x31965040,
    0x31c9d5c0, 0x31fcf240, 0x322fa480, 0x3261ec00,
    0x3293c7c0, 0x32c53680, 0x32f63780, 0x3326c9c0,
    0x3356ec00, 0x33869d00, 0x33b5db80, 0x33e4a700,
    0x3412fdc0, 0x3440df40, 0x346e4a80, 0x349b3e40,
    0x34c7ba00, 0x34f3bd80, 0x351f47c0, 0x354a5840,
    0x3574ee40, 0x359f0900, 0x35c8a840, 0x35f1cb80,
    0x361a71c0, 0x36429a80, 0x366a4580, 0x36917280,
    0x36b82100, 0x36de5180, 0x37040340, 0x372936c0,
    0x374dec40, 0x37722340, 0x3795dc40, 0x37b91780,
    0x37dbd600, 0x37fe18c0, 0x381fe080, 0x38412e00,
    0x38620280, 0x38825f40, 0x38a24540, 0x38c1b680,
    0x38e0b5c0, 0x38ff4540, 0x391d6800, 0x393b20c0,
    0x39587280, 0x39755fc0, 0x3991eb80, 0x39ae1a80,
    0x39c9f280, 0x39e57980, 0x3a00b600, 0x3a1bae00,
    0x3a366800, 0x3a50e9c0, 0x3a6b3a40, 0x3a8560c0,
    0x3a9f6640, 0x3ab95400, 0x3ad332c0, 0x3aed0680,
    0x3b06cf80, 0x3b208d40, 0x3b3a3e80, 0x3b53cb80,
    0x3b6d0780, 0x3b85c380, 0x3b9dd0c0, 0x3bb4eb40,
    0x3bcabac0, 0x3bdee680, 0x3bf11680, 0x3c011440,
    0x3c179ac0, 0x3c1c4f00, 0x3c21aa40, 0x3c278880,
    0x3c2dba80, 0x3c341140, 0x3c3a5e80, 0x3c409100,
    0x3c46b480, 0x3c4cd5c0, 0x3c530180, 0x3c593cc0,
    0x3c5f84c0, 0x3c65d640, 0x3c6c2e40, 0x3c728b40,
    0x3c78ee80, 0x3c7f5840, 0x3c85c940, 0x3c8c4240,
    0x3c92c380, 0x3c994cc0, 0x3c9fde40, 0x3ca67880,
    0x3cad1ac0, 0x3cb3c540, 0x3cba7800, 0x3cc132c0,
    0x3cc7f640, 0x3ccec280, 0x3cd59800, 0x3cdc76c0,
    0x3ce35e80, 0x3cea4f00, 0x3cf147c0, 0x3cf84900,
    0x3cff5340, 0x3d0666c0, 0x3d0d8400, 0x3d14ab40,
    0x3d1bdc00, 0x3d2315c0, 0x3d2a5880, 0x3d31a440,
    0x3d38f900, 0x3d405780, 0x3d47c040, 0x3d4f3300,
    0x3d56af40, 0x3d5e3500, 0x3d65c380, 0x3d6d5ac0,
    0x3d74fb40, 0x3d7ca540, 0x3d845900, 0x3d8c1680,
    0x3d93dd00, 0x3d9bac80, 0x3da38400, 0x3dab6400,
    0x3db34c80, 0x3dbb3dc0, 0x3dc33840, 0x3dcb3bc0,
    0x3dd347c0, 0x3ddb5bc0, 0x3de37780, 0x3deb9b00,
    0x3df3c600, 0x3dfbf940, 0x3e0434c0, 0x3e0c7840,
    0x3e14c3c0, 0x3e1d1640, 0x3e256f80, 0x3e2dcf40,
    0x3e363580, 0x3e3ea300, 0x3e4717c0, 0x3e4f9380,
    0x3e581600, 0x3e609e40, 0x3e692c40, 0x3e71bf80,
    0x3e7a5840, 0x3e82f740, 0x3e8b9c40, 0x3e944700,
    0x3e9cf780, 0x3ea5ad00, 0x3eae66c0, 0x3eb72500,
    0x3ebfe780, 0x3ec8af00, 0x3ed17b80, 0x3eda4d00,
    0x3ee32340, 0x3eebfd40, 0x3ef4dac0, 0x3efdbbc0,
    0x3f06a040, 0x3f0f88c0, 0x3f187540, 0x3f216600,
    0x3f2a5a80, 0x3f335200, 0x3f3c4c40, 0x3f454940,
    0x3f4e4940, 0x3f574c80, 0x3f605340, 0x3f695dc0,
    0x3f726b40, 0x3f7b7b40, 0x3f848dc0, 0x3f8da240,
    0x3f96b940, 0x3f9fd300, 0x3fa8f040, 0x3fb21080,
    0x3fbb33c0, 0x3fc459c0, 0x3fcd81c0, 0x3fd6abc0,
    0x3fdfd780, 0x3fe90480, 0x3ff23280, 0x3ffb6100,
    0x40049f80, 0x400dd080, 0x40170400, 0x40203880,
    0x40296f00, 0x4032a600, 0x403bde00, 0x40451680,
    0x404e4f00, 0x40578700, 0x4060be80, 0x4069f500,
    0x40732b80, 0x407c6280, 0x40859980, 0x408ed100,
    0x40980800, 0x40a13f00, 0x40aa7500, 0x40b3a980,
    0x40bcdd80, 0x40c61180, 0x40cf4500, 0x40d87800,
    0x40e1ab00, 0x40eadc80, 0x40f40c80, 0x40fd3a80,
    0x41066700, 0x410f9300, 0x4118bd80, 0x4121e700,
    0x412b0f80, 0x41343580, 0x413d5880, 0x41467980,
    0x414f9780, 0x4158b380, 0x4161cd80, 0x416ae580,
    0x4173fb00, 0x417d0d00, 0x41861b80, 0x418f2600,
    0x41982c80, 0x41a12f80, 0x41aa3000, 0x41b32c80,
    0x41bc2580, 0x41c51a00, 0x41ce0900, 0x41d6f300,
    0x41dfd800, 0x41e8b880, 0x41f19400, 0x41fa6b80,
    0x42033d00, 0x420c0900, 0x4214cf00, 0x421d8e00,
    0x42264680, 0x422ef980, 0x4237a680, 0x42404d80,
    0x4248ee00, 0x42518780, 0x425a1a00, 0x4262a480,
    0x426b2800, 0x4273a400, 0x427c1980, 0x42848880,
    0x428cef80, 0x42954f00, 0x429da680, 0x42a5f500,
    0x42ae3b80, 0x42b67a00, 0x42beb100, 0x42c6e080,
    0x42cf0780, 0x42d72680, 0x42df3c00, 0x42e74880,
    0x42ef4c80, 0x42f74880, 0x42ff3c80, 0x43072880,
    0x430f0c80, 0x4316e800, 0x431eba00, 0x43268380,
    0x432e4480, 0x4335fd00, 0x433dae80, 0x43455800,
    0x434cfa00, 0x43549400, 0x435c2500, 0x4363ad80,
    0x436b2e00, 0x4372a700, 0x437a1800, 0x43818200,
    0x4388e400, 0x43903f00, 0x43979200, 0x439edd00,
    0x43a62080, 0x43ad5c80, 0x43b49180, 0x43bbbf80,
    0x43c2e800, 0x43ca0b00, 0x43d12980, 0x43d84280,
    0x43df5200, 0x43e65500, 0x43ed4800, 0x43f43080,
    0x43fb1c80, 0x44021b80, 0x44093a00, 0x44106480,
    0x44176700, 0x441e0c00, 0x44241e00, 0x44297380,
    0x4425dc00, 0x44240180, 0x441ff300, 0x4419e300,
    0x44123f80, 0x44097500, 0x43ffe900, 0x43f5e700,
    0x43eb9f00, 0x43e13f00, 0x43d6f200, 0x43ccbd80,
    0x43c28400, 0x43b82780, 0x43ad8b00, 0x43a29c80,
    0x43975180, 0x438ba080, 0x437f8180, 0x4372fd00,
    0x43662b00, 0x43592480, 0x434c0000, 0x433ecd00,
    0x43319180, 0x43245300, 0x43171700, 0x4309da80,
    0x42fc9300, 0x42ef3500, 0x42e1b600, 0x42d40280,
    0x42c60000, 0x42b79300, 0x42a8a180, 0x42991a00,
    0x4288f200, 0x42782100, 0x42669e00, 0x42546880,
    0x42418800, 0x422e0480, 0x4219e500, 0x42053680,
    0x41f00980, 0x41da7080, 0x41c47b00, 0x41ae3600,
    0x4197ab80, 0x4180e400, 0x4169e780, 0x4152bb00,
    0x413b5e80, 0x4123d180, 0x410c1480, 0x40f42100,
    0x40dbed00, 0x40c36c80, 0x40aa9600, 0x40915f80,
    0x4077c100, 0x405db280, 0x40432c80, 0x40282580,
    0x400c9280, 0x3ff068c0, 0x3fd39dc0, 0x3fb62bc0,
    0x3f981200, 0x3f795080, 0x3f59e780, 0x3f39ebc0,
    0x3f198680, 0x3ef8e100, 0x3ed82440, 0x3eb76c80,
    0x3e96c940, 0x3e764900, 0x3e55f980, 0x3e35cb00,
    0x3e1590c0, 0x3df51cc0, 0x3dd44200, 0x3db2e640,
    0x3d910200, 0x3d6e8e40, 0x3d4b8480, 0x3d27e600,
    0x3d03bc00, 0x3cdf0fc0, 0x3cb9eb80, 0x3c946240,
    0x3c6e9180, 0x3c489700, 0x3c229000, 0x3bfc95c0,
    0x3bd6bd00, 0x3bb11a80, 0x3b8bc180, 0x3b669bc0,
    0x3b416a00, 0x3b1beb80, 0x3af5e140, 0x3acf3300,
    0x3aa7ef80, 0x3a802780, 0x3a57eb80, 0x3a2f5880,
    0x3a069640, 0x39ddcd40, 0x39b524c0, 0x398ca540,
    0x39643800, 0x393bc540, 0x39133580, 0x38ea7ac0,
    0x38c19040, 0x389871c0, 0x386f1b40, 0x38458e00,
    0x381bd000, 0x37f1e780, 0x37c7db00, 0x379db080,
    0x37736e80, 0x37491b00, 0x371ebcc0, 0x36f45980,
    0x36c96600, 0x369ed300, 0x36740380, 0x3648ffc0,
    0x361dcf40, 0x35f27a00, 0x35c70780, 0x359b7f80,
    0x356fe9c0, 0x35444dc0, 0x3518b280, 0x34ed1940,
    0x34c17c00, 0x3495d4c0, 0x346a1d40, 0x343e4300,
    0x34122840, 0x33e5ae00, 0x33b8b780, 0x338b4dc0,
    0x335d9f00, 0x332fdc00, 0x33023440, 0x32d4cc40,
    0x32a7bc80, 0x327b1d40, 0x324f04c0, 0x32235280,
    0x31f7b100, 0x31cbc7c0, 0x319f4140, 0x3171fb40,
    0x31440840, 0x31157d00, 0x30e66e80, 0x30b6fc40,
    0x30875080, 0x30579600, 0x3027f700, 0x2ff89140,
    0x2fc976c0, 0x2f9ab880, 0x2f6c6780, 0x2f3e8780,
    0x2f111000, 0x2ee3f800, 0x2eb73480, 0x2e8a9840,
    0x2e5dd340, 0x2e3093c0, 0x2e028ac0, 0x2dd39680,
    0x2da3c480, 0x2d732380, 0x2d41c400, 0x2d0fd300,
    0x2cdd9ac0, 0x2cab6640, 0x2c797f00, 0x2c480d40,
    0x2c171700, 0x2be6a0c0, 0x2bb6ae80, 0x2b8739c0,
    0x2b583200, 0x2b298600, 0x2afb2400, 0x2accfa40,
    0x2a9ef500, 0x2a710100, 0x2a430ac0, 0x2a14f9c0,
    0x29e6b0c0, 0x29b81240, 0x29890140, 0x29596900,
    0x29293e00, 0x28f87500, 0x28c70340, 0x2894efc0,
    0x28625140, 0x282f4040, 0x27fbd5c0, 0x27c83540,
    0x27948ec0, 0x27611240, 0x272def80, 0x26fb4cc0,
    0x26c94780, 0x2697fcc0, 0x26678880, 0x2637f740,
    0x26094540, 0x25db6dc0, 0x25ae6b40, 0x25821680,
    0x255627c0, 0x252a55c0, 0x24fe5680, 0x24d1db40,
    0x24a48fc0, 0x24761f40, 0x244637c0, 0x2414c900,
    0x23e20240, 0x23ae1740, 0x23793bc0, 0x2343cc00,
    0x230e4ac0, 0x22d93c80, 0x22a52400, 0x22725180,
    0x2240e480, 0x2210f9c0, 0x21e2ab40, 0x21b5c7c0,
    0x2189d2c0, 0x215e4d40, 0x2132b900, 0x2106ba80,
    0x20da1940, 0x20ac9d80, 0x207e11c0, 0x204e77c0,
    0x201e0880, 0x1fecfea0, 0x1fbb94e0, 0x1f8a0500,
    0x1f59d340, 0x1f27ac20, 0x1ef67c60, 0x1ec64e40,
    0x1e96fdc0, 0x1e686400, 0x1e3a5a00, 0x1e0cae80,
    0x1ddf25e0, 0x1db18460, 0x1d839020, 0x1d5536e0,
    0x1d268e80, 0x1cf7ae60, 0x1cc8aea0, 0x1c99af00,
    0x1c6ad820, 0x1c3c5280, 0x1c0e4500, 0x1be0ab60,
    0x1bb35620, 0x1b861400, 0x1b58b480, 0x1b2b1a00,
    0x1afd39c0, 0x1acf09a0, 0x1aa080c0, 0x1a71b020,
    0x1a42c2a0, 0x1a13e420, 0x19e53fc0, 0x19b6eb00,
    0x1988e620, 0x195b3060, 0x192dc8a0, 0x1900a8a0,
    0x18d3c4e0, 0x18a711e0, 0x187a83e0, 0x184e10e0,
    0x1821b060, 0x17f55a00, 0x17c90580, 0x179cb100,
    0x177060a0, 0x17441880, 0x1717dd20, 0x16ebb080,
    0x16bf9260, 0x169382e0, 0x166781c0, 0x163b8f80,
    0x160fade0, 0x15e3de40, 0x15b82220, 0x158c7ae0,
    0x1560ea80, 0x15357240, 0x150a1400, 0x14ded020,
    0x14b3a640, 0x148895a0, 0x145d9dc0, 0x1432bde0,
    0x1407f540, 0x13dd4380, 0x13b2a860, 0x13882460,
    0x135db880, 0x133365a0, 0x13092cc0, 0x12df0e60,
    0x12b50aa0, 0x128b2120, 0x12615200, 0x12379da0,
    0x120e04c0, 0x11e48820, 0x11bb2860, 0x1191e600,
    0x1168c080, 0x113fb7a0, 0x1116cb40, 0x10edfba0,
    0x10c54a00, 0x109cb7a0, 0x10744560, 0x104bf420,
    0x1023c3e0, 0x0ffbb500, 0x0fd3c790, 0x0fabfbe0,
    0x0f845290, 0x0f5ccc40, 0x0f356970, 0x0f0e2a60,
    0x0ee70eb0, 0x0ec01610, 0x0e994040, 0x0e728d50,
    0x0e4bfdf0, 0x0e2592c0, 0x0dff4c70, 0x0dd92af0,
    0x0db32da0, 0x0d8d53e0, 0x0d679cf0, 0x0d420880,
    0x0d1c9680, 0x0cf74700, 0x0cd219f0, 0x0cad0eb0,
    0x0c882450, 0x0c6359a0, 0x0c3ead90, 0x0c1a1f80,
    0x0bf5af40, 0x0bd15cf0, 0x0bad2870, 0x0b891440,
    0x0b652530, 0x0b416020, 0x0b1dca30, 0x0afa6810,
    0x0ad73ee0, 0x0ab45370, 0x0a91aac0, 0x0a6f49b0,
    0x0a4da7f0, 0x0a2c7e20, 0x0a0ba310, 0x09eb1220,
    0x09cac6e0, 0x09aabc70, 0x098aee40, 0x096b57a0,
    0x094bf400, 0x092cbea0, 0x090db2e0, 0x08eecef0,
    0x08d01360, 0x08b18110, 0x089318b0, 0x0874db00,
    0x0856c880, 0x0838e1b0, 0x081b2730, 0x07fd99a8,
    0x07e03a28, 0x07c309a8, 0x07a60910, 0x07893918,
    0x076c99d0, 0x07502b90, 0x0733ee70, 0x0717e2f8,
    0x06fc09b8, 0x06e06378, 0x06c4f0b8, 0x06a9b1c8,
    0x068ea6a0, 0x0673cf18, 0x06592b18, 0x063ebad0,
    0x06247ed0, 0x060a7780, 0x05f0a570, 0x05d708b8,
    0x05bda128, 0x05a46e80, 0x058b7078, 0x0572a740,
    0x055a1330, 0x0541b4d8, 0x05298c98, 0x05119a88,
    0x04f9de50, 0x04e257a0, 0x04cb0630, 0x04b3ea00,
    0x049d0378, 0x04865308, 0x046fd918, 0x045995a8,
    0x04438860, 0x042db0d0, 0x04180ea0, 0x0402a1d0,
    0x03ed6abc, 0x03d869b8, 0x03c39f28, 0x03af0af0,
    0x039aaca0, 0x038683b4, 0x03728fc0, 0x035ed0b0,
    0x034b46c4, 0x0337f254, 0x0324d3a0, 0x0311eab0,
    0x02ff370c, 0x02ecb85c, 0x02da6e34, 0x02c858a8,
    0x02b67820, 0x02a4cd28, 0x02935820, 0x02821920,
    0x02710fac, 0x02603b54, 0x024f9bb4, 0x023f308c,
    0x022ef9e8, 0x021ef7c8, 0x020f2a40, 0x01ff908e,
    0x01f02974, 0x01e0f38a, 0x01d1ed94, 0x01c316d6,
    0x01b46f5e, 0x01a5f720, 0x0197ae28, 0x018994ea,
    0x017bac54, 0x016df546, 0x016070ae, 0x01532078,
    0x01460760, 0x01392834, 0x012c85a4, 0x01201f7a,
    0x0113f27c, 0x0107fb6c, 0x00fc36fd, 0x00f0a2d5,
    0x00e53d51, 0x00da050f, 0x00cef88c, 0x00c41869,
    0x00b9671f, 0x00aee754, 0x00a49b80, 0x009a8384,
    0x00909ca6, 0x0086e400, 0x007d56e3, 0x0073f48e,
    0x006abe70, 0x0061b5de, 0x0058dc65, 0x005033b4,
    0x0047be30, 0x003f7e30, 0x00377619, 0x002fa4d4,
    0x002805ee, 0x002094cb, 0x00194cb8, 0x00122856,
    0x000b215c, 0x00043148, 0xfffd51f0, 0xfff683a0,
    0xffefcd4d, 0xffe9362f, 0xffe2c57d, 0xffdc855c,
    0xffd682c4, 0xffd0cad4, 0xffcb6a2c, 0xffc663bc,
    0xffc1b06f, 0xffbd48e1, 0xffb92570, 0xffb53a54,
    0xffb1779c, 0xffadcd38, 0xffaa2b42, 0xffa68855,
    0xffa2e141, 0xff9f332c, 0xff9b7b9c, 0xff97bf2e,
    0xff9409e2, 0xff9067e2, 0xff8ce556, 0xff898bf0,
    0xff866306, 0xff8371d0, 0xff80bf63, 0xff7e4eba,
    0xff7c1eaa, 0xff7a2e04, 0xff787b47, 0xff770280,
    0xff75bd06, 0xff74a3f7, 0xff73b0b2, 0xff72dd02,
    0xff72237e, 0xff717ebe, 0xff70e94c, 0xff705f59,
    0xff6fde6a, 0xff6f6426, 0xff6eee40, 0xff6e7d0b,
    0xff6e1359, 0xff6db403, 0xff6d61f8, 0xff6d2054,
    0xff6cf267, 0xff6cdb76, 0xff6cdebb, 0xff6cff47,
    0xff6d3fc9, 0xff6da306, 0xff6e2b82, 0xff6eda13,
    0xff6fad6d, 0xff70a463, 0xff71bd9d, 0xff72f662,
    0xff744a80, 0xff75b5c4, 0xff773409, 0xff78c0a6,
    0xff7a5693, 0xff7bf0dc, 0xff7d8abb, 0xff7f2301,
    0xff80bc08, 0xff825854, 0xff83fa56, 0xff85a55c,
    0xff875d22, 0xff892598, 0xff8b025d, 0xff8cf53c,
    0xff8efdf4, 0xff911c48, 0xff934fc9, 0xff959675,
    0xff97ec86, 0xff9a4e35, 0xff9cb7d2, 0xff9f26cc,
    0xffa199ce, 0xffa40f74, 0xffa6867c, 0xffa8feb2,
    0xffab78e0, 0xffadf5c7, 0xffb07640, 0xffb2fba0,
    0xffb587a2, 0xffb81bfb, 0xffbaba46, 0xffbd6236,
    0xffc011a8, 0xffc2c679, 0xffc57e84, 0xffc83894,
    0xffcaf41a, 0xffcdb0b8, 0xffd06e17, 0xffd32bf7,
    0xffd5ea38, 0xffd8a8c3, 0xffdb6764, 0xffde25fb,
    0xffe0e471, 0xffe3a2b2, 0xffe66087, 0xffe91da6,
    0xffebd978, 0xffee9351, 0xfff14ab0, 0xfff3fef6,
    0xfff6af94, 0xfff95c0c, 0xfffc03c7, 0xfffea659,
    0x00015885, 0x0003f2e9, 0x00068a73, 0x00091e8d,
    0x000bae7f, 0x000e39bf, 0x0010bf96, 0x00133f78,
    0x0015b8c4, 0x00182ae4, 0x001a9558, 0x001cf7b2,
    0x001f51e0, 0x0021a3b4, 0x0023ed25, 0x00262df2,
    0x002865c5, 0x002a9469, 0x002cb967, 0x002ed4aa,
    0x0030e607, 0x0032ed88, 0x0034eb2f, 0x0036de23,
    0x0038c503, 0x003a9e4c, 0x003c68a6, 0x003e23dd,
    0x003fd0db, 0x00417083, 0x0043038b, 0x00448adf,
    0x00460740, 0x0047799c, 0x0048e2b2, 0x004a42af,
    0x004b98fb, 0x004ce50b, 0x004e2654, 0x004f5b5d,
    0x005081c3, 0x00519716, 0x00529920, 0x005386d0,
    0x0054603f, 0x00552581, 0x0055d6cc, 0x00567558,
    0x0057033c, 0x005782b4, 0x0057f5b6, 0x00585e46,
    0x0058be68, 0x005917ff, 0x00596ce4, 0x0059bcc0,
    0x005a053a, 0x005a43ee, 0x005a76ae, 0x005a9b37,
    0x005aaf38, 0x005ab07a, 0x005a9cef, 0x005a7349,
    0x005a3328, 0x0059dc0a, 0x00596db0, 0x0058e8e5,
    0x00584f98, 0x0057a3c0, 0x0056e738, 0x00561bec,
    0x005543df, 0x0054610b, 0x0053753e, 0x0052824e,
    0x005189f6, 0x00508dec, 0x004f8fc0, 0x004e8fd0,
    0x004d8d26, 0x004c86d7, 0x004b7c0a, 0x004a6b33,
    0x00495239, 0x00482f0e, 0x0046ffc4, 0x0045c201,
    0x00447337, 0x004310cc, 0x00419871, 0x004008e4,
    0x003e6231, 0x003ca460, 0x003acf8a, 0x0038e57a,
    0x0036e981, 0x0034defa, 0x0032c94b, 0x0030acc6,
    0x002e8eb4, 0x002c7452, 0x002a62aa, 0x00285bbf,
    0x00265eda, 0x00246b24, 0x00227f9c, 0x002098e7,
    0x001eb13b, 0x001cc2ef, 0x001ac899, 0x0018be3d,
    0x0016a198, 0x00147065, 0x00122897, 0x000fcbc5,
    0x000d5f03, 0x000ae77a, 0x00086a52, 0x0005eb92,
    0x00036e4a, 0x0000f57e, 0xfffe8414, 0xfffc1a78,
    0xfff9b6bb, 0xfff756d9, 0xfff4f8d0, 0xfff29add,
    0xfff03b87, 0xffedd94c, 0xffeb7295, 0xffe9072b,
    0xffe6981a, 0xffe4265b, 0xffe1b30e, 0xffdf3f2b,
    0xffdccb9e, 0xffda5993, 0xffd7ea0c, 0xffd57d60,
    0xffd31302, 0xffd0aa27, 0xffce4243, 0xffcbdb40,
    0xffc97595, 0xffc711a2, 0xffc4af9d, 0xffc24fa6,
    0xffbff1de, 0xffbd9699, 0xffbb3e44, 0xffb8e8d5,
    0xffb695f4, 0xffb44522, 0xffb1f627, 0xffafa8f0,
    0xffad5d91, 0xffab140a, 0xffa8cc1c, 0xffa68590,
    0xffa44066, 0xffa1fca0, 0xff9fba30, 0xff9d7902,
    0xff9b3916, 0xff98fa6d, 0xff96bd06, 0xff9480b6,
    0xff924532, 0xff900a24, 0xff8dcf41, 0xff8b9433,
    0xff895884, 0xff871bd3, 0xff84dd8a, 0xff829d34,
    0xff805a43, 0xff7e142d, 0xff7bca71, 0xff797c83,
    0xff7729e3, 0xff74d204, 0xff727451, 0xff70101e,
    0xff6da493, 0xff6b30d1, 0xff68b3f4, 0xff662d31,
    0xff639bd1, 0xff60ff09, 0xff5e562c, 0xff5ba3e0,
    0xff58ee39, 0xff563c22, 0xff5394f3, 0xff50fd1e,
    0xff4e7599, 0xff4bff32, 0xff499ad4, 0xff47490a,
    0xff450a36, 0xff42deb7, 0xff40c6cf, 0xff3ec2be,
    0xff3cd299, 0xff3af681, 0xff392e6a, 0xff377a4a,
    0xff35d9f7, 0xff344d44, 0xff32d3e8, 0xff316d96,
    0xff3019d9, 0xff2ed83a, 0xff2da82f, 0xff2c88bf,
    0xff2b78b4, 0xff2a76cc, 0xff298184, 0xff289890,
    0xff27bc7d, 0xff26ee21, 0xff262e28, 0xff257cdc,
    0xff24d9f4, 0xff244524, 0xff23be15, 0xff234488,
    0xff22d852, 0xff227947, 0xff22273d, 0xff21e1d2,
    0xff21a871, 0xff217a79, 0xff215748, 0xff213eca,
    0xff21319e, 0xff21305c, 0xff213baf, 0xff2153c2,
    0xff21782b, 0xff21a892, 0xff21e477, 0xff222bda,
    0xff227f26, 0xff22debd, 0xff234b09, 0xff23c394,
    0xff24471d, 0xff24d42b, 0xff25695c, 0xff260538,
    0xff26a652, 0xff274b28, 0xff27f22d, 0xff2899d2,
    0xff295975, 0xff29f2ad, 0xff2a96d7, 0xff2b45f4,
    0xff2bffe3, 0xff2cc4ba, 0xff2d9458, 0xff2e6ede,
    0xff2f544c, 0xff3044b7, 0xff314034, 0xff3246fa,
    0xff33591e, 0xff3476e0, 0xff35a060, 0xff36d534,
    0xff38148f, 0xff395daf, 0xff3aafd4, 0xff3c0ac8,
    0xff3d6ed6, 0xff3edc54, 0xff405382, 0xff41d3f5,
    0xff435ccc, 0xff44ed0f, 0xff4683d3, 0xff482080,
    0xff49c297, 0xff4b69ab, 0xff4d1547, 0xff4ec4f5,
    0xff50781d, 0xff522e20, 0xff53e692, 0xff55a15d,
    0xff575f17, 0xff592022, 0xff5ae4de, 0xff5cacb4,
    0xff5e75e2, 0xff603ee5, 0xff62062f, 0xff63caab,
    0xff658b55, 0xff67476d, 0xff68fe11, 0xff6aaea0,
    0xff6c5899, 0xff6dfb86, 0xff6f96e7, 0xff712a65,
    0xff72b59f, 0xff74382b, 0xff75b1d3, 0xff772276,
    0xff788a20, 0xff79e8e5, 0xff7b3ef0, 0xff7c8c98,
    0xff7dd249, 0xff7f108c, 0xff804804, 0xff817d0e,
    0xff82b74a, 0xff83fde6, 0xff855762, 0xff86c622,
    0xff884904, 0xff89ded1, 0xff8b8646, 0xff8d3e4c,
    0xff8f05cc, 0xff90dbc6, 0xff92bf2a, 0xff94af04,
    0xff96aa26, 0xff98af9a, 0xff9abe48, 0xff9cd543,
    0xff9ef3c1, 0xffa118ea, 0xffa343fd, 0xffa57423,
    0xffa7a890, 0xffa9e084, 0xffac1b31, 0xffae5802,
    0xffb09680, 0xffb2d621, 0xffb51678, 0xffb75704,
    0xffb99726, 0xffbbd645, 0xffbe13d7, 0xffc04f26,
    0xffc2879a, 0xffc4bc72, 0xffc6ed24, 0xffc918e3,
    0xffcb3eb8, 0xffcd5dcc, 0xffcf7549, 0xffd184d8,
    0xffd38c8f, 0xffd58ca4, 0xffd7854d, 0xffd97694,
    0xffdb606e, 0xffdd42d1, 0xffdf1da8, 0xffe0f09b,
    0xffe2bb00, 0xffe47c41, 0xffe633c6, 0xffe7e150,
    0xffe98534, 0xffeb1fb4, 0xffecb10e, 0xffee3944,
    0xffefb7e9, 0xfff12cbe, 0xfff29762, 0xfff3f789,
    0xfff54cbe, 0xfff69695, 0xfff7d4b8, 0xfff90748,
    0xfffa2ee5, 0xfffb4c3c, 0xfffc6003, 0xfffd6af0,
    0xfffe6dda, 0xffff69b8, 0x00005f4b, 0x00014e7f,
    0x00023646, 0x000315b4, 0x0003ebd3, 0x0004b74a,
    0x00057677, 0x000627e2, 0x0006ca09, 0x00075ce1,
    0x0007e196, 0x00085955, 0x0008c556, 0x00092751,
    0x00098153, 0x0009d581, 0x000a25be, 0x000a732b,
    0x000abe1f, 0x000b06e4, 0x000b4db1, 0x000b91fa,
    0x000bd266, 0x000c0da0, 0x000c426e, 0x000c6ffb,
    0x000c95b0, 0x000cb2f7, 0x000cc76e, 0x000cd317,
    0x000cd647, 0x000cd17f, 0x000cc52b, 0x000cb1ea,
    0x000c98c0, 0x000c7a62, 0x000c57c7, 0x000c3187,
    0x000c0862, 0x000bdcd8, 0x000baf81, 0x000b80c7,
    0x000b50ec, 0x000b202f, 0x000aeec6, 0x000abcb2,
    0x000a89d2, 0x000a5605, 0x000a2116, 0x0009eafb,
    0x0009b37d, 0x00097a9d, 0x00094030, 0x00090440,
    0x0008c6b9, 0x000887ae, 0x0008470c, 0x00080512,
    0x0007c1f6, 0x00077df9, 0x0007395a, 0x0006f45b,
    0x0006af67, 0x00066abe, 0x000626b6, 0x0005e38f,
    0x0005a1a0, 0x0005611e, 0x00052234, 0x0004e502,
    0x0004a95d, 0x00046f46, 0x00043691, 0x0003ff33,
    0x0003c90d, 0x0003941f, 0x00036047, 0x00032d9c,
    0x0002fc1e, 0x0002cbed, 0x00029d1e, 0x00026fbc,
    0x000243f2, 0x000219d6, 0x0001f17d, 0x0001caf1,
    0x0001a63e, 0x00018363, 0x00016256, 0x00014316,
    0x0001258f, 0x000109cb, 0x0000efaa, 0x0000d720,
    0x0000c03a, 0x0000aacb, 0x000096de, 0x0000846a,
    0x0000736d, 0x000063d3, 0x000055a6, 0x000048d0,
    0x00003d47, 0x000032f6, 0x000029dc, 0x000021d9,
    0x00001ae3, 0x000014ee, 0x00000fdb, 0x00000ba9,
    0x00000839, 0x00000589, 0x00000370, 0x000001ee,
    0x000000d7, 0x00000036, 0xffffffe0, 0xffffffc0,
    0xffffffd5, 0xfffffff5, 0x0000000b, 0x0000000b,
    0x0000000b, 0x0000000b, 0xfffffff5, 0xffffffd5,
    0xffffffca, 0xffffffe0, 0x00000036, 0x000000d7,
    0x000001ce, 0x0000033b, 0x00000529, 0x000007ad,
    0x00000ac8, 0x00000e99, 0x00001316, 0x0000185e,
    0x00001e7e, 0x00002575, 0x00002d4c, 0x0000361b,
    0x00003fd6, 0x00004a93, 0x00005647, 0x00006312,
    0x000070de, 0x00007fad, 0x00008f87, 0x0000a064,
    0x0000b242, 0x0000c52d, 0x0000d919, 0x0000ee12,
    0x0001040c, 0x00011b13, 0x0001331b, 0x00014c30,
    0x0001663c, 0x0001814a, 0x00019d4f, 0x0001ba35,
    0x0001d7e7, 0x0001f645, 0x00021544, 0x000234c3,
    0x000254b9, 0x00027505, 0x000295a7, 0x0002b67e,
    0x0002d7a1, 0x0002f904, 0x00031ab2, 0x00033ca0,
    0x00035ee5, 0x0003818a, 0x0003a485, 0x0003c7e1,
    0x0003eb72, 0x00040f0e, 0x0004329f, 0x000455e6,
    0x000478c0, 0x00049aef, 0x0004bc52, 0x0004dca9,
    0x0004fbde, 0x000519c5, 0x00053635, 0x0005512d,
    0x00056aae, 0x000582a1, 0x00059927, 0x0005ae40,
    0x0005c1f6, 0x0005d455, 0x0005e572, 0x0005f56d,
    0x00060446, 0x0006121e, 0x00061f09, 0x00062b08,
    0x00063605, 0x00063feb, 0x00064899, 0x00064ff0,
    0x000655a5, 0x00065996, 0x00065b6f, 0x00065af8,
    0x000657e9, 0x000651d4, 0x00064884, 0x00063bae,
    0x00062b33, 0x00061706, 0x0005fefd, 0x0005e344,
    0x0005c404, 0x0005a195, 0x00057c41, 0x00055473,
    0x00052ac2, 0x0004ffc4, 0x0004d410, 0x0004a7e5,
    0x00047b4f, 0x00044e39, 0x00042096, 0x0003f208,
    0x0003c1e1, 0x00038f77, 0x00035a12, 0x00032127,
    0x0002e476, 0x0002a389, 0x00025e29, 0x0002146d,
    0x0001c700, 0x00017682, 0x000123a1, 0x0000cefd,
    0x000078f7, 0x0000221a, 0xffffcad1, 0xffff7332,
    0xffff1b1e, 0xfffec253, 0xfffe6891, 0xfffe0da2,
    0xfffdb15c, 0xfffd5393, 0xfffcf412, 0xfffc92e3,
    0xfffc3032, 0xfffbcc29, 0xfffb6714, 0xfffb0113,
    0xfffa9a5b, 0xfffa3337, 0xfff9cbd4, 0xfff96450,
    0xfff8fcac, 0xfff894dc, 0xfff82cd8, 0xfff7c4a8,
    0xfff75c6d, 0xfff6f45e, 0xfff68c84, 0xfff62500,
    0xfff5bde8, 0xfff5575a, 0xfff4f179, 0xfff48c64,
    0xfff42810, 0xfff3c488, 0xfff361d7, 0xfff30008,
    0xfff29f3a, 0xfff23f78, 0xfff1e0d8, 0xfff1835b,
    0xfff1272a, 0xfff0cc46, 0xfff072cf, 0xfff01ad0,
    0xffefc469, 0xffef6fa4, 0xffef1ca3, 0xffeecb7a,
    0xffee7c1f, 0xffee2eb2, 0xffede33d, 0xffed99c1,
    0xffed5249, 0xffed0cde, 0xffecc98d, 0xffec8849,
    0xffec4934, 0xffec0c38, 0xffebd175, 0xffeb98eb,
    0xffeb62a4, 0xffeb2ead, 0xffeafd19, 0xffeacdea,
    0xffeaa129, 0xffea76cc, 0xffea4ef4, 0xffea299f,
    0xffea06e5, 0xffe9e6ce, 0xffe9c97d, 0xffe9aebb,
    0xffe99651, 0xffe97fd6, 0xffe96ad3, 0xffe95711,
    0xffe9447d, 0xffe93315, 0xffe922ce, 0xffe913a0,
    0xffe90588, 0xffe8f887, 0xffe8ec93, 0xffe8e1c1,
    0xffe8d806, 0xffe8cf77, 0xffe8c816, 0xffe8c1eb,
    0xffe8bd03, 0xffe8b967, 0xffe8b72e, 0xffe8b64d,
    0xffe8b6d8, 0xffe8b8dc, 0xffe8bc6c, 0xffe8c18a,
    0xffe8c840, 0xffe8d0a4, 0xffe8daca, 0xffe8e69e,
    0xffe8f42a, 0xffe9035a, 0xffe9142b, 0xffe926a0,
    0xffe93ab7, 0xffe95066, 0xffe967b8, 0xffe980ad,
    0xffe99b3a, 0xffe9b754, 0xffe9d511, 0xffe9f45b,
    0xffea1532, 0xffea3797, 0xffea5b89, 0xffea8108,
    0xffeaa7ff, 0xffead079, 0xffeafa55, 0xffeb259e,
    0xffeb5254, 0xffeb8061, 0xffebafdc, 0xffebe0ae,
    0xffec12ce, 0xffec462f, 0xffec7add, 0xffecb0a3,
    0xffece774, 0xffed1f32, 0xffed57a7, 0xffed90b2,
    0xffedca48, 0xffee042a, 0xffee3e57, 0xffee788e,
};

const DECLARE_ALIGNED(32, float, ff_aac_eld_window_480)[1800] = {
     0.00101191,  0.00440397,  0.00718669,  0.01072130,
     0.01459757,  0.01875954,  0.02308987,  0.02751541,
     0.03198130,  0.03643738,  0.04085290,  0.04522835,
     0.04957620,  0.05390454,  0.05821503,  0.06251214,
     0.06680463,  0.07109582,  0.07538014,  0.07965207,
     0.08390857,  0.08815177,  0.09238785,  0.09662163,
     0.10085860,  0.10510892,  0.10938110,  0.11367819,
     0.11800355,  0.12236410,  0.12676834,  0.13122384,
     0.13573476,  0.14030106,  0.14492340,  0.14960315,
     0.15433828,  0.15912396,  0.16395663,  0.16883310,
     0.17374837,  0.17869679,  0.18367394,  0.18867661,
     0.19370368,  0.19875413,  0.20382641,  0.20892055,
     0.21403775,  0.21917761,  0.22433899,  0.22952250,
     0.23472991,  0.23996189,  0.24521859,  0.25049930,
     0.25580312,  0.26112942,  0.26647748,  0.27184703,
     0.27723785,  0.28264967,  0.28808086,  0.29352832,
     0.29898979,  0.30446379,  0.30994292,  0.31541664,
     0.32087942,  0.32632772,  0.33176291,  0.33718641,
     0.34259612,  0.34799346,  0.35338857,  0.35878843,
     0.36419504,  0.36960630,  0.37501567,  0.38042067,
     0.38582069,  0.39121276,  0.39659312,  0.40195993,
     0.40731155,  0.41264382,  0.41795277,  0.42323670,
     0.42849480,  0.43372753,  0.43893452,  0.44411398,
     0.44927117,  0.45441882,  0.45956191,  0.46470167,
     0.46983016,  0.47493636,  0.48001827,  0.48507480,
     0.49010240,  0.49509781,  0.50005986,  0.50499037,
     0.50989790,  0.51478708,  0.51965805,  0.52450975,
     0.52933955,  0.53414668,  0.53893113,  0.54369178,
     0.54842731,  0.55313757,  0.55782259,  0.56248253,
     0.56711762,  0.57172819,  0.57631468,  0.58087761,
     0.58719976,  0.59173064,  0.59623644,  0.60071719,
     0.60517294,  0.60960372,  0.61400958,  0.61839056,
     0.62274670,  0.62707805,  0.63138475,  0.63566700,
     0.63992500,  0.64415895,  0.64836893,  0.65255499,
     0.65671715,  0.66085548,  0.66497005,  0.66906094,
     0.67312824,  0.67717199,  0.68119219,  0.68518882,
     0.68916187,  0.69311129,  0.69703698,  0.70093884,
     0.70481679,  0.70867071,  0.71250047,  0.71630596,
     0.72008705,  0.72384360,  0.72757549,  0.73128256,
     0.73496463,  0.73862141,  0.74225263,  0.74585799,
     0.74943730,  0.75299039,  0.75651711,  0.76001729,
     0.76349062,  0.76693670,  0.77035516,  0.77374564,
     0.77710790,  0.78044169,  0.78374678,  0.78702291,
     0.79026979,  0.79348715,  0.79667471,  0.79983215,
     0.80295914,  0.80605536,  0.80912047,  0.81215417,
     0.81515616,  0.81812616,  0.82106389,  0.82396915,
     0.82684176,  0.82968154,  0.83248830,  0.83526186,
     0.83800204,  0.84070866,  0.84338156,  0.84602058,
     0.84862556,  0.85119636,  0.85373292,  0.85623523,
     0.85870326,  0.86113701,  0.86353649,  0.86590173,
     0.86823275,  0.87052968,  0.87279275,  0.87502220,
     0.87721829,  0.87938130,  0.88151157,  0.88360940,
     0.88567517,  0.88770954,  0.88971328,  0.89168716,
     0.89363199,  0.89554856,  0.89743771,  0.89930025,
     0.90113740,  0.90295086,  0.90474240,  0.90651380,
     0.90826684,  0.91000335,  0.91172515,  0.91343416,
     0.91513276,  0.91682357,  0.91850924,  0.92019170,
     0.92187129,  0.92354778,  0.92522116,  0.92688597,
     0.92852960,  0.93013861,  0.93169897,  0.93319114,
     0.93458502,  0.93587626,  0.93694276,  0.93825562,
     0.93882222,  0.93910780,  0.93944183,  0.93981497,
     0.94021434,  0.94062629,  0.94103714,  0.94144084,
     0.94184042,  0.94223966,  0.94264206,  0.94304859,
     0.94345831,  0.94387033,  0.94428390,  0.94469895,
     0.94511572,  0.94553441,  0.94595520,  0.94637816,
     0.94680335,  0.94723080,  0.94766054,  0.94809253,
     0.94852674,  0.94896314,  0.94940178,  0.94984276,
     0.95028618,  0.95073213,  0.95118056,  0.95163139,
     0.95208451,  0.95253992,  0.95299770,  0.95345799,
     0.95392092,  0.95438653,  0.95485472,  0.95532539,
     0.95579847,  0.95627397,  0.95675201,  0.95723273,
     0.95771618,  0.95820232,  0.95869103,  0.95918218,
     0.95967573,  0.96017172,  0.96067026,  0.96117144,
     0.96167526,  0.96218157,  0.96269026,  0.96320119,
     0.96371437,  0.96422988,  0.96474782,  0.96526824,
     0.96579106,  0.96631614,  0.96684334,  0.96737257,
     0.96790390,  0.96843740,  0.96897315,  0.96951112,
     0.97005119,  0.97059318,  0.97113697,  0.97168253,
     0.97222994,  0.97277928,  0.97333058,  0.97388375,
     0.97443863,  0.97499505,  0.97555292,  0.97611230,
     0.97667326,  0.97723589,  0.97780016,  0.97836591,
     0.97893300,  0.97950127,  0.98007071,  0.98064139,
     0.98121342,  0.98178684,  0.98236156,  0.98293743,
     0.98351428,  0.98409205,  0.98467078,  0.98525056,
     0.98583146,  0.98641348,  0.98699650,  0.98758037,
     0.98816497,  0.98875030,  0.98933647,  0.98992356,
     0.99051163,  0.99110062,  0.99169038,  0.99228079,
     0.99287177,  0.99346341,  0.99405581,  0.99464907,
     0.99524320,  0.99583812,  0.99643375,  0.99702997,
     0.99762671,  0.99822386,  0.99882134,  0.99941903,
     1.00058131,  1.00118006,  1.00177930,  1.00237893,
     1.00297887,  1.00357902,  1.00417927,  1.00477954,
     1.00537972,  1.00597973,  1.00657959,  1.00717940,
     1.00777926,  1.00837925,  1.00897929,  1.00957926,
     1.01017901,  1.01077847,  1.01137769,  1.01197678,
     1.01257582,  1.01317482,  1.01377365,  1.01437217,
     1.01497025,  1.01556786,  1.01616510,  1.01676205,
     1.01735876,  1.01795514,  1.01855103,  1.01914627,
     1.01974076,  1.02033455,  1.02092772,  1.02152037,
     1.02211247,  1.02270387,  1.02329439,  1.02388387,
     1.02447229,  1.02505972,  1.02564624,  1.02623190,
     1.02681660,  1.02740017,  1.02798242,  1.02856326,
     1.02914272,  1.02972087,  1.03029778,  1.03087344,
     1.03144768,  1.03202035,  1.03259127,  1.03316042,
     1.03372788,  1.03429373,  1.03485801,  1.03542064,
     1.03598146,  1.03654030,  1.03709708,  1.03765185,
     1.03820470,  1.03875571,  1.03930488,  1.03985206,
     1.04039712,  1.04093989,  1.04148037,  1.04201865,
     1.04255481,  1.04308893,  1.04362093,  1.04415068,
     1.04467803,  1.04520292,  1.04572542,  1.04624566,
     1.04676376,  1.04727974,  1.04779350,  1.04830493,
     1.04881391,  1.04932048,  1.04982477,  1.05032693,
     1.05082705,  1.05132510,  1.05182098,  1.05231457,
     1.05280584,  1.05329485,  1.05378171,  1.05426654,
     1.05474937,  1.05523018,  1.05570892,  1.05618554,
     1.05666005,  1.05713251,  1.05760297,  1.05807149,
     1.05853828,  1.05900355,  1.05946756,  1.05993024,
     1.06039075,  1.06084806,  1.06130111,  1.06175099,
     1.06220164,  1.06265732,  1.06312146,  1.06358726,
     1.06403924,  1.06446186,  1.06484048,  1.06516440,
     1.06527864,  1.06498077,  1.06470196,  1.06425743,
     1.06372091,  1.06311464,  1.06246622,  1.06179277,
     1.06110808,  1.06042455,  1.05974495,  1.05906206,
     1.05836706,  1.05765243,  1.05691470,  1.05615178,
     1.05536069,  1.05454152,  1.05370030,  1.05284445,
     1.05198094,  1.05111433,  1.05024634,  1.04937859,
     1.04851245,  1.04764614,  1.04677586,  1.04589855,
     1.04501046,  1.04410500,  1.04317417,  1.04221010,
     1.04120649,  1.04016012,  1.03906851,  1.03792894,
     1.03674090,  1.03550649,  1.03422800,  1.03290769,
     1.03154944,  1.03015834,  1.02873938,  1.02729712,
     1.02583470,  1.02435463,  1.02285952,  1.02135114,
     1.01982974,  1.01829520,  1.01674752,  1.01518534,
     1.01360559,  1.01200510,  1.01038076,  1.00872996,
     1.00705045,  1.00533999,  1.00359618,  1.00181613,
     0.99999673,  0.99813477,  0.99622793,  0.99427571,
     0.99227814,  0.99023501,  0.98815128,  0.98603857,
     0.98390898,  0.98177413,  0.97964151,  0.97751528,
     0.97539999,  0.97329751,  0.97119933,  0.96909179,
     0.96696152,  0.96479824,  0.96259840,  0.96036028,
     0.95808180,  0.95576295,  0.95340622,  0.95101436,
     0.94859030,  0.94614009,  0.94367232,  0.94119555,
     0.93871796,  0.93624630,  0.93378636,  0.93134465,
     0.92892076,  0.92649974,  0.92406255,  0.92159041,
     0.91907411,  0.91651711,  0.91392425,  0.91130056,
     0.90865471,  0.90599838,  0.90334350,  0.90069934,
     0.89806435,  0.89543132,  0.89279335,  0.89014496,
     0.88748403,  0.88480945,  0.88211997,  0.87941558,
     0.87669794,  0.87396891,  0.87123030,  0.86848394,
     0.86573164,  0.86297523,  0.86021649,  0.85745725,
     0.85474342,  0.85193656,  0.84911455,  0.84627969,
     0.84343424,  0.84058046,  0.83772057,  0.83485680,
     0.83199134,  0.82912621,  0.82626143,  0.82339529,
     0.82052619,  0.81765147,  0.81476433,  0.81185593,
     0.80891701,  0.80594452,  0.80294885,  0.79994431,
     0.79694485,  0.79396166,  0.79100220,  0.78807349,
     0.78518123,  0.78231422,  0.77944709,  0.77655407,
     0.77361369,  0.77062281,  0.76758806,  0.76451506,
     0.76141145,  0.75828860,  0.75515892,  0.75203479,
     0.74892561,  0.74583682,  0.74277342,  0.73974008,
     0.73673754,  0.73376310,  0.73081444,  0.72788616,
     0.72496070,  0.72201426,  0.71902283,  0.71596990,
     0.71285541,  0.70968427,  0.70646064,  0.70319589,
     0.69991077,  0.69662714,  0.69336592,  0.69013742,
     0.68694302,  0.68378420,  0.68066143,  0.67757157,
     0.67450951,  0.67147030,  0.66844879,  0.66543949,
     0.66243677,  0.65943505,  0.65642754,  0.65340591,
     0.65036160,  0.64728630,  0.64417440,  0.64102268,
     0.63782771,  0.63458757,  0.63130628,  0.62799109,
     0.62464879,  0.62128816,  0.61792203,  0.61456438,
     0.61122915,  0.60792802,  0.60466971,  0.60146257,
     0.59831460,  0.59522876,  0.59220375,  0.58923859,
     0.58632936,  0.58346064,  0.58061078,  0.57775874,
     0.57488246,  0.57195790,  0.56896078,  0.56586637,
     0.56266594,  0.55937186,  0.55599898,  0.55256299,
     0.54909184,  0.54562376,  0.54219742,  0.53884728,
     0.53559047,  0.53243453,  0.52938894,  0.52645052,
     0.52358958,  0.52076862,  0.51795080,  0.51510761,
     0.51222179,  0.50927733,  0.50625944,  0.50317073,
     0.50002767,  0.49685021,  0.49364116,  0.49048690,
     0.48726128,  0.48404889,  0.48090875,  0.47783482,
     0.47481564,  0.47184024,  0.46889391,  0.46595836,
     0.46301611,  0.46005089,  0.45705924,  0.45404822,
     0.45102447,  0.44799543,  0.44497138,  0.44196397,
     0.43898547,  0.43604105,  0.43312057,  0.43020942,
     0.42729337,  0.42436272,  0.42141388,  0.41844400,
     0.41545081,  0.41244014,  0.40942464,  0.40641716,
     0.40342874,  0.40046292,  0.39751923,  0.39459758,
     0.39169692,  0.38881435,  0.38594643,  0.38308980,
     0.38024146,  0.37739896,  0.37455986,  0.37172187,
     0.36888463,  0.36604937,  0.36321735,  0.36038967,
     0.35756668,  0.35474832,  0.35193455,  0.34912542,
     0.34632129,  0.34352258,  0.34072974,  0.33794323,
     0.33516354,  0.33239114,  0.32962648,  0.32686967,
     0.32412042,  0.32137919,  0.31864044,  0.31588373,
     0.31309909,  0.31028631,  0.30745528,  0.30462678,
     0.30180656,  0.29899424,  0.29619082,  0.29339717,
     0.29061333,  0.28783935,  0.28507563,  0.28232266,
     0.27958067,  0.27684984,  0.27413017,  0.27142157,
     0.26872396,  0.26603737,  0.26336211,  0.26069855,
     0.25804700,  0.25540830,  0.25278329,  0.25017211,
     0.24757451,  0.24498713,  0.24240740,  0.23983550,
     0.23727200,  0.23471866,  0.23217624,  0.22964458,
     0.22712346,  0.22461258,  0.22211202,  0.21962197,
     0.21714290,  0.21467522,  0.21221877,  0.20977323,
     0.20733693,  0.20490860,  0.20248823,  0.20007615,
     0.19767358,  0.19528091,  0.19289781,  0.19052347,
     0.18815661,  0.18579693,  0.18344441,  0.18110010,
     0.17876595,  0.17644344,  0.17413400,  0.17183905,
     0.16956003,  0.16729836,  0.16505547,  0.16283278,
     0.15990780,  0.15776021,  0.15563325,  0.15352557,
     0.15143584,  0.14936270,  0.14730481,  0.14526081,
     0.14322937,  0.14120918,  0.13919977,  0.13720138,
     0.13521422,  0.13323852,  0.13127445,  0.12932216,
     0.12738181,  0.12545358,  0.12353773,  0.12163457,
     0.11974436,  0.11786730,  0.11600347,  0.11415293,
     0.11231573,  0.11049201,  0.10868196,  0.10688578,
     0.10510362,  0.10333551,  0.10158143,  0.09984133,
     0.09811524,  0.09640327,  0.09470556,  0.09302228,
     0.09135347,  0.08969907,  0.08805903,  0.08643326,
     0.08482183,  0.08322486,  0.08164249,  0.08007481,
     0.07852179,  0.07698335,  0.07545938,  0.07394984,
     0.07245482,  0.07097444,  0.06950883,  0.06805800,
     0.06662187,  0.06520031,  0.06379324,  0.06240065,
     0.06102266,  0.05965936,  0.05831084,  0.05697701,
     0.05565775,  0.05435290,  0.05306239,  0.05178628,
     0.05052464,  0.04927758,  0.04804510,  0.04682709,
     0.04562344,  0.04443405,  0.04325893,  0.04209822,
     0.04095208,  0.03982059,  0.03870371,  0.03760131,
     0.03651325,  0.03543944,  0.03437987,  0.03333454,
     0.03230348,  0.03128653,  0.03028332,  0.02929346,
     0.02831658,  0.02735252,  0.02640127,  0.02546283,
     0.02453725,  0.02362471,  0.02272547,  0.02183980,
     0.02096810,  0.02011108,  0.01926957,  0.01844439,
     0.01763565,  0.01684248,  0.01606394,  0.01529909,
     0.01454726,  0.01380802,  0.01308092,  0.01236569,
     0.01166273,  0.01097281,  0.01029671,  0.00963479,
     0.00898646,  0.00835089,  0.00772725,  0.00711521,
     0.00651513,  0.00592741,  0.00535249,  0.00479089,
     0.00424328,  0.00371041,  0.00319271,  0.00268947,
     0.00219928,  0.00172084,  0.00125271,  0.00079311,
     0.00034023, -0.00010786, -0.00055144, -0.00098865,
    -0.00141741, -0.00183557, -0.00224010, -0.00262725,
    -0.00299314, -0.00333475, -0.00365250, -0.00394867,
    -0.00422533, -0.00448528, -0.00473278, -0.00497252,
    -0.00520916, -0.00544584, -0.00568360, -0.00592326,
    -0.00616547, -0.00640861, -0.00664914, -0.00688354,
    -0.00710845, -0.00732136, -0.00752022, -0.00770289,
    -0.00786789, -0.00801521, -0.00814526, -0.00825839,
    -0.00835563, -0.00843882, -0.00850996, -0.00857097,
    -0.00862360, -0.00866943, -0.00871004, -0.00874688,
    -0.00878091, -0.00881277, -0.00884320, -0.00887248,
    -0.00890002, -0.00892494, -0.00894641, -0.00896355,
    -0.00897541, -0.00898104, -0.00897948, -0.00896990,
    -0.00895149, -0.00892346, -0.00888519, -0.00883670,
    -0.00877839, -0.00871058, -0.00863388, -0.00854936,
    -0.00845826, -0.00836179, -0.00826124, -0.00815807,
    -0.00805372, -0.00794953, -0.00784572, -0.00774156,
    -0.00763634, -0.00752929, -0.00741941, -0.00730556,
    -0.00718664, -0.00706184, -0.00693107, -0.00679443,
    -0.00665200, -0.00650428, -0.00635230, -0.00619718,
    -0.00603995, -0.00588133, -0.00572169, -0.00556143,
    -0.00540085, -0.00523988, -0.00507828, -0.00491582,
    -0.00475220, -0.00458693, -0.00441953, -0.00424950,
    -0.00407681, -0.00390204, -0.00372581, -0.00354874,
    -0.00337115, -0.00319318, -0.00301494, -0.00283652,
    -0.00265797, -0.00247934, -0.00230066, -0.00212197,
    -0.00194331, -0.00176471, -0.00158620, -0.00140787,
    -0.00122989, -0.00105244, -0.00087567, -0.00069976,
    -0.00052487, -0.00035115, -0.00017875, -0.00000782,
     0.00000779,  0.00017701,  0.00034552,  0.00051313,
     0.00067966,  0.00084492,  0.00100873,  0.00117093,
     0.00133133,  0.00148978,  0.00164611,  0.00180023,
     0.00195211,  0.00210172,  0.00224898,  0.00239383,
     0.00253618,  0.00267593,  0.00281306,  0.00294756,
     0.00307942,  0.00320864,  0.00333502,  0.00345816,
     0.00357762,  0.00369297,  0.00380414,  0.00391140,
     0.00401499,  0.00411524,  0.00421242,  0.00430678,
     0.00439859,  0.00448799,  0.00457487,  0.00465908,
     0.00474045,  0.00481857,  0.00489277,  0.00496235,
     0.00502666,  0.00508546,  0.00513877,  0.00518662,
     0.00522904,  0.00526648,  0.00529956,  0.00532895,
     0.00535532,  0.00537929,  0.00540141,  0.00542228,
     0.00544196,  0.00545981,  0.00547515,  0.00548726,
     0.00549542,  0.00549899,  0.00549732,  0.00548986,
     0.00547633,  0.00545664,  0.00543067,  0.00539849,
     0.00536061,  0.00531757,  0.00526993,  0.00521822,
     0.00516300,  0.00510485,  0.00504432,  0.00498194,
     0.00491822,  0.00485364,  0.00478862,  0.00472309,
     0.00465675,  0.00458939,  0.00452067,  0.00445003,
     0.00437688,  0.00430063,  0.00422062,  0.00413609,
     0.00404632,  0.00395060,  0.00384863,  0.00374044,
     0.00362600,  0.00350540,  0.00337934,  0.00324885,
     0.00311486,  0.00297849,  0.00284122,  0.00270458,
     0.00257013,  0.00243867,  0.00231005,  0.00218399,
     0.00206023,  0.00193766,  0.00181460,  0.00168938,
     0.00156050,  0.00142701,  0.00128830,  0.00114365,
     0.00099297,  0.00083752,  0.00067884,  0.00051845,
     0.00035760,  0.00019720,  0.00003813, -0.00011885,
    -0.00027375, -0.00042718, -0.00057975, -0.00073204,
    -0.00088453, -0.00103767, -0.00119192, -0.00134747,
    -0.00150411, -0.00166151, -0.00181932, -0.00197723,
    -0.00213493, -0.00229210, -0.00244849, -0.00260415,
    -0.00275928, -0.00291410, -0.00306879, -0.00322332,
    -0.00337759, -0.00353145, -0.00368470, -0.00383722,
    -0.00398892, -0.00413972, -0.00428967, -0.00443889,
    -0.00458749, -0.00473571, -0.00488366, -0.00503137,
    -0.00517887, -0.00532610, -0.00547302, -0.00561965,
    -0.00576598, -0.00591199, -0.00605766, -0.00620300,
    -0.00634801, -0.00649273, -0.00663727, -0.00678170,
    -0.00692617, -0.00707084, -0.00721583, -0.00736129,
    -0.00750735, -0.00765415, -0.00780184, -0.00795059,
    -0.00810058, -0.00825195, -0.00840487, -0.00855950,
    -0.00871607, -0.00887480, -0.00903596, -0.00919978,
    -0.00936650, -0.00953635, -0.00970931, -0.00988421,
    -0.01005916, -0.01023208, -0.01040130, -0.01056627,
    -0.01072678, -0.01088259, -0.01103348, -0.01117933,
    -0.01132004, -0.01145552, -0.01158573, -0.01171065,
    -0.01183025, -0.01194454, -0.01205352, -0.01215722,
    -0.01225572, -0.01234911, -0.01243749, -0.01252102,
    -0.01259985, -0.01267419, -0.01274437, -0.01281078,
    -0.01287379, -0.01293350, -0.01298972, -0.01304224,
    -0.01309086, -0.01313556, -0.01317644, -0.01321357,
    -0.01324707, -0.01327697, -0.01330334, -0.01332622,
    -0.01334570, -0.01336194, -0.01337510, -0.01338538,
    -0.01339276, -0.01339708, -0.01339816, -0.01339584,
    -0.01339014, -0.01338116, -0.01336903, -0.01335382,
    -0.01333545, -0.01331381, -0.01328876, -0.01326033,
    -0.01322880, -0.01319457, -0.01315806, -0.01311968,
    -0.01307987, -0.01303906, -0.01299769, -0.01295623,
    -0.01308207, -0.01304153, -0.01299802, -0.01295155,
    -0.01290215, -0.01284980, -0.01279450, -0.01273625,
    -0.01267501, -0.01261077, -0.01254347, -0.01247306,
    -0.01239950, -0.01232277, -0.01224304, -0.01216055,
    -0.01207554, -0.01198813, -0.01189829, -0.01180590,
    -0.01171090, -0.01161335, -0.01151352, -0.01141167,
    -0.01130807, -0.01120289, -0.01109626, -0.01098830,
    -0.01087916, -0.01076898, -0.01065793, -0.01054618,
    -0.01043380, -0.01032068, -0.01020670, -0.01009171,
    -0.00997585, -0.00985959, -0.00974338, -0.00962765,
    -0.00951273, -0.00939888, -0.00928634, -0.00917534,
    -0.00906604, -0.00895860, -0.00885313, -0.00874977,
    -0.00864862, -0.00854979, -0.00845337, -0.00835939,
    -0.00826785, -0.00817872, -0.00809195, -0.00800745,
    -0.00792506, -0.00784469, -0.00776588, -0.00768695,
    -0.00760568, -0.00752004, -0.00742875, -0.00733186,
    -0.00722976, -0.00712279, -0.00701130, -0.00689559,
    -0.00677595, -0.00665269, -0.00652610, -0.00639649,
    -0.00626417, -0.00612943, -0.00599252, -0.00585368,
    -0.00571315, -0.00557115, -0.00542792, -0.00528367,
    -0.00513864, -0.00499301, -0.00484693, -0.00470054,
    -0.00455395, -0.00440733, -0.00426086, -0.00411471,
    -0.00396904, -0.00382404, -0.00367991, -0.00353684,
    -0.00339502, -0.00325472, -0.00311618, -0.00297967,
    -0.00284531, -0.00271307, -0.00258290, -0.00245475,
    -0.00232860, -0.00220447, -0.00208236, -0.00196233,
    -0.00184450, -0.00172906, -0.00161620, -0.00150603,
    -0.00139852, -0.00129358, -0.00119112, -0.00109115,
    -0.00099375, -0.00089902, -0.00080705, -0.00071796,
    -0.00063185, -0.00054886, -0.00046904, -0.00039231,
    -0.00031845, -0.00024728, -0.00017860, -0.00011216,
    -0.00004771,  0.00001500,  0.00007600,  0.00013501,
     0.00019176,  0.00024595,  0.00029720,  0.00034504,
     0.00038902,  0.00042881,  0.00046456,  0.00049662,
     0.00052534,  0.00055114,  0.00057459,  0.00059629,
     0.00061684,  0.00063660,  0.00065568,  0.00067417,
     0.00069213,  0.00070935,  0.00072545,  0.00074005,
     0.00075283,  0.00076356,  0.00077209,  0.00077828,
     0.00078205,  0.00078350,  0.00078275,  0.00077992,
     0.00077520,  0.00076884,  0.00076108,  0.00075218,
     0.00074232,  0.00073170,  0.00072048,  0.00070881,
     0.00069680,  0.00068450,  0.00067201,  0.00065934,
     0.00064647,  0.00063335,  0.00061994,  0.00060621,
     0.00059211,  0.00057763,  0.00056274,  0.00054743,
     0.00053169,  0.00051553,  0.00049897,  0.00048206,
     0.00046487,  0.00044748,  0.00042996,  0.00041241,
     0.00039492,  0.00037759,  0.00036049,  0.00034371,
     0.00032732,  0.00031137,  0.00029587,  0.00028079,
     0.00026612,  0.00025183,  0.00023789,  0.00022428,
     0.00021097,  0.00019797,  0.00018530,  0.00017297,
     0.00016100,  0.00014942,  0.00013827,  0.00012757,
     0.00011736,  0.00010764,  0.00009841,  0.00008969,
     0.00008145,  0.00007369,  0.00006641,  0.00005958,
     0.00005320,  0.00004725,  0.00004171,  0.00003659,
     0.00003186,  0.00002752,  0.00002357,  0.00001999,
     0.00001679,  0.00001392,  0.00001140,  0.00000918,
     0.00000726,  0.00000562,  0.00000424,  0.00000309,
     0.00000217,  0.00000143,  0.00000088,  0.00000048,
     0.00000020,  0.00000004, -0.00000004, -0.00000006,
    -0.00000004, -0.00000000,  0.00000002,  0.00000000,
     0.00000000,  0.00000002, -0.00000000, -0.00000004,
    -0.00000005, -0.00000004,  0.00000004,  0.00000019,
     0.00000045,  0.00000083,  0.00000134,  0.00000201,
     0.00000285,  0.00000387,  0.00000510,  0.00000654,
     0.00000821,  0.00001011,  0.00001227,  0.00001468,
     0.00001735,  0.00002030,  0.00002352,  0.00002702,
     0.00003080,  0.00003486,  0.00003918,  0.00004379,
     0.00004866,  0.00005382,  0.00005924,  0.00006495,
     0.00007093,  0.00007719,  0.00008373,  0.00009053,
     0.00009758,  0.00010488,  0.00011240,  0.00012010,
     0.00012796,  0.00013596,  0.00014406,  0.00015226,
     0.00016053,  0.00016886,  0.00017725,  0.00018571,
     0.00019424,  0.00020286,  0.00021156,  0.00022037,
     0.00022928,  0.00023825,  0.00024724,  0.00025621,
     0.00026509,  0.00027385,  0.00028241,  0.00029072,
     0.00029874,  0.00030643,  0.00031374,  0.00032065,
     0.00032715,  0.00033325,  0.00033895,  0.00034425,
     0.00034917,  0.00035374,  0.00035796,  0.00036187,
     0.00036549,  0.00036883,  0.00037194,  0.00037479,
     0.00037736,  0.00037963,  0.00038154,  0.00038306,
     0.00038411,  0.00038462,  0.00038453,  0.00038373,
     0.00038213,  0.00037965,  0.00037621,  0.00037179,
     0.00036636,  0.00035989,  0.00035244,  0.00034407,
     0.00033488,  0.00032497,  0.00031449,  0.00030361,
     0.00029252,  0.00028133,  0.00027003,  0.00025862,
     0.00024706,  0.00023524,  0.00022297,  0.00021004,
     0.00019626,  0.00018150,  0.00016566,  0.00014864,
     0.00013041,  0.00011112,  0.00009096,  0.00007014,
     0.00004884,  0.00002718,  0.00000530, -0.00001667,
    -0.00003871, -0.00006090, -0.00008331, -0.00010600,
    -0.00012902, -0.00015244, -0.00017631, -0.00020065,
    -0.00022541, -0.00025052, -0.00027594, -0.00030159,
    -0.00032740, -0.00035332, -0.00037928, -0.00040527,
    -0.00043131, -0.00045741, -0.00048357, -0.00050978,
    -0.00053599, -0.00056217, -0.00058827, -0.00061423,
    -0.00064002, -0.00066562, -0.00069100, -0.00071616,
    -0.00074110, -0.00076584, -0.00079036, -0.00081465,
    -0.00083869, -0.00086245, -0.00088590, -0.00090901,
    -0.00093176, -0.00095413, -0.00097608, -0.00099758,
    -0.00101862, -0.00103918, -0.00105924, -0.00107879,
    -0.00109783, -0.00111635, -0.00113434, -0.00115181,
    -0.00116873, -0.00118510, -0.00120091, -0.00121615,
    -0.00123082, -0.00124490, -0.00125838, -0.00127125,
    -0.00128350, -0.00129511, -0.00130610, -0.00131643,
    -0.00132610, -0.00133509, -0.00134334, -0.00135069,
    -0.00135711, -0.00136272, -0.00136768, -0.00137225,
    -0.00137649, -0.00138042, -0.00138404, -0.00138737,
    -0.00139041, -0.00139317, -0.00139565, -0.00139785,
    -0.00139976, -0.00140137, -0.00140267, -0.00140366,
    -0.00140432, -0.00140464, -0.00140461, -0.00140423,
    -0.00140347, -0.00140235, -0.00140084, -0.00139894,
    -0.00139664, -0.00139388, -0.00139065, -0.00138694,
    -0.00138278, -0.00137818, -0.00137317, -0.00136772,
    -0.00136185, -0.00135556, -0.00134884, -0.00134170,
    -0.00133415, -0.00132619, -0.00131784, -0.00130908,
    -0.00129991, -0.00129031, -0.00128031, -0.00126990,
    -0.00125912, -0.00124797, -0.00123645, -0.00122458,
    -0.00121233, -0.00119972, -0.00118676, -0.00117347,
    -0.00115988, -0.00114605, -0.00113200, -0.00111778,
    -0.00110343, -0.00108898, -0.00107448, -0.00105995,
};

const DECLARE_ALIGNED(32, int, ff_aac_eld_window_480_fixed)[1800] = {
    0x00109442, 0x00482797, 0x0075bf2a, 0x00afa864,
    0x00ef2aa5, 0x01335b36, 0x017a4df0, 0x01c2cffe,
    0x020bfb4c, 0x0254fd74, 0x029d557c, 0x02e50574,
    0x032c41a8, 0x03732c08, 0x03b9cb88, 0x040032e8,
    0x044686f0, 0x048cd578, 0x04d30738, 0x05190500,
    0x055ec210, 0x05a44750, 0x05e9aeb8, 0x062f0c80,
    0x067477a0, 0x06ba1ac0, 0x07001998, 0x074680e0,
    0x078d5ec0, 0x07d4d038, 0x081cf8f0, 0x0865f8b0,
    0x08afe0e0, 0x08fab150, 0x09466cd0, 0x09931910,
    0x09e0adb0, 0x0a2f1640, 0x0a7e43f0, 0x0ace2960,
    0x0b1eb180, 0x0b6fc4b0, 0x0bc15050, 0x0c134710,
    0x0c65a420, 0x0cb86340, 0x0d0b7df0, 0x0d5ef450,
    0x0db2cb60, 0x0e070180, 0x0e5b91f0, 0x0eb07f20,
    0x0f05d0a0, 0x0f5b8920, 0x0fb1a950, 0x10082e40,
    0x105f1400, 0x10b65820, 0x110df780, 0x1165f120,
    0x11be43e0, 0x1216eea0, 0x126feac0, 0x12c92b00,
    0x1322a620, 0x137c55c0, 0x13d61ae0, 0x142fc940,
    0x148949e0, 0x14e28da0, 0x153b9a80, 0x15947640,
    0x15ed1840, 0x16458660, 0x169deb20, 0x16f663c0,
    0x174ef8c0, 0x17a7a120, 0x180041c0, 0x1858d000,
    0x18b14940, 0x1909a140, 0x1961c820, 0x19b9b620,
    0x1a116480, 0x1a68c1a0, 0x1abfbd00, 0x1b164f60,
    0x1b6c7580, 0x1bc23120, 0x1c1780e0, 0x1c6c5d00,
    0x1cc0dbe0, 0x1d1532a0, 0x1d697660, 0x1dbdac20,
    0x1e11b280, 0x1e655b80, 0x1eb89e80, 0x1f0b7720,
    0x1f5dd680, 0x1fafaec0, 0x2000fb00, 0x2051c340,
    0x20a22ac0, 0x20f24580, 0x214213c0, 0x21919140,
    0x21e0b300, 0x222f7580, 0x227dd900, 0x22cbd880,
    0x23196ec0, 0x23669b00, 0x23b35d80, 0x23ffb6c0,
    0x244ba7c0, 0x249731c0, 0x24e25700, 0x252d1940,
    0x2594ae40, 0x25deea40, 0x2628bd00, 0x26722680,
    0x26bb2740, 0x2703bf40, 0x274beec0, 0x2793b600,
    0x27db1500, 0x28220c00, 0x28689b80, 0x28aec4c0,
    0x28f48800, 0x2939e680, 0x297ee080, 0x29c37600,
    0x2a07a740, 0x2a4b74c0, 0x2a8ede80, 0x2ad1e500,
    0x2b148880, 0x2b56c940, 0x2b98a740, 0x2bda2240,
    0x2c1b3a80, 0x2c5bef80, 0x2c9c4100, 0x2cdc2e80,
    0x2d1bb800, 0x2d5adc80, 0x2d999b80, 0x2dd7f500,
    0x2e15e800, 0x2e537400, 0x2e9098c0, 0x2ecd5540,
    0x2f09a900, 0x2f4592c0, 0x2f811140, 0x2fbc2340,
    0x2ff6c7c0, 0x3030fe80, 0x306ac6c0, 0x30a41f80,
    0x30dd07c0, 0x31157dc0, 0x314d7fc0, 0x31850c80,
    0x31bc22c0, 0x31f2c1c0, 0x3228e840, 0x325e9540,
    0x3293c7c0, 0x32c87e40, 0x32fcb800, 0x33307340,
    0x3363aec0, 0x33966940, 0x33c8a140, 0x33fa5580,
    0x342b84c0, 0x345c2dc0, 0x348c4f80, 0x34bbe900,
    0x34eaf9c0, 0x35198080, 0x35477d00, 0x3574ee40,
    0x35a1d340, 0x35ce2bc0, 0x35f9f6c0, 0x36253380,
    0x364fe180, 0x367a0040, 0x36a38f80, 0x36cc8ec0,
    0x36f4fe80, 0x371cde80, 0x37442e80, 0x376aef00,
    0x37912000, 0x37b6c200, 0x37dbd600, 0x38005d00,
    0x38245840, 0x3847c880, 0x386aaf80, 0x388d0e80,
    0x38aee700, 0x38d03bc0, 0x38f11000, 0x39116700,
    0x39314440, 0x3950ab00, 0x396f9e80, 0x398e22c0,
    0x39ac3c40, 0x39c9f280, 0x39e74cc0, 0x3a045280,
    0x3a210b40, 0x3a3d7ec0, 0x3a59b480, 0x3a75b480,
    0x3a918900, 0x3aad3cc0, 0x3ac8db00, 0x3ae46bc0,
    0x3afff080, 0x3b1b6840, 0x3b36d2c0, 0x3b521980,
    0x3b6d0780, 0x3b876400, 0x3ba0f4c0, 0x3bb96740,
    0x3bd03dc0, 0x3be56580, 0x3bf6dec0, 0x3c0c6140,
    0x3c15a9c0, 0x3c1a5780, 0x3c1fd0c0, 0x3c25edc0,
    0x3c2c78c0, 0x3c333880, 0x3c39f3c0, 0x3c409100,
    0x3c471d00, 0x3c4da780, 0x3c543f40, 0x3c5ae880,
    0x3c619f00, 0x3c685f00, 0x3c6f25c0, 0x3c75f280,
    0x3c7cc6c0, 0x3c83a2c0, 0x3c8a87c0, 0x3c9175c0,
    0x3c986d00, 0x3c9f6e00, 0x3ca67880, 0x3cad8c40,
    0x3cb4a980, 0x3cbbd000, 0x3cc2ffc0, 0x3cca3940,
    0x3cd17d40, 0x3cd8cb80, 0x3ce02480, 0x3ce78740,
    0x3ceef3c0, 0x3cf66a00, 0x3cfdea00, 0x3d0574c0,
    0x3d0d0a40, 0x3d14ab40, 0x3d1c5700, 0x3d240d00,
    0x3d2bcd40, 0x3d3397c0, 0x3d3b6cc0, 0x3d434d00,
    0x3d4b38c0, 0x3d532fc0, 0x3d5b3180, 0x3d633dc0,
    0x3d6b53c0, 0x3d737400, 0x3d7b9f00, 0x3d83d540,
    0x3d8c1680, 0x3d946200, 0x3d9cb780, 0x3da51680,
    0x3dad7f00, 0x3db5f140, 0x3dbe6dc0, 0x3dc6f480,
    0x3dcf8540, 0x3dd81fc0, 0x3de0c300, 0x3de96ec0,
    0x3df22340, 0x3dfae0c0, 0x3e03a800, 0x3e0c7840,
    0x3e155180, 0x3e1e32c0, 0x3e271bc0, 0x3e300c00,
    0x3e390400, 0x3e420400, 0x3e4b0c40, 0x3e541c80,
    0x3e5d33c0, 0x3e6651c0, 0x3e6f7580, 0x3e789fc0,
    0x3e81d080, 0x3e8b0880, 0x3e944700, 0x3e9d8c00,
    0x3ea6d680, 0x3eb02600, 0x3eb97a80, 0x3ec2d400,
    0x3ecc3340, 0x3ed59880, 0x3edf0300, 0x3ee87280,
    0x3ef1e600, 0x3efb5d40, 0x3f04d880, 0x3f0e5840,
    0x3f17dcc0, 0x3f216600, 0x3f2af340, 0x3f348440,
    0x3f3e1840, 0x3f47af40, 0x3f514a00, 0x3f5ae840,
    0x3f648b00, 0x3f6e3140, 0x3f77db00, 0x3f818740,
    0x3f8b3600, 0x3f94e780, 0x3f9e9c40, 0x3fa85480,
    0x3fb21080, 0x3fbbcfc0, 0x3fc59200, 0x3fcf56c0,
    0x3fd91dc0, 0x3fe2e640, 0x3fecb040, 0x3ff67b40,
    0x40098600, 0x40135580, 0x401d2700, 0x4026fa00,
    0x4030ce80, 0x403aa380, 0x40447900, 0x404e4f00,
    0x40582400, 0x4061f900, 0x406bcd00, 0x4075a080,
    0x407f7480, 0x40894900, 0x40931e00, 0x409cf280,
    0x40a6c600, 0x40b09800, 0x40ba6980, 0x40c43a80,
    0x40ce0b00, 0x40d7db00, 0x40e1ab00, 0x40eb7980,
    0x40f54600, 0x40ff1080, 0x4108d980, 0x4112a100,
    0x411c6800, 0x41262d80, 0x412ff080, 0x4139b180,
    0x41436e80, 0x414d2980, 0x4156e100, 0x41609700,
    0x416a4a80, 0x4173fb00, 0x417da800, 0x41875000,
    0x4190f400, 0x419a9400, 0x41a43000, 0x41adc880,
    0x41b75d00, 0x41c0ec80, 0x41ca7700, 0x41d3fb00,
    0x41dd7980, 0x41e6f280, 0x41f06600, 0x41f9d480,
    0x42033d00, 0x420c9f00, 0x4215f980, 0x421f4d00,
    0x42289900, 0x4231de80, 0x423b1d00, 0x42445500,
    0x424d8500, 0x4256ad00, 0x425fcc80, 0x4268e380,
    0x4271f200, 0x427af900, 0x4283f880, 0x428cef80,
    0x4295de00, 0x429ec280, 0x42a79d80, 0x42b06f00,
    0x42b93800, 0x42c1f800, 0x42caaf80, 0x42d35d80,
    0x42dc0100, 0x42e49b00, 0x42ed2a80, 0x42f5b080,
    0x42fe2d80, 0x4306a180, 0x430f0c80, 0x43176d80,
    0x431fc480, 0x43281100, 0x43305400, 0x43388e80,
    0x4340c000, 0x4348e900, 0x43510900, 0x43591f00,
    0x43612b80, 0x43692f00, 0x43712900, 0x43791a80,
    0x43810380, 0x4388e400, 0x4390bc00, 0x43988b00,
    0x43a05180, 0x43a80f00, 0x43afc480, 0x43b77180,
    0x43bf1780, 0x43c6b700, 0x43ce5100, 0x43d5e580,
    0x43dd7100, 0x43e4ef80, 0x43ec5b80, 0x43f3ba80,
    0x43fb1c80, 0x44029400, 0x440a2e80, 0x4411d080,
    0x44193800, 0x44202480, 0x44265880, 0x442ba780,
    0x442d8680, 0x4428a500, 0x44241380, 0x441ccb00,
    0x44140100, 0x440a1200, 0x43ff7280, 0x43f46980,
    0x43e93200, 0x43ddff00, 0x43d2dc80, 0x43c7ac00,
    0x43bc4900, 0x43b09400, 0x43a47d80, 0x4397fd80,
    0x438b0780, 0x437d9b80, 0x436fd380, 0x4361cd80,
    0x4353a800, 0x43457500, 0x43373c80, 0x43290500,
    0x431ad400, 0x430ca280, 0x42fe6000, 0x42f00080,
    0x42e17380, 0x42d29e00, 0x42c35d80, 0x42b39200,
    0x42a32080, 0x4291fc00, 0x42801900, 0x426d6d80,
    0x4259f680, 0x4245bd00, 0x4230ca80, 0x421b2900,
    0x4204e800, 0x41ee1d00, 0x41d6dd80, 0x41bf3c80,
    0x41a74680, 0x418f0680, 0x41768800, 0x415dd100,
    0x4144e400, 0x412bbf80, 0x41126400, 0x40f8cc00,
    0x40deea00, 0x40c4b100, 0x40aa1400, 0x408f0800,
    0x40738380, 0x40577d80, 0x403aeb80, 0x401dc180,
    0x3ffff240, 0x3fe170c0, 0x3fc232c0, 0x3fa23680,
    0x3f817c40, 0x3f6002c0, 0x3f3ddec0, 0x3f1b4180,
    0x3ef85d40, 0x3ed56340, 0x3eb27240, 0x3e8f9c40,
    0x3e6cf400, 0x3e4a81c0, 0x3e282140, 0x3e059980,
    0x3de2b280, 0x3dbf4100, 0x3d9b3640, 0x3d768b00,
    0x3d513640, 0x3d2b3840, 0x3d049b80, 0x3cdd6b40,
    0x3cb5b400, 0x3c8d8f40, 0x3c652080, 0x3c3c8c40,
    0x3c13f480, 0x3beb7580, 0x3bc327c0, 0x3b9b2680,
    0x3b737000, 0x3b4bc580, 0x3b23d740, 0x3afb5640,
    0x3ad21c40, 0x3aa83780, 0x3a7dbc40, 0x3a52bf80,
    0x3a276600, 0x39fbe0c0, 0x39d06140, 0x39a50ec0,
    0x3979e300, 0x394ebf40, 0x392386c0, 0x38f82280,
    0x38cc89c0, 0x38a0b7c0, 0x3874a740, 0x38485840,
    0x381bd1c0, 0x37ef1b40, 0x37c23cc0, 0x37953dc0,
    0x376825c0, 0x373afc80, 0x370dc980, 0x36e09440,
    0x36b41dc0, 0x36862100, 0x3657e480, 0x36297240,
    0x35fad380, 0x35cc1200, 0x359d36c0, 0x356e4b40,
    0x353f5880, 0x35106780, 0x34e17780, 0x34b28240,
    0x34838040, 0x345466c0, 0x34251940, 0x33f57280,
    0x33c54bc0, 0x33949840, 0x33638380, 0x33324980,
    0x33012500, 0x32d04480, 0x329fc7c0, 0x326fcbc0,
    0x324068c0, 0x32116fc0, 0x31e27600, 0x31b30fc0,
    0x3182e300, 0x3151e240, 0x312029c0, 0x30edd080,
    0x30baf700, 0x3087cd00, 0x30548600, 0x30215680,
    0x2fee65c0, 0x2fbbca40, 0x2f899980, 0x2f57e6c0,
    0x2f26b540, 0x2ef5f980, 0x2ec5aa00, 0x2e95afc0,
    0x2e65c180, 0x2e357b40, 0x2e047840, 0x2dd27380,
    0x2d9f6c40, 0x2d6b7780, 0x2d36a6c0, 0x2d012940,
    0x2ccb5680, 0x2c958a00, 0x2c601b80, 0x2c2b3640,
    0x2bf6dfc0, 0x2bc31ec0, 0x2b8ff500, 0x2b5d5540,
    0x2b2b2a00, 0x2af95e80, 0x2ac7dd80, 0x2a968f80,
    0x2a655d40, 0x2a342f00, 0x2a02e8c0, 0x29d16700,
    0x299f8640, 0x296d2380, 0x293a2740, 0x29068400,
    0x28d22b40, 0x289d1540, 0x28675280, 0x28310180,
    0x27fa3f00, 0x27c32f80, 0x278c08c0, 0x275505c0,
    0x271e60c0, 0x26e84b00, 0x26b2e880, 0x267e5cc0,
    0x264ac940, 0x26183a40, 0x25e6aa80, 0x25b615c0,
    0x25866b80, 0x25576b40, 0x2528ba00, 0x24f9ffc0,
    0x24cadfc0, 0x249af540, 0x2469da80, 0x24372780,
    0x2402b800, 0x23ccbfc0, 0x23957cc0, 0x235d3140,
    0x23245200, 0x22eb8000, 0x22b35cc0, 0x227c7940,
    0x22471d40, 0x22136840, 0x21e18240, 0x21b15d80,
    0x21827dc0, 0x21544600, 0x21261b00, 0x20f78600,
    0x20c83e00, 0x20980000, 0x20668e00, 0x2033f300,
    0x20007400, 0x1fcc64e0, 0x1f97d120, 0x1f642320,
    0x1f2f49e0, 0x1efaa840, 0x1ec73580, 0x1e94d880,
    0x1e636120, 0x1e32a160, 0x1e025ba0, 0x1dd24300,
    0x1da20e60, 0x1d717940, 0x1d407560, 0x1d0f2040,
    0x1cdd95c0, 0x1cabf500, 0x1c7a6940, 0x1c492340,
    0x1c185680, 0x1be818c0, 0x1bb83f60, 0x1b888d20,
    0x1b58c640, 0x1b28c240, 0x1af871e0, 0x1ac7c960,
    0x1a96bf00, 0x1a656b60, 0x1a340360, 0x1a02bd20,
    0x19d1c6c0, 0x19a12f40, 0x1970f480, 0x19411640,
    0x19119000, 0x18e255a0, 0x18b358a0, 0x18848b20,
    0x1855e040, 0x18274e00, 0x17f8c9e0, 0x17ca4a80,
    0x179bce40, 0x176d5a60, 0x173ef400, 0x17109fe0,
    0x16e25f60, 0x16b43240, 0x16861880, 0x16581220,
    0x162a20c0, 0x15fc4620, 0x15ce8420, 0x15a0dca0,
    0x157351c0, 0x1545e580, 0x151899a0, 0x14eb6ec0,
    0x14be63a0, 0x14917a00, 0x14649ae0, 0x14377060,
    0x1409d0c0, 0x13dbbb20, 0x13ad58e0, 0x137f0160,
    0x1350cc80, 0x1322b8c0, 0x12f4ca60, 0x12c704e0,
    0x129968a0, 0x126bf5c0, 0x123eade0, 0x12119300,
    0x11e4a660, 0x11b7e860, 0x118b5940, 0x115ef8a0,
    0x1132c600, 0x1106c1a0, 0x10daecc0, 0x10af4900,
    0x1083d7a0, 0x10589c00, 0x102d9a00, 0x1002d1e0,
    0x0fd842c0, 0x0fadde80, 0x0f839a50, 0x0f597700,
    0x0f2f76e0, 0x0f05a170, 0x0edbf9c0, 0x0eb27f30,
    0x0e8930d0, 0x0e600d70, 0x0e371550, 0x0e0e4950,
    0x0de5ab50, 0x0dbd3d20, 0x0d94fe10, 0x0d6cecb0,
    0x0d450220, 0x0d1d38f0, 0x0cf59130, 0x0cce0c30,
    0x0ca6af10, 0x0c7f7b80, 0x0c587010, 0x0c318960,
    0x0c0ac200, 0x0be418d0, 0x0bbd8da0, 0x0b9724e0,
    0x0b70e6c0, 0x0b4ad970, 0x0b2502f0, 0x0aff6930,
    0x0ada1250, 0x0ab50430, 0x0a9044d0, 0x0a6bda30,
    0x0a3bedf0, 0x0a18be40, 0x09f5e530, 0x09d35cf0,
    0x09b11ff0, 0x098f2890, 0x096d7120, 0x094bf400,
    0x092aab80, 0x09099240, 0x08e8a620, 0x08c7e850,
    0x08a75990, 0x0886fae0, 0x0866ccf0, 0x0846d070,
    0x08270610, 0x08076e70, 0x07e80ac8, 0x07c8dc60,
    0x07a9e440, 0x078b2348, 0x076c99d0, 0x074e4818,
    0x07302e50, 0x07124d18, 0x06f4a530, 0x06d73778,
    0x06ba0488, 0x069d0c88, 0x06804f68, 0x0663cce0,
    0x06478528, 0x062b78a0, 0x060fa7e8, 0x05f413b8,
    0x05d8bc38, 0x05bda128, 0x05a2c258, 0x05881f60,
    0x056db888, 0x05538e60, 0x0539a170, 0x051ff218,
    0x05068040, 0x04ed4b90, 0x04d45398, 0x04bb9820,
    0x04a31988, 0x048ad860, 0x0472d528, 0x045b0ff0,
    0x04438860, 0x042c3de8, 0x04153040, 0x03fe5f4c,
    0x03e7cb98, 0x03d17580, 0x03bb5d64, 0x03a582e8,
    0x038fe588, 0x037a8494, 0x03655fcc, 0x03507768,
    0x033bcbb4, 0x03275d28, 0x03132bc0, 0x02ff370c,
    0x02eb7e94, 0x02d801e8, 0x02c4c11c, 0x02b1bcbc,
    0x029ef578, 0x028c6ba8, 0x027a1f20, 0x02680f54,
    0x02563bac, 0x0244a3c8, 0x023347a0, 0x02222730,
    0x0211429c, 0x02009938, 0x01f02974, 0x01dff1ae,
    0x01cff058, 0x01c024c8, 0x01b08ef4, 0x01a12eda,
    0x019204b0, 0x01831138, 0x01745588, 0x0165d2c2,
    0x01578a96, 0x01497ffc, 0x013bb670, 0x012e3160,
    0x0120f146, 0x0113f27c, 0x0107310c, 0x00faa909,
    0x00ee57a1, 0x00e23b09, 0x00d6515b, 0x00ca9977,
    0x00bf1509, 0x00b3c74d, 0x00a8b388, 0x009ddb3d,
    0x00933bf2, 0x0088d22c, 0x007e9a70, 0x0074935a,
    0x006abe70, 0x00611d5c, 0x0057b1f8, 0x004e7e73,
    0x0045859b, 0x003cca96, 0x00344f32, 0x002c1074,
    0x00240873, 0x001c31ba, 0x0014863f, 0x000cfe8b,
    0x00059307, 0xfffe3b9a, 0xfff6f718, 0xffefcd4d,
    0xffe8c6f4, 0xffe1ed10, 0xffdb4c57, 0xffd4f484,
    0xffcef5dc, 0xffc95d0c, 0xffc4284e, 0xffbf4e14,
    0xffbac5ae, 0xffb68360, 0xffb27548, 0xffae87be,
    0xffaaa733, 0xffa6c67e, 0xffa2e141, 0xff9ef40c,
    0xff9afc25, 0xff970058, 0xff930f7c, 0xff8f3857,
    0xff8b8900, 0xff880bfe, 0xff84c9ea, 0xff81cbbd,
    0xff7f17ad, 0xff7cadc6, 0xff7a8c4e, 0xff78b1cd,
    0xff7719f3, 0xff75bd06, 0xff7492a4, 0xff7392bf,
    0xff72b600, 0xff71f5c6, 0xff714b72, 0xff70b0ed,
    0xff702232, 0xff6f9c90, 0xff6f1cee, 0xff6ea21f,
    0xff6e2e9c, 0xff6dc617, 0xff6d6c09, 0xff6d2425,
    0xff6cf267, 0xff6cdaca, 0xff6ce155, 0xff6d0983,
    0xff6d56bb, 0xff6dcc4c, 0xff6e6cd0, 0xff6f3832,
    0xff702cc4, 0xff71492e, 0xff728ae2, 0xff73ed63,
    0xff756b7c, 0xff77001c, 0xff78a5d9, 0xff7a5693,
    0xff7c0c40, 0xff7dc141, 0xff7f74aa, 0xff81298b,
    0xff82e2de, 0xff84a3de, 0xff8670bd, 0xff884e42,
    0xff8a410c, 0xff8c4c7f, 0xff8e70fc, 0xff90ae18,
    0xff93037e, 0xff956f12, 0xff97ec86, 0xff9a7724,
    0xff9d0a9d, 0xff9fa3ea, 0xffa2417e, 0xffa4e1ac,
    0xffa78332, 0xffaa265a, 0xffaccc26, 0xffaf758e,
    0xffb223d4, 0xffb4d906, 0xffb79726, 0xffba604e,
    0xffbd349e, 0xffc011a8, 0xffc2f4d2, 0xffc5db82,
    0xffc8c45f, 0xffcbaed5, 0xffce9a6d, 0xffd186c6,
    0xffd473aa, 0xffd760e5, 0xffda4e55, 0xffdd3bd0,
    0xffe0292b, 0xffe31645, 0xffe602ff, 0xffe8eef7,
    0xffebd978, 0xffeec1bf, 0xfff1a72c, 0xfff488fe,
    0xfff76689, 0xfffa3f2c, 0xfffd1245, 0xffffdf33,
    0x000020ac, 0x0002e66f, 0x0005a937, 0x00086839,
    0x000b22b3, 0x000dd7da, 0x001086ec, 0x00132f3c,
    0x0015d001, 0x00186897, 0x001af849, 0x001d7eb6,
    0x001ffbbe, 0x00226f41, 0x0024d8e8, 0x00273874,
    0x00298d82, 0x002bd7aa, 0x002e16d4, 0x00304af6,
    0x00327406, 0x00349203, 0x0036a416, 0x0038a893,
    0x003a9da0, 0x003c8170, 0x003e53b8, 0x0040159a,
    0x0041c816, 0x00436c92, 0x0045042c, 0x00468ff2,
    0x00481106, 0x004987fe, 0x004af466, 0x004c5599,
    0x004daae4, 0x004ef28c, 0x005029c4, 0x00514d9a,
    0x00525b57, 0x005351f7, 0x00543190, 0x0054fa43,
    0x0055ac2f, 0x00564938, 0x0056d3f7, 0x00574f3c,
    0x0057bdd7, 0x00582260, 0x00587f28, 0x0058d6b1,
    0x0059293c, 0x0059741a, 0x0059b472, 0x0059e73c,
    0x005a0976, 0x005a1870, 0x005a116e, 0x0059f224,
    0x0059b964, 0x005966ce, 0x0058f9e2, 0x005872e8,
    0x0057d407, 0x00571f82, 0x005657b0, 0x00557ecd,
    0x00549731, 0x0053a34b, 0x0052a56a, 0x00519fc6,
    0x00509482, 0x004f85a4, 0x004e74ee, 0x004d6214,
    0x004c4bd3, 0x004b314c, 0x004a1110, 0x0048e8c8,
    0x0047b5f7, 0x00467626, 0x00452690, 0x0043c405,
    0x00424b7f, 0x0040ba04, 0x003f0e53, 0x003d488b,
    0x003b688c, 0x00396eb6, 0x00375dfb, 0x00353aaa,
    0x003308ac, 0x0030ccb1, 0x002e8cf1, 0x002c4fd5,
    0x002a1be8, 0x0027f486, 0x0025d90d, 0x0023c852,
    0x0021c13b, 0x001fbf23, 0x001dbafc, 0x001badc6,
    0x00199136, 0x00176150, 0x00151b86, 0x0012bcd1,
    0x001044d1, 0x000db8d0, 0x000b1f43, 0x00087e89,
    0x0005dbe2, 0x00033b1e, 0x00009fee, 0xfffe0d82,
    0xfffb83cf, 0xfff90047, 0xfff6805a, 0xfff4019a,
    0xfff18203, 0xffeeffb2, 0xffec78ba, 0xffe9ec4d,
    0xffe75b4e, 0xffe4c71f, 0xffe23138, 0xffdf9ae6,
    0xffdd0574, 0xffda723c, 0xffd7e24a, 0xffd55567,
    0xffd2cabe, 0xffd04161, 0xffcdb890, 0xffcb306a,
    0xffc8a95c, 0xffc62406, 0xffc3a140, 0xffc12188,
    0xffbea542, 0xffbc2cc2, 0xffb9b7d2, 0xffb745f2,
    0xffb4d6ac, 0xffb268fe, 0xffaffc72, 0xffad90e8,
    0xffab263e, 0xffa8bcb8, 0xffa6547e, 0xffa3ed7b,
    0xffa187ba, 0xff9f2351, 0xff9cc055, 0xff9a5ebc,
    0xff97fe84, 0xff959f84, 0xff934146, 0xff90e37d,
    0xff8e858a, 0xff8c26c0, 0xff89c69e, 0xff876483,
    0xff84ffe4, 0xff82982b, 0xff802cb6, 0xff7dbccf,
    0xff7b47b4, 0xff78ccd0, 0xff764b6c, 0xff73c2db,
    0xff713227, 0xff6e9864, 0xff6bf470, 0xff694553,
    0xff668a0d, 0xff63c1a6, 0xff60ec34, 0xff5e0e9e,
    0xff5b30d3, 0xff585b8c, 0xff5595c9, 0xff52e1da,
    0xff5040a0, 0xff4db31c, 0xff4b3a3b, 0xff48d67e,
    0xff468850, 0xff445011, 0xff422ded, 0xff4021f9,
    0xff3e2c56, 0xff3c4cf8, 0xff3a83df, 0xff38d0ec,
    0xff3733c9, 0xff35ac14, 0xff343963, 0xff32db09,
    0xff319066, 0xff305898, 0xff2f323d, 0xff2e1bb2,
    0xff2d1369, 0xff2c18f8, 0xff2b2d2a, 0xff2a50e1,
    0xff2984f4, 0xff28c978, 0xff281e01, 0xff278245,
    0xff26f5c3, 0xff26785a, 0xff2609bf, 0xff25a9c8,
    0xff255814, 0xff2513f6, 0xff24dcc4, 0xff24b1a6,
    0xff2492b1, 0xff248093, 0xff247c0b, 0xff2485c6,
    0xff249daf, 0xff24c359, 0xff24f639, 0xff253605,
    0xff258312, 0xff25ddd5, 0xff2646e7, 0xff26be25,
    0xff274264, 0xff27d1f6, 0xff286b19, 0xff290c13,
    0xff29b30d, 0xff2a5e38, 0xff2b0bbd, 0xff2bb9a2,
    0xff29a9d2, 0xff2a53dc, 0xff2b0a5a, 0xff2bcd43,
    0xff2c9c76, 0xff2d7808, 0xff2e5ffa, 0xff2f544c,
    0xff305528, 0xff316299, 0xff327ce0, 0xff33a432,
    0xff34d8ba, 0xff361a8e, 0xff3768f8, 0xff38c2f5,
    0xff3a2784, 0xff3b9623, 0xff3d0ef4, 0xff3e9277,
    0xff4020ed, 0xff41ba14, 0xff435ccc, 0xff4507fd,
    0xff46ba84, 0xff4873ac, 0xff4a32ea, 0xff4bf7bb,
    0xff4dc17f, 0xff4f8fa0, 0xff516167, 0xff53361d,
    0xff550d79, 0xff56e7ee, 0xff58c5ff, 0xff5aa84d,
    0xff5c8e41, 0xff5e75e2, 0xff605d4d, 0xff6242b6,
    0xff6424b8, 0xff66023d, 0xff67da44, 0xff69abd6,
    0xff6b7646, 0xff6d38e8, 0xff6ef348, 0xff70a4ce,
    0xff724d0f, 0xff73eb95, 0xff757fff, 0xff770a2d,
    0xff788a20, 0xff79fff6, 0xff7b6be7, 0xff7cce52,
    0xff7e27e4, 0xff7f78fc, 0xff80c38a, 0xff820e98,
    0xff836378, 0xff84caaa, 0xff864990, 0xff87dff4,
    0xff898c30, 0xff8b4cda, 0xff8d207a, 0xff8f05cc,
    0xff90fb9b, 0xff930098, 0xff95138e, 0xff97332d,
    0xff995e2a, 0xff9b934e, 0xff9dd18c, 0xffa017e3,
    0xffa26550, 0xffa4b8e7, 0xffa711a8, 0xffa96eae,
    0xffabcefc, 0xffae31cc, 0xffb09680, 0xffb2fc82,
    0xffb5635a, 0xffb7ca52, 0xffba30a8, 0xffbc95a8,
    0xffbef8a4, 0xffc158d0, 0xffc3b557, 0xffc60d6b,
    0xffc86041, 0xffcaacb7, 0xffccf1cb, 0xffcf2e5c,
    0xffd161e8, 0xffd38c8f, 0xffd5ae88, 0xffd7c808,
    0xffd9d925, 0xffdbe1c8, 0xffdde1f3, 0xffdfd964,
    0xffe1c79b, 0xffe3abcc, 0xffe5852a, 0xffe75341,
    0xffe9162f, 0xffeace55, 0xffec7c15, 0xffee1f63,
    0xffefb7e9, 0xfff1453d, 0xfff2c6fd, 0xfff43ca8,
    0xfff5a5d4, 0xfff701ea, 0xfff850b4, 0xfff99288,
    0xfffac853, 0xfffbf2d5, 0xfffd12e6, 0xfffe2991,
    0xffff37e4, 0x00003eea, 0x00013ec4, 0x00023646,
    0x0003244d, 0x00040797, 0x0004de8c, 0x0005a734,
    0x00065fab, 0x0007068f, 0x00079c82, 0x000822fa,
    0x00089b70, 0x000907a6, 0x00096a01, 0x0009c506,
    0x000a1b37, 0x000a6e18, 0x000abe1f, 0x000b0bac,
    0x000b5701, 0x000b9f3b, 0x000be2c2, 0x000c1fff,
    0x000c5599, 0x000c829a, 0x000ca661, 0x000cc058,
    0x000cd028, 0x000cd63d, 0x000cd317, 0x000cc739,
    0x000cb36d, 0x000c98c0, 0x000c7833, 0x000c52df,
    0x000c2984, 0x000bfcf9, 0x000bcdea, 0x000b9cf7,
    0x000b6a97, 0x000b3700, 0x000b029d, 0x000acd79,
    0x000a977e, 0x000a6076, 0x000a2838, 0x0009eea1,
    0x0009b37d, 0x000976c2, 0x0009384e, 0x0008f816,
    0x0008b612, 0x0008724a, 0x00082cd5, 0x0007e5e8,
    0x00079dce, 0x000754de, 0x00070b62, 0x0006c1c6,
    0x0006786a, 0x00062fba, 0x0005e801, 0x0005a1a0,
    0x00055ce1, 0x000519fb, 0x0004d8f8, 0x000499b8,
    0x00045c30, 0x00042040, 0x0003e5c8, 0x0003acb3,
    0x000374df, 0x00033e59, 0x00030934, 0x0002d57d,
    0x0002a348, 0x000272b6, 0x000243f2, 0x00021711,
    0x0001ec3e, 0x0001c37a, 0x00019cc3, 0x00017830,
    0x000155a0, 0x00013514, 0x0001168b, 0x0000f9e6,
    0x0000df23, 0x0000c62e, 0x0000aef2, 0x00009978,
    0x000085a1, 0x0000736d, 0x000062dc, 0x000053d8,
    0x0000466c, 0x00003a62, 0x00002fd1, 0x00002681,
    0x00001e73, 0x00001792, 0x000011c9, 0x00000cf6,
    0x0000091a, 0x000005ff, 0x000003b1, 0x00000203,
    0x000000d7, 0x0000002b, 0xffffffd5, 0xffffffc0,
    0xffffffd5, 0x00000000, 0x00000015, 0x00000000,
    0x00000000, 0x00000015, 0x00000000, 0xffffffd5,
    0xffffffca, 0xffffffd5, 0x0000002b, 0x000000cc,
    0x000001e3, 0x0000037b, 0x0000059f, 0x0000086e,
    0x00000bf4, 0x0000103b, 0x00001564, 0x00001b6e,
    0x0000226f, 0x00002a68, 0x00003377, 0x00003d93,
    0x000048c5, 0x00005525, 0x000062a6, 0x00007155,
    0x0000812f, 0x00009237, 0x0000a455, 0x0000b7ab,
    0x0000cc18, 0x0000e1bd, 0x0000f878, 0x0001106c,
    0x00012981, 0x000143c2, 0x00015f30, 0x00017bb6,
    0x00019948, 0x0001b7e6, 0x0001d771, 0x0001f7bc,
    0x000218b4, 0x00023a42, 0x00025c3b, 0x00027ea0,
    0x0002a150, 0x0002c440, 0x0002e771, 0x00030aed,
    0x00032eb4, 0x000352db, 0x00037759, 0x00039c4c,
    0x0003c1ac, 0x0003e74b, 0x00040d00, 0x0004329f,
    0x000457de, 0x00047c9c, 0x0004a083, 0x0004c35e,
    0x0004e502, 0x00050543, 0x000523ec, 0x000540e7,
    0x00055c2b, 0x000575c0, 0x00058da9, 0x0005a3e4,
    0x0005b886, 0x0005cbb1, 0x0005dd65, 0x0005edcb,
    0x0005fcfa, 0x00060afc, 0x00061808, 0x000623fc,
    0x00062ec3, 0x00063849, 0x0006404b, 0x000646ac,
    0x00064b13, 0x00064d37, 0x00064cd6, 0x0006497b,
    0x000642c5, 0x0006385e, 0x000629f0, 0x00061766,
    0x000600a0, 0x0005e57d, 0x0005c63e, 0x0005a322,
    0x00057c97, 0x00055306, 0x00052711, 0x0004f96f,
    0x0004caeb, 0x00049bfc, 0x00046c96, 0x00043cbb,
    0x00040c3f, 0x0003daab, 0x0003a734, 0x000370f9,
    0x0003372d, 0x0002f944, 0x0002b6d4, 0x00026f71,
    0x000222fb, 0x0001d212, 0x00017d84, 0x00012630,
    0x0000ccda, 0x00007200, 0x0000163b, 0xffffba15,
    0xffff5da3, 0xffff0091, 0xfffea293, 0xfffe4367,
    0xfffde2da, 0xfffd809f, 0xfffd1c81, 0xfffcb66a,
    0xfffc4e90, 0xfffbe53e, 0xfffb7aa0, 0xfffb0f0a,
    0xfffaa2c9, 0xfffa3612, 0xfff9c92f, 0xfff95c2d,
    0xfff8eef4, 0xfff8817c, 0xfff813c3, 0xfff7a5d4,
    0xfff737e5, 0xfff6ca17, 0xfff65c9e, 0xfff5efbc,
    0xfff58390, 0xfff51830, 0xfff4adbc, 0xfff44435,
    0xfff3db9a, 0xfff373d6, 0xfff30cfd, 0xfff2a71c,
    0xfff24248, 0xfff1de9f, 0xfff17c44, 0xfff11b56,
    0xfff0bbea, 0xfff05e17, 0xfff00206, 0xffefa7d9,
    0xffef4f99, 0xffeef95d, 0xffeea53a, 0xffee533a,
    0xffee035e, 0xffedb5b0, 0xffed6a3c, 0xffed20f5,
    0xffecd9fe, 0xffec9555, 0xffec5305, 0xffec1319,
    0xffebd591, 0xffeb9a83, 0xffeb61f9, 0xffeb2bfe,
    0xffeaf89c, 0xffeac7ea, 0xffea99d2, 0xffea6e7e,
    0xffea45ef, 0xffea203a, 0xffe9fda0, 0xffe9decc,
    0xffe9c3de, 0xffe9ac56, 0xffe99789, 0xffe9845e,
    0xffe97295, 0xffe96219, 0xffe952ea, 0xffe944f3,
    0xffe93833, 0xffe92c9f, 0xffe92238, 0xffe918fe,
    0xffe910fb, 0xffe90a3a, 0xffe904c6, 0xffe900a0,
    0xffe8fddb, 0xffe8fc83, 0xffe8fca4, 0xffe8fe3c,
    0xffe9016c, 0xffe9061e, 0xffe90c74, 0xffe9146c,
    0xffe91e11, 0xffe929a5, 0xffe93731, 0xffe946c0,
    0xffe95833, 0xffe96b7e, 0xffe98082, 0xffe9975e,
    0xffe9affd, 0xffe9ca5e, 0xffe9e68e, 0xffea0481,
    0xffea242b, 0xffea458e, 0xffea6894, 0xffea8d52,
    0xffeab3c8, 0xffeadc0c, 0xffeb05fe, 0xffeb31a7,
    0xffeb5ede, 0xffeb8da2, 0xffebbdf4, 0xffebefbd,
    0xffec231f, 0xffec5802, 0xffec8e5e, 0xffecc61c,
    0xffecff1c, 0xffed391e, 0xffed740c, 0xffedafb1,
    0xffedebe1, 0xffee287d, 0xffee654e, 0xffeea23f,
};
