/*
 * This file is part of FFmpeg.
 *
 * This table was generated from the long and short names of AVCodecs
 * please see the respective codec sources for authorship
 *
 * FFmpeg is free software; you can redistribute it and/or
 * modify it under the terms of the GNU Lesser General Public
 * License as published by the Free Software Foundation; either
 * version 2.1 of the License, or (at your option) any later version.
 *
 * FFmpeg is distributed in the hope that it will be useful,
 * but WITHOUT ANY WARRANTY; without even the implied warranty of
 * MERCHANTABILITY or FITNESS FOR A PARTICULAR PURPOSE.  See the GNU
 * Lesser General Public License for more details.
 *
 * You should have received a copy of the GNU Lesser General Public
 * License along with FFmpeg; if not, write to the Free Software
 * Foundation, Inc., 51 Franklin Street, Fifth Floor, Boston, MA 02110-1301 USA
 */

#include <string.h>

#include "libavutil/common.h"
#include "libavutil/internal.h"
#include "avcodec.h"
#include "profiles.h"
#include "version.h"

#define MT(...) (const char *const[]){ __VA_ARGS__, NULL }

static const AVCodecDescriptor codec_descriptors[] = {
    /* video codecs */
    {
        .id        = AV_CODEC_ID_MPEG1VIDEO,
        .type      = AVMEDIA_TYPE_VIDEO,
        .name      = "mpeg1video",
        .long_name = NULL_IF_CONFIG_SMALL("MPEG-1 video"),
        .props     = AV_CODEC_PROP_LOSSY | AV_CODEC_PROP_REORDER,
    },
    {
        .id        = AV_CODEC_ID_MPEG2VIDEO,
        .type      = AVMEDIA_TYPE_VIDEO,
        .name      = "mpeg2video",
        .long_name = NULL_IF_CONFIG_SMALL("MPEG-2 video"),
        .props     = AV_CODEC_PROP_LOSSY | AV_CODEC_PROP_REORDER,
        .profiles  = NULL_IF_CONFIG_SMALL(ff_mpeg2_video_profiles),
    },
    {
        .id        = AV_CODEC_ID_H261,
        .type      = AVMEDIA_TYPE_VIDEO,
        .name      = "h261",
        .long_name = NULL_IF_CONFIG_SMALL("H.261"),
        .props     = AV_CODEC_PROP_LOSSY,
    },
    {
        .id        = AV_CODEC_ID_H263,
        .type      = AVMEDIA_TYPE_VIDEO,
        .name      = "h263",
        .long_name = NULL_IF_CONFIG_SMALL("H.263 / H.263-1996, H.263+ / H.263-1998 / H.263 version 2"),
        .props     = AV_CODEC_PROP_LOSSY | AV_CODEC_PROP_REORDER,
    },
    {
        .id        = AV_CODEC_ID_RV10,
        .type      = AVMEDIA_TYPE_VIDEO,
        .name      = "rv10",
        .long_name = NULL_IF_CONFIG_SMALL("RealVideo 1.0"),
        .props     = AV_CODEC_PROP_LOSSY,
    },
    {
        .id        = AV_CODEC_ID_RV20,
        .type      = AVMEDIA_TYPE_VIDEO,
        .name      = "rv20",
        .long_name = NULL_IF_CONFIG_SMALL("RealVideo 2.0"),
        .props     = AV_CODEC_PROP_LOSSY | AV_CODEC_PROP_REORDER,
    },
    {
        .id        = AV_CODEC_ID_MJPEG,
        .type      = AVMEDIA_TYPE_VIDEO,
        .name      = "mjpeg",
        .long_name = NULL_IF_CONFIG_SMALL("Motion JPEG"),
        .props     = AV_CODEC_PROP_INTRA_ONLY | AV_CODEC_PROP_LOSSY,
        .mime_types= MT("image/jpeg"),
    },
    {
        .id        = AV_CODEC_ID_MJPEGB,
        .type      = AVMEDIA_TYPE_VIDEO,
        .name      = "mjpegb",
        .long_name = NULL_IF_CONFIG_SMALL("Apple MJPEG-B"),
        .props     = AV_CODEC_PROP_INTRA_ONLY | AV_CODEC_PROP_LOSSY,
    },
    {
        .id        = AV_CODEC_ID_LJPEG,
        .type      = AVMEDIA_TYPE_VIDEO,
        .name      = "ljpeg",
        .long_name = NULL_IF_CONFIG_SMALL("Lossless JPEG"),
        .props     = AV_CODEC_PROP_INTRA_ONLY | AV_CODEC_PROP_LOSSLESS,
    },
    {
        .id        = AV_CODEC_ID_SP5X,
        .type      = AVMEDIA_TYPE_VIDEO,
        .name      = "sp5x",
        .long_name = NULL_IF_CONFIG_SMALL("Sunplus JPEG (SP5X)"),
        .props     = AV_CODEC_PROP_INTRA_ONLY | AV_CODEC_PROP_LOSSY,
    },
    {
        .id        = AV_CODEC_ID_JPEGLS,
        .type      = AVMEDIA_TYPE_VIDEO,
        .name      = "jpegls",
        .long_name = NULL_IF_CONFIG_SMALL("JPEG-LS"),
        .props     = AV_CODEC_PROP_INTRA_ONLY | AV_CODEC_PROP_LOSSY |
                     AV_CODEC_PROP_LOSSLESS,
    },
    {
        .id        = AV_CODEC_ID_MPEG4,
        .type      = AVMEDIA_TYPE_VIDEO,
        .name      = "mpeg4",
        .long_name = NULL_IF_CONFIG_SMALL("MPEG-4 part 2"),
        .props     = AV_CODEC_PROP_LOSSY | AV_CODEC_PROP_REORDER,
        .profiles  = NULL_IF_CONFIG_SMALL(ff_mpeg4_video_profiles),
    },
    {
        .id        = AV_CODEC_ID_RAWVIDEO,
        .type      = AVMEDIA_TYPE_VIDEO,
        .name      = "rawvideo",
        .long_name = NULL_IF_CONFIG_SMALL("raw video"),
        .props     = AV_CODEC_PROP_INTRA_ONLY | AV_CODEC_PROP_LOSSLESS,
    },
    {
        .id        = AV_CODEC_ID_MSMPEG4V1,
        .type      = AVMEDIA_TYPE_VIDEO,
        .name      = "msmpeg4v1",
        .long_name = NULL_IF_CONFIG_SMALL("MPEG-4 part 2 Microsoft variant version 1"),
        .props     = AV_CODEC_PROP_LOSSY,
    },
    {
        .id        = AV_CODEC_ID_MSMPEG4V2,
        .type      = AVMEDIA_TYPE_VIDEO,
        .name      = "msmpeg4v2",
        .long_name = NULL_IF_CONFIG_SMALL("MPEG-4 part 2 Microsoft variant version 2"),
        .props     = AV_CODEC_PROP_LOSSY,
    },
    {
        .id        = AV_CODEC_ID_MSMPEG4V3,
        .type      = AVMEDIA_TYPE_VIDEO,
        .name      = "msmpeg4v3",
        .long_name = NULL_IF_CONFIG_SMALL("MPEG-4 part 2 Microsoft variant version 3"),
        .props     = AV_CODEC_PROP_LOSSY,
    },
    {
        .id        = AV_CODEC_ID_WMV1,
        .type      = AVMEDIA_TYPE_VIDEO,
        .name      = "wmv1",
        .long_name = NULL_IF_CONFIG_SMALL("Windows Media Video 7"),
        .props     = AV_CODEC_PROP_LOSSY,
    },
    {
        .id        = AV_CODEC_ID_WMV2,
        .type      = AVMEDIA_TYPE_VIDEO,
        .name      = "wmv2",
        .long_name = NULL_IF_CONFIG_SMALL("Windows Media Video 8"),
        .props     = AV_CODEC_PROP_LOSSY,
    },
    {
        .id        = AV_CODEC_ID_H263P,
        .type      = AVMEDIA_TYPE_VIDEO,
        .name      = "h263p",
        .long_name = NULL_IF_CONFIG_SMALL("H.263+ / H.263-1998 / H.263 version 2"),
        .props     = AV_CODEC_PROP_LOSSY | AV_CODEC_PROP_REORDER,
    },
    {
        .id        = AV_CODEC_ID_H263I,
        .type      = AVMEDIA_TYPE_VIDEO,
        .name      = "h263i",
        .long_name = NULL_IF_CONFIG_SMALL("Intel H.263"),
        .props     = AV_CODEC_PROP_LOSSY | AV_CODEC_PROP_REORDER,
    },
    {
        .id        = AV_CODEC_ID_FLV1,
        .type      = AVMEDIA_TYPE_VIDEO,
        .name      = "flv1",
        .long_name = NULL_IF_CONFIG_SMALL("FLV / Sorenson Spark / Sorenson H.263 (Flash Video)"),
        .props     = AV_CODEC_PROP_LOSSY,
    },
    {
        .id        = AV_CODEC_ID_SVQ1,
        .type      = AVMEDIA_TYPE_VIDEO,
        .name      = "svq1",
        .long_name = NULL_IF_CONFIG_SMALL("Sorenson Vector Quantizer 1 / Sorenson Video 1 / SVQ1"),
        .props     = AV_CODEC_PROP_LOSSY,
    },
    {
        .id        = AV_CODEC_ID_SVQ3,
        .type      = AVMEDIA_TYPE_VIDEO,
        .name      = "svq3",
        .long_name = NULL_IF_CONFIG_SMALL("Sorenson Vector Quantizer 3 / Sorenson Video 3 / SVQ3"),
        .props     = AV_CODEC_PROP_LOSSY | AV_CODEC_PROP_REORDER,
    },
    {
        .id        = AV_CODEC_ID_DVVIDEO,
        .type      = AVMEDIA_TYPE_VIDEO,
        .name      = "dvvideo",
        .long_name = NULL_IF_CONFIG_SMALL("DV (Digital Video)"),
        .props     = AV_CODEC_PROP_INTRA_ONLY | AV_CODEC_PROP_LOSSY,
    },
    {
        .id        = AV_CODEC_ID_HUFFYUV,
        .type      = AVMEDIA_TYPE_VIDEO,
        .name      = "huffyuv",
        .long_name = NULL_IF_CONFIG_SMALL("HuffYUV"),
        .props     = AV_CODEC_PROP_INTRA_ONLY | AV_CODEC_PROP_LOSSLESS,
    },
    {
        .id        = AV_CODEC_ID_CYUV,
        .type      = AVMEDIA_TYPE_VIDEO,
        .name      = "cyuv",
        .long_name = NULL_IF_CONFIG_SMALL("Creative YUV (CYUV)"),
        .props     = AV_CODEC_PROP_INTRA_ONLY | AV_CODEC_PROP_LOSSY,
    },
    {
        .id        = AV_CODEC_ID_H264,
        .type      = AVMEDIA_TYPE_VIDEO,
        .name      = "h264",
        .long_name = NULL_IF_CONFIG_SMALL("H.264 / AVC / MPEG-4 AVC / MPEG-4 part 10"),
        .props     = AV_CODEC_PROP_LOSSY | AV_CODEC_PROP_LOSSLESS | AV_CODEC_PROP_REORDER,
        .profiles  = NULL_IF_CONFIG_SMALL(ff_h264_profiles),
    },
    {
        .id        = AV_CODEC_ID_INDEO3,
        .type      = AVMEDIA_TYPE_VIDEO,
        .name      = "indeo3",
        .long_name = NULL_IF_CONFIG_SMALL("Intel Indeo 3"),
        .props     = AV_CODEC_PROP_LOSSY,
    },
    {
        .id        = AV_CODEC_ID_VP3,
        .type      = AVMEDIA_TYPE_VIDEO,
        .name      = "vp3",
        .long_name = NULL_IF_CONFIG_SMALL("On2 VP3"),
        .props     = AV_CODEC_PROP_LOSSY,
    },
    {
        .id        = AV_CODEC_ID_THEORA,
        .type      = AVMEDIA_TYPE_VIDEO,
        .name      = "theora",
        .long_name = NULL_IF_CONFIG_SMALL("Theora"),
        .props     = AV_CODEC_PROP_LOSSY,
    },
    {
        .id        = AV_CODEC_ID_ASV1,
        .type      = AVMEDIA_TYPE_VIDEO,
        .name      = "asv1",
        .long_name = NULL_IF_CONFIG_SMALL("ASUS V1"),
        .props     = AV_CODEC_PROP_INTRA_ONLY | AV_CODEC_PROP_LOSSY,
    },
    {
        .id        = AV_CODEC_ID_ASV2,
        .type      = AVMEDIA_TYPE_VIDEO,
        .name      = "asv2",
        .long_name = NULL_IF_CONFIG_SMALL("ASUS V2"),
        .props     = AV_CODEC_PROP_INTRA_ONLY | AV_CODEC_PROP_LOSSY,
    },
    {
        .id        = AV_CODEC_ID_FFV1,
        .type      = AVMEDIA_TYPE_VIDEO,
        .name      = "ffv1",
        .long_name = NULL_IF_CONFIG_SMALL("FFmpeg video codec #1"),
        .props     = AV_CODEC_PROP_LOSSLESS,
    },
    {
        .id        = AV_CODEC_ID_4XM,
        .type      = AVMEDIA_TYPE_VIDEO,
        .name      = "4xm",
        .long_name = NULL_IF_CONFIG_SMALL("4X Movie"),
        .props     = AV_CODEC_PROP_LOSSY,
    },
    {
        .id        = AV_CODEC_ID_VCR1,
        .type      = AVMEDIA_TYPE_VIDEO,
        .name      = "vcr1",
        .long_name = NULL_IF_CONFIG_SMALL("ATI VCR1"),
        .props     = AV_CODEC_PROP_INTRA_ONLY | AV_CODEC_PROP_LOSSY,
    },
    {
        .id        = AV_CODEC_ID_CLJR,
        .type      = AVMEDIA_TYPE_VIDEO,
        .name      = "cljr",
        .long_name = NULL_IF_CONFIG_SMALL("Cirrus Logic AccuPak"),
        .props     = AV_CODEC_PROP_INTRA_ONLY | AV_CODEC_PROP_LOSSY,
    },
    {
        .id        = AV_CODEC_ID_MDEC,
        .type      = AVMEDIA_TYPE_VIDEO,
        .name      = "mdec",
        .long_name = NULL_IF_CONFIG_SMALL("Sony PlayStation MDEC (Motion DECoder)"),
        .props     = AV_CODEC_PROP_INTRA_ONLY | AV_CODEC_PROP_LOSSY,
    },
    {
        .id        = AV_CODEC_ID_ROQ,
        .type      = AVMEDIA_TYPE_VIDEO,
        .name      = "roq",
        .long_name = NULL_IF_CONFIG_SMALL("id RoQ video"),
        .props     = AV_CODEC_PROP_LOSSY,
    },
    {
        .id        = AV_CODEC_ID_INTERPLAY_VIDEO,
        .type      = AVMEDIA_TYPE_VIDEO,
        .name      = "interplayvideo",
        .long_name = NULL_IF_CONFIG_SMALL("Interplay MVE video"),
        .props     = AV_CODEC_PROP_LOSSY,
    },
    {
        .id        = AV_CODEC_ID_XAN_WC3,
        .type      = AVMEDIA_TYPE_VIDEO,
        .name      = "xan_wc3",
        .long_name = NULL_IF_CONFIG_SMALL("Wing Commander III / Xan"),
        .props     = AV_CODEC_PROP_LOSSY,
    },
    {
        .id        = AV_CODEC_ID_XAN_WC4,
        .type      = AVMEDIA_TYPE_VIDEO,
        .name      = "xan_wc4",
        .long_name = NULL_IF_CONFIG_SMALL("Wing Commander IV / Xxan"),
        .props     = AV_CODEC_PROP_LOSSY,
    },
    {
        .id        = AV_CODEC_ID_RPZA,
        .type      = AVMEDIA_TYPE_VIDEO,
        .name      = "rpza",
        .long_name = NULL_IF_CONFIG_SMALL("QuickTime video (RPZA)"),
        .props     = AV_CODEC_PROP_LOSSY,
    },
    {
        .id        = AV_CODEC_ID_CINEPAK,
        .type      = AVMEDIA_TYPE_VIDEO,
        .name      = "cinepak",
        .long_name = NULL_IF_CONFIG_SMALL("Cinepak"),
        .props     = AV_CODEC_PROP_LOSSY,
    },
    {
        .id        = AV_CODEC_ID_WS_VQA,
        .type      = AVMEDIA_TYPE_VIDEO,
        .name      = "ws_vqa",
        .long_name = NULL_IF_CONFIG_SMALL("Westwood Studios VQA (Vector Quantized Animation) video"),
        .props     = AV_CODEC_PROP_LOSSY,
    },
    {
        .id        = AV_CODEC_ID_MSRLE,
        .type      = AVMEDIA_TYPE_VIDEO,
        .name      = "msrle",
        .long_name = NULL_IF_CONFIG_SMALL("Microsoft RLE"),
        .props     = AV_CODEC_PROP_LOSSLESS,
    },
    {
        .id        = AV_CODEC_ID_MSVIDEO1,
        .type      = AVMEDIA_TYPE_VIDEO,
        .name      = "msvideo1",
        .long_name = NULL_IF_CONFIG_SMALL("Microsoft Video 1"),
        .props     = AV_CODEC_PROP_LOSSY,
    },
    {
        .id        = AV_CODEC_ID_IDCIN,
        .type      = AVMEDIA_TYPE_VIDEO,
        .name      = "idcin",
        .long_name = NULL_IF_CONFIG_SMALL("id Quake II CIN video"),
        .props     = AV_CODEC_PROP_LOSSY,
    },
    {
        .id        = AV_CODEC_ID_8BPS,
        .type      = AVMEDIA_TYPE_VIDEO,
        .name      = "8bps",
        .long_name = NULL_IF_CONFIG_SMALL("QuickTime 8BPS video"),
        .props     = AV_CODEC_PROP_INTRA_ONLY | AV_CODEC_PROP_LOSSLESS,
    },
    {
        .id        = AV_CODEC_ID_SMC,
        .type      = AVMEDIA_TYPE_VIDEO,
        .name      = "smc",
        .long_name = NULL_IF_CONFIG_SMALL("QuickTime Graphics (SMC)"),
        .props     = AV_CODEC_PROP_LOSSY,
    },
    {
        .id        = AV_CODEC_ID_FLIC,
        .type      = AVMEDIA_TYPE_VIDEO,
        .name      = "flic",
        .long_name = NULL_IF_CONFIG_SMALL("Autodesk Animator Flic video"),
        .props     = AV_CODEC_PROP_LOSSLESS,
    },
    {
        .id        = AV_CODEC_ID_TRUEMOTION1,
        .type      = AVMEDIA_TYPE_VIDEO,
        .name      = "truemotion1",
        .long_name = NULL_IF_CONFIG_SMALL("Duck TrueMotion 1.0"),
        .props     = AV_CODEC_PROP_LOSSY,
    },
    {
        .id        = AV_CODEC_ID_VMDVIDEO,
        .type      = AVMEDIA_TYPE_VIDEO,
        .name      = "vmdvideo",
        .long_name = NULL_IF_CONFIG_SMALL("Sierra VMD video"),
        .props     = AV_CODEC_PROP_LOSSY,
    },
    {
        .id        = AV_CODEC_ID_MSZH,
        .type      = AVMEDIA_TYPE_VIDEO,
        .name      = "mszh",
        .long_name = NULL_IF_CONFIG_SMALL("LCL (LossLess Codec Library) MSZH"),
        .props     = AV_CODEC_PROP_INTRA_ONLY | AV_CODEC_PROP_LOSSLESS,
    },
    {
        .id        = AV_CODEC_ID_ZLIB,
        .type      = AVMEDIA_TYPE_VIDEO,
        .name      = "zlib",
        .long_name = NULL_IF_CONFIG_SMALL("LCL (LossLess Codec Library) ZLIB"),
        .props     = AV_CODEC_PROP_INTRA_ONLY | AV_CODEC_PROP_LOSSLESS,
    },
    {
        .id        = AV_CODEC_ID_QTRLE,
        .type      = AVMEDIA_TYPE_VIDEO,
        .name      = "qtrle",
        .long_name = NULL_IF_CONFIG_SMALL("QuickTime Animation (RLE) video"),
        .props     = AV_CODEC_PROP_LOSSLESS,
    },
    {
        .id        = AV_CODEC_ID_TSCC,
        .type      = AVMEDIA_TYPE_VIDEO,
        .name      = "tscc",
        .long_name = NULL_IF_CONFIG_SMALL("TechSmith Screen Capture Codec"),
        .props     = AV_CODEC_PROP_LOSSLESS,
    },
    {
        .id        = AV_CODEC_ID_ULTI,
        .type      = AVMEDIA_TYPE_VIDEO,
        .name      = "ulti",
        .long_name = NULL_IF_CONFIG_SMALL("IBM UltiMotion"),
        .props     = AV_CODEC_PROP_LOSSY,
    },
    {
        .id        = AV_CODEC_ID_QDRAW,
        .type      = AVMEDIA_TYPE_VIDEO,
        .name      = "qdraw",
        .long_name = NULL_IF_CONFIG_SMALL("Apple QuickDraw"),
        .props     = AV_CODEC_PROP_INTRA_ONLY | AV_CODEC_PROP_LOSSLESS,
    },
    {
        .id        = AV_CODEC_ID_VIXL,
        .type      = AVMEDIA_TYPE_VIDEO,
        .name      = "vixl",
        .long_name = NULL_IF_CONFIG_SMALL("Miro VideoXL"),
        .props     = AV_CODEC_PROP_INTRA_ONLY | AV_CODEC_PROP_LOSSY,
    },
    {
        .id        = AV_CODEC_ID_QPEG,
        .type      = AVMEDIA_TYPE_VIDEO,
        .name      = "qpeg",
        .long_name = NULL_IF_CONFIG_SMALL("Q-team QPEG"),
        .props     = AV_CODEC_PROP_LOSSY,
    },
    {
        .id        = AV_CODEC_ID_PNG,
        .type      = AVMEDIA_TYPE_VIDEO,
        .name      = "png",
        .long_name = NULL_IF_CONFIG_SMALL("PNG (Portable Network Graphics) image"),
        .props     = AV_CODEC_PROP_LOSSLESS,
        .mime_types= MT("image/png"),
    },
    {
        .id        = AV_CODEC_ID_PPM,
        .type      = AVMEDIA_TYPE_VIDEO,
        .name      = "ppm",
        .long_name = NULL_IF_CONFIG_SMALL("PPM (Portable PixelMap) image"),
        .props     = AV_CODEC_PROP_INTRA_ONLY | AV_CODEC_PROP_LOSSLESS,
    },
    {
        .id        = AV_CODEC_ID_PBM,
        .type      = AVMEDIA_TYPE_VIDEO,
        .name      = "pbm",
        .long_name = NULL_IF_CONFIG_SMALL("PBM (Portable BitMap) image"),
        .props     = AV_CODEC_PROP_INTRA_ONLY | AV_CODEC_PROP_LOSSLESS,
    },
    {
        .id        = AV_CODEC_ID_PGM,
        .type      = AVMEDIA_TYPE_VIDEO,
        .name      = "pgm",
        .long_name = NULL_IF_CONFIG_SMALL("PGM (Portable GrayMap) image"),
        .props     = AV_CODEC_PROP_INTRA_ONLY | AV_CODEC_PROP_LOSSLESS,
    },
    {
        .id        = AV_CODEC_ID_PGMYUV,
        .type      = AVMEDIA_TYPE_VIDEO,
        .name      = "pgmyuv",
        .long_name = NULL_IF_CONFIG_SMALL("PGMYUV (Portable GrayMap YUV) image"),
        .props     = AV_CODEC_PROP_INTRA_ONLY | AV_CODEC_PROP_LOSSLESS,
    },
    {
        .id        = AV_CODEC_ID_PAM,
        .type      = AVMEDIA_TYPE_VIDEO,
        .name      = "pam",
        .long_name = NULL_IF_CONFIG_SMALL("PAM (Portable AnyMap) image"),
        .props     = AV_CODEC_PROP_INTRA_ONLY | AV_CODEC_PROP_LOSSLESS,
        .mime_types= MT("image/x-portable-pixmap"),
    },
    {
        .id        = AV_CODEC_ID_FFVHUFF,
        .type      = AVMEDIA_TYPE_VIDEO,
        .name      = "ffvhuff",
        .long_name = NULL_IF_CONFIG_SMALL("Huffyuv FFmpeg variant"),
        .props     = AV_CODEC_PROP_INTRA_ONLY | AV_CODEC_PROP_LOSSLESS,
    },
    {
        .id        = AV_CODEC_ID_RV30,
        .type      = AVMEDIA_TYPE_VIDEO,
        .name      = "rv30",
        .long_name = NULL_IF_CONFIG_SMALL("RealVideo 3.0"),
        .props     = AV_CODEC_PROP_LOSSY | AV_CODEC_PROP_REORDER,
    },
    {
        .id        = AV_CODEC_ID_RV40,
        .type      = AVMEDIA_TYPE_VIDEO,
        .name      = "rv40",
        .long_name = NULL_IF_CONFIG_SMALL("RealVideo 4.0"),
        .props     = AV_CODEC_PROP_LOSSY | AV_CODEC_PROP_REORDER,
    },
    {
        .id        = AV_CODEC_ID_VC1,
        .type      = AVMEDIA_TYPE_VIDEO,
        .name      = "vc1",
        .long_name = NULL_IF_CONFIG_SMALL("SMPTE VC-1"),
        .props     = AV_CODEC_PROP_LOSSY | AV_CODEC_PROP_REORDER,
        .profiles  = NULL_IF_CONFIG_SMALL(ff_vc1_profiles),
    },
    {
        .id        = AV_CODEC_ID_WMV3,
        .type      = AVMEDIA_TYPE_VIDEO,
        .name      = "wmv3",
        .long_name = NULL_IF_CONFIG_SMALL("Windows Media Video 9"),
        .props     = AV_CODEC_PROP_LOSSY | AV_CODEC_PROP_REORDER,
        .profiles  = NULL_IF_CONFIG_SMALL(ff_vc1_profiles),
    },
    {
        .id        = AV_CODEC_ID_LOCO,
        .type      = AVMEDIA_TYPE_VIDEO,
        .name      = "loco",
        .long_name = NULL_IF_CONFIG_SMALL("LOCO"),
        .props     = AV_CODEC_PROP_INTRA_ONLY | AV_CODEC_PROP_LOSSLESS,
    },
    {
        .id        = AV_CODEC_ID_WNV1,
        .type      = AVMEDIA_TYPE_VIDEO,
        .name      = "wnv1",
        .long_name = NULL_IF_CONFIG_SMALL("Winnov WNV1"),
        .props     = AV_CODEC_PROP_INTRA_ONLY | AV_CODEC_PROP_LOSSY,
    },
    {
        .id        = AV_CODEC_ID_AASC,
        .type      = AVMEDIA_TYPE_VIDEO,
        .name      = "aasc",
        .long_name = NULL_IF_CONFIG_SMALL("Autodesk RLE"),
        .props     = AV_CODEC_PROP_LOSSLESS,
    },
    {
        .id        = AV_CODEC_ID_INDEO2,
        .type      = AVMEDIA_TYPE_VIDEO,
        .name      = "indeo2",
        .long_name = NULL_IF_CONFIG_SMALL("Intel Indeo 2"),
        .props     = AV_CODEC_PROP_LOSSY,
    },
    {
        .id        = AV_CODEC_ID_FRAPS,
        .type      = AVMEDIA_TYPE_VIDEO,
        .name      = "fraps",
        .long_name = NULL_IF_CONFIG_SMALL("Fraps"),
        .props     = AV_CODEC_PROP_INTRA_ONLY | AV_CODEC_PROP_LOSSLESS,
    },
    {
        .id        = AV_CODEC_ID_TRUEMOTION2,
        .type      = AVMEDIA_TYPE_VIDEO,
        .name      = "truemotion2",
        .long_name = NULL_IF_CONFIG_SMALL("Duck TrueMotion 2.0"),
        .props     = AV_CODEC_PROP_LOSSY,
    },
    {
        .id        = AV_CODEC_ID_BMP,
        .type      = AVMEDIA_TYPE_VIDEO,
        .name      = "bmp",
        .long_name = NULL_IF_CONFIG_SMALL("BMP (Windows and OS/2 bitmap)"),
        .props     = AV_CODEC_PROP_INTRA_ONLY | AV_CODEC_PROP_LOSSLESS,
        .mime_types= MT("image/x-ms-bmp"),
    },
    {
        .id        = AV_CODEC_ID_CSCD,
        .type      = AVMEDIA_TYPE_VIDEO,
        .name      = "cscd",
        .long_name = NULL_IF_CONFIG_SMALL("CamStudio"),
        .props     = AV_CODEC_PROP_LOSSLESS,
    },
    {
        .id        = AV_CODEC_ID_MMVIDEO,
        .type      = AVMEDIA_TYPE_VIDEO,
        .name      = "mmvideo",
        .long_name = NULL_IF_CONFIG_SMALL("American Laser Games MM Video"),
        .props     = AV_CODEC_PROP_LOSSY,
    },
    {
        .id        = AV_CODEC_ID_ZMBV,
        .type      = AVMEDIA_TYPE_VIDEO,
        .name      = "zmbv",
        .long_name = NULL_IF_CONFIG_SMALL("Zip Motion Blocks Video"),
        .props     = AV_CODEC_PROP_LOSSLESS,
    },
    {
        .id        = AV_CODEC_ID_AVS,
        .type      = AVMEDIA_TYPE_VIDEO,
        .name      = "avs",
        .long_name = NULL_IF_CONFIG_SMALL("AVS (Audio Video Standard) video"),
        .props     = AV_CODEC_PROP_LOSSY,
    },
    {
        .id        = AV_CODEC_ID_SMACKVIDEO,
        .type      = AVMEDIA_TYPE_VIDEO,
        .name      = "smackvideo",
        .long_name = NULL_IF_CONFIG_SMALL("Smacker video"),
        .props     = AV_CODEC_PROP_LOSSY,
    },
    {
        .id        = AV_CODEC_ID_NUV,
        .type      = AVMEDIA_TYPE_VIDEO,
        .name      = "nuv",
        .long_name = NULL_IF_CONFIG_SMALL("NuppelVideo/RTJPEG"),
        .props     = AV_CODEC_PROP_LOSSY,
    },
    {
        .id        = AV_CODEC_ID_KMVC,
        .type      = AVMEDIA_TYPE_VIDEO,
        .name      = "kmvc",
        .long_name = NULL_IF_CONFIG_SMALL("Karl Morton's video codec"),
        .props     = AV_CODEC_PROP_LOSSY,
    },
    {
        .id        = AV_CODEC_ID_FLASHSV,
        .type      = AVMEDIA_TYPE_VIDEO,
        .name      = "flashsv",
        .long_name = NULL_IF_CONFIG_SMALL("Flash Screen Video v1"),
        .props     = AV_CODEC_PROP_LOSSLESS,
    },
    {
        .id        = AV_CODEC_ID_CAVS,
        .type      = AVMEDIA_TYPE_VIDEO,
        .name      = "cavs",
        .long_name = NULL_IF_CONFIG_SMALL("Chinese AVS (Audio Video Standard) (AVS1-P2, JiZhun profile)"),
        .props     = AV_CODEC_PROP_LOSSY | AV_CODEC_PROP_REORDER,
    },
    {
        .id        = AV_CODEC_ID_JPEG2000,
        .type      = AVMEDIA_TYPE_VIDEO,
        .name      = "jpeg2000",
        .long_name = NULL_IF_CONFIG_SMALL("JPEG 2000"),
        .props     = AV_CODEC_PROP_INTRA_ONLY | AV_CODEC_PROP_LOSSY |
                     AV_CODEC_PROP_LOSSLESS,
        .mime_types= MT("image/jp2"),
        .profiles  = NULL_IF_CONFIG_SMALL(ff_jpeg2000_profiles),
    },
    {
        .id        = AV_CODEC_ID_VMNC,
        .type      = AVMEDIA_TYPE_VIDEO,
        .name      = "vmnc",
        .long_name = NULL_IF_CONFIG_SMALL("VMware Screen Codec / VMware Video"),
        .props     = AV_CODEC_PROP_LOSSLESS,
    },
    {
        .id        = AV_CODEC_ID_VP5,
        .type      = AVMEDIA_TYPE_VIDEO,
        .name      = "vp5",
        .long_name = NULL_IF_CONFIG_SMALL("On2 VP5"),
        .props     = AV_CODEC_PROP_LOSSY,
    },
    {
        .id        = AV_CODEC_ID_VP6,
        .type      = AVMEDIA_TYPE_VIDEO,
        .name      = "vp6",
        .long_name = NULL_IF_CONFIG_SMALL("On2 VP6"),
        .props     = AV_CODEC_PROP_LOSSY,
    },
    {
        .id        = AV_CODEC_ID_VP6F,
        .type      = AVMEDIA_TYPE_VIDEO,
        .name      = "vp6f",
        .long_name = NULL_IF_CONFIG_SMALL("On2 VP6 (Flash version)"),
        .props     = AV_CODEC_PROP_LOSSY,
    },
    {
        .id        = AV_CODEC_ID_TARGA,
        .type      = AVMEDIA_TYPE_VIDEO,
        .name      = "targa",
        .long_name = NULL_IF_CONFIG_SMALL("Truevision Targa image"),
        .props     = AV_CODEC_PROP_INTRA_ONLY | AV_CODEC_PROP_LOSSLESS,
        .mime_types= MT("image/x-targa", "image/x-tga"),
    },
    {
        .id        = AV_CODEC_ID_DSICINVIDEO,
        .type      = AVMEDIA_TYPE_VIDEO,
        .name      = "dsicinvideo",
        .long_name = NULL_IF_CONFIG_SMALL("Delphine Software International CIN video"),
        .props     = AV_CODEC_PROP_LOSSY,
    },
    {
        .id        = AV_CODEC_ID_TIERTEXSEQVIDEO,
        .type      = AVMEDIA_TYPE_VIDEO,
        .name      = "tiertexseqvideo",
        .long_name = NULL_IF_CONFIG_SMALL("Tiertex Limited SEQ video"),
        .props     = AV_CODEC_PROP_LOSSY,
    },
    {
        .id        = AV_CODEC_ID_TIFF,
        .type      = AVMEDIA_TYPE_VIDEO,
        .name      = "tiff",
        .long_name = NULL_IF_CONFIG_SMALL("TIFF image"),
        .props     = AV_CODEC_PROP_INTRA_ONLY | AV_CODEC_PROP_LOSSLESS,
        .mime_types= MT("image/tiff"),
    },
    {
        .id        = AV_CODEC_ID_GIF,
        .type      = AVMEDIA_TYPE_VIDEO,
        .name      = "gif",
        .long_name = NULL_IF_CONFIG_SMALL("GIF (Graphics Interchange Format)"),
        .props     = AV_CODEC_PROP_LOSSLESS,
        .mime_types= MT("image/gif"),
    },
    {
        .id        = AV_CODEC_ID_DXA,
        .type      = AVMEDIA_TYPE_VIDEO,
        .name      = "dxa",
        .long_name = NULL_IF_CONFIG_SMALL("Feeble Files/ScummVM DXA"),
        .props     = AV_CODEC_PROP_LOSSLESS,
    },
    {
        .id        = AV_CODEC_ID_DNXHD,
        .type      = AVMEDIA_TYPE_VIDEO,
        .name      = "dnxhd",
        .long_name = NULL_IF_CONFIG_SMALL("VC3/DNxHD"),
        .props     = AV_CODEC_PROP_INTRA_ONLY | AV_CODEC_PROP_LOSSY,
        .profiles  = NULL_IF_CONFIG_SMALL(ff_dnxhd_profiles),
    },
    {
        .id        = AV_CODEC_ID_THP,
        .type      = AVMEDIA_TYPE_VIDEO,
        .name      = "thp",
        .long_name = NULL_IF_CONFIG_SMALL("Nintendo Gamecube THP video"),
        .props     = AV_CODEC_PROP_INTRA_ONLY | AV_CODEC_PROP_LOSSY,
    },
    {
        .id        = AV_CODEC_ID_SGI,
        .type      = AVMEDIA_TYPE_VIDEO,
        .name      = "sgi",
        .long_name = NULL_IF_CONFIG_SMALL("SGI image"),
        .props     = AV_CODEC_PROP_INTRA_ONLY | AV_CODEC_PROP_LOSSLESS,
    },
    {
        .id        = AV_CODEC_ID_C93,
        .type      = AVMEDIA_TYPE_VIDEO,
        .name      = "c93",
        .long_name = NULL_IF_CONFIG_SMALL("Interplay C93"),
        .props     = AV_CODEC_PROP_LOSSY,
    },
    {
        .id        = AV_CODEC_ID_BETHSOFTVID,
        .type      = AVMEDIA_TYPE_VIDEO,
        .name      = "bethsoftvid",
        .long_name = NULL_IF_CONFIG_SMALL("Bethesda VID video"),
        .props     = AV_CODEC_PROP_LOSSY,
    },
    {
        .id        = AV_CODEC_ID_PTX,
        .type      = AVMEDIA_TYPE_VIDEO,
        .name      = "ptx",
        .long_name = NULL_IF_CONFIG_SMALL("V.Flash PTX image"),
        .props     = AV_CODEC_PROP_INTRA_ONLY | AV_CODEC_PROP_LOSSY,
    },
    {
        .id        = AV_CODEC_ID_TXD,
        .type      = AVMEDIA_TYPE_VIDEO,
        .name      = "txd",
        .long_name = NULL_IF_CONFIG_SMALL("Renderware TXD (TeXture Dictionary) image"),
        .props     = AV_CODEC_PROP_INTRA_ONLY | AV_CODEC_PROP_LOSSY,
    },
    {
        .id        = AV_CODEC_ID_VP6A,
        .type      = AVMEDIA_TYPE_VIDEO,
        .name      = "vp6a",
        .long_name = NULL_IF_CONFIG_SMALL("On2 VP6 (Flash version, with alpha channel)"),
        .props     = AV_CODEC_PROP_LOSSY,
    },
    {
        .id        = AV_CODEC_ID_AMV,
        .type      = AVMEDIA_TYPE_VIDEO,
        .name      = "amv",
        .long_name = NULL_IF_CONFIG_SMALL("AMV Video"),
        .props     = AV_CODEC_PROP_INTRA_ONLY | AV_CODEC_PROP_LOSSY,
    },
    {
        .id        = AV_CODEC_ID_VB,
        .type      = AVMEDIA_TYPE_VIDEO,
        .name      = "vb",
        .long_name = NULL_IF_CONFIG_SMALL("Beam Software VB"),
        .props     = AV_CODEC_PROP_LOSSY,
    },
    {
        .id        = AV_CODEC_ID_PCX,
        .type      = AVMEDIA_TYPE_VIDEO,
        .name      = "pcx",
        .long_name = NULL_IF_CONFIG_SMALL("PC Paintbrush PCX image"),
        .props     = AV_CODEC_PROP_INTRA_ONLY | AV_CODEC_PROP_LOSSLESS,
        .mime_types= MT("image/x-pcx"),
    },
    {
        .id        = AV_CODEC_ID_SUNRAST,
        .type      = AVMEDIA_TYPE_VIDEO,
        .name      = "sunrast",
        .long_name = NULL_IF_CONFIG_SMALL("Sun Rasterfile image"),
        .props     = AV_CODEC_PROP_INTRA_ONLY | AV_CODEC_PROP_LOSSLESS,
    },
    {
        .id        = AV_CODEC_ID_INDEO4,
        .type      = AVMEDIA_TYPE_VIDEO,
        .name      = "indeo4",
        .long_name = NULL_IF_CONFIG_SMALL("Intel Indeo Video Interactive 4"),
        .props     = AV_CODEC_PROP_LOSSY,
    },
    {
        .id        = AV_CODEC_ID_INDEO5,
        .type      = AVMEDIA_TYPE_VIDEO,
        .name      = "indeo5",
        .long_name = NULL_IF_CONFIG_SMALL("Intel Indeo Video Interactive 5"),
        .props     = AV_CODEC_PROP_LOSSY,
    },
    {
        .id        = AV_CODEC_ID_MIMIC,
        .type      = AVMEDIA_TYPE_VIDEO,
        .name      = "mimic",
        .long_name = NULL_IF_CONFIG_SMALL("Mimic"),
        .props     = AV_CODEC_PROP_LOSSY,
    },
    {
        .id        = AV_CODEC_ID_RL2,
        .type      = AVMEDIA_TYPE_VIDEO,
        .name      = "rl2",
        .long_name = NULL_IF_CONFIG_SMALL("RL2 video"),
        .props     = AV_CODEC_PROP_INTRA_ONLY | AV_CODEC_PROP_LOSSY,
    },
    {
        .id        = AV_CODEC_ID_ESCAPE124,
        .type      = AVMEDIA_TYPE_VIDEO,
        .name      = "escape124",
        .long_name = NULL_IF_CONFIG_SMALL("Escape 124"),
        .props     = AV_CODEC_PROP_LOSSY,
    },
    {
        .id        = AV_CODEC_ID_DIRAC,
        .type      = AVMEDIA_TYPE_VIDEO,
        .name      = "dirac",
        .long_name = NULL_IF_CONFIG_SMALL("Dirac"),
        .props     = AV_CODEC_PROP_LOSSY | AV_CODEC_PROP_LOSSLESS | AV_CODEC_PROP_REORDER,
    },
    {
        .id        = AV_CODEC_ID_BFI,
        .type      = AVMEDIA_TYPE_VIDEO,
        .name      = "bfi",
        .long_name = NULL_IF_CONFIG_SMALL("Brute Force & Ignorance"),
        .props     = AV_CODEC_PROP_LOSSY,
    },
    {
        .id        = AV_CODEC_ID_CMV,
        .type      = AVMEDIA_TYPE_VIDEO,
        .name      = "cmv",
        .long_name = NULL_IF_CONFIG_SMALL("Electronic Arts CMV video"),
        .props     = AV_CODEC_PROP_LOSSY,
    },
    {
        .id        = AV_CODEC_ID_MOTIONPIXELS,
        .type      = AVMEDIA_TYPE_VIDEO,
        .name      = "motionpixels",
        .long_name = NULL_IF_CONFIG_SMALL("Motion Pixels video"),
        .props     = AV_CODEC_PROP_LOSSY,
    },
    {
        .id        = AV_CODEC_ID_TGV,
        .type      = AVMEDIA_TYPE_VIDEO,
        .name      = "tgv",
        .long_name = NULL_IF_CONFIG_SMALL("Electronic Arts TGV video"),
        .props     = AV_CODEC_PROP_LOSSY,
    },
    {
        .id        = AV_CODEC_ID_TGQ,
        .type      = AVMEDIA_TYPE_VIDEO,
        .name      = "tgq",
        .long_name = NULL_IF_CONFIG_SMALL("Electronic Arts TGQ video"),
        .props     = AV_CODEC_PROP_LOSSY,
    },
    {
        .id        = AV_CODEC_ID_TQI,
        .type      = AVMEDIA_TYPE_VIDEO,
        .name      = "tqi",
        .long_name = NULL_IF_CONFIG_SMALL("Electronic Arts TQI video"),
        .props     = AV_CODEC_PROP_LOSSY,
    },
    {
        .id        = AV_CODEC_ID_AURA,
        .type      = AVMEDIA_TYPE_VIDEO,
        .name      = "aura",
        .long_name = NULL_IF_CONFIG_SMALL("Auravision AURA"),
        .props     = AV_CODEC_PROP_INTRA_ONLY | AV_CODEC_PROP_LOSSY,
    },
    {
        .id        = AV_CODEC_ID_AURA2,
        .type      = AVMEDIA_TYPE_VIDEO,
        .name      = "aura2",
        .long_name = NULL_IF_CONFIG_SMALL("Auravision Aura 2"),
        .props     = AV_CODEC_PROP_INTRA_ONLY | AV_CODEC_PROP_LOSSY,
    },
    {
        .id        = AV_CODEC_ID_V210X,
        .type      = AVMEDIA_TYPE_VIDEO,
        .name      = "v210x",
        .long_name = NULL_IF_CONFIG_SMALL("Uncompressed 4:2:2 10-bit"),
        .props     = AV_CODEC_PROP_INTRA_ONLY | AV_CODEC_PROP_LOSSLESS,
    },
    {
        .id        = AV_CODEC_ID_TMV,
        .type      = AVMEDIA_TYPE_VIDEO,
        .name      = "tmv",
        .long_name = NULL_IF_CONFIG_SMALL("8088flex TMV"),
        .props     = AV_CODEC_PROP_INTRA_ONLY | AV_CODEC_PROP_LOSSY,
    },
    {
        .id        = AV_CODEC_ID_V210,
        .type      = AVMEDIA_TYPE_VIDEO,
        .name      = "v210",
        .long_name = NULL_IF_CONFIG_SMALL("Uncompressed 4:2:2 10-bit"),
        .props     = AV_CODEC_PROP_INTRA_ONLY | AV_CODEC_PROP_LOSSLESS,
    },
    {
        .id        = AV_CODEC_ID_DPX,
        .type      = AVMEDIA_TYPE_VIDEO,
        .name      = "dpx",
        .long_name = NULL_IF_CONFIG_SMALL("DPX (Digital Picture Exchange) image"),
        .props     = AV_CODEC_PROP_INTRA_ONLY | AV_CODEC_PROP_LOSSLESS,
    },
    {
        .id        = AV_CODEC_ID_MAD,
        .type      = AVMEDIA_TYPE_VIDEO,
        .name      = "mad",
        .long_name = NULL_IF_CONFIG_SMALL("Electronic Arts Madcow Video"),
        .props     = AV_CODEC_PROP_LOSSY,
    },
    {
        .id        = AV_CODEC_ID_FRWU,
        .type      = AVMEDIA_TYPE_VIDEO,
        .name      = "frwu",
        .long_name = NULL_IF_CONFIG_SMALL("Forward Uncompressed"),
        .props     = AV_CODEC_PROP_INTRA_ONLY | AV_CODEC_PROP_LOSSLESS,
    },
    {
        .id        = AV_CODEC_ID_FLASHSV2,
        .type      = AVMEDIA_TYPE_VIDEO,
        .name      = "flashsv2",
        .long_name = NULL_IF_CONFIG_SMALL("Flash Screen Video v2"),
        .props     = AV_CODEC_PROP_LOSSY,
    },
    {
        .id        = AV_CODEC_ID_CDGRAPHICS,
        .type      = AVMEDIA_TYPE_VIDEO,
        .name      = "cdgraphics",
        .long_name = NULL_IF_CONFIG_SMALL("CD Graphics video"),
        .props     = AV_CODEC_PROP_LOSSY,
    },
    {
        .id        = AV_CODEC_ID_R210,
        .type      = AVMEDIA_TYPE_VIDEO,
        .name      = "r210",
        .long_name = NULL_IF_CONFIG_SMALL("Uncompressed RGB 10-bit"),
        .props     = AV_CODEC_PROP_INTRA_ONLY | AV_CODEC_PROP_LOSSLESS,
    },
    {
        .id        = AV_CODEC_ID_ANM,
        .type      = AVMEDIA_TYPE_VIDEO,
        .name      = "anm",
        .long_name = NULL_IF_CONFIG_SMALL("Deluxe Paint Animation"),
        .props     = AV_CODEC_PROP_LOSSY,
    },
    {
        .id        = AV_CODEC_ID_BINKVIDEO,
        .type      = AVMEDIA_TYPE_VIDEO,
        .name      = "binkvideo",
        .long_name = NULL_IF_CONFIG_SMALL("Bink video"),
        .props     = AV_CODEC_PROP_LOSSY,
    },
    {
        .id        = AV_CODEC_ID_IFF_ILBM,
        .type      = AVMEDIA_TYPE_VIDEO,
        .name      = "iff_ilbm",
        .long_name = NULL_IF_CONFIG_SMALL("IFF ACBM/ANIM/DEEP/ILBM/PBM/RGB8/RGBN"),
        .props     = AV_CODEC_PROP_LOSSY,
    },
    {
        .id        = AV_CODEC_ID_KGV1,
        .type      = AVMEDIA_TYPE_VIDEO,
        .name      = "kgv1",
        .long_name = NULL_IF_CONFIG_SMALL("Kega Game Video"),
        .props     = AV_CODEC_PROP_LOSSY,
    },
    {
        .id        = AV_CODEC_ID_YOP,
        .type      = AVMEDIA_TYPE_VIDEO,
        .name      = "yop",
        .long_name = NULL_IF_CONFIG_SMALL("Psygnosis YOP Video"),
        .props     = AV_CODEC_PROP_LOSSY,
    },
    {
        .id        = AV_CODEC_ID_VP8,
        .type      = AVMEDIA_TYPE_VIDEO,
        .name      = "vp8",
        .long_name = NULL_IF_CONFIG_SMALL("On2 VP8"),
        .props     = AV_CODEC_PROP_LOSSY,
    },
    {
        .id        = AV_CODEC_ID_PICTOR,
        .type      = AVMEDIA_TYPE_VIDEO,
        .name      = "pictor",
        .long_name = NULL_IF_CONFIG_SMALL("Pictor/PC Paint"),
        .props     = AV_CODEC_PROP_INTRA_ONLY | AV_CODEC_PROP_LOSSY,
    },
    {
        .id        = AV_CODEC_ID_ANSI,
        .type      = AVMEDIA_TYPE_VIDEO,
        .name      = "ansi",
        .long_name = NULL_IF_CONFIG_SMALL("ASCII/ANSI art"),
        .props     = AV_CODEC_PROP_LOSSY,
    },
    {
        .id        = AV_CODEC_ID_A64_MULTI,
        .type      = AVMEDIA_TYPE_VIDEO,
        .name      = "a64_multi",
        .long_name = NULL_IF_CONFIG_SMALL("Multicolor charset for Commodore 64"),
        .props     = AV_CODEC_PROP_INTRA_ONLY | AV_CODEC_PROP_LOSSY,
    },
    {
        .id        = AV_CODEC_ID_A64_MULTI5,
        .type      = AVMEDIA_TYPE_VIDEO,
        .name      = "a64_multi5",
        .long_name = NULL_IF_CONFIG_SMALL("Multicolor charset for Commodore 64, extended with 5th color (colram)"),
        .props     = AV_CODEC_PROP_INTRA_ONLY | AV_CODEC_PROP_LOSSY,
    },
    {
        .id        = AV_CODEC_ID_R10K,
        .type      = AVMEDIA_TYPE_VIDEO,
        .name      = "r10k",
        .long_name = NULL_IF_CONFIG_SMALL("AJA Kona 10-bit RGB Codec"),
        .props     = AV_CODEC_PROP_INTRA_ONLY | AV_CODEC_PROP_LOSSLESS,
    },
    {
        .id        = AV_CODEC_ID_MXPEG,
        .type      = AVMEDIA_TYPE_VIDEO,
        .name      = "mxpeg",
        .long_name = NULL_IF_CONFIG_SMALL("Mobotix MxPEG video"),
        .props     = AV_CODEC_PROP_LOSSY,
    },
    {
        .id        = AV_CODEC_ID_LAGARITH,
        .type      = AVMEDIA_TYPE_VIDEO,
        .name      = "lagarith",
        .long_name = NULL_IF_CONFIG_SMALL("Lagarith lossless"),
        .props     = AV_CODEC_PROP_INTRA_ONLY | AV_CODEC_PROP_LOSSLESS,
    },
    {
        .id        = AV_CODEC_ID_PRORES,
        .type      = AVMEDIA_TYPE_VIDEO,
        .name      = "prores",
        .long_name = NULL_IF_CONFIG_SMALL("Apple ProRes (iCodec Pro)"),
        .props     = AV_CODEC_PROP_INTRA_ONLY | AV_CODEC_PROP_LOSSY,
    },
    {
        .id        = AV_CODEC_ID_JV,
        .type      = AVMEDIA_TYPE_VIDEO,
        .name      = "jv",
        .long_name = NULL_IF_CONFIG_SMALL("Bitmap Brothers JV video"),
        .props     = AV_CODEC_PROP_INTRA_ONLY | AV_CODEC_PROP_LOSSY,
    },
    {
        .id        = AV_CODEC_ID_DFA,
        .type      = AVMEDIA_TYPE_VIDEO,
        .name      = "dfa",
        .long_name = NULL_IF_CONFIG_SMALL("Chronomaster DFA"),
        .props     = AV_CODEC_PROP_LOSSY,
    },
    {
        .id        = AV_CODEC_ID_WMV3IMAGE,
        .type      = AVMEDIA_TYPE_VIDEO,
        .name      = "wmv3image",
        .long_name = NULL_IF_CONFIG_SMALL("Windows Media Video 9 Image"),
        .props     = AV_CODEC_PROP_LOSSY,
    },
    {
        .id        = AV_CODEC_ID_VC1IMAGE,
        .type      = AVMEDIA_TYPE_VIDEO,
        .name      = "vc1image",
        .long_name = NULL_IF_CONFIG_SMALL("Windows Media Video 9 Image v2"),
        .props     = AV_CODEC_PROP_LOSSY,
    },
    {
        .id        = AV_CODEC_ID_UTVIDEO,
        .type      = AVMEDIA_TYPE_VIDEO,
        .name      = "utvideo",
        .long_name = NULL_IF_CONFIG_SMALL("Ut Video"),
        .props     = AV_CODEC_PROP_INTRA_ONLY | AV_CODEC_PROP_LOSSLESS,
    },
    {
        .id        = AV_CODEC_ID_BMV_VIDEO,
        .type      = AVMEDIA_TYPE_VIDEO,
        .name      = "bmv_video",
        .long_name = NULL_IF_CONFIG_SMALL("Discworld II BMV video"),
        .props     = AV_CODEC_PROP_LOSSLESS,
    },
    {
        .id        = AV_CODEC_ID_VBLE,
        .type      = AVMEDIA_TYPE_VIDEO,
        .name      = "vble",
        .long_name = NULL_IF_CONFIG_SMALL("VBLE Lossless Codec"),
        .props     = AV_CODEC_PROP_INTRA_ONLY | AV_CODEC_PROP_LOSSLESS,
    },
    {
        .id        = AV_CODEC_ID_DXTORY,
        .type      = AVMEDIA_TYPE_VIDEO,
        .name      = "dxtory",
        .long_name = NULL_IF_CONFIG_SMALL("Dxtory"),
        .props     = AV_CODEC_PROP_INTRA_ONLY | AV_CODEC_PROP_LOSSLESS,
    },
    {
        .id        = AV_CODEC_ID_V410,
        .type      = AVMEDIA_TYPE_VIDEO,
        .name      = "v410",
        .long_name = NULL_IF_CONFIG_SMALL("Uncompressed 4:4:4 10-bit"),
        .props     = AV_CODEC_PROP_INTRA_ONLY | AV_CODEC_PROP_LOSSLESS,
    },
    {
        .id        = AV_CODEC_ID_XWD,
        .type      = AVMEDIA_TYPE_VIDEO,
        .name      = "xwd",
        .long_name = NULL_IF_CONFIG_SMALL("XWD (X Window Dump) image"),
        .props     = AV_CODEC_PROP_INTRA_ONLY | AV_CODEC_PROP_LOSSLESS,
        .mime_types= MT("image/x-xwindowdump"),
    },
    {
        .id        = AV_CODEC_ID_CDXL,
        .type      = AVMEDIA_TYPE_VIDEO,
        .name      = "cdxl",
        .long_name = NULL_IF_CONFIG_SMALL("Commodore CDXL video"),
        .props     = AV_CODEC_PROP_INTRA_ONLY | AV_CODEC_PROP_LOSSY,
    },
    {
        .id        = AV_CODEC_ID_XBM,
        .type      = AVMEDIA_TYPE_VIDEO,
        .name      = "xbm",
        .long_name = NULL_IF_CONFIG_SMALL("XBM (X BitMap) image"),
        .props     = AV_CODEC_PROP_INTRA_ONLY | AV_CODEC_PROP_LOSSLESS,
        .mime_types= MT("image/x-xbitmap"),
    },
    {
        .id        = AV_CODEC_ID_ZEROCODEC,
        .type      = AVMEDIA_TYPE_VIDEO,
        .name      = "zerocodec",
        .long_name = NULL_IF_CONFIG_SMALL("ZeroCodec Lossless Video"),
        .props     = AV_CODEC_PROP_LOSSLESS,
    },
    {
        .id        = AV_CODEC_ID_MSS1,
        .type      = AVMEDIA_TYPE_VIDEO,
        .name      = "mss1",
        .long_name = NULL_IF_CONFIG_SMALL("MS Screen 1"),
        .props     = AV_CODEC_PROP_LOSSY,
    },
    {
        .id        = AV_CODEC_ID_MSA1,
        .type      = AVMEDIA_TYPE_VIDEO,
        .name      = "msa1",
        .long_name = NULL_IF_CONFIG_SMALL("MS ATC Screen"),
        .props     = AV_CODEC_PROP_LOSSY,
    },
    {
        .id        = AV_CODEC_ID_TSCC2,
        .type      = AVMEDIA_TYPE_VIDEO,
        .name      = "tscc2",
        .long_name = NULL_IF_CONFIG_SMALL("TechSmith Screen Codec 2"),
        .props     = AV_CODEC_PROP_LOSSY,
    },
    {
        .id        = AV_CODEC_ID_MTS2,
        .type      = AVMEDIA_TYPE_VIDEO,
        .name      = "mts2",
        .long_name = NULL_IF_CONFIG_SMALL("MS Expression Encoder Screen"),
        .props     = AV_CODEC_PROP_LOSSY,
    },
    {
        .id        = AV_CODEC_ID_CLLC,
        .type      = AVMEDIA_TYPE_VIDEO,
        .name      = "cllc",
        .long_name = NULL_IF_CONFIG_SMALL("Canopus Lossless Codec"),
        .props     = AV_CODEC_PROP_INTRA_ONLY | AV_CODEC_PROP_LOSSLESS,
    },
    {
        .id        = AV_CODEC_ID_MSS2,
        .type      = AVMEDIA_TYPE_VIDEO,
        .name      = "mss2",
        .long_name = NULL_IF_CONFIG_SMALL("MS Windows Media Video V9 Screen"),
        .props     = AV_CODEC_PROP_INTRA_ONLY | AV_CODEC_PROP_LOSSY,
    },
    {
        .id        = AV_CODEC_ID_VP9,
        .type      = AVMEDIA_TYPE_VIDEO,
        .name      = "vp9",
        .long_name = NULL_IF_CONFIG_SMALL("Google VP9"),
        .props     = AV_CODEC_PROP_LOSSY,
        .profiles  = NULL_IF_CONFIG_SMALL(ff_vp9_profiles),
    },
    {
        .id        = AV_CODEC_ID_AIC,
        .type      = AVMEDIA_TYPE_VIDEO,
        .name      = "aic",
        .long_name = NULL_IF_CONFIG_SMALL("Apple Intermediate Codec"),
        .props     = AV_CODEC_PROP_INTRA_ONLY | AV_CODEC_PROP_LOSSY,
    },
    {
        .id        = AV_CODEC_ID_ESCAPE130,
        .type      = AVMEDIA_TYPE_VIDEO,
        .name      = "escape130",
        .long_name = NULL_IF_CONFIG_SMALL("Escape 130"),
        .props     = AV_CODEC_PROP_LOSSY,
    },
    {
        .id        = AV_CODEC_ID_G2M,
        .type      = AVMEDIA_TYPE_VIDEO,
        .name      = "g2m",
        .long_name = NULL_IF_CONFIG_SMALL("Go2Meeting"),
        .props     = AV_CODEC_PROP_LOSSY,
    },
    {
        .id        = AV_CODEC_ID_WEBP,
        .type      = AVMEDIA_TYPE_VIDEO,
        .name      = "webp",
        .long_name = NULL_IF_CONFIG_SMALL("WebP"),
        .props     = AV_CODEC_PROP_INTRA_ONLY | AV_CODEC_PROP_LOSSY |
                     AV_CODEC_PROP_LOSSLESS,
        .mime_types= MT("image/webp"),
    },
    {
        .id        = AV_CODEC_ID_HNM4_VIDEO,
        .type      = AVMEDIA_TYPE_VIDEO,
        .name      = "hnm4video",
        .long_name = NULL_IF_CONFIG_SMALL("HNM 4 video"),
        .props     = AV_CODEC_PROP_LOSSY,
    },
    {
        .id        = AV_CODEC_ID_HEVC,
        .type      = AVMEDIA_TYPE_VIDEO,
        .name      = "hevc",
        .long_name = NULL_IF_CONFIG_SMALL("H.265 / HEVC (High Efficiency Video Coding)"),
        .props     = AV_CODEC_PROP_LOSSY | AV_CODEC_PROP_REORDER,
        .profiles  = NULL_IF_CONFIG_SMALL(ff_hevc_profiles),
    },
    {
        .id        = AV_CODEC_ID_FIC,
        .type      = AVMEDIA_TYPE_VIDEO,
        .name      = "fic",
        .long_name = NULL_IF_CONFIG_SMALL("Mirillis FIC"),
        .props     = AV_CODEC_PROP_LOSSY,
    },
    {
        .id        = AV_CODEC_ID_ALIAS_PIX,
        .type      = AVMEDIA_TYPE_VIDEO,
        .name      = "alias_pix",
        .long_name = NULL_IF_CONFIG_SMALL("Alias/Wavefront PIX image"),
        .props     = AV_CODEC_PROP_INTRA_ONLY | AV_CODEC_PROP_LOSSLESS,
    },
    {
        .id        = AV_CODEC_ID_BRENDER_PIX,
        .type      = AVMEDIA_TYPE_VIDEO,
        .name      = "brender_pix",
        .long_name = NULL_IF_CONFIG_SMALL("BRender PIX image"),
        .props     = AV_CODEC_PROP_INTRA_ONLY | AV_CODEC_PROP_LOSSLESS,
    },
    {
        .id        = AV_CODEC_ID_PAF_VIDEO,
        .type      = AVMEDIA_TYPE_VIDEO,
        .name      = "paf_video",
        .long_name = NULL_IF_CONFIG_SMALL("Amazing Studio Packed Animation File Video"),
        .props     = AV_CODEC_PROP_LOSSY,
    },
    {
        .id        = AV_CODEC_ID_EXR,
        .type      = AVMEDIA_TYPE_VIDEO,
        .name      = "exr",
        .long_name = NULL_IF_CONFIG_SMALL("OpenEXR image"),
        .props     = AV_CODEC_PROP_INTRA_ONLY | AV_CODEC_PROP_LOSSY |
                     AV_CODEC_PROP_LOSSLESS,
    },
    {
        .id        = AV_CODEC_ID_VP7,
        .type      = AVMEDIA_TYPE_VIDEO,
        .name      = "vp7",
        .long_name = NULL_IF_CONFIG_SMALL("On2 VP7"),
        .props     = AV_CODEC_PROP_LOSSY,
    },
    {
        .id        = AV_CODEC_ID_SANM,
        .type      = AVMEDIA_TYPE_VIDEO,
        .name      = "sanm",
        .long_name = NULL_IF_CONFIG_SMALL("LucasArts SANM/SMUSH video"),
        .props     = AV_CODEC_PROP_LOSSY,
    },
    {
        .id        = AV_CODEC_ID_SGIRLE,
        .type      = AVMEDIA_TYPE_VIDEO,
        .name      = "sgirle",
        .long_name = NULL_IF_CONFIG_SMALL("SGI RLE 8-bit"),
        .props     = AV_CODEC_PROP_INTRA_ONLY | AV_CODEC_PROP_LOSSLESS,
    },
    {
        .id        = AV_CODEC_ID_MVC1,
        .type      = AVMEDIA_TYPE_VIDEO,
        .name      = "mvc1",
        .long_name = NULL_IF_CONFIG_SMALL("Silicon Graphics Motion Video Compressor 1"),
        .props     = AV_CODEC_PROP_INTRA_ONLY | AV_CODEC_PROP_LOSSY,
    },
    {
        .id        = AV_CODEC_ID_MVC2,
        .type      = AVMEDIA_TYPE_VIDEO,
        .name      = "mvc2",
        .long_name = NULL_IF_CONFIG_SMALL("Silicon Graphics Motion Video Compressor 2"),
        .props     = AV_CODEC_PROP_INTRA_ONLY | AV_CODEC_PROP_LOSSY,
    },
    {
        .id        = AV_CODEC_ID_HQX,
        .type      = AVMEDIA_TYPE_VIDEO,
        .name      = "hqx",
        .long_name = NULL_IF_CONFIG_SMALL("Canopus HQX"),
        .props     = AV_CODEC_PROP_INTRA_ONLY | AV_CODEC_PROP_LOSSY,
    },
    {
        .id        = AV_CODEC_ID_TDSC,
        .type      = AVMEDIA_TYPE_VIDEO,
        .name      = "tdsc",
        .long_name = NULL_IF_CONFIG_SMALL("TDSC"),
        .props     = AV_CODEC_PROP_LOSSY,
    },
    {
        .id        = AV_CODEC_ID_HQ_HQA,
        .type      = AVMEDIA_TYPE_VIDEO,
        .name      = "hq_hqa",
        .long_name = NULL_IF_CONFIG_SMALL("Canopus HQ/HQA"),
        .props     = AV_CODEC_PROP_INTRA_ONLY | AV_CODEC_PROP_LOSSY,
    },
    {
        .id        = AV_CODEC_ID_HAP,
        .type      = AVMEDIA_TYPE_VIDEO,
        .name      = "hap",
        .long_name = NULL_IF_CONFIG_SMALL("Vidvox Hap"),
        .props     = AV_CODEC_PROP_INTRA_ONLY | AV_CODEC_PROP_LOSSY,
    },
    {
        .id        = AV_CODEC_ID_DDS,
        .type      = AVMEDIA_TYPE_VIDEO,
        .name      = "dds",
        .long_name = NULL_IF_CONFIG_SMALL("DirectDraw Surface image decoder"),
        .props     = AV_CODEC_PROP_INTRA_ONLY | AV_CODEC_PROP_LOSSY |
                     AV_CODEC_PROP_LOSSLESS,
    },
    {
        .id        = AV_CODEC_ID_DXV,
        .type      = AVMEDIA_TYPE_VIDEO,
        .name      = "dxv",
        .long_name = NULL_IF_CONFIG_SMALL("Resolume DXV"),
        .props     = AV_CODEC_PROP_INTRA_ONLY | AV_CODEC_PROP_LOSSY,
    },
    {
        .id        = AV_CODEC_ID_SCREENPRESSO,
        .type      = AVMEDIA_TYPE_VIDEO,
        .name      = "screenpresso",
        .long_name = NULL_IF_CONFIG_SMALL("Screenpresso"),
        .props     = AV_CODEC_PROP_LOSSLESS,
    },
    {
        .id        = AV_CODEC_ID_RSCC,
        .type      = AVMEDIA_TYPE_VIDEO,
        .name      = "rscc",
        .long_name = NULL_IF_CONFIG_SMALL("innoHeim/Rsupport Screen Capture Codec"),
        .props     = AV_CODEC_PROP_LOSSLESS,
    },
    {
        .id        = AV_CODEC_ID_AVS2,
        .type      = AVMEDIA_TYPE_VIDEO,
        .name      = "avs2",
        .long_name = NULL_IF_CONFIG_SMALL("AVS2-P2/IEEE1857.4"),
        .props     = AV_CODEC_PROP_LOSSY,
    },
    {
        .id        = AV_CODEC_ID_Y41P,
        .type      = AVMEDIA_TYPE_VIDEO,
        .name      = "y41p",
        .long_name = NULL_IF_CONFIG_SMALL("Uncompressed YUV 4:1:1 12-bit"),
        .props     = AV_CODEC_PROP_INTRA_ONLY | AV_CODEC_PROP_LOSSLESS,
    },
    {
        .id        = AV_CODEC_ID_AVRP,
        .type      = AVMEDIA_TYPE_VIDEO,
        .name      = "avrp",
        .long_name = NULL_IF_CONFIG_SMALL("Avid 1:1 10-bit RGB Packer"),
        .props     = AV_CODEC_PROP_INTRA_ONLY | AV_CODEC_PROP_LOSSLESS,
    },
    {
        .id        = AV_CODEC_ID_012V,
        .type      = AVMEDIA_TYPE_VIDEO,
        .name      = "012v",
        .long_name = NULL_IF_CONFIG_SMALL("Uncompressed 4:2:2 10-bit"),
        .props     = AV_CODEC_PROP_INTRA_ONLY | AV_CODEC_PROP_LOSSLESS,
    },
    {
        .id        = AV_CODEC_ID_AVUI,
        .type      = AVMEDIA_TYPE_VIDEO,
        .name      = "avui",
        .long_name = NULL_IF_CONFIG_SMALL("Avid Meridien Uncompressed"),
        .props     = AV_CODEC_PROP_INTRA_ONLY | AV_CODEC_PROP_LOSSLESS,
    },
    {
        .id        = AV_CODEC_ID_AYUV,
        .type      = AVMEDIA_TYPE_VIDEO,
        .name      = "ayuv",
        .long_name = NULL_IF_CONFIG_SMALL("Uncompressed packed MS 4:4:4:4"),
        .props     = AV_CODEC_PROP_INTRA_ONLY | AV_CODEC_PROP_LOSSLESS,
    },
    {
        .id        = AV_CODEC_ID_TARGA_Y216,
        .type      = AVMEDIA_TYPE_VIDEO,
        .name      = "targa_y216",
        .long_name = NULL_IF_CONFIG_SMALL("Pinnacle TARGA CineWave YUV16"),
        .props     = AV_CODEC_PROP_INTRA_ONLY | AV_CODEC_PROP_LOSSLESS,
    },
    {
        .id        = AV_CODEC_ID_V308,
        .type      = AVMEDIA_TYPE_VIDEO,
        .name      = "v308",
        .long_name = NULL_IF_CONFIG_SMALL("Uncompressed packed 4:4:4"),
        .props     = AV_CODEC_PROP_INTRA_ONLY | AV_CODEC_PROP_LOSSLESS,
    },
    {
        .id        = AV_CODEC_ID_V408,
        .type      = AVMEDIA_TYPE_VIDEO,
        .name      = "v408",
        .long_name = NULL_IF_CONFIG_SMALL("Uncompressed packed QT 4:4:4:4"),
        .props     = AV_CODEC_PROP_INTRA_ONLY | AV_CODEC_PROP_LOSSLESS,
    },
    {
        .id        = AV_CODEC_ID_YUV4,
        .type      = AVMEDIA_TYPE_VIDEO,
        .name      = "yuv4",
        .long_name = NULL_IF_CONFIG_SMALL("Uncompressed packed 4:2:0"),
        .props     = AV_CODEC_PROP_INTRA_ONLY | AV_CODEC_PROP_LOSSLESS,
    },
    {
        .id        = AV_CODEC_ID_AVRN,
        .type      = AVMEDIA_TYPE_VIDEO,
        .name      = "avrn",
        .long_name = NULL_IF_CONFIG_SMALL("Avid AVI Codec"),
    },
    {
        .id        = AV_CODEC_ID_CPIA,
        .type      = AVMEDIA_TYPE_VIDEO,
        .name      = "cpia",
        .long_name = NULL_IF_CONFIG_SMALL("CPiA video format"),
    },
    {
        .id        = AV_CODEC_ID_XFACE,
        .type      = AVMEDIA_TYPE_VIDEO,
        .name      = "xface",
        .long_name = NULL_IF_CONFIG_SMALL("X-face image"),
        .props     = AV_CODEC_PROP_INTRA_ONLY | AV_CODEC_PROP_LOSSY,
    },
    {
        .id        = AV_CODEC_ID_SNOW,
        .type      = AVMEDIA_TYPE_VIDEO,
        .name      = "snow",
        .long_name = NULL_IF_CONFIG_SMALL("Snow"),
        .props     = AV_CODEC_PROP_LOSSY | AV_CODEC_PROP_LOSSLESS,
    },
    {
        .id        = AV_CODEC_ID_SMVJPEG,
        .type      = AVMEDIA_TYPE_VIDEO,
        .name      = "smvjpeg",
        .long_name = NULL_IF_CONFIG_SMALL("Sigmatel Motion Video"),
    },
    {
        .id        = AV_CODEC_ID_APNG,
        .type      = AVMEDIA_TYPE_VIDEO,
        .name      = "apng",
        .long_name = NULL_IF_CONFIG_SMALL("APNG (Animated Portable Network Graphics) image"),
        .props     = AV_CODEC_PROP_LOSSLESS,
        .mime_types= MT("image/png"),
    },
    {
        .id        = AV_CODEC_ID_DAALA,
        .type      = AVMEDIA_TYPE_VIDEO,
        .name      = "daala",
        .long_name = NULL_IF_CONFIG_SMALL("Daala"),
        .props     = AV_CODEC_PROP_LOSSY | AV_CODEC_PROP_LOSSLESS,
    },
    {
        .id        = AV_CODEC_ID_CFHD,
        .type      = AVMEDIA_TYPE_VIDEO,
        .name      = "cfhd",
        .long_name = NULL_IF_CONFIG_SMALL("Cineform HD"),
        .props     = AV_CODEC_PROP_LOSSY,
    },
    {
        .id        = AV_CODEC_ID_TRUEMOTION2RT,
        .type      = AVMEDIA_TYPE_VIDEO,
        .name      = "truemotion2rt",
        .long_name = NULL_IF_CONFIG_SMALL("Duck TrueMotion 2.0 Real Time"),
        .props     = AV_CODEC_PROP_INTRA_ONLY | AV_CODEC_PROP_LOSSY,
    },
    {
        .id        = AV_CODEC_ID_M101,
        .type      = AVMEDIA_TYPE_VIDEO,
        .name      = "m101",
        .long_name = NULL_IF_CONFIG_SMALL("Matrox Uncompressed SD"),
        .props     = AV_CODEC_PROP_INTRA_ONLY | AV_CODEC_PROP_LOSSLESS,
    },
    {
        .id        = AV_CODEC_ID_MAGICYUV,
        .type      = AVMEDIA_TYPE_VIDEO,
        .name      = "magicyuv",
        .long_name = NULL_IF_CONFIG_SMALL("MagicYUV video"),
        .props     = AV_CODEC_PROP_INTRA_ONLY | AV_CODEC_PROP_LOSSLESS,
    },
    {
        .id        = AV_CODEC_ID_SHEERVIDEO,
        .type      = AVMEDIA_TYPE_VIDEO,
        .name      = "sheervideo",
        .long_name = NULL_IF_CONFIG_SMALL("BitJazz SheerVideo"),
        .props     = AV_CODEC_PROP_INTRA_ONLY | AV_CODEC_PROP_LOSSLESS,
    },
    {
        .id        = AV_CODEC_ID_YLC,
        .type      = AVMEDIA_TYPE_VIDEO,
        .name      = "ylc",
        .long_name = NULL_IF_CONFIG_SMALL("YUY2 Lossless Codec"),
        .props     = AV_CODEC_PROP_INTRA_ONLY | AV_CODEC_PROP_LOSSLESS,
    },
    {
        .id        = AV_CODEC_ID_PSD,
        .type      = AVMEDIA_TYPE_VIDEO,
        .name      = "psd",
        .long_name = NULL_IF_CONFIG_SMALL("Photoshop PSD file"),
        .props     = AV_CODEC_PROP_INTRA_ONLY | AV_CODEC_PROP_LOSSLESS,
    },
    {
        .id        = AV_CODEC_ID_PIXLET,
        .type      = AVMEDIA_TYPE_VIDEO,
        .name      = "pixlet",
        .long_name = NULL_IF_CONFIG_SMALL("Apple Pixlet"),
        .props     = AV_CODEC_PROP_INTRA_ONLY | AV_CODEC_PROP_LOSSY,
    },
    {
        .id        = AV_CODEC_ID_SPEEDHQ,
        .type      = AVMEDIA_TYPE_VIDEO,
        .name      = "speedhq",
        .long_name = NULL_IF_CONFIG_SMALL("NewTek SpeedHQ"),
        .props     = AV_CODEC_PROP_INTRA_ONLY | AV_CODEC_PROP_LOSSY,
    },
    {
        .id        = AV_CODEC_ID_FMVC,
        .type      = AVMEDIA_TYPE_VIDEO,
        .name      = "fmvc",
        .long_name = NULL_IF_CONFIG_SMALL("FM Screen Capture Codec"),
        .props     = AV_CODEC_PROP_LOSSLESS,
    },
    {
        .id        = AV_CODEC_ID_SCPR,
        .type      = AVMEDIA_TYPE_VIDEO,
        .name      = "scpr",
        .long_name = NULL_IF_CONFIG_SMALL("ScreenPressor"),
        .props     = AV_CODEC_PROP_LOSSLESS | AV_CODEC_PROP_LOSSY,
    },
    {
        .id        = AV_CODEC_ID_CLEARVIDEO,
        .type      = AVMEDIA_TYPE_VIDEO,
        .name      = "clearvideo",
        .long_name = NULL_IF_CONFIG_SMALL("Iterated Systems ClearVideo"),
        .props     = AV_CODEC_PROP_LOSSY,
    },
    {
        .id        = AV_CODEC_ID_XPM,
        .type      = AVMEDIA_TYPE_VIDEO,
        .name      = "xpm",
        .long_name = NULL_IF_CONFIG_SMALL("XPM (X PixMap) image"),
        .props     = AV_CODEC_PROP_INTRA_ONLY | AV_CODEC_PROP_LOSSLESS,
        .mime_types= MT("image/x-xpixmap"),
    },
    {
        .id        = AV_CODEC_ID_AV1,
        .type      = AVMEDIA_TYPE_VIDEO,
        .name      = "av1",
        .long_name = NULL_IF_CONFIG_SMALL("Alliance for Open Media AV1"),
        .props     = AV_CODEC_PROP_LOSSY,
        .profiles  = NULL_IF_CONFIG_SMALL(ff_av1_profiles),
    },
    {
        .id        = AV_CODEC_ID_BITPACKED,
        .type      = AVMEDIA_TYPE_VIDEO,
        .name      = "bitpacked",
        .long_name = NULL_IF_CONFIG_SMALL("Bitpacked"),
        .props     = AV_CODEC_PROP_INTRA_ONLY | AV_CODEC_PROP_LOSSLESS,
    },
    {
        .id        = AV_CODEC_ID_MSCC,
        .type      = AVMEDIA_TYPE_VIDEO,
        .name      = "mscc",
        .long_name = NULL_IF_CONFIG_SMALL("Mandsoft Screen Capture Codec"),
        .props     = AV_CODEC_PROP_INTRA_ONLY | AV_CODEC_PROP_LOSSLESS,
    },
    {
        .id        = AV_CODEC_ID_SRGC,
        .type      = AVMEDIA_TYPE_VIDEO,
        .name      = "srgc",
        .long_name = NULL_IF_CONFIG_SMALL("Screen Recorder Gold Codec"),
        .props     = AV_CODEC_PROP_INTRA_ONLY | AV_CODEC_PROP_LOSSLESS,
    },
    {
        .id        = AV_CODEC_ID_SVG,
        .type      = AVMEDIA_TYPE_VIDEO,
        .name      = "svg",
        .long_name = NULL_IF_CONFIG_SMALL("Scalable Vector Graphics"),
        .props     = AV_CODEC_PROP_LOSSLESS,
        .mime_types= MT("image/svg+xml"),
    },
    {
        .id        = AV_CODEC_ID_GDV,
        .type      = AVMEDIA_TYPE_VIDEO,
        .name      = "gdv",
        .long_name = NULL_IF_CONFIG_SMALL("Gremlin Digital Video"),
        .props     = AV_CODEC_PROP_LOSSY,
    },
    {
        .id        = AV_CODEC_ID_FITS,
        .type      = AVMEDIA_TYPE_VIDEO,
        .name      = "fits",
        .long_name = NULL_IF_CONFIG_SMALL("FITS (Flexible Image Transport System)"),
        .props     = AV_CODEC_PROP_INTRA_ONLY | AV_CODEC_PROP_LOSSLESS,
    },
    {
        .id        = AV_CODEC_ID_IMM4,
        .type      = AVMEDIA_TYPE_VIDEO,
        .name      = "imm4",
        .long_name = NULL_IF_CONFIG_SMALL("Infinity IMM4"),
        .props     = AV_CODEC_PROP_LOSSY,
    },
    {
        .id        = AV_CODEC_ID_PROSUMER,
        .type      = AVMEDIA_TYPE_VIDEO,
        .name      = "prosumer",
        .long_name = NULL_IF_CONFIG_SMALL("Brooktree ProSumer Video"),
        .props     = AV_CODEC_PROP_INTRA_ONLY | AV_CODEC_PROP_LOSSY,
    },
    {
        .id        = AV_CODEC_ID_MWSC,
        .type      = AVMEDIA_TYPE_VIDEO,
        .name      = "mwsc",
        .long_name = NULL_IF_CONFIG_SMALL("MatchWare Screen Capture Codec"),
        .props     = AV_CODEC_PROP_LOSSLESS,
    },
    {
        .id        = AV_CODEC_ID_WCMV,
        .type      = AVMEDIA_TYPE_VIDEO,
        .name      = "wcmv",
        .long_name = NULL_IF_CONFIG_SMALL("WinCAM Motion Video"),
        .props     = AV_CODEC_PROP_LOSSLESS,
    },
    {
        .id        = AV_CODEC_ID_RASC,
        .type      = AVMEDIA_TYPE_VIDEO,
        .name      = "rasc",
        .long_name = NULL_IF_CONFIG_SMALL("RemotelyAnywhere Screen Capture"),
        .props     = AV_CODEC_PROP_LOSSY,
    },

    /* various PCM "codecs" */
    {
        .id        = AV_CODEC_ID_PCM_S16LE,
        .type      = AVMEDIA_TYPE_AUDIO,
        .name      = "pcm_s16le",
        .long_name = NULL_IF_CONFIG_SMALL("PCM signed 16-bit little-endian"),
        .props     = AV_CODEC_PROP_LOSSLESS,
    },
    {
        .id        = AV_CODEC_ID_PCM_S16BE,
        .type      = AVMEDIA_TYPE_AUDIO,
        .name      = "pcm_s16be",
        .long_name = NULL_IF_CONFIG_SMALL("PCM signed 16-bit big-endian"),
        .props     = AV_CODEC_PROP_LOSSLESS,
    },
    {
        .id        = AV_CODEC_ID_PCM_U16LE,
        .type      = AVMEDIA_TYPE_AUDIO,
        .name      = "pcm_u16le",
        .long_name = NULL_IF_CONFIG_SMALL("PCM unsigned 16-bit little-endian"),
        .props     = AV_CODEC_PROP_LOSSLESS,
    },
    {
        .id        = AV_CODEC_ID_PCM_U16BE,
        .type      = AVMEDIA_TYPE_AUDIO,
        .name      = "pcm_u16be",
        .long_name = NULL_IF_CONFIG_SMALL("PCM unsigned 16-bit big-endian"),
        .props     = AV_CODEC_PROP_LOSSLESS,
    },
    {
        .id        = AV_CODEC_ID_PCM_S8,
        .type      = AVMEDIA_TYPE_AUDIO,
        .name      = "pcm_s8",
        .long_name = NULL_IF_CONFIG_SMALL("PCM signed 8-bit"),
        .props     = AV_CODEC_PROP_LOSSLESS,
    },
    {
        .id        = AV_CODEC_ID_PCM_U8,
        .type      = AVMEDIA_TYPE_AUDIO,
        .name      = "pcm_u8",
        .long_name = NULL_IF_CONFIG_SMALL("PCM unsigned 8-bit"),
        .props     = AV_CODEC_PROP_LOSSLESS,
    },
    {
        .id        = AV_CODEC_ID_PCM_MULAW,
        .type      = AVMEDIA_TYPE_AUDIO,
        .name      = "pcm_mulaw",
        .long_name = NULL_IF_CONFIG_SMALL("PCM mu-law / G.711 mu-law"),
        .props     = AV_CODEC_PROP_LOSSY,
    },
    {
        .id        = AV_CODEC_ID_PCM_ALAW,
        .type      = AVMEDIA_TYPE_AUDIO,
        .name      = "pcm_alaw",
        .long_name = NULL_IF_CONFIG_SMALL("PCM A-law / G.711 A-law"),
        .props     = AV_CODEC_PROP_LOSSY,
    },
    {
        .id        = AV_CODEC_ID_PCM_S32LE,
        .type      = AVMEDIA_TYPE_AUDIO,
        .name      = "pcm_s32le",
        .long_name = NULL_IF_CONFIG_SMALL("PCM signed 32-bit little-endian"),
        .props     = AV_CODEC_PROP_LOSSLESS,
    },
    {
        .id        = AV_CODEC_ID_PCM_S32BE,
        .type      = AVMEDIA_TYPE_AUDIO,
        .name      = "pcm_s32be",
        .long_name = NULL_IF_CONFIG_SMALL("PCM signed 32-bit big-endian"),
        .props     = AV_CODEC_PROP_LOSSLESS,
    },
    {
        .id        = AV_CODEC_ID_PCM_U32LE,
        .type      = AVMEDIA_TYPE_AUDIO,
        .name      = "pcm_u32le",
        .long_name = NULL_IF_CONFIG_SMALL("PCM unsigned 32-bit little-endian"),
        .props     = AV_CODEC_PROP_LOSSLESS,
    },
    {
        .id        = AV_CODEC_ID_PCM_U32BE,
        .type      = AVMEDIA_TYPE_AUDIO,
        .name      = "pcm_u32be",
        .long_name = NULL_IF_CONFIG_SMALL("PCM unsigned 32-bit big-endian"),
        .props     = AV_CODEC_PROP_LOSSLESS,
    },
    {
        .id        = AV_CODEC_ID_PCM_S24LE,
        .type      = AVMEDIA_TYPE_AUDIO,
        .name      = "pcm_s24le",
        .long_name = NULL_IF_CONFIG_SMALL("PCM signed 24-bit little-endian"),
        .props     = AV_CODEC_PROP_LOSSLESS,
    },
    {
        .id        = AV_CODEC_ID_PCM_S24BE,
        .type      = AVMEDIA_TYPE_AUDIO,
        .name      = "pcm_s24be",
        .long_name = NULL_IF_CONFIG_SMALL("PCM signed 24-bit big-endian"),
        .props     = AV_CODEC_PROP_LOSSLESS,
    },
    {
        .id        = AV_CODEC_ID_PCM_U24LE,
        .type      = AVMEDIA_TYPE_AUDIO,
        .name      = "pcm_u24le",
        .long_name = NULL_IF_CONFIG_SMALL("PCM unsigned 24-bit little-endian"),
        .props     = AV_CODEC_PROP_LOSSLESS,
    },
    {
        .id        = AV_CODEC_ID_PCM_U24BE,
        .type      = AVMEDIA_TYPE_AUDIO,
        .name      = "pcm_u24be",
        .long_name = NULL_IF_CONFIG_SMALL("PCM unsigned 24-bit big-endian"),
        .props     = AV_CODEC_PROP_LOSSLESS,
    },
    {
        .id        = AV_CODEC_ID_PCM_S24DAUD,
        .type      = AVMEDIA_TYPE_AUDIO,
        .name      = "pcm_s24daud",
        .long_name = NULL_IF_CONFIG_SMALL("PCM D-Cinema audio signed 24-bit"),
        .props     = AV_CODEC_PROP_LOSSLESS,
    },
    {
        .id        = AV_CODEC_ID_PCM_ZORK,
        .type      = AVMEDIA_TYPE_AUDIO,
        .name      = "pcm_zork",
        .long_name = NULL_IF_CONFIG_SMALL("PCM Zork"),
        .props     = AV_CODEC_PROP_LOSSY,
    },
    {
        .id        = AV_CODEC_ID_PCM_S16LE_PLANAR,
        .type      = AVMEDIA_TYPE_AUDIO,
        .name      = "pcm_s16le_planar",
        .long_name = NULL_IF_CONFIG_SMALL("PCM signed 16-bit little-endian planar"),
        .props     = AV_CODEC_PROP_LOSSLESS,
    },
    {
        .id        = AV_CODEC_ID_PCM_DVD,
        .type      = AVMEDIA_TYPE_AUDIO,
        .name      = "pcm_dvd",
        .long_name = NULL_IF_CONFIG_SMALL("PCM signed 20|24-bit big-endian"),
        .props     = AV_CODEC_PROP_LOSSLESS,
    },
    {
        .id        = AV_CODEC_ID_PCM_F32BE,
        .type      = AVMEDIA_TYPE_AUDIO,
        .name      = "pcm_f32be",
        .long_name = NULL_IF_CONFIG_SMALL("PCM 32-bit floating point big-endian"),
        .props     = AV_CODEC_PROP_LOSSLESS,
    },
    {
        .id        = AV_CODEC_ID_PCM_F32LE,
        .type      = AVMEDIA_TYPE_AUDIO,
        .name      = "pcm_f32le",
        .long_name = NULL_IF_CONFIG_SMALL("PCM 32-bit floating point little-endian"),
        .props     = AV_CODEC_PROP_LOSSLESS,
    },
    {
        .id        = AV_CODEC_ID_PCM_F64BE,
        .type      = AVMEDIA_TYPE_AUDIO,
        .name      = "pcm_f64be",
        .long_name = NULL_IF_CONFIG_SMALL("PCM 64-bit floating point big-endian"),
        .props     = AV_CODEC_PROP_LOSSLESS,
    },
    {
        .id        = AV_CODEC_ID_PCM_F64LE,
        .type      = AVMEDIA_TYPE_AUDIO,
        .name      = "pcm_f64le",
        .long_name = NULL_IF_CONFIG_SMALL("PCM 64-bit floating point little-endian"),
        .props     = AV_CODEC_PROP_LOSSLESS,
    },
    {
        .id        = AV_CODEC_ID_PCM_BLURAY,
        .type      = AVMEDIA_TYPE_AUDIO,
        .name      = "pcm_bluray",
        .long_name = NULL_IF_CONFIG_SMALL("PCM signed 16|20|24-bit big-endian for Blu-ray media"),
        .props     = AV_CODEC_PROP_LOSSLESS,
    },
    {
        .id        = AV_CODEC_ID_PCM_LXF,
        .type      = AVMEDIA_TYPE_AUDIO,
        .name      = "pcm_lxf",
        .long_name = NULL_IF_CONFIG_SMALL("PCM signed 20-bit little-endian planar"),
        .props     = AV_CODEC_PROP_LOSSLESS,
    },
    {
        .id        = AV_CODEC_ID_S302M,
        .type      = AVMEDIA_TYPE_AUDIO,
        .name      = "s302m",
        .long_name = NULL_IF_CONFIG_SMALL("SMPTE 302M"),
        .props     = AV_CODEC_PROP_LOSSLESS,
    },
    {
        .id        = AV_CODEC_ID_PCM_S8_PLANAR,
        .type      = AVMEDIA_TYPE_AUDIO,
        .name      = "pcm_s8_planar",
        .long_name = NULL_IF_CONFIG_SMALL("PCM signed 8-bit planar"),
        .props     = AV_CODEC_PROP_LOSSLESS,
    },
    {
        .id        = AV_CODEC_ID_PCM_S24LE_PLANAR,
        .type      = AVMEDIA_TYPE_AUDIO,
        .name      = "pcm_s24le_planar",
        .long_name = NULL_IF_CONFIG_SMALL("PCM signed 24-bit little-endian planar"),
        .props     = AV_CODEC_PROP_LOSSLESS,
    },
    {
        .id        = AV_CODEC_ID_PCM_S32LE_PLANAR,
        .type      = AVMEDIA_TYPE_AUDIO,
        .name      = "pcm_s32le_planar",
        .long_name = NULL_IF_CONFIG_SMALL("PCM signed 32-bit little-endian planar"),
        .props     = AV_CODEC_PROP_LOSSLESS,
    },
    {
        .id        = AV_CODEC_ID_PCM_S16BE_PLANAR,
        .type      = AVMEDIA_TYPE_AUDIO,
        .name      = "pcm_s16be_planar",
        .long_name = NULL_IF_CONFIG_SMALL("PCM signed 16-bit big-endian planar"),
        .props     = AV_CODEC_PROP_LOSSLESS,
    },
    {
        .id        = AV_CODEC_ID_PCM_S64LE,
        .type      = AVMEDIA_TYPE_AUDIO,
        .name      = "pcm_s64le",
        .long_name = NULL_IF_CONFIG_SMALL("PCM signed 64-bit little-endian"),
        .props     = AV_CODEC_PROP_LOSSLESS,
    },
    {
        .id        = AV_CODEC_ID_PCM_S64BE,
        .type      = AVMEDIA_TYPE_AUDIO,
        .name      = "pcm_s64be",
        .long_name = NULL_IF_CONFIG_SMALL("PCM signed 64-bit big-endian"),
        .props     = AV_CODEC_PROP_LOSSLESS,
    },
    {
        .id        = AV_CODEC_ID_PCM_F16LE,
        .type      = AVMEDIA_TYPE_AUDIO,
        .name      = "pcm_f16le",
        .long_name = NULL_IF_CONFIG_SMALL("PCM 16.8 floating point little-endian"),
        .props     = AV_CODEC_PROP_LOSSLESS,
    },
    {
        .id        = AV_CODEC_ID_PCM_F24LE,
        .type      = AVMEDIA_TYPE_AUDIO,
        .name      = "pcm_f24le",
        .long_name = NULL_IF_CONFIG_SMALL("PCM 24.0 floating point little-endian"),
        .props     = AV_CODEC_PROP_LOSSLESS,
    },
    {
        .id        = AV_CODEC_ID_PCM_VIDC,
        .type      = AVMEDIA_TYPE_AUDIO,
        .name      = "pcm_vidc",
        .long_name = NULL_IF_CONFIG_SMALL("PCM Archimedes VIDC"),
        .props     = AV_CODEC_PROP_LOSSY,
    },

    /* various ADPCM codecs */
    {
        .id        = AV_CODEC_ID_ADPCM_IMA_QT,
        .type      = AVMEDIA_TYPE_AUDIO,
        .name      = "adpcm_ima_qt",
        .long_name = NULL_IF_CONFIG_SMALL("ADPCM IMA QuickTime"),
        .props     = AV_CODEC_PROP_LOSSY,
    },
    {
        .id        = AV_CODEC_ID_ADPCM_IMA_WAV,
        .type      = AVMEDIA_TYPE_AUDIO,
        .name      = "adpcm_ima_wav",
        .long_name = NULL_IF_CONFIG_SMALL("ADPCM IMA WAV"),
        .props     = AV_CODEC_PROP_LOSSY,
    },
    {
        .id        = AV_CODEC_ID_ADPCM_IMA_DK3,
        .type      = AVMEDIA_TYPE_AUDIO,
        .name      = "adpcm_ima_dk3",
        .long_name = NULL_IF_CONFIG_SMALL("ADPCM IMA Duck DK3"),
        .props     = AV_CODEC_PROP_LOSSY,
    },
    {
        .id        = AV_CODEC_ID_ADPCM_IMA_DK4,
        .type      = AVMEDIA_TYPE_AUDIO,
        .name      = "adpcm_ima_dk4",
        .long_name = NULL_IF_CONFIG_SMALL("ADPCM IMA Duck DK4"),
        .props     = AV_CODEC_PROP_LOSSY,
    },
    {
        .id        = AV_CODEC_ID_ADPCM_IMA_WS,
        .type      = AVMEDIA_TYPE_AUDIO,
        .name      = "adpcm_ima_ws",
        .long_name = NULL_IF_CONFIG_SMALL("ADPCM IMA Westwood"),
        .props     = AV_CODEC_PROP_LOSSY,
    },
    {
        .id        = AV_CODEC_ID_ADPCM_IMA_SMJPEG,
        .type      = AVMEDIA_TYPE_AUDIO,
        .name      = "adpcm_ima_smjpeg",
        .long_name = NULL_IF_CONFIG_SMALL("ADPCM IMA Loki SDL MJPEG"),
        .props     = AV_CODEC_PROP_LOSSY,
    },
    {
        .id        = AV_CODEC_ID_ADPCM_MS,
        .type      = AVMEDIA_TYPE_AUDIO,
        .name      = "adpcm_ms",
        .long_name = NULL_IF_CONFIG_SMALL("ADPCM Microsoft"),
        .props     = AV_CODEC_PROP_LOSSY,
    },
    {
        .id        = AV_CODEC_ID_ADPCM_4XM,
        .type      = AVMEDIA_TYPE_AUDIO,
        .name      = "adpcm_4xm",
        .long_name = NULL_IF_CONFIG_SMALL("ADPCM 4X Movie"),
        .props     = AV_CODEC_PROP_LOSSY,
    },
    {
        .id        = AV_CODEC_ID_ADPCM_XA,
        .type      = AVMEDIA_TYPE_AUDIO,
        .name      = "adpcm_xa",
        .long_name = NULL_IF_CONFIG_SMALL("ADPCM CDROM XA"),
        .props     = AV_CODEC_PROP_LOSSY,
    },
    {
        .id        = AV_CODEC_ID_ADPCM_ADX,
        .type      = AVMEDIA_TYPE_AUDIO,
        .name      = "adpcm_adx",
        .long_name = NULL_IF_CONFIG_SMALL("SEGA CRI ADX ADPCM"),
        .props     = AV_CODEC_PROP_LOSSY,
    },
    {
        .id        = AV_CODEC_ID_ADPCM_EA,
        .type      = AVMEDIA_TYPE_AUDIO,
        .name      = "adpcm_ea",
        .long_name = NULL_IF_CONFIG_SMALL("ADPCM Electronic Arts"),
        .props     = AV_CODEC_PROP_LOSSY,
    },
    {
        .id        = AV_CODEC_ID_ADPCM_G726,
        .type      = AVMEDIA_TYPE_AUDIO,
        .name      = "adpcm_g726",
        .long_name = NULL_IF_CONFIG_SMALL("G.726 ADPCM"),
        .props     = AV_CODEC_PROP_LOSSY,
    },
    {
        .id        = AV_CODEC_ID_ADPCM_CT,
        .type      = AVMEDIA_TYPE_AUDIO,
        .name      = "adpcm_ct",
        .long_name = NULL_IF_CONFIG_SMALL("ADPCM Creative Technology"),
        .props     = AV_CODEC_PROP_LOSSY,
    },
    {
        .id        = AV_CODEC_ID_ADPCM_SWF,
        .type      = AVMEDIA_TYPE_AUDIO,
        .name      = "adpcm_swf",
        .long_name = NULL_IF_CONFIG_SMALL("ADPCM Shockwave Flash"),
        .props     = AV_CODEC_PROP_LOSSY,
    },
    {
        .id        = AV_CODEC_ID_ADPCM_YAMAHA,
        .type      = AVMEDIA_TYPE_AUDIO,
        .name      = "adpcm_yamaha",
        .long_name = NULL_IF_CONFIG_SMALL("ADPCM Yamaha"),
        .props     = AV_CODEC_PROP_LOSSY,
    },
    {
        .id        = AV_CODEC_ID_ADPCM_SBPRO_4,
        .type      = AVMEDIA_TYPE_AUDIO,
        .name      = "adpcm_sbpro_4",
        .long_name = NULL_IF_CONFIG_SMALL("ADPCM Sound Blaster Pro 4-bit"),
        .props     = AV_CODEC_PROP_LOSSY,
    },
    {
        .id        = AV_CODEC_ID_ADPCM_SBPRO_3,
        .type      = AVMEDIA_TYPE_AUDIO,
        .name      = "adpcm_sbpro_3",
        .long_name = NULL_IF_CONFIG_SMALL("ADPCM Sound Blaster Pro 2.6-bit"),
        .props     = AV_CODEC_PROP_LOSSY,
    },
    {
        .id        = AV_CODEC_ID_ADPCM_SBPRO_2,
        .type      = AVMEDIA_TYPE_AUDIO,
        .name      = "adpcm_sbpro_2",
        .long_name = NULL_IF_CONFIG_SMALL("ADPCM Sound Blaster Pro 2-bit"),
        .props     = AV_CODEC_PROP_LOSSY,
    },
    {
        .id        = AV_CODEC_ID_ADPCM_THP,
        .type      = AVMEDIA_TYPE_AUDIO,
        .name      = "adpcm_thp",
        .long_name = NULL_IF_CONFIG_SMALL("ADPCM Nintendo THP"),
        .props     = AV_CODEC_PROP_LOSSY,
    },
    {
        .id        = AV_CODEC_ID_ADPCM_IMA_AMV,
        .type      = AVMEDIA_TYPE_AUDIO,
        .name      = "adpcm_ima_amv",
        .long_name = NULL_IF_CONFIG_SMALL("ADPCM IMA AMV"),
        .props     = AV_CODEC_PROP_LOSSY,
    },
    {
        .id        = AV_CODEC_ID_ADPCM_EA_R1,
        .type      = AVMEDIA_TYPE_AUDIO,
        .name      = "adpcm_ea_r1",
        .long_name = NULL_IF_CONFIG_SMALL("ADPCM Electronic Arts R1"),
        .props     = AV_CODEC_PROP_LOSSY,
    },
    {
        .id        = AV_CODEC_ID_ADPCM_EA_R3,
        .type      = AVMEDIA_TYPE_AUDIO,
        .name      = "adpcm_ea_r3",
        .long_name = NULL_IF_CONFIG_SMALL("ADPCM Electronic Arts R3"),
        .props     = AV_CODEC_PROP_LOSSY,
    },
    {
        .id        = AV_CODEC_ID_ADPCM_EA_R2,
        .type      = AVMEDIA_TYPE_AUDIO,
        .name      = "adpcm_ea_r2",
        .long_name = NULL_IF_CONFIG_SMALL("ADPCM Electronic Arts R2"),
        .props     = AV_CODEC_PROP_LOSSY,
    },
    {
        .id        = AV_CODEC_ID_ADPCM_IMA_EA_SEAD,
        .type      = AVMEDIA_TYPE_AUDIO,
        .name      = "adpcm_ima_ea_sead",
        .long_name = NULL_IF_CONFIG_SMALL("ADPCM IMA Electronic Arts SEAD"),
        .props     = AV_CODEC_PROP_LOSSY,
    },
    {
        .id        = AV_CODEC_ID_ADPCM_IMA_EA_EACS,
        .type      = AVMEDIA_TYPE_AUDIO,
        .name      = "adpcm_ima_ea_eacs",
        .long_name = NULL_IF_CONFIG_SMALL("ADPCM IMA Electronic Arts EACS"),
        .props     = AV_CODEC_PROP_LOSSY,
    },
    {
        .id        = AV_CODEC_ID_ADPCM_EA_XAS,
        .type      = AVMEDIA_TYPE_AUDIO,
        .name      = "adpcm_ea_xas",
        .long_name = NULL_IF_CONFIG_SMALL("ADPCM Electronic Arts XAS"),
        .props     = AV_CODEC_PROP_LOSSY,
    },
    {
        .id        = AV_CODEC_ID_ADPCM_EA_MAXIS_XA,
        .type      = AVMEDIA_TYPE_AUDIO,
        .name      = "adpcm_ea_maxis_xa",
        .long_name = NULL_IF_CONFIG_SMALL("ADPCM Electronic Arts Maxis CDROM XA"),
        .props     = AV_CODEC_PROP_LOSSY,
    },
    {
        .id        = AV_CODEC_ID_ADPCM_IMA_ISS,
        .type      = AVMEDIA_TYPE_AUDIO,
        .name      = "adpcm_ima_iss",
        .long_name = NULL_IF_CONFIG_SMALL("ADPCM IMA Funcom ISS"),
        .props     = AV_CODEC_PROP_LOSSY,
    },
    {
        .id        = AV_CODEC_ID_ADPCM_G722,
        .type      = AVMEDIA_TYPE_AUDIO,
        .name      = "adpcm_g722",
        .long_name = NULL_IF_CONFIG_SMALL("G.722 ADPCM"),
        .props     = AV_CODEC_PROP_LOSSY,
    },
    {
        .id        = AV_CODEC_ID_ADPCM_IMA_APC,
        .type      = AVMEDIA_TYPE_AUDIO,
        .name      = "adpcm_ima_apc",
        .long_name = NULL_IF_CONFIG_SMALL("ADPCM IMA CRYO APC"),
        .props     = AV_CODEC_PROP_LOSSY,
    },
    {
        .id        = AV_CODEC_ID_ADPCM_VIMA,
        .type      = AVMEDIA_TYPE_AUDIO,
        .name      = "adpcm_vima",
        .long_name = NULL_IF_CONFIG_SMALL("LucasArts VIMA audio"),
        .props     = AV_CODEC_PROP_LOSSY,
    },
    {
        .id        = AV_CODEC_ID_ADPCM_AFC,
        .type      = AVMEDIA_TYPE_AUDIO,
        .name      = "adpcm_afc",
        .long_name = NULL_IF_CONFIG_SMALL("ADPCM Nintendo Gamecube AFC"),
        .props     = AV_CODEC_PROP_LOSSY,
    },
    {
        .id        = AV_CODEC_ID_ADPCM_IMA_OKI,
        .type      = AVMEDIA_TYPE_AUDIO,
        .name      = "adpcm_ima_oki",
        .long_name = NULL_IF_CONFIG_SMALL("ADPCM IMA Dialogic OKI"),
        .props     = AV_CODEC_PROP_LOSSY,
    },
    {
        .id        = AV_CODEC_ID_ADPCM_DTK,
        .type      = AVMEDIA_TYPE_AUDIO,
        .name      = "adpcm_dtk",
        .long_name = NULL_IF_CONFIG_SMALL("ADPCM Nintendo Gamecube DTK"),
        .props     = AV_CODEC_PROP_LOSSY,
    },
    {
        .id        = AV_CODEC_ID_ADPCM_IMA_RAD,
        .type      = AVMEDIA_TYPE_AUDIO,
        .name      = "adpcm_ima_rad",
        .long_name = NULL_IF_CONFIG_SMALL("ADPCM IMA Radical"),
        .props     = AV_CODEC_PROP_LOSSY,
    },
    {
        .id        = AV_CODEC_ID_ADPCM_G726LE,
        .type      = AVMEDIA_TYPE_AUDIO,
        .name      = "adpcm_g726le",
        .long_name = NULL_IF_CONFIG_SMALL("G.726 ADPCM little-endian"),
        .props     = AV_CODEC_PROP_LOSSY,
    },
    {
        .id        = AV_CODEC_ID_ADPCM_THP_LE,
        .type      = AVMEDIA_TYPE_AUDIO,
        .name      = "adpcm_thp_le",
        .long_name = NULL_IF_CONFIG_SMALL("ADPCM Nintendo THP (Little-Endian)"),
        .props     = AV_CODEC_PROP_LOSSY,
    },
    {
        .id        = AV_CODEC_ID_ADPCM_PSX,
        .type      = AVMEDIA_TYPE_AUDIO,
        .name      = "adpcm_psx",
        .long_name = NULL_IF_CONFIG_SMALL("ADPCM Playstation"),
        .props     = AV_CODEC_PROP_LOSSY,
    },
    {
        .id        = AV_CODEC_ID_ADPCM_AICA,
        .type      = AVMEDIA_TYPE_AUDIO,
        .name      = "adpcm_aica",
        .long_name = NULL_IF_CONFIG_SMALL("ADPCM Yamaha AICA"),
        .props     = AV_CODEC_PROP_LOSSY,
    },
    {
        .id        = AV_CODEC_ID_ADPCM_IMA_DAT4,
        .type      = AVMEDIA_TYPE_AUDIO,
        .name      = "adpcm_ima_dat4",
        .long_name = NULL_IF_CONFIG_SMALL("ADPCM IMA Eurocom DAT4"),
        .props     = AV_CODEC_PROP_LOSSY,
    },
    {
        .id        = AV_CODEC_ID_ADPCM_MTAF,
        .type      = AVMEDIA_TYPE_AUDIO,
        .name      = "adpcm_mtaf",
        .long_name = NULL_IF_CONFIG_SMALL("ADPCM MTAF"),
        .props     = AV_CODEC_PROP_LOSSY,
    },

    /* AMR */
    {
        .id        = AV_CODEC_ID_AMR_NB,
        .type      = AVMEDIA_TYPE_AUDIO,
        .name      = "amr_nb",
        .long_name = NULL_IF_CONFIG_SMALL("AMR-NB (Adaptive Multi-Rate NarrowBand)"),
        .props     = AV_CODEC_PROP_LOSSY,
    },
    {
        .id        = AV_CODEC_ID_AMR_WB,
        .type      = AVMEDIA_TYPE_AUDIO,
        .name      = "amr_wb",
        .long_name = NULL_IF_CONFIG_SMALL("AMR-WB (Adaptive Multi-Rate WideBand)"),
        .props     = AV_CODEC_PROP_LOSSY,
    },

    /* RealAudio codecs*/
    {
        .id        = AV_CODEC_ID_RA_144,
        .type      = AVMEDIA_TYPE_AUDIO,
        .name      = "ra_144",
        .long_name = NULL_IF_CONFIG_SMALL("RealAudio 1.0 (14.4K)"),
        .props     = AV_CODEC_PROP_LOSSY,
    },
    {
        .id        = AV_CODEC_ID_RA_288,
        .type      = AVMEDIA_TYPE_AUDIO,
        .name      = "ra_288",
        .long_name = NULL_IF_CONFIG_SMALL("RealAudio 2.0 (28.8K)"),
        .props     = AV_CODEC_PROP_LOSSY,
    },

    /* various DPCM codecs */
    {
        .id        = AV_CODEC_ID_ROQ_DPCM,
        .type      = AVMEDIA_TYPE_AUDIO,
        .name      = "roq_dpcm",
        .long_name = NULL_IF_CONFIG_SMALL("DPCM id RoQ"),
        .props     = AV_CODEC_PROP_LOSSY,
    },
    {
        .id        = AV_CODEC_ID_INTERPLAY_DPCM,
        .type      = AVMEDIA_TYPE_AUDIO,
        .name      = "interplay_dpcm",
        .long_name = NULL_IF_CONFIG_SMALL("DPCM Interplay"),
        .props     = AV_CODEC_PROP_LOSSY,
    },
    {
        .id        = AV_CODEC_ID_XAN_DPCM,
        .type      = AVMEDIA_TYPE_AUDIO,
        .name      = "xan_dpcm",
        .long_name = NULL_IF_CONFIG_SMALL("DPCM Xan"),
        .props     = AV_CODEC_PROP_LOSSY,
    },
    {
        .id        = AV_CODEC_ID_SOL_DPCM,
        .type      = AVMEDIA_TYPE_AUDIO,
        .name      = "sol_dpcm",
        .long_name = NULL_IF_CONFIG_SMALL("DPCM Sol"),
        .props     = AV_CODEC_PROP_LOSSY,
    },
    {
        .id        = AV_CODEC_ID_SDX2_DPCM,
        .type      = AVMEDIA_TYPE_AUDIO,
        .name      = "sdx2_dpcm",
        .long_name = NULL_IF_CONFIG_SMALL("DPCM Squareroot-Delta-Exact"),
        .props     = AV_CODEC_PROP_LOSSY,
    },
    {
        .id        = AV_CODEC_ID_GREMLIN_DPCM,
        .type      = AVMEDIA_TYPE_AUDIO,
        .name      = "gremlin_dpcm",
        .long_name = NULL_IF_CONFIG_SMALL("DPCM Gremlin"),
        .props     = AV_CODEC_PROP_LOSSY,
    },

    /* audio codecs */
    {
        .id        = AV_CODEC_ID_MP2,
        .type      = AVMEDIA_TYPE_AUDIO,
        .name      = "mp2",
        .long_name = NULL_IF_CONFIG_SMALL("MP2 (MPEG audio layer 2)"),
        .props     = AV_CODEC_PROP_LOSSY,
    },
    {
        .id        = AV_CODEC_ID_MP3,
        .type      = AVMEDIA_TYPE_AUDIO,
        .name      = "mp3",
        .long_name = NULL_IF_CONFIG_SMALL("MP3 (MPEG audio layer 3)"),
        .props     = AV_CODEC_PROP_LOSSY,
    },
    {
        .id        = AV_CODEC_ID_AAC,
        .type      = AVMEDIA_TYPE_AUDIO,
        .name      = "aac",
        .long_name = NULL_IF_CONFIG_SMALL("AAC (Advanced Audio Coding)"),
        .props     = AV_CODEC_PROP_LOSSY,
        .profiles  = NULL_IF_CONFIG_SMALL(ff_aac_profiles),
    },
    {
        .id        = AV_CODEC_ID_AC3,
        .type      = AVMEDIA_TYPE_AUDIO,
        .name      = "ac3",
        .long_name = NULL_IF_CONFIG_SMALL("ATSC A/52A (AC-3)"),
        .props     = AV_CODEC_PROP_LOSSY,
    },
    {
        .id        = AV_CODEC_ID_DTS,
        .type      = AVMEDIA_TYPE_AUDIO,
        .name      = "dts",
        .long_name = NULL_IF_CONFIG_SMALL("DCA (DTS Coherent Acoustics)"),
        .props     = AV_CODEC_PROP_LOSSY | AV_CODEC_PROP_LOSSLESS,
        .profiles  = NULL_IF_CONFIG_SMALL(ff_dca_profiles),
    },
    {
        .id        = AV_CODEC_ID_VORBIS,
        .type      = AVMEDIA_TYPE_AUDIO,
        .name      = "vorbis",
        .long_name = NULL_IF_CONFIG_SMALL("Vorbis"),
        .props     = AV_CODEC_PROP_LOSSY,
    },
    {
        .id        = AV_CODEC_ID_DVAUDIO,
        .type      = AVMEDIA_TYPE_AUDIO,
        .name      = "dvaudio",
        .long_name = NULL_IF_CONFIG_SMALL("DV audio"),
        .props     = AV_CODEC_PROP_LOSSY,
    },
    {
        .id        = AV_CODEC_ID_WMAV1,
        .type      = AVMEDIA_TYPE_AUDIO,
        .name      = "wmav1",
        .long_name = NULL_IF_CONFIG_SMALL("Windows Media Audio 1"),
        .props     = AV_CODEC_PROP_LOSSY,
    },
    {
        .id        = AV_CODEC_ID_WMAV2,
        .type      = AVMEDIA_TYPE_AUDIO,
        .name      = "wmav2",
        .long_name = NULL_IF_CONFIG_SMALL("Windows Media Audio 2"),
        .props     = AV_CODEC_PROP_LOSSY,
    },
    {
        .id        = AV_CODEC_ID_MACE3,
        .type      = AVMEDIA_TYPE_AUDIO,
        .name      = "mace3",
        .long_name = NULL_IF_CONFIG_SMALL("MACE (Macintosh Audio Compression/Expansion) 3:1"),
        .props     = AV_CODEC_PROP_LOSSY,
    },
    {
        .id        = AV_CODEC_ID_MACE6,
        .type      = AVMEDIA_TYPE_AUDIO,
        .name      = "mace6",
        .long_name = NULL_IF_CONFIG_SMALL("MACE (Macintosh Audio Compression/Expansion) 6:1"),
        .props     = AV_CODEC_PROP_LOSSY,
    },
    {
        .id        = AV_CODEC_ID_VMDAUDIO,
        .type      = AVMEDIA_TYPE_AUDIO,
        .name      = "vmdaudio",
        .long_name = NULL_IF_CONFIG_SMALL("Sierra VMD audio"),
        .props     = AV_CODEC_PROP_LOSSY,
    },
    {
        .id        = AV_CODEC_ID_FLAC,
        .type      = AVMEDIA_TYPE_AUDIO,
        .name      = "flac",
        .long_name = NULL_IF_CONFIG_SMALL("FLAC (Free Lossless Audio Codec)"),
        .props     = AV_CODEC_PROP_INTRA_ONLY | AV_CODEC_PROP_LOSSLESS,
    },
    {
        .id        = AV_CODEC_ID_MP3ADU,
        .type      = AVMEDIA_TYPE_AUDIO,
        .name      = "mp3adu",
        .long_name = NULL_IF_CONFIG_SMALL("ADU (Application Data Unit) MP3 (MPEG audio layer 3)"),
        .props     = AV_CODEC_PROP_LOSSY,
    },
    {
        .id        = AV_CODEC_ID_MP3ON4,
        .type      = AVMEDIA_TYPE_AUDIO,
        .name      = "mp3on4",
        .long_name = NULL_IF_CONFIG_SMALL("MP3onMP4"),
        .props     = AV_CODEC_PROP_LOSSY,
    },
    {
        .id        = AV_CODEC_ID_SHORTEN,
        .type      = AVMEDIA_TYPE_AUDIO,
        .name      = "shorten",
        .long_name = NULL_IF_CONFIG_SMALL("Shorten"),
        .props     = AV_CODEC_PROP_LOSSLESS,
    },
    {
        .id        = AV_CODEC_ID_ALAC,
        .type      = AVMEDIA_TYPE_AUDIO,
        .name      = "alac",
        .long_name = NULL_IF_CONFIG_SMALL("ALAC (Apple Lossless Audio Codec)"),
        .props     = AV_CODEC_PROP_INTRA_ONLY | AV_CODEC_PROP_LOSSLESS,
    },
    {
        .id        = AV_CODEC_ID_WESTWOOD_SND1,
        .type      = AVMEDIA_TYPE_AUDIO,
        .name      = "westwood_snd1",
        .long_name = NULL_IF_CONFIG_SMALL("Westwood Audio (SND1)"),
        .props     = AV_CODEC_PROP_LOSSY,
    },
    {
        .id        = AV_CODEC_ID_GSM,
        .type      = AVMEDIA_TYPE_AUDIO,
        .name      = "gsm",
        .long_name = NULL_IF_CONFIG_SMALL("GSM"),
        .props     = AV_CODEC_PROP_LOSSY,
    },
    {
        .id        = AV_CODEC_ID_QDM2,
        .type      = AVMEDIA_TYPE_AUDIO,
        .name      = "qdm2",
        .long_name = NULL_IF_CONFIG_SMALL("QDesign Music Codec 2"),
        .props     = AV_CODEC_PROP_LOSSY,
    },
    {
        .id        = AV_CODEC_ID_COOK,
        .type      = AVMEDIA_TYPE_AUDIO,
        .name      = "cook",
        .long_name = NULL_IF_CONFIG_SMALL("Cook / Cooker / Gecko (RealAudio G2)"),
        .props     = AV_CODEC_PROP_LOSSY,
    },
    {
        .id        = AV_CODEC_ID_TRUESPEECH,
        .type      = AVMEDIA_TYPE_AUDIO,
        .name      = "truespeech",
        .long_name = NULL_IF_CONFIG_SMALL("DSP Group TrueSpeech"),
        .props     = AV_CODEC_PROP_LOSSY,
    },
    {
        .id        = AV_CODEC_ID_TTA,
        .type      = AVMEDIA_TYPE_AUDIO,
        .name      = "tta",
        .long_name = NULL_IF_CONFIG_SMALL("TTA (True Audio)"),
        .props     = AV_CODEC_PROP_INTRA_ONLY | AV_CODEC_PROP_LOSSLESS,
    },
    {
        .id        = AV_CODEC_ID_SMACKAUDIO,
        .type      = AVMEDIA_TYPE_AUDIO,
        .name      = "smackaudio",
        .long_name = NULL_IF_CONFIG_SMALL("Smacker audio"),
        .props     = AV_CODEC_PROP_LOSSY,
    },
    {
        .id        = AV_CODEC_ID_QCELP,
        .type      = AVMEDIA_TYPE_AUDIO,
        .name      = "qcelp",
        .long_name = NULL_IF_CONFIG_SMALL("QCELP / PureVoice"),
        .props     = AV_CODEC_PROP_LOSSY,
    },
    {
        .id        = AV_CODEC_ID_WAVPACK,
        .type      = AVMEDIA_TYPE_AUDIO,
        .name      = "wavpack",
        .long_name = NULL_IF_CONFIG_SMALL("WavPack"),
        .props     = AV_CODEC_PROP_INTRA_ONLY |
                     AV_CODEC_PROP_LOSSY | AV_CODEC_PROP_LOSSLESS,
    },
    {
        .id        = AV_CODEC_ID_DSICINAUDIO,
        .type      = AVMEDIA_TYPE_AUDIO,
        .name      = "dsicinaudio",
        .long_name = NULL_IF_CONFIG_SMALL("Delphine Software International CIN audio"),
        .props     = AV_CODEC_PROP_LOSSY,
    },
    {
        .id        = AV_CODEC_ID_IMC,
        .type      = AVMEDIA_TYPE_AUDIO,
        .name      = "imc",
        .long_name = NULL_IF_CONFIG_SMALL("IMC (Intel Music Coder)"),
        .props     = AV_CODEC_PROP_LOSSY,
    },
    {
        .id        = AV_CODEC_ID_MUSEPACK7,
        .type      = AVMEDIA_TYPE_AUDIO,
        .name      = "musepack7",
        .long_name = NULL_IF_CONFIG_SMALL("Musepack SV7"),
        .props     = AV_CODEC_PROP_LOSSY,
    },
    {
        .id        = AV_CODEC_ID_MLP,
        .type      = AVMEDIA_TYPE_AUDIO,
        .name      = "mlp",
        .long_name = NULL_IF_CONFIG_SMALL("MLP (Meridian Lossless Packing)"),
        .props     = AV_CODEC_PROP_LOSSLESS,
    },
    {
        .id        = AV_CODEC_ID_GSM_MS,
        .type      = AVMEDIA_TYPE_AUDIO,
        .name      = "gsm_ms",
        .long_name = NULL_IF_CONFIG_SMALL("GSM Microsoft variant"),
        .props     = AV_CODEC_PROP_LOSSY,
    },
    {
        .id        = AV_CODEC_ID_ATRAC3,
        .type      = AVMEDIA_TYPE_AUDIO,
        .name      = "atrac3",
        .long_name = NULL_IF_CONFIG_SMALL("ATRAC3 (Adaptive TRansform Acoustic Coding 3)"),
        .props     = AV_CODEC_PROP_LOSSY,
    },
    {
        .id        = AV_CODEC_ID_APE,
        .type      = AVMEDIA_TYPE_AUDIO,
        .name      = "ape",
        .long_name = NULL_IF_CONFIG_SMALL("Monkey's Audio"),
        .props     = AV_CODEC_PROP_LOSSLESS,
    },
    {
        .id        = AV_CODEC_ID_NELLYMOSER,
        .type      = AVMEDIA_TYPE_AUDIO,
        .name      = "nellymoser",
        .long_name = NULL_IF_CONFIG_SMALL("Nellymoser Asao"),
        .props     = AV_CODEC_PROP_LOSSY,
    },
    {
        .id        = AV_CODEC_ID_MUSEPACK8,
        .type      = AVMEDIA_TYPE_AUDIO,
        .name      = "musepack8",
        .long_name = NULL_IF_CONFIG_SMALL("Musepack SV8"),
        .props     = AV_CODEC_PROP_LOSSY,
    },
    {
        .id        = AV_CODEC_ID_SPEEX,
        .type      = AVMEDIA_TYPE_AUDIO,
        .name      = "speex",
        .long_name = NULL_IF_CONFIG_SMALL("Speex"),
        .props     = AV_CODEC_PROP_LOSSY,
    },
    {
        .id        = AV_CODEC_ID_WMAVOICE,
        .type      = AVMEDIA_TYPE_AUDIO,
        .name      = "wmavoice",
        .long_name = NULL_IF_CONFIG_SMALL("Windows Media Audio Voice"),
        .props     = AV_CODEC_PROP_LOSSY,
    },
    {
        .id        = AV_CODEC_ID_WMAPRO,
        .type      = AVMEDIA_TYPE_AUDIO,
        .name      = "wmapro",
        .long_name = NULL_IF_CONFIG_SMALL("Windows Media Audio 9 Professional"),
        .props     = AV_CODEC_PROP_LOSSY,
    },
    {
        .id        = AV_CODEC_ID_WMALOSSLESS,
        .type      = AVMEDIA_TYPE_AUDIO,
        .name      = "wmalossless",
        .long_name = NULL_IF_CONFIG_SMALL("Windows Media Audio Lossless"),
        .props     = AV_CODEC_PROP_LOSSLESS,
    },
    {
        .id        = AV_CODEC_ID_ATRAC3P,
        .type      = AVMEDIA_TYPE_AUDIO,
        .name      = "atrac3p",
        .long_name = NULL_IF_CONFIG_SMALL("ATRAC3+ (Adaptive TRansform Acoustic Coding 3+)"),
        .props     = AV_CODEC_PROP_LOSSY,
    },
    {
        .id        = AV_CODEC_ID_EAC3,
        .type      = AVMEDIA_TYPE_AUDIO,
        .name      = "eac3",
        .long_name = NULL_IF_CONFIG_SMALL("ATSC A/52B (AC-3, E-AC-3)"),
        .props     = AV_CODEC_PROP_LOSSY,
    },
    {
        .id        = AV_CODEC_ID_SIPR,
        .type      = AVMEDIA_TYPE_AUDIO,
        .name      = "sipr",
        .long_name = NULL_IF_CONFIG_SMALL("RealAudio SIPR / ACELP.NET"),
        .props     = AV_CODEC_PROP_LOSSY,
    },
    {
        .id        = AV_CODEC_ID_MP1,
        .type      = AVMEDIA_TYPE_AUDIO,
        .name      = "mp1",
        .long_name = NULL_IF_CONFIG_SMALL("MP1 (MPEG audio layer 1)"),
        .props     = AV_CODEC_PROP_LOSSY,
    },
    {
        .id        = AV_CODEC_ID_TWINVQ,
        .type      = AVMEDIA_TYPE_AUDIO,
        .name      = "twinvq",
        .long_name = NULL_IF_CONFIG_SMALL("VQF TwinVQ"),
        .props     = AV_CODEC_PROP_LOSSY,
    },
    {
        .id        = AV_CODEC_ID_TRUEHD,
        .type      = AVMEDIA_TYPE_AUDIO,
        .name      = "truehd",
        .long_name = NULL_IF_CONFIG_SMALL("TrueHD"),
        .props     = AV_CODEC_PROP_LOSSLESS,
    },
    {
        .id        = AV_CODEC_ID_MP4ALS,
        .type      = AVMEDIA_TYPE_AUDIO,
        .name      = "mp4als",
        .long_name = NULL_IF_CONFIG_SMALL("MPEG-4 Audio Lossless Coding (ALS)"),
        .props     = AV_CODEC_PROP_LOSSLESS,
    },
    {
        .id        = AV_CODEC_ID_ATRAC1,
        .type      = AVMEDIA_TYPE_AUDIO,
        .name      = "atrac1",
        .long_name = NULL_IF_CONFIG_SMALL("ATRAC1 (Adaptive TRansform Acoustic Coding)"),
        .props     = AV_CODEC_PROP_LOSSY,
    },
    {
        .id        = AV_CODEC_ID_BINKAUDIO_RDFT,
        .type      = AVMEDIA_TYPE_AUDIO,
        .name      = "binkaudio_rdft",
        .long_name = NULL_IF_CONFIG_SMALL("Bink Audio (RDFT)"),
        .props     = AV_CODEC_PROP_LOSSY,
    },
    {
        .id        = AV_CODEC_ID_BINKAUDIO_DCT,
        .type      = AVMEDIA_TYPE_AUDIO,
        .name      = "binkaudio_dct",
        .long_name = NULL_IF_CONFIG_SMALL("Bink Audio (DCT)"),
        .props     = AV_CODEC_PROP_LOSSY,
    },
    {
        .id        = AV_CODEC_ID_AAC_LATM,
        .type      = AVMEDIA_TYPE_AUDIO,
        .name      = "aac_latm",
        .long_name = NULL_IF_CONFIG_SMALL("AAC LATM (Advanced Audio Coding LATM syntax)"),
        .props     = AV_CODEC_PROP_LOSSY,
        .profiles  = NULL_IF_CONFIG_SMALL(ff_aac_profiles),
    },
    {
        .id        = AV_CODEC_ID_QDMC,
        .type      = AVMEDIA_TYPE_AUDIO,
        .name      = "qdmc",
        .long_name = NULL_IF_CONFIG_SMALL("QDesign Music"),
        .props     = AV_CODEC_PROP_LOSSY,
    },
    {
        .id        = AV_CODEC_ID_CELT,
        .type      = AVMEDIA_TYPE_AUDIO,
        .name      = "celt",
        .long_name = NULL_IF_CONFIG_SMALL("Constrained Energy Lapped Transform (CELT)"),
        .props     = AV_CODEC_PROP_LOSSY,
    },
    {
        .id        = AV_CODEC_ID_G723_1,
        .type      = AVMEDIA_TYPE_AUDIO,
        .name      = "g723_1",
        .long_name = NULL_IF_CONFIG_SMALL("G.723.1"),
        .props     = AV_CODEC_PROP_LOSSY,
    },
    {
        .id        = AV_CODEC_ID_G729,
        .type      = AVMEDIA_TYPE_AUDIO,
        .name      = "g729",
        .long_name = NULL_IF_CONFIG_SMALL("G.729"),
        .props     = AV_CODEC_PROP_LOSSY,
    },
    {
        .id        = AV_CODEC_ID_8SVX_EXP,
        .type      = AVMEDIA_TYPE_AUDIO,
        .name      = "8svx_exp",
        .long_name = NULL_IF_CONFIG_SMALL("8SVX exponential"),
        .props     = AV_CODEC_PROP_LOSSY,
    },
    {
        .id        = AV_CODEC_ID_8SVX_FIB,
        .type      = AVMEDIA_TYPE_AUDIO,
        .name      = "8svx_fib",
        .long_name = NULL_IF_CONFIG_SMALL("8SVX fibonacci"),
        .props     = AV_CODEC_PROP_LOSSY,
    },
    {
        .id        = AV_CODEC_ID_BMV_AUDIO,
        .type      = AVMEDIA_TYPE_AUDIO,
        .name      = "bmv_audio",
        .long_name = NULL_IF_CONFIG_SMALL("Discworld II BMV audio"),
        .props     = AV_CODEC_PROP_LOSSY,
    },
    {
        .id        = AV_CODEC_ID_RALF,
        .type      = AVMEDIA_TYPE_AUDIO,
        .name      = "ralf",
        .long_name = NULL_IF_CONFIG_SMALL("RealAudio Lossless"),
        .props     = AV_CODEC_PROP_LOSSLESS,
    },
    {
        .id        = AV_CODEC_ID_IAC,
        .type      = AVMEDIA_TYPE_AUDIO,
        .name      = "iac",
        .long_name = NULL_IF_CONFIG_SMALL("IAC (Indeo Audio Coder)"),
        .props     = AV_CODEC_PROP_LOSSY,
    },
    {
        .id        = AV_CODEC_ID_ILBC,
        .type      = AVMEDIA_TYPE_AUDIO,
        .name      = "ilbc",
        .long_name = NULL_IF_CONFIG_SMALL("iLBC (Internet Low Bitrate Codec)"),
        .props     = AV_CODEC_PROP_LOSSY,
    },
    {
        .id        = AV_CODEC_ID_OPUS,
        .type      = AVMEDIA_TYPE_AUDIO,
        .name      = "opus",
        .long_name = NULL_IF_CONFIG_SMALL("Opus (Opus Interactive Audio Codec)"),
        .props     = AV_CODEC_PROP_LOSSY,
    },
    {
        .id        = AV_CODEC_ID_COMFORT_NOISE,
        .type      = AVMEDIA_TYPE_AUDIO,
        .name      = "comfortnoise",
        .long_name = NULL_IF_CONFIG_SMALL("RFC 3389 Comfort Noise"),
        .props     = AV_CODEC_PROP_LOSSY,
    },
    {
        .id        = AV_CODEC_ID_TAK,
        .type      = AVMEDIA_TYPE_AUDIO,
        .name      = "tak",
        .long_name = NULL_IF_CONFIG_SMALL("TAK (Tom's lossless Audio Kompressor)"),
        .props     = AV_CODEC_PROP_INTRA_ONLY | AV_CODEC_PROP_LOSSLESS,
    },
    {
        .id        = AV_CODEC_ID_METASOUND,
        .type      = AVMEDIA_TYPE_AUDIO,
        .name      = "metasound",
        .long_name = NULL_IF_CONFIG_SMALL("Voxware MetaSound"),
        .props     = AV_CODEC_PROP_LOSSY,
    },
    {
        .id        = AV_CODEC_ID_PAF_AUDIO,
        .type      = AVMEDIA_TYPE_AUDIO,
        .name      = "paf_audio",
        .long_name = NULL_IF_CONFIG_SMALL("Amazing Studio Packed Animation File Audio"),
        .props     = AV_CODEC_PROP_LOSSY,
    },
    {
        .id        = AV_CODEC_ID_ON2AVC,
        .type      = AVMEDIA_TYPE_AUDIO,
        .name      = "avc",
        .long_name = NULL_IF_CONFIG_SMALL("On2 Audio for Video Codec"),
        .props     = AV_CODEC_PROP_LOSSY,
    },
    {
        .id        = AV_CODEC_ID_DSS_SP,
        .type      = AVMEDIA_TYPE_AUDIO,
        .name      = "dss_sp",
        .long_name = NULL_IF_CONFIG_SMALL("Digital Speech Standard - Standard Play mode (DSS SP)"),
        .props     = AV_CODEC_PROP_LOSSY,
    },
    {
        .id        = AV_CODEC_ID_CODEC2,
        .type      = AVMEDIA_TYPE_AUDIO,
        .name      = "codec2",
        .long_name = NULL_IF_CONFIG_SMALL("codec2 (very low bitrate speech codec)"),
        .props     = AV_CODEC_PROP_LOSSY,
    },
    {
        .id        = AV_CODEC_ID_FFWAVESYNTH,
        .type      = AVMEDIA_TYPE_AUDIO,
        .name      = "wavesynth",
        .long_name = NULL_IF_CONFIG_SMALL("Wave synthesis pseudo-codec"),
    },
    {
        .id        = AV_CODEC_ID_SONIC,
        .type      = AVMEDIA_TYPE_AUDIO,
        .name      = "sonic",
        .long_name = NULL_IF_CONFIG_SMALL("Sonic"),
    },
    {
        .id        = AV_CODEC_ID_SONIC_LS,
        .type      = AVMEDIA_TYPE_AUDIO,
        .name      = "sonicls",
        .long_name = NULL_IF_CONFIG_SMALL("Sonic lossless"),
    },
    {
        .id        = AV_CODEC_ID_EVRC,
        .type      = AVMEDIA_TYPE_AUDIO,
        .name      = "evrc",
        .long_name = NULL_IF_CONFIG_SMALL("EVRC (Enhanced Variable Rate Codec)"),
        .props     = AV_CODEC_PROP_LOSSY,
    },
    {
        .id        = AV_CODEC_ID_SMV,
        .type      = AVMEDIA_TYPE_AUDIO,
        .name      = "smv",
        .long_name = NULL_IF_CONFIG_SMALL("SMV (Selectable Mode Vocoder)"),
        .props     = AV_CODEC_PROP_LOSSY,
    },
    {
        .id        = AV_CODEC_ID_DSD_LSBF,
        .type      = AVMEDIA_TYPE_AUDIO,
        .name      = "dsd_lsbf",
        .long_name = NULL_IF_CONFIG_SMALL("DSD (Direct Stream Digital), least significant bit first"),
        .props     = AV_CODEC_PROP_LOSSY,
    },
    {
        .id        = AV_CODEC_ID_DSD_MSBF,
        .type      = AVMEDIA_TYPE_AUDIO,
        .name      = "dsd_msbf",
        .long_name = NULL_IF_CONFIG_SMALL("DSD (Direct Stream Digital), most significant bit first"),
        .props     = AV_CODEC_PROP_LOSSY,
    },
    {
        .id        = AV_CODEC_ID_DSD_LSBF_PLANAR,
        .type      = AVMEDIA_TYPE_AUDIO,
        .name      = "dsd_lsbf_planar",
        .long_name = NULL_IF_CONFIG_SMALL("DSD (Direct Stream Digital), least significant bit first, planar"),
        .props     = AV_CODEC_PROP_LOSSY,
    },
    {
        .id        = AV_CODEC_ID_DSD_MSBF_PLANAR,
        .type      = AVMEDIA_TYPE_AUDIO,
        .name      = "dsd_msbf_planar",
        .long_name = NULL_IF_CONFIG_SMALL("DSD (Direct Stream Digital), most significant bit first, planar"),
        .props     = AV_CODEC_PROP_LOSSY,
    },
    {
        .id        = AV_CODEC_ID_4GV,
        .type      = AVMEDIA_TYPE_AUDIO,
        .name      = "4gv",
        .long_name = NULL_IF_CONFIG_SMALL("4GV (Fourth Generation Vocoder)"),
        .props     = AV_CODEC_PROP_LOSSY,
    },
    {
        .id        = AV_CODEC_ID_INTERPLAY_ACM,
        .type      = AVMEDIA_TYPE_AUDIO,
        .name      = "interplayacm",
        .long_name = NULL_IF_CONFIG_SMALL("Interplay ACM"),
        .props     = AV_CODEC_PROP_LOSSY,
    },
    {
        .id        = AV_CODEC_ID_XMA1,
        .type      = AVMEDIA_TYPE_AUDIO,
        .name      = "xma1",
        .long_name = NULL_IF_CONFIG_SMALL("Xbox Media Audio 1"),
        .props     = AV_CODEC_PROP_LOSSY,
    },
    {
        .id        = AV_CODEC_ID_XMA2,
        .type      = AVMEDIA_TYPE_AUDIO,
        .name      = "xma2",
        .long_name = NULL_IF_CONFIG_SMALL("Xbox Media Audio 2"),
        .props     = AV_CODEC_PROP_LOSSY,
    },
    {
        .id        = AV_CODEC_ID_DST,
        .type      = AVMEDIA_TYPE_AUDIO,
        .name      = "dst",
        .long_name = NULL_IF_CONFIG_SMALL("DST (Direct Stream Transfer)"),
        .props     = AV_CODEC_PROP_LOSSLESS,
    },
    {
        .id        = AV_CODEC_ID_ATRAC3AL,
        .type      = AVMEDIA_TYPE_AUDIO,
        .name      = "atrac3al",
        .long_name = NULL_IF_CONFIG_SMALL("ATRAC3 AL (Adaptive TRansform Acoustic Coding 3 Advanced Lossless)"),
        .props     = AV_CODEC_PROP_LOSSLESS,
    },
    {
        .id        = AV_CODEC_ID_ATRAC3PAL,
        .type      = AVMEDIA_TYPE_AUDIO,
        .name      = "atrac3pal",
        .long_name = NULL_IF_CONFIG_SMALL("ATRAC3+ AL (Adaptive TRansform Acoustic Coding 3+ Advanced Lossless)"),
        .props     = AV_CODEC_PROP_LOSSLESS,
    },
    {
        .id        = AV_CODEC_ID_DOLBY_E,
        .type      = AVMEDIA_TYPE_AUDIO,
        .name      = "dolby_e",
        .long_name = NULL_IF_CONFIG_SMALL("Dolby E"),
        .props     = AV_CODEC_PROP_LOSSY,
    },
    {
        .id        = AV_CODEC_ID_APTX,
        .type      = AVMEDIA_TYPE_AUDIO,
        .name      = "aptx",
        .long_name = NULL_IF_CONFIG_SMALL("aptX (Audio Processing Technology for Bluetooth)"),
        .props     = AV_CODEC_PROP_LOSSY,
    },
    {
        .id        = AV_CODEC_ID_APTX_HD,
        .type      = AVMEDIA_TYPE_AUDIO,
        .name      = "aptx_hd",
        .long_name = NULL_IF_CONFIG_SMALL("aptX HD (Audio Processing Technology for Bluetooth)"),
        .props     = AV_CODEC_PROP_LOSSY,
    },
    {
        .id        = AV_CODEC_ID_SBC,
        .type      = AVMEDIA_TYPE_AUDIO,
        .name      = "sbc",
        .long_name = NULL_IF_CONFIG_SMALL("SBC (low-complexity subband codec)"),
        .props     = AV_CODEC_PROP_LOSSY,
    },
    {
        .id        = AV_CODEC_ID_ATRAC9,
        .type      = AVMEDIA_TYPE_AUDIO,
        .name      = "atrac9",
        .long_name = NULL_IF_CONFIG_SMALL("ATRAC9 (Adaptive TRansform Acoustic Coding 9)"),
        .props     = AV_CODEC_PROP_LOSSY,
    },

    /* subtitle codecs */
    {
        .id        = AV_CODEC_ID_DVD_SUBTITLE,
        .type      = AVMEDIA_TYPE_SUBTITLE,
        .name      = "dvd_subtitle",
        .long_name = NULL_IF_CONFIG_SMALL("DVD subtitles"),
        .props     = AV_CODEC_PROP_BITMAP_SUB,
    },
    {
        .id        = AV_CODEC_ID_DVB_SUBTITLE,
        .type      = AVMEDIA_TYPE_SUBTITLE,
        .name      = "dvb_subtitle",
        .long_name = NULL_IF_CONFIG_SMALL("DVB subtitles"),
        .props     = AV_CODEC_PROP_BITMAP_SUB,
    },
    {
        .id        = AV_CODEC_ID_TEXT,
        .type      = AVMEDIA_TYPE_SUBTITLE,
        .name      = "text",
        .long_name = NULL_IF_CONFIG_SMALL("raw UTF-8 text"),
        .props     = AV_CODEC_PROP_TEXT_SUB,
    },
    {
        .id        = AV_CODEC_ID_XSUB,
        .type      = AVMEDIA_TYPE_SUBTITLE,
        .name      = "xsub",
        .long_name = NULL_IF_CONFIG_SMALL("XSUB"),
        .props     = AV_CODEC_PROP_BITMAP_SUB,
    },
    {
        .id        = AV_CODEC_ID_SSA,
        .type      = AVMEDIA_TYPE_SUBTITLE,
        .name      = "ssa",
        .long_name = NULL_IF_CONFIG_SMALL("SSA (SubStation Alpha) subtitle"),
        .props     = AV_CODEC_PROP_TEXT_SUB,
    },
    {
        .id        = AV_CODEC_ID_MOV_TEXT,
        .type      = AVMEDIA_TYPE_SUBTITLE,
        .name      = "mov_text",
        .long_name = NULL_IF_CONFIG_SMALL("MOV text"),
        .props     = AV_CODEC_PROP_TEXT_SUB,
    },
    {
        .id        = AV_CODEC_ID_HDMV_PGS_SUBTITLE,
        .type      = AVMEDIA_TYPE_SUBTITLE,
        .name      = "hdmv_pgs_subtitle",
        .long_name = NULL_IF_CONFIG_SMALL("HDMV Presentation Graphic Stream subtitles"),
        .props     = AV_CODEC_PROP_BITMAP_SUB,
    },
    {
        .id        = AV_CODEC_ID_DVB_TELETEXT,
        .type      = AVMEDIA_TYPE_SUBTITLE,
        .name      = "dvb_teletext",
        .long_name = NULL_IF_CONFIG_SMALL("DVB teletext"),
    },
    {
        .id        = AV_CODEC_ID_SRT,
        .type      = AVMEDIA_TYPE_SUBTITLE,
        .name      = "srt",
        .long_name = NULL_IF_CONFIG_SMALL("SubRip subtitle with embedded timing"),
        .props     = AV_CODEC_PROP_TEXT_SUB,
    },
    {
        .id        = AV_CODEC_ID_MICRODVD,
        .type      = AVMEDIA_TYPE_SUBTITLE,
        .name      = "microdvd",
        .long_name = NULL_IF_CONFIG_SMALL("MicroDVD subtitle"),
        .props     = AV_CODEC_PROP_TEXT_SUB,
    },
    {
        .id        = AV_CODEC_ID_EIA_608,
        .type      = AVMEDIA_TYPE_SUBTITLE,
        .name      = "eia_608",
        .long_name = NULL_IF_CONFIG_SMALL("EIA-608 closed captions"),
        .props     = AV_CODEC_PROP_TEXT_SUB,
    },
    {
        .id        = AV_CODEC_ID_JACOSUB,
        .type      = AVMEDIA_TYPE_SUBTITLE,
        .name      = "jacosub",
        .long_name = NULL_IF_CONFIG_SMALL("JACOsub subtitle"),
        .props     = AV_CODEC_PROP_TEXT_SUB,
    },
    {
        .id        = AV_CODEC_ID_SAMI,
        .type      = AVMEDIA_TYPE_SUBTITLE,
        .name      = "sami",
        .long_name = NULL_IF_CONFIG_SMALL("SAMI subtitle"),
        .props     = AV_CODEC_PROP_TEXT_SUB,
    },
    {
        .id        = AV_CODEC_ID_REALTEXT,
        .type      = AVMEDIA_TYPE_SUBTITLE,
        .name      = "realtext",
        .long_name = NULL_IF_CONFIG_SMALL("RealText subtitle"),
        .props     = AV_CODEC_PROP_TEXT_SUB,
    },
    {
        .id        = AV_CODEC_ID_STL,
        .type      = AVMEDIA_TYPE_SUBTITLE,
        .name      = "stl",
        .long_name = NULL_IF_CONFIG_SMALL("Spruce subtitle format"),
        .props     = AV_CODEC_PROP_TEXT_SUB,
    },
    {
        .id        = AV_CODEC_ID_SUBVIEWER1,
        .type      = AVMEDIA_TYPE_SUBTITLE,
        .name      = "subviewer1",
        .long_name = NULL_IF_CONFIG_SMALL("SubViewer v1 subtitle"),
        .props     = AV_CODEC_PROP_TEXT_SUB,
    },
    {
        .id        = AV_CODEC_ID_SUBVIEWER,
        .type      = AVMEDIA_TYPE_SUBTITLE,
        .name      = "subviewer",
        .long_name = NULL_IF_CONFIG_SMALL("SubViewer subtitle"),
        .props     = AV_CODEC_PROP_TEXT_SUB,
    },
    {
        .id        = AV_CODEC_ID_SUBRIP,
        .type      = AVMEDIA_TYPE_SUBTITLE,
        .name      = "subrip",
        .long_name = NULL_IF_CONFIG_SMALL("SubRip subtitle"),
        .props     = AV_CODEC_PROP_TEXT_SUB,
    },
    {
        .id        = AV_CODEC_ID_WEBVTT,
        .type      = AVMEDIA_TYPE_SUBTITLE,
        .name      = "webvtt",
        .long_name = NULL_IF_CONFIG_SMALL("WebVTT subtitle"),
        .props     = AV_CODEC_PROP_TEXT_SUB,
    },
    {
        .id        = AV_CODEC_ID_MPL2,
        .type      = AVMEDIA_TYPE_SUBTITLE,
        .name      = "mpl2",
        .long_name = NULL_IF_CONFIG_SMALL("MPL2 subtitle"),
        .props     = AV_CODEC_PROP_TEXT_SUB,
    },
    {
        .id        = AV_CODEC_ID_VPLAYER,
        .type      = AVMEDIA_TYPE_SUBTITLE,
        .name      = "vplayer",
        .long_name = NULL_IF_CONFIG_SMALL("VPlayer subtitle"),
        .props     = AV_CODEC_PROP_TEXT_SUB,
    },
    {
        .id        = AV_CODEC_ID_PJS,
        .type      = AVMEDIA_TYPE_SUBTITLE,
        .name      = "pjs",
        .long_name = NULL_IF_CONFIG_SMALL("PJS (Phoenix Japanimation Society) subtitle"),
        .props     = AV_CODEC_PROP_TEXT_SUB,
    },
    {
        .id        = AV_CODEC_ID_ASS,
        .type      = AVMEDIA_TYPE_SUBTITLE,
        .name      = "ass",
        .long_name = NULL_IF_CONFIG_SMALL("ASS (Advanced SSA) subtitle"),
        .props     = AV_CODEC_PROP_TEXT_SUB,
    },
    {
        .id        = AV_CODEC_ID_HDMV_TEXT_SUBTITLE,
        .type      = AVMEDIA_TYPE_SUBTITLE,
        .name      = "hdmv_text_subtitle",
        .long_name = NULL_IF_CONFIG_SMALL("HDMV Text subtitle"),
        .props     = AV_CODEC_PROP_TEXT_SUB,
    },
    {
        .id        = AV_CODEC_ID_TTML,
        .type      = AVMEDIA_TYPE_SUBTITLE,
        .name      = "ttml",
        .long_name = NULL_IF_CONFIG_SMALL("Timed Text Markup Language"),
        .props     = AV_CODEC_PROP_TEXT_SUB,
    },


    /* other kind of codecs and pseudo-codecs */
    {
        .id        = AV_CODEC_ID_TTF,
        .type      = AVMEDIA_TYPE_DATA,
        .name      = "ttf",
        .long_name = NULL_IF_CONFIG_SMALL("TrueType font"),
        .mime_types= MT("application/x-truetype-font", "application/x-font"),
    },
    {
        .id        = AV_CODEC_ID_SCTE_35,
        .type      = AVMEDIA_TYPE_DATA,
        .name      = "scte_35",
        .long_name = NULL_IF_CONFIG_SMALL("SCTE 35 Message Queue"),
    },
    {
        .id        = AV_CODEC_ID_BINTEXT,
        .type      = AVMEDIA_TYPE_VIDEO,
        .name      = "bintext",
        .long_name = NULL_IF_CONFIG_SMALL("Binary text"),
        .props     = AV_CODEC_PROP_INTRA_ONLY,
    },
    {
        .id        = AV_CODEC_ID_XBIN,
        .type      = AVMEDIA_TYPE_VIDEO,
        .name      = "xbin",
        .long_name = NULL_IF_CONFIG_SMALL("eXtended BINary text"),
        .props     = AV_CODEC_PROP_INTRA_ONLY,
    },
    {
        .id        = AV_CODEC_ID_IDF,
        .type      = AVMEDIA_TYPE_VIDEO,
        .name      = "idf",
        .long_name = NULL_IF_CONFIG_SMALL("iCEDraw text"),
        .props     = AV_CODEC_PROP_INTRA_ONLY,
    },
    {
        .id        = AV_CODEC_ID_OTF,
        .type      = AVMEDIA_TYPE_DATA,
        .name      = "otf",
        .long_name = NULL_IF_CONFIG_SMALL("OpenType font"),
        .mime_types= MT("application/vnd.ms-opentype"),
    },
    {
        .id        = AV_CODEC_ID_SMPTE_KLV,
        .type      = AVMEDIA_TYPE_DATA,
        .name      = "klv",
        .long_name = NULL_IF_CONFIG_SMALL("SMPTE 336M Key-Length-Value (KLV) metadata"),
    },
    {
        .id        = AV_CODEC_ID_DVD_NAV,
        .type      = AVMEDIA_TYPE_DATA,
        .name      = "dvd_nav_packet",
        .long_name = NULL_IF_CONFIG_SMALL("DVD Nav packet"),
    },
    {
        .id        = AV_CODEC_ID_TIMED_ID3,
        .type      = AVMEDIA_TYPE_DATA,
        .name      = "timed_id3",
        .long_name = NULL_IF_CONFIG_SMALL("timed ID3 metadata"),
    },
    {
        .id        = AV_CODEC_ID_BIN_DATA,
        .type      = AVMEDIA_TYPE_DATA,
        .name      = "bin_data",
        .long_name = NULL_IF_CONFIG_SMALL("binary data"),
        .mime_types= MT("application/octet-stream"),
    },
    {
        .id        = AV_CODEC_ID_WRAPPED_AVFRAME,
        .type      = AVMEDIA_TYPE_VIDEO,
        .name      = "wrapped_avframe",
        .long_name = NULL_IF_CONFIG_SMALL("AVFrame to AVPacket passthrough"),
        .props     = AV_CODEC_PROP_LOSSLESS,
    },
};

static int descriptor_compare(const void *key, const void *member)
{
    enum AVCodecID id = *(const enum AVCodecID *) key;
    const AVCodecDescriptor *desc = member;

    return id - desc->id;
}

const AVCodecDescriptor *avcodec_descriptor_get(enum AVCodecID id)
{
    return bsearch(&id, codec_descriptors, FF_ARRAY_ELEMS(codec_descriptors),
                   sizeof(codec_descriptors[0]), descriptor_compare);
}

const AVCodecDescriptor *avcodec_descriptor_next(const AVCodecDescriptor *prev)
{
    if (!prev)
        return &codec_descriptors[0];
    if (prev - codec_descriptors < FF_ARRAY_ELEMS(codec_descriptors) - 1)
        return prev + 1;
    return NULL;
}

const AVCodecDescriptor *avcodec_descriptor_get_by_name(const char *name)
{
    const AVCodecDescriptor *desc = NULL;

    while ((desc = avcodec_descriptor_next(desc)))
        if (!strcmp(desc->name, name))
            return desc;
    return NULL;
}

enum AVMediaType avcodec_get_type(enum AVCodecID codec_id)
{
    const AVCodecDescriptor *desc = avcodec_descriptor_get(codec_id);
    return desc ? desc->type : AVMEDIA_TYPE_UNKNOWN;
}
