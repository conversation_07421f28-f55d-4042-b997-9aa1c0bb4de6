/*
 * Windows resource file for libswresample
 *
 * Copyright (C) 2012 <PERSON>
 * Copyright (C) 2013 Tiancheng "Timothy" Gu
 *
 * This file is part of FFmpeg.
 *
 * FFmpeg is free software; you can redistribute it and/or
 * modify it under the terms of the GNU Lesser General Public
 * License as published by the Free Software Foundation; either
 * version 2.1 of the License, or (at your option) any later version.
 *
 * FFmpeg is distributed in the hope that it will be useful,
 * but WITHOUT ANY WARRANTY; without even the implied warranty of
 * MERCHANTABILITY or FITNESS FOR A PARTICULAR PURPOSE.  See the GNU
 * Lesser General Public License for more details.
 *
 * You should have received a copy of the GNU Lesser General Public
 * License along with FFmpeg; if not, write to the Free Software
 * Foundation, Inc., 51 Franklin Street, Fifth Floor, Boston, MA 02110-1301 USA
 */

#include <windows.h>
#include "libswresample/version.h"
#include "libavutil/ffversion.h"
#include "config.h"

1 VERSIONINFO
FILEVERSION     LIBSWRESAMPLE_VERSION_MAJOR, LIBSWRESAMPLE_VERSION_MINOR, LIBSWRESAMPLE_VERSION_MICRO, 0
PRODUCTVERSION  LIBSWRESAMPLE_VERSION_MAJOR, LIBSWRESAMPLE_VERSION_MINOR, LIBSWRESAMPLE_VERSION_MICRO, 0
FILEFLAGSMASK   VS_FFI_FILEFLAGSMASK
FILEOS          VOS_NT_WINDOWS32
FILETYPE        VFT_DLL
{
    BLOCK "StringFileInfo"
    {
        BLOCK "040904B0"
        {
            VALUE "CompanyName",      "FFmpeg Project"
            VALUE "FileDescription",  "FFmpeg audio resampling library"
            VALUE "FileVersion",      AV_STRINGIFY(LIBSWRESAMPLE_VERSION)
            VALUE "InternalName",     "libswresample"
            VALUE "LegalCopyright",   "Copyright (C) 2000-" AV_STRINGIFY(CONFIG_THIS_YEAR) " FFmpeg Project"
            VALUE "OriginalFilename", "swresample" BUILDSUF "-" AV_STRINGIFY(LIBSWRESAMPLE_VERSION_MAJOR) SLIBSUF
            VALUE "ProductName",      "FFmpeg"
            VALUE "ProductVersion",   FFMPEG_VERSION
        }
    }

    BLOCK "VarFileInfo"
    {
        VALUE "Translation", 0x0409, 0x04B0
    }
}
