//go:build !go1.8
// +build !go1.8

package ws

import "crypto/tls"

func tlsCloneConfig(c *tls.Config) *tls.Config {
	// NOTE: we copying SessionTicketsDisabled and SessionTicketKey here
	// without calling inner c.initOnceServer somehow because we only could get
	// here from the ws.Dialer code, which is obviously a client and makes
	// tls.Client() when it gets new net.Conn.
	return &tls.Config{
		Rand:                        c.Rand,
		Time:                        c.Time,
		Certificates:                c.Certificates,
		NameToCertificate:           c.NameToCertificate,
		GetCertificate:              c.GetCertificate,
		RootCAs:                     c.RootCAs,
		NextProtos:                  c.NextProtos,
		ServerName:                  c.ServerName,
		ClientAuth:                  c.<PERSON>,
		ClientCAs:                   c.ClientCAs,
		InsecureSkipVerify:          c.InsecureSkipVerify,
		CipherSuites:                c.CipherSuites,
		PreferServerCipherSuites:    c.PreferServerCipherSuites,
		SessionTicketsDisabled:      c.SessionTicketsDisabled,
		SessionTicketKey:            c.<PERSON>,
		ClientSessionCache:          c.ClientSessionCache,
		MinVersion:                  c.<PERSON>ion,
		MaxVersion:                  c.MaxVersion,
		CurvePreferences:            c.CurvePreferences,
		DynamicRecordSizingDisabled: c.DynamicRecordSizingDisabled,
		Renegotiation:               c.Renegotiation,
	}
}
