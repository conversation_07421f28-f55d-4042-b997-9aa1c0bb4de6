// SPDX-FileCopyrightText: 2023 The Pion community <https://pion.ly>
// SPDX-License-Identifier: MIT

//go:build !js
// +build !js

package webrtc

// A Configuration defines how peer-to-peer communication via PeerConnection
// is established or re-established.
// Configurations may be set up once and reused across multiple connections.
// Configurations are treated as readonly. As long as they are unmodified,
// they are safe for concurrent use.
type Configuration struct {
	// ICEServers defines a slice describing servers available to be used by
	// ICE, such as STUN and TURN servers.
	ICEServers []ICEServer `json:"iceServers,omitempty"`

	// ICETransportPolicy indicates which candidates the ICEAgent is allowed
	// to use.
	ICETransportPolicy ICETransportPolicy `json:"iceTransportPolicy,omitempty"`

	// BundlePolicy indicates which media-bundling policy to use when gathering
	// ICE candidates.
	BundlePolicy BundlePolicy `json:"bundlePolicy,omitempty"`

	// RTCPMuxPolicy indicates which rtcp-mux policy to use when gathering ICE
	// candidates.
	RTCPMuxPolicy RTCPMuxPolicy `json:"rtcpMuxPolicy,omitempty"`

	// PeerIdentity sets the target peer identity for the PeerConnection.
	// The PeerConnection will not establish a connection to a remote peer
	// unless it can be successfully authenticated with the provided name.
	PeerIdentity string `json:"peerIdentity,omitempty"`

	// Certificates describes a set of certificates that the PeerConnection
	// uses to authenticate. Valid values for this parameter are created
	// through calls to the GenerateCertificate function. Although any given
	// DTLS connection will use only one certificate, this attribute allows the
	// caller to provide multiple certificates that support different
	// algorithms. The final certificate will be selected based on the DTLS
	// handshake, which establishes which certificates are allowed. The
	// PeerConnection implementation selects which of the certificates is
	// used for a given connection; how certificates are selected is outside
	// the scope of this specification. If this value is absent, then a default
	// set of certificates is generated for each PeerConnection instance.
	Certificates []Certificate `json:"certificates,omitempty"`

	// ICECandidatePoolSize describes the size of the prefetched ICE pool.
	ICECandidatePoolSize uint8 `json:"iceCandidatePoolSize,omitempty"`

	// SDPSemantics controls the type of SDP offers accepted by and
	// SDP answers generated by the PeerConnection.
	SDPSemantics SDPSemantics `json:"sdpSemantics,omitempty"`
}
