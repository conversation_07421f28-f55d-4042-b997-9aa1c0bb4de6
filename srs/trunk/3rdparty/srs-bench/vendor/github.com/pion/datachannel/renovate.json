{"extends": ["config:base", ":disableDependencyDashboard"], "postUpdateOptions": ["gomodTidy"], "commitBody": "Generated by renovateBot", "packageRules": [{"matchUpdateTypes": ["minor", "patch", "pin", "digest"], "automerge": true}, {"packagePatterns": ["^golang.org/x/"], "schedule": ["on the first day of the month"]}], "ignorePaths": [".github/workflows/generate-authors.yml", ".github/workflows/lint.yaml", ".github/workflows/renovate-go-mod-fix.yaml", ".github/workflows/test.yaml", ".github/workflows/tidy-check.yaml"]}