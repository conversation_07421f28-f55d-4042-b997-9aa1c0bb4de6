#
# DO NOT EDIT THIS FILE
#
# It is automatically copied from https://github.com/pion/.goassets repository.
# If this repository should have package specific CI config,
# remove the repository name from .goassets/.github/workflows/assets-sync.yml.
#
# If you want to update the shared CI config, send a PR to
# https://github.com/pion/.goassets instead of this repository.
#

dist: bionic
language: go


branches:
  only:
  - master

env:
  global:
    - GO111MODULE=on
    - GOLANGCI_LINT_VERSION=1.19.1

cache:
  directories:
    - ${HOME}/.cache/go-build
    - ${GOPATH}/pkg/mod
  npm: true
  yarn: true

_lint_job: &lint_job
  env: CACHE_NAME=lint
  before_install:
    - if [ -f .github/.ci.conf ]; then . .github/.ci.conf; fi
  install: skip
  before_script:
    - |
      curl -sSfL https://raw.githubusercontent.com/golangci/golangci-lint/master/install.sh \
        | bash -s - -b $GOPATH/bin v${GOLANGCI_LINT_VERSION}
  script:
    - bash .github/assert-contributors.sh
    - bash .github/lint-disallowed-functions-in-library.sh
    - bash .github/lint-commit-message.sh
    - bash .github/lint-filename.sh
    - golangci-lint run ./...
_test_job: &test_job
  env: CACHE_NAME=test
  before_install:
    - if [ -f .github/.ci.conf ]; then . .github/.ci.conf; fi
    - go mod download
  install:
    - go build ./...
  script:
    # If you want to specify repository specific test packages rule,
    # add `TEST_PACKAGES=$(command to list test target packages)` to .github/.ci.conf
    - testpkgs=${TEST_PACKAGES:-$(go list ./... | grep -v examples)}
    - coverpkgs=$(echo "${testpkgs}" | paste -s -d ',')
    - |
      go test \
        -coverpkg=${coverpkgs} -coverprofile=cover.out -covermode=atomic \
        ${TEST_EXTRA_ARGS:-} \
        -v -race ${testpkgs}
    - if [ -n "${TEST_HOOK}" ]; then ${TEST_HOOK}; fi
  after_success:
    - travis_retry bash <(curl -s https://codecov.io/bash) -c -F go
_test_i386_job: &test_i386_job
  env: CACHE_NAME=test386
  services: docker
  before_install:
    - if [ -f .github/.ci.conf ]; then . .github/.ci.conf; fi
  script:
    - testpkgs=${TEST_PACKAGES:-$(go list ./... | grep -v examples)}
    - |
      docker run \
        -u $(id -u):$(id -g) \
        -e "GO111MODULE=on" \
        -e "CGO_ENABLED=0" \
        -v ${PWD}:/go/src/github.com/pion/$(basename ${PWD}) \
        -v ${HOME}/gopath/pkg/mod:/go/pkg/mod \
        -v ${HOME}/.cache/go-build:/.cache/go-build \
        -w /go/src/github.com/pion/$(basename ${PWD}) \
        -it i386/golang:${GO_VERSION}-alpine \
        /usr/local/go/bin/go test \
          ${TEST_EXTRA_ARGS:-} \
          -v ${testpkgs}
_test_wasm_job: &test_wasm_job
  env: CACHE_NAME=wasm
  language: node_js
  node_js: 12
  before_install:
    - if [ -f .github/.ci.conf ]; then . .github/.ci.conf; fi
    - if ${SKIP_WASM_TEST:-false}; then exit 0; fi
  install:
    # Manually download and install Go instead of using gimme.
    # It looks like gimme Go causes some errors on go-test for Wasm.
    - curl -sSfL https://dl.google.com/go/go${GO_VERSION}.linux-amd64.tar.gz | tar -C ~ -xzf -
    - export GOROOT=${HOME}/go
    - export PATH=${GOROOT}/bin:${PATH}
    - yarn install
    - export GO_JS_WASM_EXEC=${GO_JS_WASM_EXEC:-${GOROOT}/misc/wasm/go_js_wasm_exec}
  script:
    - testpkgs=${TEST_PACKAGES:-$(go list ./... | grep -v examples)}
    - coverpkgs=$(echo "${testpkgs}" | paste -s -d ',')
    - |
      GOOS=js GOARCH=wasm go test \
        -coverpkg=${coverpkgs} -coverprofile=cover.out -covermode=atomic \
        -exec="${GO_JS_WASM_EXEC}" \
        -v ${testpkgs}
  after_success:
    - travis_retry bash <(curl -s https://codecov.io/bash) -c -F wasm

jobs:
  include:
    - <<: *lint_job
      name: Lint 1.14
      go: 1.14
    - <<: *test_job
      name: Test 1.13
      go: 1.13
    - <<: *test_job
      name: Test 1.14
      go: 1.14
    - <<: *test_i386_job
      name: Test i386 1.13
      env: GO_VERSION=1.13
      go: 1.14  # Go version for host environment only for `go list`.
                # All tests are done on the version specified by GO_VERSION.
    - <<: *test_i386_job
      name: Test i386 1.14
      env: GO_VERSION=1.14
      go: 1.14  # Go version for host environment only for `go list`.
                # All tests are done on the version specified by GO_VERSION.
    - <<: *test_wasm_job
      name: Test WASM 1.13
      env: GO_VERSION=1.13
    - <<: *test_wasm_job
      name: Test WASM 1.14
      env: GO_VERSION=1.14

notifications:
  email: false
