# Thank you to everyone that made <PERSON><PERSON> possible. If you are interested in contributing
# we would love to have you https://github.com/pion/webrtc/wiki/Contributing
#
# This file is auto generated, using git to list all individuals contributors.
# see https://github.com/pion/.goassets/blob/master/scripts/generate-authors.sh for the scripting
<PERSON> <<EMAIL>>
al<PERSON><PERSON><PERSON> <alvarowolf<PERSON>@gmail.com>
A<PERSON><PERSON> <<EMAIL>>
Atsushi Watanabe <<EMAIL>>
backkem <<EMAIL>>
bjdgyc <<EMAIL>>
boks1971 <<EMAIL>>
Braga<PERSON>esh <<EMAIL>>
<PERSON> <<EMAIL>>
<PERSON><PERSON><PERSON> <<EMAIL>>
<PERSON> <<EMAIL>>
cnderrauber <<EMAIL>>
<PERSON><PERSON> <<EMAIL>>
folbrich <<EMAIL>>
<PERSON> <<EMAIL>>
<PERSON> <<EMAIL>>
<PERSON> <<EMAIL>>
igolaizola <<EMAIL>>
Jeffrey Stoke <<EMAIL>>
Jeroen de Bruijn <<EMAIL>>
Jeroen de Bruijn <<EMAIL>>
Jim Wert <<EMAIL>>
jinleileiking <<EMAIL>>
Jozef Kralik <<EMAIL>>
Julien Salleyron <<EMAIL>>
Juliusz Chroboczek <<EMAIL>>
Kegan Dougal <<EMAIL>>
Kevin Wang <<EMAIL>>
Lander Noterman <<EMAIL>>
Len <<EMAIL>>
Lukas Lihotzki <<EMAIL>>
ManuelBk <<EMAIL>>
Michael Zabka <<EMAIL>>
Michiel De Backker <<EMAIL>>
Rachel Chen <<EMAIL>>
Robert Eperjesi <<EMAIL>>
Ryan Gordon <<EMAIL>>
Sam Lancia <<EMAIL>>
Sean DuBois <<EMAIL>>
Sean DuBois <<EMAIL>>
Sean DuBois <<EMAIL>>
Shelikhoo <<EMAIL>>
Stefan Tatschner <<EMAIL>>
Steffen Vogel <<EMAIL>>
Vadim <<EMAIL>>
Vadim Filimonov <<EMAIL>>
wmiao <<EMAIL>>
ZHENK <<EMAIL>>
吕海涛 <<EMAIL>>

# List of contributors not appearing in Git history

