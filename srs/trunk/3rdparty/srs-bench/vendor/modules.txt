# github.com/davecgh/go-spew v1.1.1
## explicit
github.com/davecgh/go-spew/spew
# github.com/ghettovoice/gosip v0.0.0-20220929080231-de8ba881be83
## explicit; go 1.13
github.com/ghettovoice/gosip/log
github.com/ghettovoice/gosip/sip
github.com/ghettovoice/gosip/sip/parser
github.com/ghettovoice/gosip/timing
github.com/ghettovoice/gosip/transport
github.com/ghettovoice/gosip/util
# github.com/gobwas/httphead v0.1.0
## explicit; go 1.15
github.com/gobwas/httphead
# github.com/gobwas/pool v0.2.1
## explicit
github.com/gobwas/pool
github.com/gobwas/pool/internal/pmath
github.com/gobwas/pool/pbufio
github.com/gobwas/pool/pbytes
# github.com/gobwas/ws v1.1.0-rc.1
## explicit; go 1.15
github.com/gobwas/ws
github.com/gobwas/ws/wsutil
# github.com/google/uuid v1.3.0
## explicit
github.com/google/uuid
# github.com/konsorten/go-windows-terminal-sequences v1.0.2
## explicit
github.com/konsorten/go-windows-terminal-sequences
# github.com/mattn/go-colorable v0.1.4
## explicit
github.com/mattn/go-colorable
# github.com/mattn/go-isatty v0.0.8
## explicit
github.com/mattn/go-isatty
# github.com/mgutz/ansi v0.0.0-20170206155736-9520e82c474b
## explicit
github.com/mgutz/ansi
# github.com/ossrs/go-oryx-lib v0.0.9
## explicit
github.com/ossrs/go-oryx-lib/aac
github.com/ossrs/go-oryx-lib/amf0
github.com/ossrs/go-oryx-lib/avc
github.com/ossrs/go-oryx-lib/errors
github.com/ossrs/go-oryx-lib/flv
github.com/ossrs/go-oryx-lib/http
github.com/ossrs/go-oryx-lib/logger
github.com/ossrs/go-oryx-lib/rtmp
# github.com/pion/datachannel v1.5.5
## explicit; go 1.13
github.com/pion/datachannel
# github.com/pion/dtls/v2 v2.2.7
## explicit; go 1.13
github.com/pion/dtls/v2
github.com/pion/dtls/v2/internal/ciphersuite
github.com/pion/dtls/v2/internal/ciphersuite/types
github.com/pion/dtls/v2/internal/closer
github.com/pion/dtls/v2/internal/util
github.com/pion/dtls/v2/pkg/crypto/ccm
github.com/pion/dtls/v2/pkg/crypto/ciphersuite
github.com/pion/dtls/v2/pkg/crypto/clientcertificate
github.com/pion/dtls/v2/pkg/crypto/elliptic
github.com/pion/dtls/v2/pkg/crypto/fingerprint
github.com/pion/dtls/v2/pkg/crypto/hash
github.com/pion/dtls/v2/pkg/crypto/prf
github.com/pion/dtls/v2/pkg/crypto/signature
github.com/pion/dtls/v2/pkg/crypto/signaturehash
github.com/pion/dtls/v2/pkg/protocol
github.com/pion/dtls/v2/pkg/protocol/alert
github.com/pion/dtls/v2/pkg/protocol/extension
github.com/pion/dtls/v2/pkg/protocol/handshake
github.com/pion/dtls/v2/pkg/protocol/recordlayer
# github.com/pion/ice/v2 v2.3.6
## explicit; go 1.13
github.com/pion/ice/v2
github.com/pion/ice/v2/internal/atomic
github.com/pion/ice/v2/internal/fakenet
github.com/pion/ice/v2/internal/stun
# github.com/pion/interceptor v0.1.17
## explicit; go 1.15
github.com/pion/interceptor
github.com/pion/interceptor/internal/ntp
github.com/pion/interceptor/pkg/nack
github.com/pion/interceptor/pkg/report
github.com/pion/interceptor/pkg/twcc
# github.com/pion/logging v0.2.2
## explicit; go 1.12
github.com/pion/logging
# github.com/pion/mdns v0.0.7
## explicit; go 1.12
github.com/pion/mdns
# github.com/pion/randutil v0.1.0
## explicit; go 1.14
github.com/pion/randutil
# github.com/pion/rtcp v1.2.10
## explicit; go 1.13
github.com/pion/rtcp
# github.com/pion/rtp v1.7.13
## explicit; go 1.13
github.com/pion/rtp
github.com/pion/rtp/codecs
github.com/pion/rtp/pkg/frame
github.com/pion/rtp/pkg/obu
# github.com/pion/sctp v1.8.7
## explicit; go 1.13
github.com/pion/sctp
# github.com/pion/sdp/v3 v3.0.6
## explicit; go 1.13
github.com/pion/sdp/v3
# github.com/pion/srtp/v2 v2.0.15
## explicit; go 1.14
github.com/pion/srtp/v2
# github.com/pion/stun v0.6.0
## explicit; go 1.12
github.com/pion/stun
github.com/pion/stun/internal/hmac
# github.com/pion/transport/v2 v2.2.1
## explicit; go 1.12
github.com/pion/transport/v2
github.com/pion/transport/v2/connctx
github.com/pion/transport/v2/deadline
github.com/pion/transport/v2/packetio
github.com/pion/transport/v2/replaydetector
github.com/pion/transport/v2/stdnet
github.com/pion/transport/v2/udp
github.com/pion/transport/v2/utils/xor
github.com/pion/transport/v2/vnet
# github.com/pion/turn/v2 v2.1.0
## explicit; go 1.13
github.com/pion/turn/v2
github.com/pion/turn/v2/internal/allocation
github.com/pion/turn/v2/internal/client
github.com/pion/turn/v2/internal/ipnet
github.com/pion/turn/v2/internal/proto
github.com/pion/turn/v2/internal/server
# github.com/pion/webrtc/v3 v3.2.9
## explicit; go 1.13
github.com/pion/webrtc/v3
github.com/pion/webrtc/v3/internal/fmtp
github.com/pion/webrtc/v3/internal/mux
github.com/pion/webrtc/v3/internal/util
github.com/pion/webrtc/v3/pkg/media
github.com/pion/webrtc/v3/pkg/media/h264reader
github.com/pion/webrtc/v3/pkg/media/h264writer
github.com/pion/webrtc/v3/pkg/media/ivfwriter
github.com/pion/webrtc/v3/pkg/media/oggreader
github.com/pion/webrtc/v3/pkg/media/oggwriter
github.com/pion/webrtc/v3/pkg/rtcerr
# github.com/pkg/errors v0.9.1
## explicit
github.com/pkg/errors
# github.com/pmezard/go-difflib v1.0.0
## explicit
github.com/pmezard/go-difflib/difflib
# github.com/satori/go.uuid v1.2.1-0.20181028125025-b2ce2384e17b
## explicit
github.com/satori/go.uuid
# github.com/sirupsen/logrus v1.4.2
## explicit
github.com/sirupsen/logrus
# github.com/stretchr/testify v1.8.4
## explicit; go 1.20
github.com/stretchr/testify/assert
github.com/stretchr/testify/require
# github.com/tevino/abool v0.0.0-20170917061928-9b9efcf221b5
## explicit
github.com/tevino/abool
# github.com/x-cray/logrus-prefixed-formatter v0.5.2
## explicit
github.com/x-cray/logrus-prefixed-formatter
# github.com/yapingcat/gomedia/codec v0.0.0-20220617074658-94762898dc25
## explicit; go 1.16
github.com/yapingcat/gomedia/codec
# github.com/yapingcat/gomedia/mpeg2 v0.0.0-20220617074658-94762898dc25
## explicit; go 1.16
github.com/yapingcat/gomedia/mpeg2
# golang.org/x/crypto v0.9.0
## explicit; go 1.17
golang.org/x/crypto/cryptobyte
golang.org/x/crypto/cryptobyte/asn1
golang.org/x/crypto/curve25519
golang.org/x/crypto/curve25519/internal/field
golang.org/x/crypto/ssh/terminal
# golang.org/x/net v0.10.0
## explicit; go 1.17
golang.org/x/net/bpf
golang.org/x/net/dns/dnsmessage
golang.org/x/net/internal/iana
golang.org/x/net/internal/socket
golang.org/x/net/internal/socks
golang.org/x/net/ipv4
golang.org/x/net/proxy
# golang.org/x/sys v0.8.0
## explicit; go 1.17
golang.org/x/sys/cpu
golang.org/x/sys/internal/unsafeheader
golang.org/x/sys/plan9
golang.org/x/sys/unix
golang.org/x/sys/windows
# golang.org/x/term v0.8.0
## explicit; go 1.17
golang.org/x/term
# gopkg.in/yaml.v3 v3.0.1
## explicit
gopkg.in/yaml.v3
