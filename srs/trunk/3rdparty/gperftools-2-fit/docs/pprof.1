.\" DO NOT MODIFY THIS FILE!  It was generated by help2man 1.23.
.TH PPROF "1" "February 2005" "pprof (part of gperftools)" Google
.SH NAME
pprof \- manual page for pprof (part of gperftools)
.SH SYNOPSIS
.B pprof
[\fIoptions\fR] \fI<program> <profile>\fR
.SH DESCRIPTION
.IP
Prints specified cpu- or heap-profile
.SH OPTIONS
.TP
\fB\-\-cum\fR
Sort by cumulative data
.TP
\fB\-\-base=\fR<base>
Subtract <base> from <profile> before display
.SS "Reporting Granularity:"
.TP
\fB\-\-addresses\fR
Report at address level
.TP
\fB\-\-lines\fR
Report at source line level
.TP
\fB\-\-functions\fR
Report at function level [default]
.TP
\fB\-\-files\fR
Report at source file level
.SS "Output type:"
.TP
\fB\-\-text\fR
Generate text report [default]
.TP
\fB\-\-gv\fR
Generate Postscript and display
.TP
\fB\-\-list=\fR<regexp>
Generate source listing of matching routines
.TP
\fB\-\-disasm=\fR<regexp>
Generate disassembly of matching routines
.TP
\fB\-\-dot\fR
Generate DOT file to stdout
.TP
\fB\-\-ps\fR
Generate Postscript to stdout
.TP
\fB\-\-pdf\fR
Generate PDF to stdout
.TP
\fB\-\-gif\fR
Generate GIF to stdout
.SS "Heap-Profile Options:"
.TP
\fB\-\-inuse_space\fR
Display in-use (mega)bytes [default]
.TP
\fB\-\-inuse_objects\fR
Display in-use objects
.TP
\fB\-\-alloc_space\fR
Display allocated (mega)bytes
.TP
\fB\-\-alloc_objects\fR
Display allocated objects
.TP
\fB\-\-show_bytes\fR
Display space in bytes
.TP
\fB\-\-drop_negative\fR
Ignore negaive differences
.SS "Call-graph Options:"
.TP
\fB\-\-nodecount=\fR<n>
Show at most so many nodes [default=80]
.TP
\fB\-\-nodefraction=\fR<f>
Hide nodes below <f>*total [default=.005]
.TP
\fB\-\-edgefraction=\fR<f>
Hide edges below <f>*total [default=.001]
.TP
\fB\-\-focus=\fR<regexp>
Focus on nodes matching <regexp>
.TP
\fB\-\-ignore=\fR<regexp>
Ignore nodes matching <regexp>
.TP
\fB\-\-scale=\fR<n>
Set GV scaling [default=0]
.SH EXAMPLES

pprof /bin/ls ls.prof
.IP
Outputs one line per procedure
.PP
pprof \fB\-\-gv\fR /bin/ls ls.prof
.IP
Displays annotated call-graph via 'gv'
.PP
pprof \fB\-\-gv\fR \fB\-\-focus\fR=\fIMutex\fR /bin/ls ls.prof
.IP
Restricts to code paths including a .*Mutex.* entry
.PP
pprof \fB\-\-gv\fR \fB\-\-focus\fR=\fIMutex\fR \fB\-\-ignore\fR=\fIstring\fR /bin/ls ls.prof
.IP
Code paths including Mutex but not string
.PP
pprof \fB\-\-list\fR=\fIgetdir\fR /bin/ls ls.prof
.IP
Dissassembly (with per-line annotations) for getdir()
.PP
pprof \fB\-\-disasm\fR=\fIgetdir\fR /bin/ls ls.prof
.IP
Dissassembly (with per-PC annotations) for getdir()
.SH COPYRIGHT
Copyright \(co 2005 Google Inc.
.SH "SEE ALSO"
Further documentation for
.B pprof
is maintained as a web page called
.B cpu_profiler.html
and is likely installed at one of the following locations:
.IP
.B /usr/share/gperftools/cpu_profiler.html
.br
.B /usr/local/share/gperftools/cpu_profiler.html
.PP
