<!DOCTYPE HTML PUBLIC "-//W3C//DTD HTML 4.01 Transitional//EN">
<HTML>

<HEAD>
  <link rel="stylesheet" href="designstyle.css">
  <title>Google CPU Profiler Binary Data File Format</title>
</HEAD>

<BODY>

<h1>Google CPU Profiler Binary Data File Format</h1>

<p align=right>
  <i>Last modified
    <script type=text/javascript>
    var lm = new Date(document.lastModified);
    document.write(lm.toDateString());
  </script></i>
</p>

<p>This file documents the binary data file format produced by the
Google CPU Profiler.  For information about using the CPU Profiler,
see <a href="cpuprofile.html">its user guide</a>.

<p>The profiler source code, which generates files using this format, is at
<code>src/profiler.cc</code></a>.


<h2>CPU Profile Data File Structure</h2>

<p>CPU profile data files each consist of four parts, in order:

<ul>
  <li> Binary header
  <li> Binary profile records
  <li> Binary trailer
  <li> Text list of mapped objects
</ul>

<p>The binary data is expressed in terms of "slots."  These are words
large enough to hold the program's pointer type, i.e., for 32-bit
programs they are 4 bytes in size, and for 64-bit programs they are 8
bytes.  They are stored in the profile data file in the native byte
order (i.e., little-endian for x86 and x86_64).


<h2>Binary Header</h2>

<p>The binary header format is show below.  Values written by the
profiler, along with requirements currently enforced by the analysis
tools, are shown in parentheses.

<p>
<table summary="Header Format"
       frame="box" rules="sides" cellpadding="5" width="50%">
  <tr>
    <th width="30%">slot</th>
    <th width="70%">data</th>
  </tr>

  <tr>
    <td>0</td>
    <td>header count (0; must be 0)</td>
  </tr>

  <tr>
    <td>1</td>
    <td>header slots after this one (3; must be &gt;= 3)</td>
  </tr>

  <tr>
    <td>2</td>
    <td>format version (0; must be 0)</td>
  </tr>

  <tr>
    <td>3</td>
    <td>sampling period, in microseconds</td>
  </tr>

  <tr>
    <td>4</td>
    <td>padding (0)</td>
  </tr>
</table>

<p>The headers currently generated for 32-bit and 64-bit little-endian
(x86 and x86_64) profiles are shown below, for comparison.

<p>
<table summary="Header Example" frame="box" rules="sides" cellpadding="5">
  <tr>
    <th></th>
    <th>hdr count</th>
    <th>hdr words</th>
    <th>version</th>
    <th>sampling period</th>
    <th>pad</th>
  </tr>
  <tr>
    <td>32-bit or 64-bit (slots)</td>
    <td>0</td>
    <td>3</td>
    <td>0</td>
    <td>10000</td>
    <td>0</td>
  </tr>
  <tr>
    <td>32-bit (4-byte words in file)</td>
    <td><tt>0x00000</tt></td>
    <td><tt>0x00003</tt></td>
    <td><tt>0x00000</tt></td>
    <td><tt>0x02710</tt></td>
    <td><tt>0x00000</tt></td>
  </tr>
  <tr>
    <td>64-bit LE (4-byte words in file)</td>
    <td><tt>0x00000&nbsp;0x00000</tt></td>
    <td><tt>0x00003&nbsp;0x00000</tt></td>
    <td><tt>0x00000&nbsp;0x00000</tt></td>
    <td><tt>0x02710&nbsp;0x00000</tt></td>
    <td><tt>0x00000&nbsp;0x00000</tt></td>
  </tr>
</table>

<p>The contents are shown in terms of slots, and in terms of 4-byte
words in the profile data file.  The slot contents for 32-bit and
64-bit headers are identical.  For 32-bit profiles, the 4-byte word
view matches the slot view.  For 64-bit profiles, each (8-byte) slot
is shown as two 4-byte words, ordered as they would appear in the
file.

<p>The profiling tools examine the contents of the file and use the
expected locations and values of the header words field to detect
whether the file is 32-bit or 64-bit.


<h2>Binary Profile Records</h2>

<p>The binary profile record format is shown below.

<p>
<table summary="Profile Record Format"
       frame="box" rules="sides" cellpadding="5" width="50%">
  <tr>
    <th width="30%">slot</th>
    <th width="70%">data</th>
  </tr>

  <tr>
    <td>0</td>
    <td>sample count, must be &gt;= 1</td>
  </tr>

  <tr>
    <td>1</td>
    <td>number of call chain PCs (num_pcs), must be &gt;= 1</td>
  </tr>

  <tr>
    <td>2 .. (num_pcs + 1)</td>
    <td>call chain PCs, most-recently-called function first.
  </tr>
</table>

<p>The total length of a given record is 2 + num_pcs.

<p>Note that multiple profile records can be emitted by the profiler
having an identical call chain.  In that case, analysis tools should
sum the counts of all records having identical call chains.

<p><b>Note:</b> Some profile analysis tools terminate if they see
<em>any</em> profile record with a call chain with its first entry
having the address 0.  (This is similar to the binary trailer.)

<h3>Example</h3>

This example shows the slots contained in a sample profile record.

<p>
<table summary="Profile Record Example"
       frame="box" rules="sides" cellpadding="5">
  <tr>
    <td>5</td>
    <td>3</td>
    <td>0xa0000</td>
    <td>0xc0000</td>
    <td>0xe0000</td>
  </tr>
</table>

<p>In this example, 5 ticks were received at PC 0xa0000, whose
function had been called by the function containing 0xc0000, which had
been called from the function containing 0xe0000.


<h2>Binary Trailer</h2>

<p>The binary trailer consists of three slots of data with fixed
values, shown below.

<p>
<table summary="Trailer Format"
       frame="box" rules="sides" cellpadding="5" width="50%">
  <tr>
    <th width="30%">slot</th>
    <th width="70%">value</th>
  </tr>

  <tr>
    <td>0</td>
    <td>0</td>
  </tr>

  <tr>
    <td>1</td>
    <td>1</td>
  </tr>

  <tr>
    <td>2</td>
    <td>0</td>
  </tr>
</table>

<p>Note that this is the same data that would contained in a profile
record with sample count = 0, num_pcs = 1, and a one-element call
chain containing the address 0.


<h2>Text List of Mapped Objects</h2>

<p>The binary data in the file is followed immediately by a list of
mapped objects.  This list consists of lines of text separated by
newline characters.

<p>Each line is one of the following types:

<ul>
  <li>Build specifier, starting with "<tt>build=</tt>".  For example:
    <pre>  build=/path/to/binary</pre>
    Leading spaces on the line are ignored.

  <li>Mapping line from ProcMapsIterator::FormatLine.  For example:
    <pre>  40000000-40015000 r-xp 00000000 03:01 12845071   /lib/ld-2.3.2.so</pre>
    The first address must start at the beginning of the line.
</ul>

<p>Unrecognized lines should be ignored by analysis tools.

<p>When processing the paths see in mapping lines, occurrences of
<tt>$build</tt> followed by a non-word character (i.e., characters
other than underscore or alphanumeric characters), should be replaced
by the path given on the last build specifier line.

<hr>
<address>Chris Demetriou<br>
<!-- Created: Mon Aug 27 12:18:26 PDT 2007 -->
<!-- hhmts start -->
Last modified: Mon Aug 27 12:18:26 PDT 2007  (cgd)
<!-- hhmts end -->
</address>
</BODY>
</HTML>
