commit f7c6fb6c8e99d6b1b725e5994373bcd19ffdf8fd
Author: <PERSON><PERSON><PERSON> <<EMAIL>>
Date:   Tue Mar 2 19:32:07 2021 -0800

    bump version to 2.9.1

M	CMakeLists.txt
M	NEWS
M	configure.ac
M	src/windows/config.h
M	src/windows/gperftools/tcmalloc.h

commit c2f60400a86a5211bf009941bf1ccf9b2947a7c1
Author: <PERSON><PERSON><PERSON> <<EMAIL>>
Date:	Sun Feb 28 17:52:17 2021 -0800

    prefer backtrace() on OSX

    It seems to work and _Unwind_Backtrace which is configured otherwise
    doesn't.

M	CMakeLists.txt
M	cmake/DefineTargetVariables.cmake
M	configure.ac

commit a015377a54eb09fca4b893ae530187a705164df5
Author: <PERSON><PERSON><PERSON> <<EMAIL>>
Date:	Sun Feb 28 17:47:56 2021 -0800

    Set tcmalloc heap limit prior to testing oom

    Otherwise it can take long time to OOM on osex.

M	src/tcmalloc.cc
M	src/tests/tcmalloc_unittest.cc

commit c939dd5531fd95e8af2181ac60e0b6e6230226c8
Author: Aliaksey Kandratsenka <<EMAIL>>
Date:	Sun Feb 28 15:42:00 2021 -0800

    correctly check sized delete hint when asserts are on

    We previously tested wrong assumption that larger than page size size
    classes have addresses aligned on page size. New code is making proper
    check of size class.

    Also added is unit test coverage for this previously failing
    condition. And we now also run "assert-ful" unittests for big tcmalloc
    too, not only tcmalloc_minimal configuration.

    This fixes github issue #1254

M	.gitignore
M	Makefile.am
M	src/tcmalloc.cc
M	src/tests/tcmalloc_unittest.cc

commit 47b5b59ca9b62ee42cf30273cce463aa9cbba8c7
Author: Aliaksey Kandratsenka <<EMAIL>>
Date:	Sun Feb 21 15:16:06 2021 -0800

    bump version to 2.9

M	CMakeLists.txt
M	NEWS
M	configure.ac
M	src/windows/config.h
M	src/windows/gperftools/tcmalloc.h

commit d7cbc8c2ff4ab8dbdc411ab1f25eca459f88502e
Author: Aliaksey Kandratsenka <<EMAIL>>
Date:	Sun Feb 21 14:55:15 2021 -0800

    unbreak cmake build

    It was trying to use third_party/valgrind.h header which I recently
    removed.

M	CMakeLists.txt

commit be0bbdb3403685798a7c1d5acf6405d2635fba4c
Author: Aliaksey Kandratsenka <<EMAIL>>
Date:	Sun Feb 21 13:48:11 2021 -0800

    amputate various unused bits from elfcore.h

M	src/base/elfcore.h

commit 42bab59f25dee53f91c0ba45e95b73754e197c20
Author: Aliaksey Kandratsenka <<EMAIL>>
Date:	Sun Feb 21 13:35:43 2021 -0800

    liberate profile handler from linux_syscall_support

    We can get thread's tid in more portable way via libc syscall wrapper.

M	src/profile-handler.cc

commit 4629511e99f85fa8ca40fcbe105e014a725059d2
Author: Aliaksey Kandratsenka <<EMAIL>>
Date:	Sun Feb 21 13:32:08 2021 -0800

    liberate spinlock futex waits from linux_syscall_support includes

    We've been using libc syscall wrapper some time now.

M	src/base/spinlock_linux-inl.h

commit 2e7094a86215118f3dcd3ad6f2c9099dc409ecc5
Author: Aliaksey Kandratsenka <<EMAIL>>
Date:	Sun Feb 21 13:28:48 2021 -0800

    liberate malloc_hook_mmap_linux.h from linux_syscall_support

    It was not used some time now, as we're rightfully trusting libc
    syscall wrapper instead.

M	src/malloc_hook_mmap_linux.h

commit 35301e2e59b30b7379981e60f3046124a2dfd68e
Author: Aliaksey Kandratsenka <<EMAIL>>
Date:	Sun Feb 21 12:40:45 2021 -0800

    add missing noopt wrappings around more operator new calls

    This fixes tests passing on clang which otherwise eliminates those
    new/delete calls so checking for hooks being called failed.

M	src/tests/tcmalloc_unittest.cc

commit fa412adfe38ffd3f545a0e10139bd20b38b688e9
Author: Venkatesh Srinivas <<EMAIL>>
Date:	Wed Feb 17 17:50:43 2021 -0800

    Fix thread-safety (annotalysis) annotations

    tcmalloc contains some thread-safety annotations; however those
    annotations have not been exercised for some time, as they used
    macros/attributes only supported by a legacy branch of gcc.

    Pull request #1251 converted those macros to support modern
    versions of clang; this CR fixes the annotations that were
    enabled. For the most part, this just requires re-enabling
    annotations on member functions that take/release locks. For the
    tcmalloc fork (pre-fork and post-fork) handlers, we mark the
    functions as exempt from this analysis, as it takes a dynamic
    number of locks.

M	src/base/low_level_alloc.cc
M	src/base/spinlock.h
M	src/central_freelist.h
M	src/static_vars.cc

commit cc496aecb81ee5966c865f3723743ff02046c5ad
Author: Venkatesh Srinivas <<EMAIL>>
Date:	Wed Feb 17 16:23:17 2021 -0800

    tcmalloc: Switch thread-safety annotations to support clang

    tcmalloc contains its own copy of thread_annotations.h, wrapper
    macros for static thread-safety analysis expressions. These thread-
    safety expressions allow asserting (at compile time) that certain
    locks are held or excluded or certain data is protected by specific
    locks; they are checked at compile-time by recent versions of clang
    or a gcc branch (https://gcc.gnu.org/wiki/ThreadSafetyAnnotation).

    Convert the #if-guard and macro names from the no-longer-supported
    gcc branch's defines & macros to the versions supported by recent
    versions of clang.

M	src/base/thread_annotations.h

commit 96ba58e19b7b93fcf1db6a0551c9510296ce26da
Author: Aliaksey Kandratsenka <<EMAIL>>
Date:	Sun Feb 14 23:18:57 2021 -0800

    bump version to 2.9rc

M	CMakeLists.txt
M	NEWS
M	configure.ac
M	src/windows/config.h
M	src/windows/gperftools/tcmalloc.h

commit 9ce32aefa9c66698817be50e141c5a39fe3823c8
Author: Aliaksey Kandratsenka <<EMAIL>>
Date:	Sun Feb 14 23:02:28 2021 -0800

    upgrade test bot to xenial (ubuntu 16.04 LTS)

    Older glibc seemingly doesn't have right unwind info for signal frame,
    so fails recently upgraded stacktrace_unittest. But since this version
    is not supported anymore, lets just test newer glibc.

M	.travis.yml

commit 91ff3114492f368a651ee790c8311eb6fadf3e8e
Author: Aliaksey Kandratsenka <<EMAIL>>
Date:	Sun Feb 14 22:36:55 2021 -0800

    don't default to generic_fp without frame pointers

    I.e. on x86-64 and riscv.

M	src/stacktrace.cc

commit 4cf7dd0a7502849b117e15ad5eb1eb68eab4b6dc
Author: Aliaksey Kandratsenka <<EMAIL>>
Date:	Sun Feb 14 22:28:34 2021 -0800

    enable emergency_malloc on all architectures with mmap

    It is actually needed for libgcc backtracer from time to time. And
    we've seen libunwind to need it too. Plus we've not heard of any
    problems with it. So lets just always enable it.

    This should fix github issue #1248.

M	configure.ac

commit 37087ec5360e821627155d3129f9b55a321a0001
Author: Aliaksey Kandratsenka <<EMAIL>>
Date:	Sun Feb 14 21:37:20 2021 -0800

    prefer libunwind on x86-64 even with -fno-omit-frame-pointer

    Libunwind is mostly safe on x86-64 and most of OS (e.g. libc) is
    compiled without frame pointers anyways. Even when libc is built with
    frame pointers, various libc asm routines (e.g. memcpy) are not. And
    even for code built with frame pointers we actually sometimes get
    SIGPROF inside function prologues, before frame pointer is
    established. Which then causes imperfect backtraces.

    Libunwind should handle this all (when it doesn't crash or deadlock).

M	src/stacktrace.cc

commit f4aa2a435eed63fc047448635f705a9c6037bd97
Author: Aliaksey Kandratsenka <<EMAIL>>
Date:	Sun Feb 7 11:59:44 2021 -0800

    implement generic frame pointer backtracer

    This supports frame pointer backtracing on x86-64, aarch64 and
    riscv-s (should work for both 32 and 64 bits).

    Also added is detection of borked libunwind on aarch64-s. In this case
    frame pointer unwinder is preferred.

M	Makefile.am
M	src/stacktrace.cc
A	src/stacktrace_generic_fp-inl.h
M	src/tests/stacktrace_unittest.cc

commit 17bab484aea43cf1a5247c823e036dfb52f5d92b
Author: Aliaksey Kandratsenka <<EMAIL>>
Date:	Sat Feb 6 13:21:12 2021 -0800

    always respect --enable-frame-pointers

    Previously it only was respected on x86_64, but this days lots
    of modern
    ABIs are without frame pointers by default (e.g. arm64 and riscv, and
    even older mips).

M	CMakeLists.txt
M	Makefile.am
D	cmake/CheckNoFPByDefault.cmake
M	configure.ac

commit 22c0eceddcf986a9b3f853b36d855acb38fa678f
Author: Aliaksey Kandratsenka <<EMAIL>>
Date:	Sun Feb 14 16:29:14 2021 -0800

    add emacs mode line annotations to remaining files

M	src/base/arm_instruction_set_select.h
M	src/base/dynamic_annotations.c
M	src/base/dynamic_annotations.h
M	src/base/linuxthreads.h
M	src/base/thread_annotations.h
M	src/base/thread_lister.c
M	src/base/vdso_support.cc
M	src/base/vdso_support.h
M	src/gperftools/heap-profiler.h
M	src/gperftools/profiler.h
M	src/gperftools/tcmalloc.h.in
M	src/stacktrace_powerpc-darwin-inl.h
M	src/tests/stacktrace_unittest.cc
M	src/windows/addr2line-pdb.c
M	src/windows/config.h
M	src/windows/ia32_modrm_map.cc
M	src/windows/ia32_opcode_map.cc
M	src/windows/nm-pdb.c
M	src/windows/patch_functions.cc
M	src/windows/system-alloc.cc

commit b12139ddbaa3e16ba491a24ce6fec51009e70cce
Author: Aliaksey Kandratsenka <<EMAIL>>
Date:	Sun Feb 14 16:13:15 2021 -0800

    delete-trailing-whitespace on all files

M	src/addressmap-inl.h
M	src/base/atomicops-internals-gcc.h
M	src/base/atomicops-internals-linuxppc.h
M	src/base/atomicops-internals-macosx.h
M	src/base/atomicops-internals-windows.h
M	src/base/atomicops-internals-x86.cc
M	src/base/atomicops-internals-x86.h
M	src/base/atomicops.h
M	src/base/commandlineflags.h
M	src/base/googleinit.h
M	src/base/linuxthreads.cc
M	src/base/logging.cc
M	src/base/logging.h
M	src/base/low_level_alloc.cc
M	src/base/low_level_alloc.h
M	src/base/simple_mutex.h
M	src/base/spinlock.cc
M	src/base/spinlock.h
M	src/base/spinlock_linux-inl.h
M	src/base/spinlock_posix-inl.h
M	src/base/spinlock_win32-inl.h
M	src/base/stl_allocator.h
M	src/base/sysinfo.cc
M	src/base/sysinfo.h
M	src/base/thread_annotations.h
M	src/base/vdso_support.cc
M	src/common.cc
M	src/config_for_unittests.h
M	src/getenv_safe.h
M	src/google/heap-profiler.h
M	src/google/malloc_extension.h
M	src/google/malloc_extension_c.h
M	src/google/malloc_hook.h
M	src/google/malloc_hook_c.h
M	src/google/profiler.h
M	src/google/stacktrace.h
M	src/google/tcmalloc.h
M	src/gperftools/heap-profiler.h
M	src/gperftools/malloc_extension.h
M	src/gperftools/malloc_extension_c.h
M	src/gperftools/malloc_hook.h
M	src/gperftools/malloc_hook_c.h
M	src/gperftools/profiler.h
M	src/gperftools/stacktrace.h
M	src/heap-checker-bcad.cc
M	src/heap-profile-stats.h
M	src/heap-profile-table.cc
M	src/heap-profile-table.h
M	src/heap-profiler.cc
M	src/internal_logging.cc
M	src/internal_logging.h
M	src/malloc_extension.cc
M	src/malloc_hook-inl.h
M	src/malloc_hook.cc
M	src/maybe_threads.cc
M	src/maybe_threads.h
M	src/memfs_malloc.cc
M	src/memory_region_map.cc
M	src/memory_region_map.h
M	src/pagemap.h
M	src/profile-handler.cc
M	src/profile-handler.h
M	src/raw_printer.cc
M	src/raw_printer.h
M	src/sampler.cc
M	src/sampler.h
M	src/stacktrace_powerpc-inl.h
M	src/symbolize.cc
M	src/symbolize.h
M	src/system-alloc.cc
M	src/system-alloc.h
M	src/tests/addressmap_unittest.cc
M	src/tests/atomicops_unittest.cc
M	src/tests/frag_unittest.cc
M	src/tests/heap-profiler_unittest.cc
M	src/tests/low_level_alloc_unittest.cc
M	src/tests/malloc_extension_c_test.c
M	src/tests/malloc_extension_test.cc
M	src/tests/markidle_unittest.cc
M	src/tests/memalign_unittest.cc
M	src/tests/pagemap_unittest.cc
M	src/tests/profiler_unittest.cc
M	src/tests/realloc_unittest.cc
M	src/tests/sampler_test.cc
M	src/tests/stacktrace_unittest.cc
M	src/tests/system-alloc_unittest.cc
M	src/tests/tcmalloc_large_unittest.cc
M	src/tests/testutil.cc
M	src/tests/testutil.h
M	src/tests/thread_dealloc_unittest.cc
M	src/windows/get_mangled_names.cc
M	src/windows/google/tcmalloc.h
M	src/windows/ia32_modrm_map.cc
M	src/windows/ia32_opcode_map.cc
M	src/windows/mingw.h
M	src/windows/mini_disassembler.cc
M	src/windows/mini_disassembler.h
M	src/windows/mini_disassembler_types.h
M	src/windows/nm-pdb.c
M	src/windows/override_functions.cc
M	src/windows/patch_functions.cc
M	src/windows/port.cc
M	src/windows/preamble_patcher.cc
M	src/windows/preamble_patcher.h
M	src/windows/preamble_patcher_test.cc
M	src/windows/preamble_patcher_with_stub.cc
M	src/windows/system-alloc.cc

commit 419c85814de497d6dfaf6f9a19d153138a7dec08
Author: Aliaksey Kandratsenka <<EMAIL>>
Date:	Sun Feb 14 16:02:56 2021 -0800

    amputate unused dynamic annotations support

M	CMakeLists.txt
M	Makefile.am
M	cmake/config.h.in
M	configure.ac
M	src/base/dynamic_annotations.c
M	src/base/dynamic_annotations.h
M	src/base/low_level_alloc.cc
M	src/base/spinlock.h
M	src/profile-handler.cc
D	src/third_party/valgrind.h
M	src/windows/config.h

commit 73a72cdb6154ab7bdd49b1dde91349a30e8d7973
Author: Aliaksey Kandratsenka <<EMAIL>>
Date:	Sun Feb 14 15:11:12 2021 -0800

    don't check for snprintf

M	CMakeLists.txt
M	cmake/config.h.in
M	src/windows/config.h
M	src/windows/mingw.h
M	src/windows/port.h

commit 95b52b0504a7cb2bce36d72b8bb0825833500b4c
Author: Aliaksey Kandratsenka <<EMAIL>>
Date:	Sun Feb 14 15:02:57 2021 -0800

    don't check for unused uname symbol

M	CMakeLists.txt
M	cmake/config.h.in
M	configure.ac
M	src/windows/config.h

commit 01c2697fac41a5d6175ad0da21d9d753f0b385a1
Author: Aliaksey Kandratsenka <<EMAIL>>
Date:	Sun Feb 14 14:30:29 2021 -0800

    amputate unused SleepForMilliseconds from sysinfo.{h,cc}

M	src/base/sysinfo.cc
M	src/base/sysinfo.h

commit ac68c97187e3729668ff5048c9bd27aae14e974d
Author: Aliaksey Kandratsenka <<EMAIL>>
Date:	Sun Feb 14 14:25:49 2021 -0800

    don't check for useless __builtin_stack_pointer

    It doesn't seem to be supported anyways, and we're not using it too.

M	CMakeLists.txt
M	cmake/config.h.in
M	configure.ac
M	src/windows/config.h

commit 7271bb72be08db491d3f0c60c1b0d5593cc1b5b5
Author: Aliaksey Kandratsenka <<EMAIL>>
Date:	Sun Feb 14 14:22:08 2021 -0800

    unbreak cmake check for TLS support

M	CMakeLists.txt

commit 7c106ca241a344993f8011397de8004a224e094a
Author: Aliaksey Kandratsenka <<EMAIL>>
Date:	Sun Feb 14 14:14:55 2021 -0800

    don't bother checking for stl namespace and use std

    Because there are no compilers left that don't do std namespace.

M	CMakeLists.txt
M	cmake/config.h.in
M	configure.ac
D	m4/namespaces.m4
D	m4/stl_namespace.m4
M	src/heap-profiler.cc
M	src/malloc_extension.cc
M	src/tcmalloc.cc
M	src/windows/config.h

commit 0d6f32b9cef7ee044e55a746e7c76db62d23cd86
Author: Aliaksey Kandratsenka <<EMAIL>>
Date:	Sun Feb 14 14:04:10 2021 -0800

    use standard way to print size_t-sized ints

    I.e. just use zu/zd/zx instead of finding out right size and defining
    PRI{u,x,d}S defines. Compilers have long caught up to this part of
    standard.

M	cmake/config.h.in
M	configure.ac
D	m4/compiler_characteristics.m4
M	src/debugallocation.cc
M	src/emergency_malloc.cc
M	src/heap-checker.cc
M	src/heap-profile-table.cc
M	src/heap-profiler.cc
M	src/memory_region_map.cc
M	src/profiledata.cc
M	src/tcmalloc.cc
M	src/tests/markidle_unittest.cc
M	src/tests/tcmalloc_unittest.cc
M	src/windows/config.h

commit 0c11d35f4a2a0f5f42ee684a3f5b27cbab8abae2
Author: Aliaksey Kandratsenka <<EMAIL>>
Date:	Sun Feb 14 13:39:55 2021 -0800

    amputate checking for __int64

    Since everything supports stdint.h this days.

M	CMakeLists.txt
M	cmake/config.h.in
M	configure.ac
M	src/windows/config.h
M	src/windows/port.h

commit 92718aaaeb08d139b346633333235bf2260d0848
Author: Aliaksey Kandratsenka <<EMAIL>>
Date:	Sun Feb 14 13:34:58 2021 -0800

    amputate checking for conflict-signal.h

    It was never found or used.

M	CMakeLists.txt
M	cmake/config.h.in
M	src/profiler.cc
M	src/windows/config.h

commit 9bb2937261bca6c0257a8cfd62ff97f69c450acb
Author: Aliaksey Kandratsenka <<EMAIL>>
Date:	Sun Feb 14 13:33:10 2021 -0800

    amputate checking for inline keyword support

    It is supported everywhere now.

M	CMakeLists.txt
M	cmake/config.h.in
M	configure.ac
M	src/windows/config.h

commit d9c4c3b481e641b719d3d790987ed7d094157bf2
Author: Đoàn Trần Công Danh <<EMAIL>>
Date:	Fri Feb 12 19:21:18 2021 +0700

    profile-handler: use documented sigev_notify_thread_id in sigevent

    sigevent(7) is documented to have sigev_notify_thread_id as its
    member.
    In glibc system, it's a macro expanded to the legacy _sigev_un._tid,
    _sigev_un._tid is obviously an internal implementation detail as
    signaled by its underscore prefix. And this macro was hidden inside
    linux/signal.h in older version of glibc.

    On Linux that use musl libc, sigev_notify_thread_id is also a
    macro, but
    it's expanded to __sev_fields.sigev_notify_thread_id

    [<EMAIL>: amputated broken linux/signal.h dependency]
    [<EMAIL>: see
    https://github.com/gperftools/gperftools/pull/1250]
    Signed-off-by: Aliaksey Kandratsenka <<EMAIL>>

M	src/profile-handler.cc

commit 43459feb33b87d3300e3b8b4917e5226678e4a0f
Author: Đoàn Trần Công Danh <<EMAIL>>
Date:	Sat Feb 13 17:35:20 2021 +0700

    configure.ac: check for features.h once

M	configure.ac

commit 290b123c01f6578391b20bb31849f26b02401112
Author: Venkatesh Srinivas <<EMAIL>>
Date:	Mon Feb 8 12:32:23 2021 -0800

    atomicops: Remove Acquire_Store / Release_Load

    gperftools' internal atomicops library included atomic
    Release Load and Acquire Store operations; those operations
    were unused and expressed ordering constraints that aren't
    expressible in the C++ standard memory model.

    Remove them, to make a transition to C++11 atomics easier
    and to avoid confusing use of them.

M	src/base/atomicops-internals-arm-generic.h
M	src/base/atomicops-internals-arm-v6plus.h
M	src/base/atomicops-internals-gcc.h
M	src/base/atomicops-internals-linuxppc.h
M	src/base/atomicops-internals-macosx.h
M	src/base/atomicops-internals-mips.h
M	src/base/atomicops-internals-windows.h
M	src/base/atomicops-internals-x86.h
M	src/base/atomicops.h
M	src/tests/atomicops_unittest.cc

commit 3b1c60cc4ea737ff151977dbcd164c5e2eb859fb
Author: SSE4 <<EMAIL>>
Date:	Wed Jan 27 11:15:56 2021 +0300

    Add support for Elbrus 2000 (e2k)

M	cmake/PCFromUContext.cmake
M	m4/pc_from_ucontext.m4
M	src/base/basictypes.h
M	src/malloc_hook_mmap_linux.h

commit c5747615da78f7c2d91112f2a6b6b4da4f18540a
Author: Jarno Rajahalme <<EMAIL>>
Date:	Fri Jan 29 09:54:18 2021 -0800

    syscall: Mark x8 as clobbered

    Mark arm64 register x8 as clobbered by syscall body inline assembly as
    it is being used to store the syscall number. Otherwise the compiler
    may try to use it for some other purpose.

    This fix is derived from a resolution to clang Bugzilla report
    https://bugs.llvm.org/show_bug.cgi?id=48798.  See this report for a
    minimal reproducer derived from the code fixed here as well as the
    resolution.

    This should fix SEGFAULTs as reported in
    https://github.com/envoyproxy/envoy/issues/14756.

    Fixes: #1241
    Signed-off-by: Jarno Rajahalme <<EMAIL>>

M	src/base/linux_syscall_support.h

commit d8eb315fb18f6fb0d6efa923401f166343727bc6
Author: Aliaksey Kandratsenka <<EMAIL>>
Date:	Sun Dec 20 23:35:08 2020 -0800

    bump version to 2.8.1

M	CMakeLists.txt
M	NEWS
M	configure.ac
M	src/windows/config.h
M	src/windows/gperftools/tcmalloc.h

commit 6ed61f8e91ffc00310b253ad2c777c0e862bd742
Author: Aliaksey Kandratsenka <<EMAIL>>
Date:	Sat Dec 19 19:14:50 2020 -0800

    add note that cmake build is preliminary

M	CMakeLists.txt

commit 6bbf2ed1501e27e215bb412b9b0b930921598bca
Author: okhowang(王沛文) <<EMAIL>>
Date:	Sat Sep 12 02:43:56 2020 +0800

    Update cmake

M	CMakeLists.txt
M	cmake/FindObjcopyWithWeaken.cmake
D	cmake/GPerftoolsAddTarget.cmake
M	cmake/config.h.in
A	cmake/pkgconfig.pc
M	cmake/tcmalloc.h.in
M	src/tests/addressmap_unittest.cc
A	src/windows/CMakeLists.txt

commit 913d3eb7d7fc1c5cc025920d5699b528c268f0fa
Author: Isaac Hier <<EMAIL>>
Date:	Wed Nov 8 11:20:25 2017 -0500

    Fix a few macros for Apple

M	CMakeLists.txt
M	cmake/config.h.in

commit 64a73b1cb87a6661e4c3092b07bfd5032fe9a1fa
Author: Isaac Hier <<EMAIL>>
Date:	Wed Nov 8 07:24:08 2017 -0500

    Work on fixing linking errors in stacktrace

M	CMakeLists.txt
M	cmake/config.h.in

commit b788d51eb4cf80adc7d782fa8df7175ee96d41d9
Author: Isaac Hier <<EMAIL>>
Date:	Tue Nov 7 21:25:34 2017 -0500

    Fix conditional definitions

M	CMakeLists.txt

commit 495229b625d65b19f3fc98fed5b626d31a89ee03
Author: Isaac Hier <<EMAIL>>
Date:	Tue Nov 7 13:26:10 2017 -0500

    Make internal tcmalloc libs

M	CMakeLists.txt

commit cca7f6f669657218a15ef5a176706660ad2c1d67
Author: Isaac Hier <<EMAIL>>
Date:	Sun Nov 5 16:53:28 2017 -0500

    More unit tests and libraries

M	CMakeLists.txt

commit 11dc65c3c40618065ae48ccdb1e1548e339bff32
Author: Isaac Hier <<EMAIL>>
Date:	Sun Nov 5 12:53:07 2017 -0500

    Fix config headers, add more unit tests

M	CMakeLists.txt
M	cmake/config.h.in
M	cmake/tcmalloc.h.in

commit 6078fe40d9a5b829cbbadc6ec824163d6d9eda5b
Author: Isaac Hier <<EMAIL>>
Date:	Sun Nov 5 12:23:42 2017 -0500

    Finish configure.ac conversion to CMake, start on Makefile.am

M	CMakeLists.txt
A	cmake/CheckNoFPByDefault.cmake
M	cmake/DefineTargetVariables.cmake
A	cmake/FindObjcopyWithWeaken.cmake
A	cmake/GPerftoolsAddTarget.cmake
M	cmake/PCFromUContext.cmake
M	cmake/config.h.in

commit 515fb221966a2413d00184186da97719b90e3915
Author: Isaac Hier <<EMAIL>>
Date:	Fri Nov 3 09:10:27 2017 -0400

    Generate config header

M	CMakeLists.txt
D	cmake/DefineArchVariables.cmake
A	cmake/DefineTargetVariables.cmake
M	cmake/PCFromUContext.cmake
A	cmake/config.h.in
A	cmake/tcmalloc.h.in

commit 4adb5ff74d35900dd8d0ed07959eef68f85c8886
Author: Isaac Hier <<EMAIL>>
Date:	Thu Nov 2 20:07:29 2017 -0400

    Add architecture checks

M	CMakeLists.txt
A	cmake/DefineArchVariables.cmake
M	src/tcmalloc.h

commit fa9bedc82cbbc289875a07b6e431e553b1a15b77
Author: Isaac Hier <<EMAIL>>
Date:	Wed Nov 1 09:52:02 2017 -0400

    Add most of CMake build

M	CMakeLists.txt
A	cmake/PCFromUContext.cmake

commit 9e4f72fd1faa3f406f468796b4b6908391c8019c
Author: Isaac Hier <<EMAIL>>
Date:	Mon Oct 30 22:26:13 2017 -0400

    Define options, start system checks

M	CMakeLists.txt

commit a6ce98174b47213f11c84d57cfbc1e3cdd609bfb
Author: Isaac Hier <<EMAIL>>
Date:	Mon Oct 30 09:35:34 2017 -0400

    Add CMakeLists.txt

A	CMakeLists.txt

commit 313495587598fb1a69b94c44a83735f6056f6049
Author: Steve Langasek <<EMAIL>>
Date:	Thu Oct 8 16:49:21 2020 -0700

    Additional porting for riscv64.

    Adds handling for the PC register, malloc_hook_mmap, and syscalls.

    Successfully built in Ubuntu with these changes.

M	m4/pc_from_ucontext.m4
M	src/base/linux_syscall_support.h
M	src/malloc_hook_mmap_linux.h

commit f0e289bdbb2fbbb7fa71496d846988c1bac0e310
Author: Steve Langasek <<EMAIL>>
Date:	Thu Oct 8 07:54:03 2020 -0700

    Enable build on riscv64.

    Cacheline length may be implementation-specific, but this is known
    to be
    correct on the U54.

M	src/base/basictypes.h

commit 6c715b4fa183e221a29c5d7a4ea87aeba4ee02cf
Author: Tim Gates <<EMAIL>>
Date:	Sat Oct 10 17:26:00 2020 +1100

    docs: fix simple typo, defininitions -> definitions

    There is a small typo in src/base/linux_syscall_support.h.

    Should read `definitions` rather than `defininitions`.

M	src/base/linux_syscall_support.h

commit 02d5264018cc76a36713f97329870dbf85969519
Author: Aliaksey Kandratsenka <<EMAIL>>
Date:	Sat Dec 19 17:15:31 2020 -0800

    Revert "drop page heap lock when returning memory back to kernel"

    This reverts commit be3da70298bf3d25c7d64655922ab82dd819ec98.

    There are reports of crashes and false-positive OOMs from this
    patch. Crashes under aggressive decommit mode are understood, but I
    have yet to get confirmations whether false-positive OOMs were seen
    under aggressive decommit or not. Thus lets revert for now.

    Updates issue #1227 and issue #1204.

M	src/page_heap.cc
M	src/page_heap.h
M	src/tests/page_heap_test.cc

commit 151cbf5146cd5b359a4470860d1f7d8f6a843d62
Author: Grant Henke <<EMAIL>>
Date:	Thu Nov 26 20:56:46 2020 -0600

    Add OS X arm64 program counter

    [<EMAIL>: updated comment that it is tested]

    Signed-off-by: Aliaksey Kandratsenka <<EMAIL>>

M	m4/pc_from_ucontext.m4

commit 140e3481d0541044cdc27f45129b47f92d5940b7
Merge: bda3c82 0fc5cab
Author: Aliaksey Kandratsenka (aka Aliaksei Kandratsenka)
<<EMAIL>>
Date:	Tue Nov 17 14:30:59 2020 -0800

    Merge pull request #1231 from PatriosTheGreat/master

    Fix implicit-int-float-conversion warning.

commit 0fc5cabdfc966affb6f336848d9587ec5d904d0c
Author: Levon Ter-Grigoryan <<EMAIL>>
Date:	Tue Nov 17 12:29:22 2020 +0100

    Fix implicit-int-float-conversion warning.

M	src/sampler.cc

commit bda3c82e11615ca9e7751d1f3cfb161026ee742a
Author: Kerrick Staley <<EMAIL>>
Date:	Wed Jul 8 16:42:12 2020 -0700

    Increase kMaxStackDepth to 254

    [<EMAIL>: reduced value from 256 to 254]

M	src/profiledata.h

commit 1d9b8bb59d5610f2c3e9faad6bbd91ceca17fb93
Author: Aliaksey Kandratsenka <<EMAIL>>
Date:	Sat Jul 11 20:33:40 2020 -0700

    don't test sbrk hook if we're on linux and don't have __sbrk

    "mmap" hooks code for linux only hooks sbrk when __sbrk is
    defined. But musl doesn't offer this function (reasonably so), so
    sbrk hooking doesn't work there. Not big loss, but lets make sure
    tests don't fail.

    Lets reference this to issue #1198

M	src/tests/tcmalloc_unittest.cc

commit 180bfa10d7cb38e8b3784d60943d50e8fcef0dcb
Author: Aliaksey Kandratsenka <<EMAIL>>
Date:	Mon Jul 6 02:51:43 2020 -0700

    bumped version to 2.8

M	Makefile.am
M	NEWS
M	configure.ac
M	src/windows/config.h
M	src/windows/gperftools/tcmalloc.h

commit c1bcc412ba4a94deaf8c3230519ecb4dda60557d
Author: Ryan Macnak <<EMAIL>>
Date:	Fri May 1 11:50:16 2020 -0700

    Don't try to mark esp as clobbered in linux syscall support.

    Compare 9e5b1628737c67b4587f937164572774592978c4, issue #1076

M	src/base/linux_syscall_support.h

commit 50f89afaed3d07d384fa8d86c98460ccff1e870e
Author: Aliaksey Kandratsenka <<EMAIL>>
Date:	Sun Apr 19 22:21:07 2020 -0700

    liberate gperftools tests from relying on -fno-builtin-XXX flags

    Clang mostly ignores those anyways, so our tests needed better way to
    disable optimizations (clang is quite aggressive replacing new/delete
    pair with stack allocation).

M	Makefile.am
M	README
M	src/tests/debugallocation_test.cc
M	src/tests/system-alloc_unittest.cc
M	src/tests/tcmalloc_large_unittest.cc
M	src/tests/tcmalloc_unittest.cc
M	src/tests/testutil.h

commit 98ccd0f1029c11a3df0a51e4a14b543e430a96f8
Author: Aliaksey Kandratsenka <<EMAIL>>
Date:	Sun Apr 19 22:50:16 2020 -0700

    prevent inlining in heap-checker unittest

    The test relies on stuff "mmaper" function not being inlined, but
    compilers are getting smarter and latest clang actually does inline it
    now. Adding volatile to pointer declaration fixes it.

M	src/tests/heap-checker_unittest.cc

commit e521472f1ac1ecf028e8c922ca4f8741473afd66
Author: Aliaksey Kandratsenka <<EMAIL>>
Date:	Sun Jul 5 22:49:41 2020 -0700

    fix linking of page_heap_test on windows

    After change to release page heap lock around returning memory back to
    kernel, page heap test got dependency on page heap lock. Which was not
    available on windows since relevant symbols are not exported.

    Proposed fix is to simply duplicate all needed .cc files in
    page_heap_test project instead of linking to dll. This is not perfect
    but gets job done, until we figure out better solution (GNU/Linux will
    eventually get hidden visibility and will need it).

    This fixes github issue 1189.

M	vsprojects/page_heap_test/page_heap_test.vcxproj
M	vsprojects/page_heap_test/page_heap_test.vcxproj.filters

commit e5f77d6485bd2f6ce43862e3e57118b1bb97d30a
Author: Aliaksey Kandratsenka <<EMAIL>>
Date:	Mon Mar 23 01:21:27 2020 -0700

    chmod -x Makefile.am gperftools.sln

M	Makefile.am
M	gperftools.sln

commit 6b92e00cecb82e7fae6e019583865faced29c6f7
Author: Aliaksey Kandratsenka <<EMAIL>>
Date:	Mon Mar 23 01:15:40 2020 -0700

    don't assume HAVE_MMAP on mingw builds

    Fixes github issue #1108.

M	configure.ac

commit 4cddede3990a96969aac9ff13ce3c8195ec91ce7
Author: Kirill Müller <<EMAIL>>
Date:	Tue Feb 25 09:09:22 2020 +0100

    New ProfilerGetStackTrace()

M	Makefile.am
M	src/gperftools/profiler.h
M	src/profiler.cc

commit db7aa547abb5abdd558587a15502584cbc825438
Author: Aliaksey Kandratsenka <<EMAIL>>
Date:	Sun Mar 8 20:57:01 2020 -0700

    bumped version to 2.8rc

M	NEWS
M	configure.ac
M	src/windows/config.h
M	src/windows/gperftools/tcmalloc.h

commit be3da70298bf3d25c7d64655922ab82dd819ec98
Author: Aliaksey Kandratsenka <<EMAIL>>
Date:	Sun Mar 8 19:32:02 2020 -0700

    drop page heap lock when returning memory back to kernel

    Fixes issue #754.

M	src/page_heap.cc
M	src/page_heap.h
M	src/tests/page_heap_test.cc

commit 87acc2782fe49a8b57d19783f61ff8bc667db68d
Author: Aliaksey Kandratsenka <<EMAIL>>
Date:	Sun Mar 8 18:22:17 2020 -0700

    amputate span events history

    Nobody used it in years. Abseil tcmalloc had it amputated years ago.

M	src/central_freelist.cc
M	src/page_heap.cc
M	src/span.cc
M	src/span.h

commit e40c7f231ad89e1ee8bf37a1d6680880c519c901
Author: Ilya Leoshkevich <<EMAIL>>
Date:	Thu Feb 27 13:10:29 2020 +0100

    Fix mmap syscall on s390

    mmap arguments must be passed in an array on s390.

    Signed-off-by: Ilya Leoshkevich <<EMAIL>>

M	src/malloc_hook_mmap_linux.h

commit b7607ee7d4ab35504a54588e5912a8e850686db2
Author: Jon Kohler <<EMAIL>>
Date:	Sun Jan 19 22:09:08 2020 -0500

    tcmalloc: ability to disable fallback allocator in memfs

    Whenever tcmalloc memfs allocator runs out of huge memory, it falls
    back to the
    default system allocator, which will start allocating from normal
    anonymous
    memory pool (small pages).	For use cases that exclusively want
    hugepage backed
    memory, such as using tcmalloc in combination with Intel SPDK, this is
    problematic.

    This change adds an environment variable
    "TCMALLOC_MEMFS_DISABLE_FALLBACK"
    which when set to true will ensure that tcmalloc doesn't fallback
    to using the
    system allocator when the system runs out of hugepages. This is
    false by
    default, such that all other tcmalloc consumers will not see any
    changes.

    For those curious, you can see part of our upstream SPDK work here:
    https://review.gerrithub.io/c/spdk/spdk/+/475943

M	src/memfs_malloc.cc

commit 1bfcb5bc3a779b6c0c63125737f035c2afcc9e95
Author: Jon Kohler <<EMAIL>>
Date:	Tue Feb 18 11:14:49 2020 -0500

    tcmalloc: fragmentation overhead instrumentation

    This patch adds visibility into the overhead due to fragmentation
    for each size
    class in the tcmalloc central free list, which is helpful when
    debugging
    fragmentation issues.

M	src/tcmalloc.cc

commit 36bf1309de52433f0088274b347b4fc385ea056f
Author: Todd Lipcon <<EMAIL>>
Date:	Mon Jun 3 10:29:06 2019 -0700

    Fix a clang-tidy readability warning for static member access

    This fixes the following warning:

    src/thread_cache.cc:281:10: warning: static member accessed through
    instance [readability-static-accessed-through-instance]
      return sampler_.GetSamplePeriod();
	     ^~~~~~~~~~~~~~~~~~~~~~~~
	     tcmalloc::Sampler::

M	src/thread_cache.cc

commit 2b2a962c2bdc3ecbdbd45873a07989c7ccb56560
Author: prehistoricpenguin <<EMAIL>>
Date:	Fri Jul 12 16:25:55 2019 +0800

    Remove executable flag for c++ files

M	src/heap-checker.cc
M	src/heap-profiler.cc
M	src/stacktrace_instrument-inl.h
M	src/symbolize.cc
M	src/system-alloc.cc
M	src/tests/sampler_test.cc

commit 8f308afbfedeca0991ef431e3b8e0b01bd5b8a8f
Author: Henrik Edin <<EMAIL>>
Date:	Tue Apr 9 15:16:55 2019 -0400

    Increase kClassSizesMax to 128 to allow for page size of 4K

M	src/common.h

commit d3fefdb69439292ec914cd8c65dd83ed1bcbdc65
Author: Henrik Edin <<EMAIL>>
Date:	Tue Jan 29 13:25:40 2019 -0500

    Allow configuring page size to 4K, 8K, 16K, 32K, 64K, 128K and 256K

M	INSTALL
M	configure.ac
M	src/common.h
M	src/windows/config.h

commit cf2df3b000c58155d3c83bd01b603894b4559c0b
Author: Laurent Stacul <<EMAIL>>
Date:	Fri Feb 7 09:37:34 2020 +0000

    Fix the removed std::allocator::pointer member type removed in C++20

M	src/tests/heap-checker_unittest.cc

commit 31024506c543132adb315b8de89d5faa85e0ff0e
Author: shipujin <<EMAIL>>
Date:	Tue Aug 20 13:24:57 2019 +0800

    Add mips64* support

M	src/base/linux_syscall_support.h

commit fe62a0baab87ba3abca12f4a621532bf67c9a7d2
Author: Holy Wu <<EMAIL>>
Date:	Tue Apr 9 18:33:26 2019 +0800

    Update config.h in Windows

M	src/windows/config.h

commit 8272631b5a9f9e840eb1b014f43b3ddea16ff67d
Author: 风 <<EMAIL>>
Date:	Wed Apr 17 10:55:06 2019 +0800

    Fix a long time typo

    Thought this fix may not effect the behavior or performance, but it
    seems indeed a bug.

M	src/pprof

commit c1d546d7b22cc503f37e9a6efa1d249be60243a3
Author: Aliaksey Kandratsenka <<EMAIL>>
Date:	Sun Apr 7 15:27:17 2019 -0700

    never test and always default HAVE_MMAP to on

    This should alleviate vboxfs problems from issue #1008.

M	configure.ac

commit fba6ce0e7aaa094fec09ef8763e86b19dc815624
Author: pkubaj <<EMAIL>>
Date:	Mon Feb 4 16:10:06 2019 +0000

    Fix build on FreeBSD

    On FreeBSD the following syntax is used:
    mr REGA,REGB

M	src/stacktrace_powerpc-darwin-inl.h

commit 98ac4ee9bc6ab285429fc5d97335b4c94fc0b919
Author: Jakub Wilk <<EMAIL>>
Date:	Wed Apr 3 09:50:40 2019 +0200

    Fix typos

M	README

commit 9e5b1628737c67b4587f937164572774592978c4
Author: Aliaksey Kandratsenka <<EMAIL>>
Date:	Sun Feb 10 12:46:18 2019 -0800

    don't try to mark rsp as clobbered in linux syscall support

    rsp is not actually clobbered by that code and later gccs actually
    (correctly) bark
    at it.

    Fixed issue #1076.

M	src/base/linux_syscall_support.h

commit 1e36ed705548ae2a5170bb00dfee0954b8ef76de
Author: Romain Geissler <<EMAIL>>
Date:	Tue Jan 29 18:31:19 2019 +0000

    Use initial exec TLS model for all thread local variables from
    thread_cache.cc

    This avoids a deadlock when a library which is being dlopen'ed creates
    as part of its static constructors a thread which quickly need to call
    malloc. We are still in the dlopen call (so with some internal glibc
    mutex taken) when the thread executes code and later needs to call
    malloc which in term calls tls_get_addr_tail, which wait for the
    dlopen
    mutex to be unlocked. If later the dlopen'ing thread also calls malloc
    as part of its constructors, we are in a deadlock.

    Fix is similar to
    https://github.com/gperftools/gperftools/commit/7852eeb75b9375cf52a7da01be044da6e915dd08

    Stack of the dlopening thread:
	#0  0x00007fd5406ca93c in __lll_lock_wait () from
	/data3/mwrep/rgeissler/core.tls/opt/1A/toolchain/x86_64-2.6.32-v2/lib64/libpthread.so.0
	#1  0x00007fd5406c45a5 in pthread_mutex_lock () from
	/data3/mwrep/rgeissler/core.tls/opt/1A/toolchain/x86_64-2.6.32-v2/lib64/libpthread.so.0
	... proprietary code in the stack
	#9  0x00007fd5074f0367 in
	__static_initialization_and_destruction_0 (__initialize_p=1,
	__priority=65535) at src/ClientImpl.cpp:15
	#10 0x00007fd5074f06d7 in _GLOBAL__sub_I_ClientImpl.cpp(void)
	() at src/ClientImpl.cpp:85
	#11 0x00007fd50757aa46 in __do_global_ctors_aux ()
	#12 0x00007fd5073e985f in _init () from ...
	#13 0x00007fd53bf9dec8 in ?? () from ...
	#14 0x00007fd54d637a5d in call_init.part () from
	/data3/mwrep/rgeissler/core.tls/opt/1A/toolchain/x86_64-2.6.32-v2/lib64/ld-linux-x86-64.so.2
	#15 0x00007fd54d637bab in _dl_init () from
	/data3/mwrep/rgeissler/core.tls/opt/1A/toolchain/x86_64-2.6.32-v2/lib64/ld-linux-x86-64.so.2
	#16 0x00007fd54d63c160 in dl_open_worker () from
	/data3/mwrep/rgeissler/core.tls/opt/1A/toolchain/x86_64-2.6.32-v2/lib64/ld-linux-x86-64.so.2
	#17 0x00007fd54d637944 in _dl_catch_error () from
	/data3/mwrep/rgeissler/core.tls/opt/1A/toolchain/x86_64-2.6.32-v2/lib64/ld-linux-x86-64.so.2
	#18 0x00007fd54d63b7d9 in _dl_open () from
	/data3/mwrep/rgeissler/core.tls/opt/1A/toolchain/x86_64-2.6.32-v2/lib64/ld-linux-x86-64.so.2
	#19 0x00007fd54d61f2b9 in dlopen_doit () from
	/data3/mwrep/rgeissler/core.tls/opt/1A/toolchain/x86_64-2.6.32-v2/lib64/libdl.so.2
	#20 0x00007fd54d637944 in _dl_catch_error () from
	/data3/mwrep/rgeissler/core.tls/opt/1A/toolchain/x86_64-2.6.32-v2/lib64/ld-linux-x86-64.so.2
	#21 0x00007fd54d61f889 in _dlerror_run () from
	/data3/mwrep/rgeissler/core.tls/opt/1A/toolchain/x86_64-2.6.32-v2/lib64/libdl.so.2
	#22 0x00007fd54d61f351 in dlopen@@GLIBC_2.2.5 () from
	/data3/mwrep/rgeissler/core.tls/opt/1A/toolchain/x86_64-2.6.32-v2/lib64/libdl.so.2

    Stack of the newly created thread calling tls_get_addr_tail:
	#0  0x00007fd5406ca93c in __lll_lock_wait () from
	/data3/mwrep/rgeissler/core.tls/opt/1A/toolchain/x86_64-2.6.32-v2/lib64/libpthread.so.0
	#1  0x00007fd5406c4622 in pthread_mutex_lock () from
	/data3/mwrep/rgeissler/core.tls/opt/1A/toolchain/x86_64-2.6.32-v2/lib64/libpthread.so.0
	#2  0x00007fd54d63a2ed in tls_get_addr_tail () from
	/data3/mwrep/rgeissler/core.tls/opt/1A/toolchain/x86_64-2.6.32-v2/lib64/ld-linux-x86-64.so.2
	#3  0x00007fd53fee877d in
	tcmalloc::ThreadCache::CreateCacheIfNecessary () at
	src/thread_cache.cc:344
	#4  0x00007fd53fecb4ab in tcmalloc::ThreadCache::GetCache ()
	at src/thread_cache.h:437
	#5  0x00007fd53fefeccb in (anonymous namespace)::do_malloc
	(size=56) at src/tcmalloc.cc:1354
	#6  tcmalloc::do_allocate_full<tcmalloc::cpp_throw_oom> (size=56)
	at src/tcmalloc.cc:1762
	#7  tcmalloc::allocate_full_cpp_throw_oom (size=56) at
	src/tcmalloc.cc:1776
	#8  0x00007fd53ff01b80 in
	tcmalloc::dispatch_allocate_full<tcmalloc::cpp_throw_oom>
	(size=56) at src/tcmalloc.cc:1785
	#9  malloc_fast_path<tcmalloc::cpp_throw_oom> (size=56) at
	src/tcmalloc.cc:1845
	#10 tc_new (size=56) at src/tcmalloc.cc:1980
	... proprietary code in the stack
	#26 0x00007fd5406c1ef4 in start_thread () from
	/data3/mwrep/rgeissler/core.tls/opt/1A/toolchain/x86_64-2.6.32-v2/lib64/libpthread.so.0
	#27 0x00007fd5403ba01d in clone () from
	/data3/mwrep/rgeissler/core.tls/opt/1A/toolchain/x86_64-2.6.32-v2/lib64/libc.so.6

M	src/thread_cache.cc

commit 8f9a873fce14337e113a3837603a11ade06da533
Author: pkubaj <<EMAIL>>
Date:	Mon Feb 4 16:06:32 2019 +0000

    Fix accessing PC on FreeBSD/powerpc and powerpc64

M	m4/pc_from_ucontext.m4

commit fc00474ddc21fff618fc3f009b46590e241e425e
Author: Tulio Magno Quites Machado Filho <<EMAIL>>
Date:	Fri Feb 8 10:33:09 2019 -0200

    Include asm/ptrace.h when accessing ucontext_t

    In some architectures (e.g. powerpc, powerpc64 and powerpc64le), the
    macro that specifies the position of the program counter is defined in
    header asm/ptrace.h.

    When glibc bug 21457 got fixed in version 2.27, header sys/ucontext.h
    stopped including signal.h, which indirectly removed asm/ptrace.h too.

    Signed-off-by: Tulio Magno Quites Machado Filho <<EMAIL>>

M	m4/pc_from_ucontext.m4
M	src/getpc.h

commit 5574c87e39ee592c909cb48245c1d91e90ddaf4d
Author: Laurent Stacul <<EMAIL>>
Date:	Mon Oct 15 16:56:18 2018 +0200

    Compile time aggressive decommit option

    [<EMAIL>: performed s/agressive/aggressive/g]
    Signed-off-by: Aliaksey Kandratsenka <<EMAIL>>

M	configure.ac
M	src/static_vars.cc

commit e9ab4c53041ac62feefbbb076d326e9a77dd1567
Author: Fabrice Fontaine <<EMAIL>>
Date:	Sun Oct 7 18:12:03 2018 +0200

    undef mmap64 function

    mmap64 can already been defined and as a result the following error is
    raised:

    In file included from src/malloc_hook.cc:686:0:
    src/malloc_hook_mmap_linux.h: In function ‘void* mmap(void*,
    size_t, int, int, int, off_t)’:
    src/malloc_hook_mmap_linux.h:173:18: error: redefinition of ‘void*
    mmap(void*, size_t, int, int, int, off_t)’
     extern "C" void* mmap(void *start, size_t length, int prot,
     int flags,
		      ^~~~
    In file included from src/malloc_hook.cc:41:0:
    src/malloc_hook_mmap_linux.h:159:18: note: ‘void* mmap(void*,
    size_t, int, int, int, off_t)’ previously defined here
     extern "C" void* mmap64(void *start, size_t length, int prot,
     int flags,
		      ^
    Makefile:4874: recipe for target
    'src/libtcmalloc_minimal_internal_la-malloc_hook.lo' failed

    Signed-off-by: Fabrice Fontaine <<EMAIL>>

M	src/malloc_hook_mmap_linux.h

commit 5eec9d0ae396aa90ebcb5756dfd0f3d101667490
Author: Aliaksey Kandratsenka <<EMAIL>>
Date:	Sun Oct 7 08:17:04 2018 -0700

    Drop not very portable and not very useful unwind benchmark.

M	.gitignore
M	Makefile.am
D	benchmark/getcontext_light.cc
D	benchmark/unwind_bench.cc

commit 1561f0946fcc96d1384dae2a81f3ed111338a341
Author: Fabrice Fontaine <<EMAIL>>
Date:	Sat Oct 6 20:43:19 2018 +0200

    check for __sbrk

    __sbrk is not defined in musl so add a check for it in configure.ac
    and
    replace the check on ifndef __UCLIBC__ by a check on ifdef HAVE__SBRK
    in src/malloc_hook_mmap_linux.h

    Signed-off-by: Fabrice Fontaine <<EMAIL>>

M	configure.ac
M	src/malloc_hook_mmap_linux.h

commit 1de76671d478e848f0dd210e00df43bfa463ca9f
Author: Gabriel Marin <<EMAIL>>
Date:	Thu Oct 4 22:52:35 2018 +0000

    Fix mmap region iteration while no regions are recorded.

    If no mmap regions are recorded, iteration failed since the RegionSet
    (std::set) object is not initialized.

    Original CL https://codereview.chromium.org/14769008

    Reviewed-on: https://chromium-review.googlesource.com/c/1130807

M	src/memory_region_map.cc
M	src/memory_region_map.h

commit acdcacc28f17d2e8db1d1bb634025f820f912d93
Author: Fabrice Fontaine <<EMAIL>>
Date:	Sun Apr 29 23:48:41 2018 +0200

    Use off64_t instead of __off64_t

    __off64_t is a kernel internal symbol, which happens to be
    user-visible
    with glibc, but not necessarily with other C libraries such as musl.

    Switching from __off64_t to off64_t allows the code to build properly
    with musl.

    Signed-off-by: Fabrice Fontaine <<EMAIL>>

M	src/malloc_hook_mmap_linux.h

commit 0177a2420aa68229f55bec970c59136813a50613
Author: Gabriel Marin <<EMAIL>>
Date:	Fri Oct 5 21:49:50 2018 +0000

    Return early in WriteProfile to reduce indentation

    Small code refactoring to reduce indentation. This change was
    originally
    part of https://codereview.chromium.org/9963095.

    Reviewed-on: https://chromium-review.googlesource.com/c/1130804

M	src/heap-profile-table.cc

commit b85652bf268d061a0ad0aec90f0d3c29aa80a6b5
Author: Gabriel Marin <<EMAIL>>
Date:	Wed Oct 3 18:15:16 2018 +0000

    Add generic.total_physical_bytes property to MallocExtension

    Original CL:

    - https://codereview.chromium.org/**********

      Add generic.total_physical_bytes property to MallocExtension

      The actual physical memory usage of tcmalloc cannot be obtained by
      GetNumericProperty. This accounts for the current_allocated_bytes,
      fragmentation and malloc metadata, and excludes the unmapped memory
      regions. This helps the user to understand how much memory is
      actually
      being used for the allocations that were made.

    Reviewed-on: https://chromium-review.googlesource.com/1130803

M	src/gperftools/malloc_extension.h
M	src/tcmalloc.cc

commit 90df23c81fe6baf7799b5d3df37d2846ab88fc60
Author: Gabriel Marin <<EMAIL>>
Date:	Fri Oct 5 22:00:33 2018 +0000

    Make some tcmalloc constants truly const

    Reviewed-on: https://chromium-review.googlesource.com/c/1130809

M	src/tcmalloc.cc

commit 49dbe4362b431629111b85929d91fe9a46c42295
Author: Aliaksey Kandratsenka <<EMAIL>>
Date:	Mon Sep 3 16:44:52 2018 -0700

    Add comment about gperftools 2.8 not deduplicating heapz samples.

M	src/gperftools/malloc_extension.h

commit 63a12a5ed3c4aca61cc46078b6cdf1d161425a69
Author: Aliaksey Kandratsenka <<EMAIL>>
Date:	Mon Aug 27 20:10:09 2018 -0700

    Drop de-duplication of heap sample (aka heapz) entries.

    pprof can aggregate them, and it can do it way better than we
    can. With
    proper unsampling etc.

M	src/stack_trace_table.cc
M	src/stack_trace_table.h
M	src/static_vars.cc
M	src/static_vars.h
M	src/tests/stack_trace_table_test.cc

commit 954f9dc0e37ff46cb0cb24edfb39dc77fd2e7d0b
Author: Aliaksey Kandratsenka <<EMAIL>>
Date:	Sun Aug 26 11:37:59 2018 -0700

    Add flag to disable installing unmaintained & deprecated pprof.

    Everyone should be using golang pprof from github.com/google/pprof,
    but
    distros still ship our perl version and not everyone is aware of
    better pprof yet.

    This is another step in completely dropping perl pprof. We still
    default to installing it, but hopefully we'll be able to convince
    distros to disable this soon.

    We still install pprof under pprof-symbolize name because
    stack traces symbolization depends on it, and because golang pprof
    won't support this feature.

    This is related to issue #1038.

M	.gitignore
M	Makefile.am
M	configure.ac
M	src/symbolize.cc

commit 893bff51bcf220b724a812d340d878b5fb8ce911
Author: Aliaksey Kandratsenka <<EMAIL>>
Date:	Sun Aug 26 11:35:44 2018 -0700

    Avoid static initialization of pprof path for symbolization.

    This is one of the things that chrome's fork fixes, but with c++11 we
    can do it even nicer. Proposed fix is to use c++11 local static
    variable to ensure that pprof path is initialized once on as-needed
    basis.

M	src/symbolize.cc

commit 69867c523b334028c90f2acf22b5ae528fdc2356
Author: Holy Wu <<EMAIL>>
Date:	Fri Aug 10 17:19:29 2018 +0800

    Clean up MSVC projects

    1.Remove superfluous per file settings for include directory and
    runtime library.
    2.Remove unnecessary project tcmalloc_minimal_unittest-static. We
    can simply build libtcmalloc_minimal as a static library and then
    link against the single .lib file.
    3.Add separate configurations of patching and overriding facility
    for release mode.

M	Makefile.am
M	gperftools.sln
M	vsprojects/addr2line-pdb/addr2line-pdb.vcxproj
M	vsprojects/addressmap_unittest/addressmap_unittest.vcxproj
M
vsprojects/current_allocated_bytes_test/current_allocated_bytes_test.vcxproj
M	vsprojects/frag_unittest/frag_unittest.vcxproj
M	vsprojects/libtcmalloc_minimal/libtcmalloc_minimal.vcxproj
M	vsprojects/libtcmalloc_minimal/libtcmalloc_minimal.vcxproj.filters
M
vsprojects/low_level_alloc_unittest/low_level_alloc_unittest.vcxproj
M	vsprojects/malloc_extension_test/malloc_extension_test.vcxproj
M	vsprojects/malloc_hook_test/malloc_hook_test.vcxproj
M	vsprojects/markidle_unittest/markidle_unittest.vcxproj
M	vsprojects/nm-pdb/nm-pdb.vcxproj
M	vsprojects/packed-cache_test/packed-cache_test.vcxproj
M	vsprojects/page_heap_test/page_heap_test.vcxproj
M	vsprojects/pagemap_unittest/pagemap_unittest.vcxproj
M	vsprojects/preamble_patcher_test/preamble_patcher_test.vcxproj
M	vsprojects/realloc_unittest/realloc_unittest.vcxproj
M	vsprojects/sampler_test/sampler_test.vcxproj
M	vsprojects/stack_trace_table_test/stack_trace_table_test.vcxproj
M	vsprojects/system-alloc_unittest/system-alloc_unittest.vcxproj
M
vsprojects/tcmalloc_minimal_large/tcmalloc_minimal_large_unittest.vcxproj
M
vsprojects/tcmalloc_minimal_unittest/tcmalloc_minimal_unittest.vcxproj
M	vsprojects/thread_dealloc_unittest/thread_dealloc_unittest.vcxproj
D	vsprojects/tmu-static/tmu-static.vcxproj
D	vsprojects/tmu-static/tmu-static.vcxproj.filters

commit f2bca77aed83107197abadee0652c39e2c4c73d3
Author: Brian Silverman <<EMAIL>>
Date:	Thu Aug 9 16:33:39 2018 -0700

    Fix page_heap_test flakiness

    After 71fa9f87 (use 2-level page map for 48-bit addresses), there is a
    potential for allocs of additional leaves in the middle of the test.
    With ASLR enabled on amd64 (without TCMALLOC_SMALL_BUT_SLOW), this
    results in the test failing sometimes (< 1% of the time). Instead,
    make
    sure all those leaves are allocated before deciding on the baseline
    heap
    usage in the test.

M	src/tests/page_heap_test.cc

commit c41688bf20186723367b560ceb539b2330951ddb
Author: Aliaksey Kandratsenka <<EMAIL>>
Date:	Sun Aug 5 20:39:07 2018 -0700

    Use standard-conforming alignof in debugallocation.cc

    Clang was giving warning that alignof(<variable>) is extension. So
    we're now doing alignof of decltype.

M	src/debugallocation.cc

commit 71c8cedacafe700e662c008b65f5064b23426070
Author: Aliaksey Kandratsenka <<EMAIL>>
Date:	Sun Aug 5 18:54:01 2018 -0700

    Fix incompatible aliasing warnings

    We aliased functions with different signatures and gcc now correctly
    gives warning for that. Originally gcc 5 same code merging feature
    caused us to alias more than necessary, but I am not able to reproduce
    this problem anymore. So we're now aliasing only compatible functions.

M	src/libc_override_gcc_and_weak.h
M	src/tcmalloc.cc

commit 8dd304035848e780372a5a4bb1afac2b2d20bc43
Author: Aliaksey Kandratsenka <<EMAIL>>
Date:	Sun Aug 5 19:52:11 2018 -0700

    Format and fix out of bound access in CpuProfilerSwitch

    GCC was giving warning on snprintf and it hinted at base_profile_name
    and full_profile_name not being long enough.

    Fix is to ensure base_profile_name is long enough for PATH_MAX and
    that full_profile_name is enough longer to fit extra chars.

M	src/profiler.cc

commit 467502e70acb89f36b475bf29fe12333939fe5c7
Author: Aliaksey Kandratsenka <<EMAIL>>
Date:	Sun Aug 5 17:42:04 2018 -0700

    provide constexpr constructor for Sampler

    Constexpr constructor lets us use Sampler in TLS variable in cleaner
    way.

M	src/sampler.h
M	src/tests/sampler_test.cc

commit 1fb543cc708f22017cb39ae874a28c0addcf5a63
Author: HolyWu <<EMAIL>>
Date:	Tue May 1 18:04:57 2018 +0800

    Patch _free_dbg to make Debug mode in MSVC works

    Like _free_base, _free_dbg is called by CRT internal functions or
    operator delete in Debug mode.

    This closes #719 and closes #894.

    [<EMAIL>: trivial formatting fixes]
    [<EMAIL>: build free_dbg even in release builds]

M	src/windows/patch_functions.cc

commit 267f431d8071799282f9f4af10455b7b6adeee38
Author: Gabriel Marin <<EMAIL>>
Date:	Thu Aug 2 17:16:25 2018 -0700

    Use indirect system calls in the linux spinlock implementation

M	src/base/spinlock_linux-inl.h

commit 73ee9b15440d72d5c4f93586ea1179c0a265980c
Author: Gabriel Marin <<EMAIL>>
Date:	Mon Jul 2 09:31:10 2018 -0700

    Use indirect system calls in the mmap malloc hooks.

    sys_{mmap|munmap|mremap}(...) calls are replaced with
    syscall(SYS_{mmap|munmap|mremap}, ...).

M	src/malloc_hook_mmap_linux.h

commit 3af509d4f91aa1fae3f8caeb17b158f20e9b5e59
Author: Gabriel Marin <<EMAIL>>
Date:	Tue Jul 17 18:28:01 2018 -0700

    benchmark: use angle brackets to include ucontext.h

    Using quotes for a system header file fails a presubmit check in
    Chromium.

M	benchmark/unwind_bench.cc

commit 0cdda6d7cc5e082bb704a9352934148e13dc0a0d
Author: Gabriel Marin <<EMAIL>>
Date:	Tue Jul 17 18:36:31 2018 -0700

    use utf-8 for special symbols

    A few files in the windows port that haven't been touched in a
    long time,
    use non-utf8 codes for special symbols, breaking a presubmit
    checker in
    Chromium.

M	src/windows/ia32_modrm_map.cc
M	src/windows/ia32_opcode_map.cc
M	src/windows/mini_disassembler.h

commit c7a0cfda880a7310a030c62546f04a2987a11ec2
Author: etienneb <<EMAIL>>
Date:	Thu Apr 20 12:58:02 2017 -0700

    Fix potential missing nul character on resolved symbol names

    The symbol name returned by SymFromName may not contains a NUL
    character
    when the symbol name is exactly the size of the buffer. It believe
    this
    may also happen when the symbol name is too long and truncated.

    The original code is based on:
      https://msdn.microsoft.com/en-us/library/windows/desktop/ms680580(v=vs.85).aspx

    A right implementation can be found here:
      https://cs.chromium.org/chromium/src/base/debug/stack_trace_win.cc?l=145&rcl=f4ecb9e37e9e2d59e32b8b96f23ac4a1e33b9552

    As described here:
      https://msdn.microsoft.com/en-us/library/windows/desktop/ms680686(v=vs.85).aspx

      NameLen
	The length of the name, in characters, not including the
	null-terminating character.
      MaxNameLen
	The size of the Name buffer, in characters. If this member is 0,
	the Name member is not used.

    This issue was causing the catapult symbolisation script to encode
    incorrect (random) characters into the symbol names.
    See the example in the bug.

    Original BUG=713741

    Review-Url: https://codereview.chromium.org/2832643004
    Cr-Commit-Position: refs/heads/master@{#466098}

    BUG=724399,b:70905156

    Change-Id: Ia6de4ddbd5c66a966b74ecec3fda5e853f36073b

M	src/windows/addr2line-pdb.c

commit e42bfc8c06abd1ea1a7e92d1d6d72d1959ec27b6
Author: Gabriel Marin <<EMAIL>>
Date:	Thu Jul 19 11:10:31 2018 -0700

    tcmalloc: use relative addresses with the windows addr2line wrapper

    Modifies the Windows addr2line wrapper to expect addresses relative to
    DllBase to better simulate how addr2line works with modules in Linux.

    Windows DLLs have a concept of "default load address" which hints
    to the OS
    where to load the binary image after relocation. The dbghelp.dll
    symbolization library will load the module at this location in
    the virtual
    address space meaning the caller of these functions would need to
    be aware
    of the base address. This makes things unnecessarily complex in the
    face of
    ASLR and also diverges from the behavior of addr2line when used
    with linux-
    style DSOs. This CL simply adds the module base address to the
    incoming
    addresses, thereby making the input relative addresses for the
    module which
    both is easier to use and lines up better with linux's addr2line
    behavior.

    These changes were made originally as part of CL
    https://codereview.chromium.org/2730473002.

    BUG=724399,b:70905156

    Change-Id: I0abe9e0c380e7e60ae29a11021bb805b31718d08

M	src/windows/addr2line-pdb.c

commit d8f8d1cced71c37ff86ff577c3afe03b7cd1648f
Author: Gabriel Marin <<EMAIL>>
Date:	Wed Jul 18 18:50:30 2018 -0700

    tcmalloc: add long form flag '--exe' to specify the binary

    This change was originally made as part of CL
    https://codereview.chromium.org/2730473002

    BUG=724399,b:70905156

    Change-Id: I33ee28eac4dab6077cf162358cf4c10b41c0f6a2

M	src/windows/addr2line-pdb.c

commit 25c53aca125b54df1d5c0e64c96f074bdb92ade2
Author: Gabriel Marin <<EMAIL>>
Date:	Wed Jul 18 18:39:15 2018 -0700

    tcmalloc: fixes for the windows addr2line wrapper

    Fixes a few small format string bugs and makes the windows library
    configuration #defines more friendly for chromium builds.

    Based on original CL: https://codereview.chromium.org/2730473002

    BUG=724399,b:70905156

    Change-Id: If54c6cfc5c508744d2ee18f251677b2456acda72

M	src/windows/addr2line-pdb.c

commit f02e28f3488122f497755de59dc822f08c6449b9
Author: Gabriel Marin <<EMAIL>>
Date:	Tue Jul 31 15:35:53 2018 -0700

    Replace builtin_expect configure test with a direct GCC compiler check

M	configure.ac
M	src/base/basictypes.h

commit 62c4eca6e7ec9dc78333dc6b499f90eef9849378
Author: obones <<EMAIL>>
Date:	Fri Jul 20 11:07:02 2018 +0200

    Under x64, the PE loader looks for callbacks in constant sections

    [<EMAIL>: deleted some trailing whitespace]

M	src/windows/port.cc

commit 0b588e7490eac4ef37b59c9265f1f3f05f4bb27c
Author: Brian Silverman <<EMAIL>>
Date:	Tue Jul 31 15:53:47 2018 -0700

    Fix uninitialized memory use in sampler_test

    Sampler's documentation states the following:
      C++03 requires that types stored in TLS be POD.  As a result,
      you must
      initialize these members to {0, 0, false} before using this class!

    However, the test code wasn't doing that. MemorySanitizer and
    UndefinedBehaviorSanitizer both failed because of it.

M	src/tests/sampler_test.cc

commit 51a5613f21a34197ed655c363c54c71cf68271ba
Author: Aliaksei Kandratsenka <<EMAIL>>
Date:	Sun Aug 5 15:28:27 2018 -0700

    Upgrade MSVC projects to MSVC2015

M	Makefile.am
M	gperftools.sln
D	vsprojects/addr2line-pdb/addr2line-pdb.vcproj
A	vsprojects/addr2line-pdb/addr2line-pdb.vcxproj
A	vsprojects/addr2line-pdb/addr2line-pdb.vcxproj.filters
D	vsprojects/addressmap_unittest/addressmap_unittest.vcproj
A	vsprojects/addressmap_unittest/addressmap_unittest.vcxproj
A	vsprojects/addressmap_unittest/addressmap_unittest.vcxproj.filters
D
vsprojects/current_allocated_bytes_test/current_allocated_bytes_test.vcproj
A
vsprojects/current_allocated_bytes_test/current_allocated_bytes_test.vcxproj
A
vsprojects/current_allocated_bytes_test/current_allocated_bytes_test.vcxproj.filters
D	vsprojects/frag_unittest/frag_unittest.vcproj
A	vsprojects/frag_unittest/frag_unittest.vcxproj
A	vsprojects/frag_unittest/frag_unittest.vcxproj.filters
D	vsprojects/libtcmalloc_minimal/libtcmalloc_minimal.vcproj
A	vsprojects/libtcmalloc_minimal/libtcmalloc_minimal.vcxproj
A	vsprojects/libtcmalloc_minimal/libtcmalloc_minimal.vcxproj.filters
D
vsprojects/low_level_alloc_unittest/low_level_alloc_unittest.vcproj
A
vsprojects/low_level_alloc_unittest/low_level_alloc_unittest.vcxproj
A
vsprojects/low_level_alloc_unittest/low_level_alloc_unittest.vcxproj.filters
D	vsprojects/malloc_extension_test/malloc_extension_test.vcproj
A	vsprojects/malloc_extension_test/malloc_extension_test.vcxproj
A
vsprojects/malloc_extension_test/malloc_extension_test.vcxproj.filters
D	vsprojects/malloc_hook_test/malloc_hook_test.vcproj
A	vsprojects/malloc_hook_test/malloc_hook_test.vcxproj
A	vsprojects/malloc_hook_test/malloc_hook_test.vcxproj.filters
D	vsprojects/markidle_unittest/markidle_unittest.vcproj
A	vsprojects/markidle_unittest/markidle_unittest.vcxproj
A	vsprojects/markidle_unittest/markidle_unittest.vcxproj.filters
D	vsprojects/nm-pdb/nm-pdb.vcproj
A	vsprojects/nm-pdb/nm-pdb.vcxproj
A	vsprojects/nm-pdb/nm-pdb.vcxproj.filters
D	vsprojects/packed-cache_test/packed-cache_test.vcproj
A	vsprojects/packed-cache_test/packed-cache_test.vcxproj
A	vsprojects/packed-cache_test/packed-cache_test.vcxproj.filters
D	vsprojects/page_heap_test/page_heap_test.vcproj
A	vsprojects/page_heap_test/page_heap_test.vcxproj
A	vsprojects/page_heap_test/page_heap_test.vcxproj.filters
D	vsprojects/pagemap_unittest/pagemap_unittest.vcproj
A	vsprojects/pagemap_unittest/pagemap_unittest.vcxproj
A	vsprojects/pagemap_unittest/pagemap_unittest.vcxproj.filters
D	vsprojects/preamble_patcher_test/preamble_patcher_test.vcproj
A	vsprojects/preamble_patcher_test/preamble_patcher_test.vcxproj
A
vsprojects/preamble_patcher_test/preamble_patcher_test.vcxproj.filters
D	vsprojects/realloc_unittest/realloc_unittest.vcproj
A	vsprojects/realloc_unittest/realloc_unittest.vcxproj
A	vsprojects/realloc_unittest/realloc_unittest.vcxproj.filters
D	vsprojects/sampler_test/sampler_test.vcproj
A	vsprojects/sampler_test/sampler_test.vcxproj
A	vsprojects/sampler_test/sampler_test.vcxproj.filters
D	vsprojects/stack_trace_table_test/stack_trace_table_test.vcproj
A	vsprojects/stack_trace_table_test/stack_trace_table_test.vcxproj
A
vsprojects/stack_trace_table_test/stack_trace_table_test.vcxproj.filters
D	vsprojects/system-alloc_unittest/system-alloc_unittest.vcproj
A	vsprojects/system-alloc_unittest/system-alloc_unittest.vcxproj
A
vsprojects/system-alloc_unittest/system-alloc_unittest.vcxproj.filters
D
vsprojects/tcmalloc_minimal_large/tcmalloc_minimal_large_unittest.vcproj
A
vsprojects/tcmalloc_minimal_large/tcmalloc_minimal_large_unittest.vcxproj
A
vsprojects/tcmalloc_minimal_large/tcmalloc_minimal_large_unittest.vcxproj.filters
D
vsprojects/tcmalloc_minimal_unittest/tcmalloc_minimal_unittest.vcproj
A
vsprojects/tcmalloc_minimal_unittest/tcmalloc_minimal_unittest.vcxproj
A
vsprojects/tcmalloc_minimal_unittest/tcmalloc_minimal_unittest.vcxproj.filters
D	vsprojects/thread_dealloc_unittest/thread_dealloc_unittest.vcproj
A	vsprojects/thread_dealloc_unittest/thread_dealloc_unittest.vcxproj
A
vsprojects/thread_dealloc_unittest/thread_dealloc_unittest.vcxproj.filters
D	vsprojects/tmu-static/tmu-static.vcproj
A	vsprojects/tmu-static/tmu-static.vcxproj
A	vsprojects/tmu-static/tmu-static.vcxproj.filters

commit 44da4ce5397344342e71ea3cc2c246c2ca4aedb6
Author: Aliaksey Kandratsenka <<EMAIL>>
Date:	Sun Aug 5 15:09:20 2018 -0700

    build with c++11 or later

M	configure.ac
A	m4/ax_cxx_compile_stdcxx.m4

commit f47a52ce85c3d8d559aaae7b7a426c359fbca225
Author: HolyWu <<EMAIL>>
Date:	Mon Apr 30 15:41:50 2018 +0800

    Make _recalloc adhere to MS's definition

M	src/tcmalloc.cc
M	src/windows/override_functions.cc

commit fe87ffb7ea5a7c6ce4dea45222331716907ddbf4
Author: Junhao Li <<EMAIL>>
Date:	Sun May 20 13:45:32 2018 -0400

    Disable large allocation report by default

    Fixes issue #360.

    [<EMAIL>: adjusted commit message a bit]
    [<EMAIL>: adjusted configure help message]
    Signed-off-by: Aliaksey Kandratsenka <<EMAIL>>

M	configure.ac
M	src/tcmalloc.cc

commit 9608fa3bcf8020d35f59fbf70cd3cbe4b015b972
Author: Aliaksey Kandratsenka <<EMAIL>>
Date:	Sun Apr 29 16:55:59 2018 -0700

    bumped version to 2.7

M	NEWS
M	configure.ac
M	src/windows/config.h
M	src/windows/gperftools/tcmalloc.h

commit db890ccfade3cf054ec330dfb4a9ddfab971367e
Author: HolyWu <<EMAIL>>
Date:	Mon Apr 30 12:08:51 2018 +0800

    Clean up src/windows/config.h

M	src/windows/config.h

commit 497ea331654f7ea9e4e6405f5beaea8ebcc9d10b
Author: HolyWu <<EMAIL>>
Date:	Sun Apr 15 22:47:17 2018 +0800

    Fix WIN32_OVERRIDE_ALLOCATORS for VS2017

    At first I try to add some functions as what Chrome does at their
    https://chromium.googlesource.com/chromium/src/+/master/base/allocator/allocator_shim_override_ucrt_symbols_win.h,
    but it still fails. So I decide to remove all heap-related objects
    from libucrt.lib to see what happens. At the end I find that a lot of
    functions in the CRT directly invoke _malloc_base instead of
    malloc (and the others alike), hence we need to override them as well.

    This should close issue #716.

    [<EMAIL>: added reference to ticket]
    Signed-off-by: Aliaksey Kandratsenka <<EMAIL>>

M	src/libc_override.h
M	src/tcmalloc.cc
M	src/windows/override_functions.cc
M	src/windows/port.h

commit ebc85cca9083241ae360e0c68e2f1d7538001fa3
Author: HolyWu <<EMAIL>>
Date:	Tue Apr 17 11:40:49 2018 +0800

    Enable aligned new/delete declarations on Windows when applicable

M	src/windows/gperftools/tcmalloc.h
M	src/windows/gperftools/tcmalloc.h.in

commit a3badd6d2127c161f6fbad853fccd6e8064fcde7
Author: Aliaksey Kandratsenka <<EMAIL>>
Date:	Sun Apr 29 16:34:54 2018 -0700

    Really fix CheckAddressBits compilation warning

    It looks like previous fix introduced another warning on gcc for
    i386. Somehow it barked on kAddressBits shift even that shift was dead
    code.

    Anyways, we now avoid possibility of undefined shift even
    stronger. And it actually removes compile warning on all compilers I
    tested.

M	src/system-alloc.cc

commit 7c718fe1768273f94acf2bd43cfa1f44a4b9bdcc
Author: HolyWu <<EMAIL>>
Date:	Thu Apr 19 13:49:28 2018 +0800

    Add tests for sized deallocation

M	src/tests/tcmalloc_unittest.cc

commit 30e5e614a846d5f046454341f108bc4cb2b70189
Author: Fabrice Fontaine <<EMAIL>>
Date:	Sun Apr 22 19:17:12 2018 +0200

    Fix build without static libraries

    Only add -static to malloc_bench_LDFLAGS and binary_trees_LDFLAGS if
    ENABLE_STATC is set otherwise build with some compilers will fail if
    user has decided to build only the shared version of gperftools
    libraries

    Signed-off-by: Fabrice Fontaine <<EMAIL>>

M	Makefile.am

commit 836c4f29a585e4b176738f64cf8d312171be5fe0
Author: Ben Dang <<EMAIL>>
Date:	Fri Apr 13 10:54:05 2018 -0700

    Update documentation for heap_checker.html

    Make it clear that the static methods used require the "namespace",
    `HeapLeakChecker::`.

M	docs/heap_checker.html

commit e47d0d1c5142a4ad1203e58db31af2f58bafcda9
Author: Tulio Magno Quites Machado Filho <<EMAIL>>
Date:	Tue Apr 10 15:56:52 2018 -0300

    powerpc: Re-enable VDSO support

    Powerpc requires VDSO support in order to produce a stack trace.
    Without this, it isn't possible to complete a build.
    Tested on powerpc, powerpc64 and powerpc64le.

M	src/base/vdso_support.h

commit 0a66dd3a6ac60e57d9c01f17309a40e2ebe06837
Author: Christoph Müllner <<EMAIL>>
Date:	Sat Apr 7 09:00:09 2018 +0200

    linux: add aarch64_ilp32 support.

    This patch adds aarch64 (arm64) ILP32 support by using
    the proper syscalls for sys_fcntl(), sys_fstat(), sys_lseek()
    and sys_mmap().

    Signed-off-by: Christoph Müllner <<EMAIL>>

M	src/base/linux_syscall_support.h

commit 05dff0966362932631a2120eaeb8eebb50dd9300
Author: Christoph Müllner <<EMAIL>>
Date:	Sat Apr 7 08:30:32 2018 +0200

    Fix signature of sbrk.

    The manpage of sbrk says that its argument is of type intptr_t.
    This patch fixes a compiler warning on gcc 7.3.0.

    Signed-off-by: Christoph Müllner <<EMAIL>>

M	src/malloc_hook_mmap_linux.h

commit 33ae0ed2ae7ce143276658be029c28ec6f2a73c9
Author: Aliaksey Kandratsenka <<EMAIL>>
Date:	Mon Apr 9 20:57:29 2018 -0700

    unbreak compilation on GNU/Linux i386

    Recent commit to fix int overflow for implausibly huge allocation
    added call to std::min. Notably, first arg was old size divided by
    unsigned long 4. And on GNU/Linux i386 size_t is not long. So such
    division was promoting first arg to unsigned long while second arg was
    still size_t, so just unsigned. And that caused compilation to fail.

    Fix is droping 'ul'.

M	src/tcmalloc.cc

commit 977e0d45003514d088e4bb83474c985094acee47
Author: Christoph Müllner <<EMAIL>>
Date:	Sat Apr 7 08:35:42 2018 +0200

    Remove not needed header in vdso_support.cc.

    Signed-off-by: Christoph Müllner <<EMAIL>>

M	src/base/vdso_support.cc

commit 36bfa9a4046109efa40ccc0806c72331de9c915b
Author: Mao <<EMAIL>>
Date:	Mon Mar 26 17:22:19 2018 +0800

    Enable tcmalloc VDSO support only on x86 to reduce static initializers

    Background context
    ------------------
    crrev.com/1466173002 switched the GN tcmalloc target from source_set
    -> static_library. There are good reasons for keeping tcmalloc a
    source_set (see "Note on static libraries" in [1]).  However, in the
    current state source_set was exposing extra static initializers in the
    GN build which, are not present in the gyp build due to the linker gc
    sections.

    Resolution of this CL
    ---------------------
    The fact that vdso_support.cc is GC-ed by the linker is the symptom
    that such code is unreachable. A search in the codebase shows that the
    only client is stacktrace_x86-inl.h, which depends on VDSO only when
    defined(__linux__) && defined(__i386__) This CL is therefore matching
    this condition in vdso_support.h and conditioning the #define
    HAVE_VDSO_SUPPORT with the same conditions.

    [1]
    https://chromium.googlesource.com/chromium/src/+/master/tools/gn/docs/cookbook.md

    References:
    https://bugs.chromium.org/p/chromium/issues/detail?id=559766
    https://bugs.chromium.org/p/chromium/issues/detail?id=564618

M	src/base/vdso_support.h

commit 1cb5de6db9f30daa56a0b54652a7ff6da288c1bd
Author: Mao <<EMAIL>>
Date:	Mon Mar 26 17:28:28 2018 +0800

    Explicitly prevent int overflow

M	src/tcmalloc.cc

commit 8f63f2bb983e93b57e24a05964104f1d833c575b
Author: Aliaksey Kandratsenka <<EMAIL>>
Date:	Sat Mar 24 21:15:01 2018 -0700

    Correctly detect presence of various functions in tcmalloc.h

    This fixes test miscompilations on some clang 6.0
    installations. Particularly issue #946.

    Defines tested by tcmalloc.h where totally wrong defines. configure
    actually puts defines of HAVE_DECL_FOOBAR kind in config.h. Which is
    what we're using now.

M	src/tcmalloc.h

commit 736648887b0b27ec70a50f0954a614dd65c1eb12
Author: Aliaksey Kandratsenka <<EMAIL>>
Date:	Sat Mar 24 15:54:48 2018 -0700

    Don't test OOM handling of debugallocator

    This may fix issue #969.

    When testing OOM handling we set up sys allocator that fails memory
    allocation. But debugallocator itself allocates some internal metadata
    memory via malloc and crashes if those allocations fail. So
    occasionally this test failed when debugallocator's internal malloc
    ended up causing sys allocator.

    So instead of failing tests from time to time, we drop it for debug
    allocator. It's OOM handling is already crashy anyways.

M	src/tests/tcmalloc_unittest.cc

commit c4a8e00da41c43da7e78858e06f3153f09f6967d
Author: Aliaksey Kandratsenka <<EMAIL>>
Date:	Sat Mar 24 19:09:15 2018 -0700

    Fix warning about one of CheckAddressBits functions unused

M	src/system-alloc.cc

commit 47c99cf492fa340671fa89afec263b8d6e859755
Author: Aliaksey Kandratsenka <<EMAIL>>
Date:	Sat Mar 24 18:29:06 2018 -0700

    unbreak printing large span stats

    One of recent commits started passing kMaxPages to printf but not used
    it. Thankfully compilers gave us warning. Apparently intention was to
    print real value of kMaxPages, so this is what we're doing now.

M	src/tcmalloc.cc

commit 34f78a2dcd8160b483d86474702785337bbeb7ac
Author: Aliaksey Kandratsenka <<EMAIL>>
Date:	Sun Mar 18 16:39:06 2018 -0700

    bumped version to 2.7rc

M	NEWS
M	configure.ac
M	src/windows/config.h
M	src/windows/gperftools/tcmalloc.h

commit db98aac55a90d45e9abe188c8a928c3561d3f223
Author: Todd Lipcon <<EMAIL>>
Date:	Sat Mar 17 09:43:34 2018 -0700

    Add a central free list for kMaxPages-sized spans

    Previously, the central free list with index '0' was always unused,
    since freelist index 'i' tracked spans of length 'i' and there are no
    spans of length 0. This meant that there was no freelist for spans of
    length 'kMaxPages'. In the default configuration, this corresponds to
    1MB, which is a relatively common allocation size in a lot of
    applications.

    This changes the free list indexing so that index 'i' tracks spans of
    length 'i + 1', meaning that free list index 0 is now used and
    freelist[kMaxPages - 1] tracks allocations of kMaxPages size (1MB by
    default).

    This also fixes the stats output to indicate '>128' for the large
    spans
    stats rather than the incorrect '>255' which must have referred to a
    historical value of kMaxPages.

    No new tests are added since this code is covered by existing tests.

M	docs/pageheap.dot
M	docs/pageheap.gif
M	docs/tcmalloc.html
M	src/common.h
M	src/page_heap.cc
M	src/page_heap.h
M	src/tcmalloc.cc

commit d7be9385609328a03c5cfe775473a7dc8e014fd3
Author: Aliaksey Kandratsenka <<EMAIL>>
Date:	Sun Mar 4 23:29:46 2018 -0800

    implement more robust detection of sized delete support

    As reported in issue #954, osx clang compiler is able to optimize our
    previous detection away while not really having runtime support for
    sized delete. So this time we use AC_LINK_IFELSE and more robust code
    to prevent compiler from optimizing away sized delete call. This
    should reliably catch "bad" compilers.

    Special thanks to Alexey Serbin for reporting the issue, suggesting a
    fix and verifying it.

    Fixes issue #954.

M	configure.ac

commit f1d3fe4a21e339a3fd6e4592ee7444484a7b92dc
Author: Aliaksey Kandratsenka <<EMAIL>>
Date:	Sun Feb 25 13:55:53 2018 -0800

    refactored handling of reverse span set iterator for correctness

    I.e. no more questionable memcpy and we run iterator's destructor when
    we remove span from SpanSet.

M	src/page_heap.cc
M	src/span.h

commit 59c77be0fad2a49e31d51877985e7c48f73afcea
Author: Todd Lipcon <<EMAIL>>
Date:	Sun Feb 11 16:21:42 2018 -0800

    Update docs for central page heap to reflect tree

M	docs/pageheap.dot
M	docs/pageheap.gif
M	docs/tcmalloc.html

commit 06c9414ec423ffe442c047b2560555f9d5847b1d
Author: Aliaksey Kandratsenka <<EMAIL>>
Date:	Sat Jun 3 15:31:06 2017 -0700

    Implemented O(log n) searching among large spans

    This is implemented via std::set with custom STL allocator that
    delegates to PageHeapAllocator. Free large spans are not linked
    together via linked list, but inserted into std::set. Spans also store
    iterators to std::set positions pointing to them. So that removing
    span from set is fast too.

    Patch implemented by Aliaksey Kandratsenka and Todd Lipcon based on
    earlier research and experimentation by James Golick.

    Addresses issue #535

    [<EMAIL>: added Todd's fix for building on OSX]
    [<EMAIL>: removed unnecessary Span constructor]
    [<EMAIL>: added const for SpanSet comparator]
    [<EMAIL>: added operator != for STLPageHeapAllocator]

M	src/page_heap.cc
M	src/page_heap.h
M	src/page_heap_allocator.h
M	src/span.h
M	src/tests/tcmalloc_unittest.cc

commit a42e44738a330783781541411392ba27df88b8b3
Author: Ishan Arora <<EMAIL>>
Date:	Tue Jan 9 01:33:13 2018 +0530

    typo in docs/tcmalloc.html

M	docs/tcmalloc.html

commit 71bf09aabe93dbe390ebb54c57d3c5b3d627c142
Author: Aliaksey Kandratsenka <<EMAIL>>
Date:	Sat Dec 9 09:58:31 2017 -0800

    bumped version to 2.6.3

M	NEWS
M	configure.ac
M	src/windows/config.h
M	src/windows/gperftools/tcmalloc.h

commit 0bccb5e658762f30e6e3abbc40dc244e8f8911c1
Author: Aliaksey Kandratsenka <<EMAIL>>
Date:	Sat Dec 9 08:51:19 2017 -0800

    fix malloc fast path for patched windows functions

    malloc_fast_path now receives oom function instead of full allocation
    function and windows/patch_function.cc wasn't updated until now. It
    caused assertion failures as reported in issue #944.

M	src/windows/patch_functions.cc

commit 8b1d13c631024ce7e80508c284c7bc5b1327e131
Author: Stephan Zuercher <<EMAIL>>
Date:	Tue Dec 5 09:49:36 2017 -0800

    configure.ac: use link check for std::align_val_t

M	configure.ac

commit 36ab068baa3b07c083bd79029622aa382aeff84a
Author: Stephan Zuercher <<EMAIL>>
Date:	Tue Dec 5 09:31:38 2017 -0800

    configure.ac: better test for -faligned-new

    XCode 9 provides only partial support for aligned new/delete when
    -faligned-new
    is specified. Require successful linking to enable aligned new/delete.

M	configure.ac

commit 6a4b0799975576c2722daa75b35f6f65220d22b6
Author: Aliaksey Kandratsenka <<EMAIL>>
Date:	Thu Nov 30 18:19:17 2017 +0000

    bumped version to 2.6.2

M	NEWS
M	configure.ac
M	src/windows/config.h
M	src/windows/gperftools/tcmalloc.h

commit 22917145186bc8bbb6d98090d788ed7c87b282b1
Author: Aliaksey Kandratsenka <<EMAIL>>
Date:	Thu Nov 30 16:36:33 2017 +0000

    implement fast-path for memalign/aligned_alloc/tc_new_aligned

    We're taking advantage of "natural" alignedness of our size classes
    and instead of previous loop over size classes looking for suitably
    aligned size, we now directly compute right size. See align_size_up
    function. And that gives us ability to use our existing malloc
    fast-path to make memalign neat and fast in most common
    cases. I.e. memalign/aligned_alloc now only tail calls and thus avoids
    expensive prologue/epilogue and is almost as fast as regular malloc.

M	src/common.cc
M	src/tcmalloc.cc

commit 8b9728b02325a9bee9afd3957e506c111ed40d40
Author: Aliaksey Kandratsenka <<EMAIL>>
Date:	Thu Nov 30 18:12:32 2017 +0000

    add memalign benchmark to malloc_bench

M	benchmark/malloc_bench.cc

commit 79c91a9810c928d7f6d0cb168c10ce470a714aa8
Author: Aliaksey Kandratsenka <<EMAIL>>
Date:	Wed Nov 29 21:15:04 2017 +0000

    always define empty PERFTOOLS_NOTHROW

    Because somehow clang still builds "this function will not throw" code
    even with noexcept. Which breaks performance of
    tc_malloc/tc_new_nothrow. The difference with throw() seems to be just
    which function is called when unexpected exception happens.

    So we work around this sillyness by simply dropping any exception
    specification when compiling tcmalloc.

M	src/tcmalloc.cc

commit 03da6afff57c12845260997213b6ad89e420bab3
Author: Aliaksey Kandratsenka <<EMAIL>>
Date:	Wed Nov 29 20:19:07 2017 +0000

    unbreak throw declarations on operators new/delete

    We now clearly separate PERFTOOLS_NOTHROW (used for tc_XXX functions)
    and throw()/noexcept (used for operators we define).

    The former is basically "nothrow() for our callers, nothing for
    us". It is roughly equivalent of what glibc declares for malloc and
    friends. If some exception-full C++ code calls such function it
    doesn't have to bother setting up exception handling around such
    call. Notably, it is still important for those functions to _not have
    throw() declarations when we're building tcmalloc. Because C++ throw()
    requires setting up handling of unexpected exceptions thrown from
    under such functions which we don't want.

    The later is necessary to have operators new/delete definitions have
    "correct" exception specifications to calm down compiler
    warnings. Particularly older clang versions warn if new/delete aren't
    defined with correct exception specifications. Also this commit fixes
    annoying gcc 7+ warning (and gnu++14 mode) that complains about
    throw() being deprecated.

M	src/base/basictypes.h
M	src/libc_override.h
M	src/libc_override_gcc_and_weak.h
M	src/libc_override_redefine.h
M	src/tests/tcmalloc_unittest.cc
M	src/windows/get_mangled_names.cc

commit 89fe59c8318b2f8cec8ce00182dd24c357252c96
Author: Aliaksey Kandratsenka <<EMAIL>>
Date:	Wed Nov 29 13:37:50 2017 +0000

    Fix OOM handling in fast-path

    Previous fast-path malloc implementation failed to arrange proper oom
    handling for operator new. I.e. operator new is supposed to call new
    handler and throw exception, which was not arranged in fast-path case.

    Fixed code now passes pointer for oom function to
    ThreadCache::FetchFromCentralCache which will call it in oom
    condition. Test is added to verify correct behavior.

    I've also updated some fast-path-related comments for more accuracy.

M	src/tcmalloc.cc
M	src/tests/tcmalloc_unittest.cc
M	src/thread_cache.cc
M	src/thread_cache.h

commit a29a0cf348e131d5b8ec26c39dabeac89cf13fcd
Author: Aliaksey Kandratsenka <<EMAIL>>
Date:	Tue Nov 28 14:43:11 2017 +0000

    delete-trailing-whitespace on thread_cache.*

M	src/thread_cache.cc
M	src/thread_cache.h

commit e6cd69bdecde074c3c7a52ccb099eafcecf668dc
Author: Aliaksey Kandratsenka <<EMAIL>>
Date:	Wed Nov 29 19:49:18 2017 +0000

    reintroduce aliasing for aligned delete

    Without aliasing performance is likely to be at least partially
    affected. There is still concern that aliasing between functions of
    different signatures is not 100% safe. We now explicitly list of
    architectures where aliasing is known to be safe.

M	src/tcmalloc.cc

commit fb30c3d435c4712785a50f8a422c76ad490a12b2
Author: Aliaksey Kandratsenka <<EMAIL>>
Date:	Wed Nov 29 19:10:23 2017 +0000

    fully disable aligned new on windows for now

M	src/windows/gperftools/tcmalloc.h
M	src/windows/gperftools/tcmalloc.h.in

commit 7efb3ecf37d88edf9cf9a43efb89b425eaf81d5e
Author: Andrey Semashev <<EMAIL>>
Date:	Sat Sep 30 17:47:35 2017 +0300

    Add support for C++17 operator new/delete for overaligned types.

    - Add auto-detection of std::align_val_t presence to configure
    scripts. This
      indicates that the compiler supports C++17 operator new/delete
      overloads
      for overaligned types.

    - Add auto-detection of -faligned-new compiler option that appeared
    in gcc 7.
      The option allows the compiler to generate calls to the new
      operators. It is
      needed for tests.

    - Added overrides for the new operators. The overrides are enabled
    if the
      support for std::align_val_t has been detected. The implementation
      is mostly
      based on the infrastructure used by memalign, which had to be
      extended to
      support being used by C++ operators in addition to C functions. In
      particular,
      the debug version of the library has to distinguish memory
      allocated by
      memalign from that by operator new. The current implementation
      of sized
      overaligned delete operators do not make use of the supplied
      size argument
      except for the debug allocator because it is difficult to calculate
      the exact
      allocation size that was used to allocate memory with
      alignment. This can be
      done in the future.

    - Removed forward declaration of std::nothrow_t. This was not
    portable as
      the standard library is not required to provide nothrow_t
      directly in
      namespace std (it could use e.g. an inline namespace within
      std). The <new>
      header needs to be included for std::align_val_t anyway.

    - Fixed operator delete[] implementation in libc_override_redefine.h.

    - Moved TC_ALIAS definition to the beginning of the file in
    tcmalloc.cc so that
      the macro is defined before its first use in nallocx.

    - Added tests to verify the added operators.

    [<EMAIL>: fixed couple minor warnings, and some
    whitespace change]
    [<EMAIL>: removed addition of TC_ALIAS in debug
    allocator]
    Signed-off-by: Aliaksey Kandratsenka <<EMAIL>>

M	Makefile.am
M	configure.ac
M	src/debugallocation.cc
M	src/gperftools/tcmalloc.h.in
M	src/libc_override_gcc_and_weak.h
M	src/libc_override_redefine.h
M	src/tcmalloc.cc
M	src/tests/tcmalloc_unittest.cc
M	src/windows/gperftools/tcmalloc.h
M	src/windows/gperftools/tcmalloc.h.in

commit 7a6e25f3b143c35ed6fff8bb2c0f8de1ab348a39
Author: Andrew Morrow <<EMAIL>>
Date:	Tue Nov 7 15:57:17 2017 -0500

    Add new statistics for the PageHeap

    [<EMAIL>: addressed init order mismatch warning]
    Signed-off-by: Aliaksey Kandratsenka <<EMAIL>>

M	src/page_heap.cc
M	src/page_heap.h
M	src/tcmalloc.cc

commit 6e3a702fb9c86eb450f22b326ecbceef4b0d6604
Author: Jianbo Yang <<EMAIL>>
Date:	Tue Oct 17 01:09:18 2017 +0800

    Fix data race setting size_left_ in ThreadCache::SetMaxSize

    This commit is to fix the data race in ThreadCache::SetMaxSize.
    ThreadCache::size_left_ is removed and ThreadCache::size_ is
    added. ThreadCache::size_left_ was introduced for optimization.
    It is updated in several functions of ThreadCache, including the
    ThreadCache::SetMaxSize. But thread A can update size_left_ of
    thread B via SetMaxSize without protection or synchronization.

    There should not be data race around ThreadCache::size_, for it
    isn't accessed by multi threads.

    The optimization of tail-call in tc_{malloc, new, free} is kept
    and no other logics are affected.

M	src/thread_cache.cc
M	src/thread_cache.h

commit 235471f96564aa95354839bb135661e561966001
Author: cs-lee <<EMAIL>>
Date:	Sun Aug 6 04:57:42 2017 -0700

    fix memory leak in Symbolize function

    [<EMAIL>: reworded commit message]
    Signed-off-by: Aliaksey Kandratsenka <<EMAIL>>

M	src/symbolize.cc

commit 47efdd60f596f7180689337cbba2dab35539ba98
Author: cs-lee <<EMAIL>>
Date:	Sun Aug 6 04:51:52 2017 -0700

    Added mising va_end() in TracePrintf function

    Normally the va_end function does not do anything,
    but it should be called because some platforms need it.

    [<EMAIL>: reworded commit message]
    Signed-off-by: Aliaksey Kandratsenka <<EMAIL>>

M	src/debugallocation.cc

commit 497b60ef0fa93ed41ccacf6ffd0d536789401d29
Author: Vladimir <<EMAIL>>
Date:	Fri Jul 14 15:13:32 2017 +0300

    Implemented GetProgramInvocationName on FreeBSD

    Few lines of code was taken from
    /usr/src/contrib/libexecinfo/backtrace.c

    [<EMAIL>: updated commit message
    Signed-off-by: Aliaksey Kandratsenka <<EMAIL>>

M	src/symbolize.cc

commit ac072a3fc768b9699b1e809a379f576aa3e48983
Author: Aliaksey Kandratsenka <<EMAIL>>
Date:	Sat Sep 23 14:55:33 2017 -0700

    Revert "Ignore current_instance heap allocation when leak sanitizer
    is enabled"

    This reverts commit 70a35422b5509a456584b132ad8ce4466af323ea.

M	Makefile.am
M	src/malloc_extension.cc

commit fb5987d57913867e3b0f20490461ea55bfabe9ee
Author: Aliaksey Kandratsenka <<EMAIL>>
Date:	Sat Sep 23 14:55:20 2017 -0700

    Revert "Ensure that lsan flags are appended on all necessary targets"

    This reverts commit a3bf61ca81b68e7792739c451aceef00cf7d7d03.

M	Makefile.am

commit 5815f02105acd5d7fd0e6ec038a9e96ccc36911f
Author: Aliaksey Kandratsenka <<EMAIL>>
Date:	Sat Sep 16 21:14:23 2017 -0700

    Use safe getenv for setting up backtrace capturing method

    This code runs very early, so using special "early" version of getenv
    is reasonable. It should fix issue #912.

M	src/stacktrace.cc

commit aab4277311759b32ae8c442b49edf002230ea165
Author: Dorin Lazăr <<EMAIL>>
Date:	Thu Sep 21 22:11:49 2017 +0300

    Fixed LTO warning about the mismatch between return values for
    ProfilingIsEnabledForAllThreads()

M	src/heap-checker.cc

commit d406f2285390c402e824dd28e6992f7f890dcdf9
Author: Aliaksey Kandratsenka <<EMAIL>>
Date:	Sat Aug 5 22:05:10 2017 -0700

    implement support for C11 aligned_alloc

    Just like glibc does, we simply alias it to memalign.

M	Makefile.am
M	src/libc_override_gcc_and_weak.h
M	src/libc_override_redefine.h

commit 92a27e41a137704772479277786d035763938921
Author: Piotr Sikora <<EMAIL>>
Date:	Mon Aug 21 15:06:23 2017 -0700

    Fix build on macOS.

    Fixes #910.

    Signed-off-by: Piotr Sikora <<EMAIL>>

M	Makefile.am

commit e033431e5a9e02e4d1882bb6fcd212c4fab56173
Author: Khem Raj <<EMAIL>>
Date:	Sun Jul 16 19:28:17 2017 -0700

    include fcntl.h for loff_t definition

    Fixes
    linux_syscall_support.h:2641:26: error: 'loff_t' has not been declared

    Signed-off-by: Khem Raj <<EMAIL>>

M	src/base/linux_syscall_support.h

commit e41bc414048eb4a37743e294bed3f1c63a1fa559
Author: Khem Raj <<EMAIL>>
Date:	Sat Jul 1 13:21:21 2017 -0700

    Use ucontext_t instead of struct ucontext

    Newer glibc has dropped the ucontext tag from exposing

    Signed-off-by: Khem Raj <<EMAIL>>

M	src/stacktrace_powerpc-linux-inl.h

commit bf840dec0495e17f5c8403e68e10b9d6bf05c559
Author: Aliaksey Kandratsenka <<EMAIL>>
Date:	Sun Jul 9 14:55:33 2017 -0700

    bumped version to 2.6.1

M	NEWS
M	configure.ac
M	src/windows/config.h
M	src/windows/gperftools/tcmalloc.h

commit 2d220c7e26bd279ec87f9d1fc694ca5114b20bee
Author: Romain Geissler <<EMAIL>>
Date:	Sun Jun 18 15:21:19 2017 +0200

    Replace "throw()" by "PERFTOOLS_NOTHROW"

    Automatically done with:
    sed -e 's/\<throw[[:space:]]*([[:space:]]*)/PERFTOOLS_NOTHROW/g' -i
    $(git grep -l 'throw[[:space:]]*([[:space:]]*)')

    [<EMAIL>: updated to define empty PERFTOOLS_NOTHROW
    only on pre-c++11 standards]

M	src/libc_override_gcc_and_weak.h
M	src/libc_override_redefine.h
M	src/tcmalloc.cc
M	src/windows/get_mangled_names.cc

commit c4de73c0e69b9a75b6795fdd4598234baed8496d
Author: Romain Geissler <<EMAIL>>
Date:	Sun Jun 18 15:16:16 2017 +0200

    Add PERFTOOLS_THROW where necessary (as detected by GCC).

M	src/base/basictypes.h
M	src/libc_override_gcc_and_weak.h
M	src/tests/tcmalloc_unittest.cc

commit e5fbd0e24e19bf3c9a8777cfc78e6188c5557025
Author: Romain Geissler <<EMAIL>>
Date:	Sun Jun 18 15:05:46 2017 +0200

    Rename PERFTOOLS_THROW into PERFTOOLS_NOTHROW.

    Automatically done with:
    sed -e 's/\<PERFTOOLS_THROW\>/PERFTOOLS_NOTHROW/g' -i $(git grep
    -l PERFTOOLS_THROW)

M	src/debugallocation.cc
M	src/gperftools/tcmalloc.h.in
M	src/tcmalloc.cc
M	src/windows/gperftools/tcmalloc.h
M	src/windows/gperftools/tcmalloc.h.in

commit eeb7b84c20146c0e2e039ce72a2ea083a94ba80d
Author: Aliaksey Kandratsenka <<EMAIL>>
Date:	Sat Jul 8 15:23:37 2017 -0700

    Register tcmalloc atfork handler as early as possible

    This is what other mallocs do (glibc malloc and jemalloc). The idea is
    malloc is usually initialized very eary. So if we register atfork
    handler at that time, we're likely to be first. And that makes our
    atfork handler a bit safer, since there is much less chance of some
    other library installing their "take all locks" handler first and
    having fork take malloc lock before library's lock and deadlocking.

    This should address issue #904.

M	src/static_vars.cc
M	src/static_vars.h
M	src/thread_cache.cc

commit 208c26caeff24fd8c1defea80cb723ec1ab4bf06
Author: Aliaksey Kandratsenka <<EMAIL>>
Date:	Tue Jul 4 19:50:04 2017 -0700

    Add initial syscall support for mips64 32-bit ABI

    This applies patch by Adhemerval Zanella from
    https://github.com/gperftools/gperftools/issues/845.

    Only malloc (i.e. tcmalloc_minimal) was tested to work so far.

M	src/base/linux_syscall_support.h
M	src/malloc_hook_mmap_linux.h

commit a3bf61ca81b68e7792739c451aceef00cf7d7d03
Author: Francis Ricci <<EMAIL>>
Date:	Wed Jul 5 12:39:41 2017 -0400

    Ensure that lsan flags are appended on all necessary targets

M	Makefile.am

commit 97646a193237af0f941e5bd3078eb72edbc2d548
Author: Aliaksey Kandratsenka <<EMAIL>>
Date:	Tue Jul 4 21:02:34 2017 -0700

    Add missing NEWS entry for recent 2.6 release

    Somehow I managed to miss this last commit in 2.6 release. So lets add
    it now even if it is too late.

M	NEWS

commit 4be05e43a1adab9ceea9bdaaae546fb938f0a86c
Author: Aliaksey Kandratsenka <<EMAIL>>
Date:	Tue Jul 4 20:35:25 2017 -0700

    bumped version up to 2.6

M	NEWS
M	configure.ac
M	src/windows/config.h
M	src/windows/gperftools/tcmalloc.h

commit 70a35422b5509a456584b132ad8ce4466af323ea
Author: Francis Ricci <<EMAIL>>
Date:	Thu Jun 15 11:04:33 2017 -0400

    Ignore current_instance heap allocation when leak sanitizer is enabled

    Without this patch, any user program that enables LeakSanitizer will
    see a leak from tcmalloc. Add a weak hook to __lsan_ignore_object,
    so that if LeakSanitizer is enabled, the allocation can be ignored.

M	Makefile.am
M	src/malloc_extension.cc

commit 6eca6c64fabbff55c43a78537e466312524b3acd
Author: Aliaksey Kandratsenka <<EMAIL>>
Date:	Sat Jul 1 18:48:58 2017 -0700

    Revert "issue-654: [pprof] handle split text segments"

    This reverts commit 8c3dc52fcfe02412a529769a22cbc75388a5d368.

    People have reported issues with this so lets stay safe and use older
    even if less powerful code.

M	src/pprof

commit a495969cb6157cc361fc03228ae12ff7c0ecac4b
Author: KernelMaker <<EMAIL>>
Date:	Wed May 24 16:46:49 2017 +0800

    update the prev_class_size in each loop, or the min_object_size of
    tcmalloc.thread will always be 1 when calling GetFreeListSizes

M	src/tcmalloc.cc

commit 163224d8af5abdbbd8208ad6d37f13fb450af46f
Author: Kim Gräsman <<EMAIL>>
Date:	Fri May 26 13:09:03 2017 +0200

    Document HEAPPROFILESIGNAL environment variable

M	docs/heapprofile.html

commit 5ac82ec5b96d24219efd4c8aec47a45466eabd00
Author: Aliaksey Kandratsenka <<EMAIL>>
Date:	Mon May 29 13:07:39 2017 -0700

    added stacktrace capturing benchmark

M	.gitignore
M	Makefile.am
A	benchmark/getcontext_light.cc
A	benchmark/unwind_bench.cc

commit c571ae2fc9433e958f29b3c3525d34c22a9cb884
Author: Aliaksey Kandratsenka <<EMAIL>>
Date:	Mon May 22 19:04:20 2017 -0700

    2.6rc4

M	NEWS
M	configure.ac
M	src/windows/config.h
M	src/windows/gperftools/tcmalloc.h

commit f2bae51e7e609855c26095f14ffbb84082694acb
Author: Aliaksey Kandratsenka <<EMAIL>>
Date:	Mon May 22 18:58:15 2017 -0700

    Revert "Revert "disable dynamic sized delete support by default""

    This reverts commit b82d89cb7c8781a6028f6f5959cabdc5a273aec3.

    Dynamic sized delete support relies on ifunc handler being able to
    look up environment variable. The issue is, when stuff is linked with
    -z now linker flags, all relocations are performed early. And sadly
    ifunc relocations are not treated specially. So when ifunc handler
    runs, it cannot rely on any dynamic relocations at all, otherwise
    crash is real possibility. So we cannot afford doing it until (and if)
    ifunc is fixed.

    This was brought to my attention by Fedora people at
    https://bugzilla.redhat.com/show_bug.cgi?id=1452813

M	configure.ac

commit 6426c0cc8049dd50a681dc337ac9962577d5fa14
Author: Aliaksey Kandratsenka <<EMAIL>>
Date:	Sun May 21 22:55:29 2017 -0700

    2.6rc3

M	NEWS
M	configure.ac
M	src/windows/config.h
M	src/windows/gperftools/tcmalloc.h

commit 0c0e2fe43b5095d19470abdc3b4d83613fe37c6d
Author: Aliaksey Kandratsenka <<EMAIL>>
Date:	Mon May 22 02:39:29 2017 -0700

    enable 48-bit page map on msvc as well

M	src/common.h

commit 83d6818295cb337b6201fe307e15755df9dcd47f
Author: Aliaksey Kandratsenka <<EMAIL>>
Date:	Mon May 22 02:38:13 2017 -0700

    speed up 3-level page map access

    There is no need to have pointer indirection for root node. This also
    helps the case of early free of garbage pointer because we didn't
    check root_ pointer for NULL.

M	src/pagemap.h

commit f7ff175b92df5577aef9caf3fb02f9ff37b822ca
Author: Aliaksey Kandratsenka <<EMAIL>>
Date:	Sun May 21 23:28:48 2017 -0700

    add configure-time warning on unsupported backtrace capturing

    Both libgcc and libc's backtrace() are not really options for stack
    trace capturing from inside profiling signal handler. So lets warn
    people.

M	configure.ac

commit cef582350c952bad761476d01ea64bb4087371ce
Author: Aliaksey Kandratsenka <<EMAIL>>
Date:	Sun May 21 23:15:59 2017 -0700

    align fast-path functions only if compiler supports that

    Apparently gcc only supports __attribute__((aligned(N))) on functions
    only since version 4.3. So lets test it in configure script and only
    use when possible. We now use CACHELINE_ALIGNED_FN macro for aligning
    functions.

M	configure.ac
M	src/base/basictypes.h
M	src/tcmalloc.cc

commit bddf862b189c4508d5212f6e0e8ea81c4dd18811
Author: Aliaksey Kandratsenka <<EMAIL>>
Date:	Sun May 21 22:24:19 2017 -0700

    actually support very early freeing of NULL

    This was caught by unit tests on centos 5. Apparently some early
    thingy is trying to do vprintf which calls free(0). Which used to
    crash since before size class cache is initialized it'll report
    hit (with size class 0) for NULL pointer, so we'd miss the case of
    checking NULL pointer free and crash.

    The fix is to check for IsInited in the case when thread cache is
    null, and if so then we escalte to free_null_or_invalid.

M	src/tcmalloc.cc
M	src/thread_cache.cc

commit 07a124d8c16bc7d52524ceb9f50d7a65b868e129
Author: Aliaksey Kandratsenka <<EMAIL>>
Date:	Sun May 21 22:22:21 2017 -0700

    don't use arg-ful constructor attribute for early nallocx test

    101 is not very early anyways and arg-ful constructor attribute is
    only supported since gcc 4.3 (and e.g. rhel 5's compiler fails to
    compile it). So there seems to be very little value trying to ask for
    priority of 101.

M	src/tests/tcmalloc_unittest.cc

commit 5346b8a4def33915e6f877e308fb948f570a6120
Author: Aliaksey Kandratsenka <<EMAIL>>
Date:	Sun May 21 22:18:01 2017 -0700

    don't depend on SIZE_MAX definition in sampler.cc

    It was reported that SIZE_MAX isn't getting defined in C++ mode when
    C++ standard is less than c++11. Because we still want to support
    non-c++11 systems (for now), lets make it simple and not depend on
    SIZE_MAX (original google-internal code used
    std::numeric_limits<ssize_t>::max, but that failed to compile on
    msvc).

    Fixes issue #887 and issue #889.

M	src/sampler.cc

commit 50125d8f70007fb2aa4d1c87df6a3092a3371edf
Author: Aliaksey Kandratsenka <<EMAIL>>
Date:	Mon May 15 00:02:43 2017 -0700

    2.6rc2

M	NEWS
M	configure.ac
M	src/windows/config.h
M	src/windows/gperftools/tcmalloc.h

commit a5e8e42a47fc0175c8044d0d77bf192b03347964
Author: Aliaksey Kandratsenka <<EMAIL>>
Date:	Sun May 14 23:45:08 2017 -0700

    don't link-in libunwind if libunwind.h is missing

    I got report that some build environments for
    https://github.com/lyft/envoy are having link-time issue due to
    linking libunwind. It was happening despite libunwind.h being present,
    which is clear bug as without header we won't really use libunwind.

M	configure.ac

commit e92acdf98db229e8dc84993fd8895d55e6c69129
Author: Rajalakshmi Srinivasaraghavan <<EMAIL>>
Date:	Wed Apr 26 06:46:43 2017 -0300

    Fix compilation error for powerpc32

    Fix the following compilation error for powerpc32 platform when using
    latest glibc.
    error: ‘siginfo_t’ was not declared in this scope

M	src/stacktrace_powerpc-linux-inl.h

commit b48403a4b065830129e238feffe022abd93af807
Author: Aliaksey Kandratsenka <<EMAIL>>
Date:	Sun Apr 16 21:45:51 2017 -0700

    2.6rc

M	NEWS
M	configure.ac
M	src/windows/config.h
M	src/windows/gperftools/tcmalloc.h

commit 53f15325d93fbe0ba17bb3fac3da86ffd3f0f1ad
Author: Aliaksey Kandratsenka <<EMAIL>>
Date:	Sun May 14 20:24:27 2017 -0700

    fix compilation of tcmalloc_unittest.cc on older llvm-gcc

M	src/tests/tcmalloc_unittest.cc

commit b1d88662cb4a38ee47aa48076745898033526f9d
Author: Aliaksey Kandratsenka <<EMAIL>>
Date:	Sun Apr 2 19:27:00 2017 -0700

    change size class to be represented by 32 bit int

    This moves code closer to Google-internal version and provides for
    slightly tighter code encoding on amd64.

M	src/common.h
M	src/packed-cache-inl.h
M	src/page_heap.cc
M	src/page_heap.h
M	src/tcmalloc.cc
M	src/tests/packed-cache_test.cc
M	src/thread_cache.cc
M	src/thread_cache.h

commit 991f47a159f0e169883f06686f13c31688fa2bf0
Author: Aliaksey Kandratsenka <<EMAIL>>
Date:	Thu Feb 2 15:58:08 2017 -0800

    change default transfer batch back to 32

    Some tensorflow benchmarks are seeing large regression with elevated
    values. So lets stick to old safe default until we understand how
    to make
    larger values work for all workloads.

M	src/common.cc

commit 7bc34ad1f60be2df6ca38f4bffdba4daa9aa9a7d
Author: Aliaksey Kandratsenka <<EMAIL>>
Date:	Thu Feb 2 15:38:41 2017 -0800

    support different number of size classes at runtime

    With TCMALLOC_TRANSFER_NUM_OBJ environment variable we can change
    transfer batch size. And with that comes slightly different number of
    size classes depending on value of transfer batch size.

    We used to have hardcoded number of size classes, so we couldn't
    really support any batch size setting.

    This commit adds support for dynamic number of size classes (runtime
    value returned by Static::num_size_classes()).

M	src/central_freelist.cc
M	src/common.cc
M	src/common.h
M	src/page_heap.cc
M	src/static_vars.cc
M	src/static_vars.h
M	src/tcmalloc.cc
M	src/thread_cache.cc
M	src/thread_cache.h

commit 4585b78c8dae9183dbf5f124c0343a9f1244ed67
Author: Aliaksey Kandratsenka <<EMAIL>>
Date:	Tue Feb 21 00:27:39 2017 -0800

    massage allocation and deallocation fast-path for performance

    This is significant speedup of fast-path of malloc. Large part comes
    from avoiding expensive function prologue/epilogue. Which is achieved
    by making sure that tc_{malloc,new,free} etc are small functions that
    do only tail-calls. We keep only critical path in those functions and
    tail-call to slower "full" versions when we need to deal with less
    common case. This helps compiler generate much tidier code.

    Fast-path readyness check is now different too. We used to have "min
    size for slow path" variable, which was set to non-zero value when we
    know that thread cache is present and ready. We now have use
    thread-cache pointer not equal to NULL as readyness check.

    There is special ThreadCache::threadlocal_data_.fast_path_heap copy of
    that pointer that can be temporarily nulled to disable malloc fast
    path. This is used to enable emergency malloc.

    There is also slight change to tracking thread cache size. Instead of
    tracking total size of free list, it now tracks size headroom. This
    allows for slightly faster deallocation fast-path check where we're
    checking headroom to stay above zero. This check is a bit faster than
    comparing with max_size_.

M	src/linked_list.h
M	src/tcmalloc.cc
M	src/thread_cache.cc
M	src/thread_cache.h
M	src/windows/patch_functions.cc

commit 5964a1d9c98ea3c178435ff01f9b06e03eeda58d
Author: Aliaksey Kandratsenka <<EMAIL>>
Date:	Tue Feb 21 00:42:29 2017 -0800

    always inline a number of hot functions

M	src/page_heap.h
M	src/pagemap.h
M	src/tcmalloc.cc
M	src/thread_cache.h

commit e419b7b9a66c39b44115b01520fb25a5100cec83
Author: Aliaksey Kandratsenka <<EMAIL>>
Date:	Mon Feb 20 21:11:14 2017 -0800

    introduce ATTRIBUTE_ALWAYS_INLINE

M	src/base/basictypes.h
M	src/tcmalloc.cc

commit 7d588da7ec4f315ea2d02824d7e8813b0f95171d
Author: Aliaksey Kandratsenka <<EMAIL>>
Date:	Sun Dec 18 18:36:57 2016 -0800

    synchronized Sampler implementation with Google-internal version

    This is mostly dropping FastLog2 which was never necessary for
    performance, and making sampler to be called always, even if sampling
    is disabled (this benefits more for always-sampling case of Google
    fork).

    We're also getting TryRecordAllocationFast which is not used yet, but
    will be as part of subsequent fast-path speedup commit.

M	src/sampler.cc
M	src/sampler.h
M	src/static_vars.cc
M	src/tests/sampler_test.cc
M	src/thread_cache.h

commit 27da4ade70d45312bfdf334aa8cf0d63bf78df14
Author: Aliaksey Kandratsenka <<EMAIL>>
Date:	Tue Feb 21 00:41:43 2017 -0800

    reduce size of class_to_size_ array

    Since 32-bit int is enough and accessing smaller array will use a bit
    less of cache.

M	src/common.h
M	src/tcmalloc.cc
M	src/thread_cache.cc
M	src/thread_cache.h

commit 335f09d4e43a8413642e17e5ac374f925906c4e3
Author: Aliaksey Kandratsenka <<EMAIL>>
Date:	Mon Feb 20 23:49:15 2017 -0800

    use static location for pageheap

    Makes it a bit faster to access, since we're dropping pointer
    indirection.

M	src/static_vars.cc
M	src/static_vars.h

commit 6ff332fb517734cf01e86272fe37521b7155c995
Author: Aliaksey Kandratsenka <<EMAIL>>
Date:	Mon Feb 20 23:37:33 2017 -0800

    move size classes map earlier in SizeMap

    Since we access them more often, having at least one of them at offset
    0 makes pi{c,e} code a bit smaller.

M	src/common.h

commit 121b1cb32e7416ecb7b0c739588f1bd916c4913a
Author: Aliaksey Kandratsenka <<EMAIL>>
Date:	Mon Feb 20 22:57:43 2017 -0800

    slightly faster size class cache

    Lower bits of page index are still used as index into hash
    table. Those lower bits are zeroed, or-ed with size class and
    placed into hash table. So checking is just loading value from hash
    table, xoring with higher bits of address and checking if resultant
    value is lower than 128. Notably, size class 0 is not considered
    "invalid" anymore.

M	src/central_freelist.cc
M	src/packed-cache-inl.h
M	src/page_heap.cc
M	src/page_heap.h
M	src/tcmalloc.cc
M	src/tests/packed-cache_test.cc

commit b57c0bad41700238f524b28483a1c6c18c0a621f
Author: Aliaksey Kandratsenka <<EMAIL>>
Date:	Sun Apr 16 23:15:16 2017 -0700

    init tcmalloc prior to replacing system alloc

    Currently on windows, we're depending on uninitialized tcmalloc
    variables to detect freeing foreign malloc's chunks. This works
    somewhat by chance due to 0-initialized size classes cache working as
    cache with no values. But this is about to change, so lets do explicit
    initialization.

M	src/tcmalloc.cc

commit 71fa9f873065e3d7c1f4ce0581d26b6498712f00
Author: Aliaksey Kandratsenka <<EMAIL>>
Date:	Mon Feb 20 21:17:48 2017 -0800

    use 2-level page map for 48-bit addresses

    48 bits is size of x86-64 and arm64 address spaces. So using 2 levels
    map for them is slightly faster. We keep 3 levels for small-but-slow
    configuration, since 2 levels consume a bit more memory.

    This is partial port of Google-internal commit by Sanjay
    Ghemawat (same idea, different implementation).

M	src/page_heap.h
M	src/pagemap.h

commit bad70249dd5c829b4981aecdc25953800d6745c3
Author: Aliaksey Kandratsenka <<EMAIL>>
Date:	Mon Feb 20 21:12:58 2017 -0800

    use 48-bit addresses on 64-bit arms too

M	src/common.h

commit 5f12147c6dbfe2cfbdc7553521fe0110073135f0
Author: Aliaksey Kandratsenka <<EMAIL>>
Date:	Mon Feb 20 21:03:58 2017 -0800

    use hidden visibility for some key global variables

    So that our -fPIC code is faster

M	src/base/basictypes.h
M	src/static_vars.h

commit dfd53da578137d5962fe8d588980870bb0ab8aa9
Author: Aliaksey Kandratsenka <<EMAIL>>
Date:	Sun Jan 22 21:08:51 2017 -0800

    set ENOMEM in handle_oom

M	src/tcmalloc.cc

commit 14fd551072426cef317f43d587f91004d4fdae75
Author: Aliaksey Kandratsenka <<EMAIL>>
Date:	Sun Jan 8 06:02:04 2017 +0300

    avoid O(N²) in thread cache creation code

M	src/thread_cache.cc

commit 507a105e849422d5ceff4348d38aaf72371a6161
Author: Aliaksey Kandratsenka <<EMAIL>>
Date:	Sat Mar 12 14:25:10 2016 -0800

    pass original size to DoSampledAllocation

    It makes heap profiles more accurate. Google's internal malloc
    is doing
    it as well.

M	src/tcmalloc.cc

commit bb77979dea796ab743e1308af25e9259ec97f2b1
Author: Aliaksey Kandratsenka <<EMAIL>>
Date:	Sat Mar 12 18:10:19 2016 -0800

    don't declare throw() on malloc funtions since it is faster

    Apparently throw() on functions actually asks compiler to generate
    code
    to detect unexpected exceptions. Which prevents tail calls
    optimization.

    So in order to re-enable this optimization, we simply don't tell
    compiler about throw() at all. C++11 noexcept would be even better,
    but
    it is not universally available yet.

    So we change to no exception specifications. Which at least for gcc &
    clang on Linux (and likely for all ELF platforms, if not just all)
    really eliminates all overhead of exceptions.

M	src/debugallocation.cc
M	src/gperftools/tcmalloc.h.in
M	src/tcmalloc.cc
M	src/windows/gperftools/tcmalloc.h
M	src/windows/gperftools/tcmalloc.h.in

commit 89c74cb79ca41cd75a1f9131af4ea2ab362593ae
Author: Aliaksey Kandratsenka <<EMAIL>>
Date:	Sat Mar 12 20:26:40 2016 -0800

    handle duplicate google_malloc frames in malloc hook stack trace

    Subsequent optimization may cause multiple malloc functions in
    google_malloc section to be in call stack. Particularly when fast-path
    malloc function calls slow-path and compiler chooses to implement such
    call as regular call instead of tail-call.

    Because we need stacktrace just until first such function, once
    we find
    innermost such frame, we're simply checking if next outer frame
    is also
    google_malloc and consider it instead.

M	src/malloc_hook.cc

commit 0feb1109ac957144a50d964b0ff244a6a286174a
Author: Aliaksey Kandratsenka <<EMAIL>>
Date:	Sat Mar 12 21:08:57 2016 -0800

    fix stack trace capturing in debug malloc

    Particularly, hardcoded skip count was relying on certain behavior of
    compiler. Namely, that tail calls inside DebugDeallocate path are not
    actually implemented as tail calls.

    New implementation is using google_malloc section as a marker
    of malloc
    boundary. But in order for this to work, we have to prevent
    tail-call in
    debugallocation's tc_XXX functions. Which is achieved by doing
    volatile
    read of static variable at the end of such functions.

M	src/debugallocation.cc

commit 0506e965ee5184490fbe7acea470458562128a79
Author: Aliaksey Kandratsenka <<EMAIL>>
Date:	Sun Dec 18 18:36:00 2016 -0800

    replace LIKELY/UNLIKELY with PREDICT_{TRUE,FALSE}

    Google-internal code is using PREDICT_TRUE/FALSE, so we should be
    doing it too.

M	src/base/basictypes.h
M	src/common.cc
M	src/common.h
M	src/emergency_malloc.h
M	src/malloc_hook-inl.h
M	src/tcmalloc.cc
M	src/thread_cache.h

commit 59a4987054021eaf24143a27cf01252fafb5052d
Author: Aliaksey Kandratsenka <<EMAIL>>
Date:	Sun Mar 13 16:04:43 2016 -0700

    prevent inlining ATTRIBUTE_SECTION functions

    So that their code is always executing in prescribed section.

M	src/base/basictypes.h

commit ebb575b8a0153a70f43fd1c3ec33fe30f94c97d0
Author: Aliaksey Kandratsenka <<EMAIL>>
Date:	Thu Feb 2 15:41:13 2017 -0800

    Revert "enabled aggressive decommit by default"

    This reverts commit 7da5bd014d77ddaf694054b1e3ae0a3ef92ab384.

    Some tensorflow benchmarks are getting slower with aggressive
    decommit.

M	src/static_vars.cc
M	src/tests/tcmalloc_unittest.sh

commit b82d89cb7c8781a6028f6f5959cabdc5a273aec3
Author: Aliaksey Kandratsenka <<EMAIL>>
Date:	Sun Jan 22 13:15:39 2017 -0800

    Revert "disable dynamic sized delete support by default"

    This reverts commit 06811b3ae4c5365880898148d188ae91d20f52f2.

M	configure.ac

commit fac0bb44d5661946d9839496ddf104942dd00abe
Author: Aliaksey Kandratsenka <<EMAIL>>
Date:	Sun Jan 22 13:11:05 2017 -0800

    Do not depend on memchr in commandlineflags::StringToBool

    It lets us use it from inside ifunc handler.

M	src/base/commandlineflags.h

commit 7d49f015a01e35155517332a2dfd95f16b568939
Author: Aliaksey Kandratsenka <<EMAIL>>
Date:	Sun Jan 22 13:08:34 2017 -0800

    Make GetenvBeforeMain work inside ifunc handler

    Depending on things such us strlen, which are ifunc-ed themselves is
    not supported from inside ifunc handler.

    Thus we implement strlen, memchr and memcmp in slow naive C++ just for
    that place.

M	src/base/sysinfo.cc

commit a2550b6309153c250f67255b6271ec988cc7d0fb
Author: Aliaksey Kandratsenka <<EMAIL>>
Date:	Sun Feb 26 23:46:18 2017 -0800

    turn bench_fastpath_throughput into actual throughput benchmark

    Previously we bumped size by 16 between iterations, but for many size
    classess that gave is subsequent iteration into same size
    class. Multiplying by prime number randomizes sizes more so speeds up
    this benchmark on at least modern x86.

M	benchmark/malloc_bench.cc

commit b762b1a4923f769487de818175655c1683d84db5
Author: Aliaksey Kandratsenka <<EMAIL>>
Date:	Sat Mar 12 14:12:02 2016 -0800

    added sized free benchmarks to malloc_bench

M	benchmark/malloc_bench.cc

commit 71ffc1cd6b3ea99c97ed6d0e16caeb9d1b20eec7
Author: Aliaksey Kandratsenka <<EMAIL>>
Date:	Sat Mar 12 14:11:40 2016 -0800

    added free lists randomization step to malloc_bench

M	benchmark/malloc_bench.cc

commit 732dfeb83d726de0aea47f0e6aa2abff3e0ad680
Author: Aliaksey Kandratsenka <<EMAIL>>
Date:	Sun Jan 22 13:57:31 2017 -0800

    Run StartStopNoOptionsEmpty profiledata unittest

    Somehow we had this test, but didn't arrange to actually run it. GCC
    gave warning and now we do.

M	src/tests/profiledata_unittest.cc

commit cbb312fbe8022378c4635b3075a80a7827555170
Author: Aliaksey Kandratsenka <<EMAIL>>
Date:	Sun Dec 18 11:08:54 2016 -0800

    aggressive decommit: only free necessary regions and fix O(N²)

    We used to decommit (with MADV_FREE or MADV_DONTNEED) whole combined
    span when freeing span in aggressive decommit mode. The issue with
    that is preceding or following span with which we combined span we're
    freeing could be freed already and fairly large. By passing all of
    that memory to decommit, we force kernel to scan all of those pages.

    When mass-freeing many objects old behavior led to O(N^2) behavior
    since freeing single span could lead to MADV_{FREE,DONTNEED}-ing of
    most of the heap.

    New implementation just does decommit of individual bits as needed.

    While there, I'm also adding locking to calls of
    PageHeap::{Get,Set}AggressiveDecommit.

    This partially (or mostly) fixes issue #839.

M	src/page_heap.cc
M	src/page_heap.h
M	src/tcmalloc.cc

commit 6d98223a9035ec1c5e9a7eb0196b21021d48a41f
Author: Aliaksey Kandratsenka <<EMAIL>>
Date:	Sun Feb 12 17:12:44 2017 -0800

    don't build with -fno-exceptions

    It looks like, in past it could produce better code. But since
    unwinding is totally different since almost forever now, there is no
    perfomance benefit of it anymore.

M	Makefile.am

commit d6a1931cce303ea279b744c9b438686e4f5994e5
Author: Aliaksey Kandratsenka <<EMAIL>>
Date:	Sat Mar 12 15:33:10 2016 -0800

    fixed warning in casting heap of checker's main_thread_counter

    I.e. compiler didn't like int to void * casting and upcasting to
    intptr_t first (which has same size as void *) makes it happier.

M	src/heap-checker.cc

commit 5c778701d98c642ecff97d044cd1c0b8b862aba3
Author: Aliaksey Kandratsenka <<EMAIL>>
Date:	Sun Feb 12 17:54:59 2017 -0800

    added tcmalloc minimal unittest with ASSERTs checked

M	.gitignore
M	Makefile.am

commit a9167617abbcebe9d472141c53a20ce00c8c8d75
Author: Aliaksey Kandratsenka <<EMAIL>>
Date:	Sat Apr 29 19:09:47 2017 -0700

    drop unused g_load_map variable in patch_functionc.cc

M	src/windows/patch_functions.cc

commit d52e56dcb5aad0521ccc67d7e57dfcb8d41ea568
Author: Aliaksey Kandratsenka <<EMAIL>>
Date:	Sat Apr 29 19:06:55 2017 -0700

    don't compare integer to NULL

M	src/windows/preamble_patcher.cc

commit bae00c0341f49ade473d24c117226552a59d3e75
Author: Aliaksey Kandratsenka <<EMAIL>>
Date:	Sun Apr 23 20:33:16 2017 -0700

    add fake_stacktrace_scope to few msvc projects

    Otherwise it predictably fails at linking phase due to missing
    symbols.

M
vsprojects/low_level_alloc_unittest/low_level_alloc_unittest.vcproj
M	vsprojects/tmu-static/tmu-static.vcproj

commit 79aab4fed40f9aac03dacb32a108861eff689beb
Author: Aliaksey Kandratsenka <<EMAIL>>
Date:	Sun Apr 16 22:17:56 2017 -0700

    correctly dllexport nallocx on windows

M	src/gperftools/nallocx.h
M	src/tcmalloc.cc

commit b010895a088703f512cb0e225c838306b2904069
Author: Aliaksey Kandratsenka <<EMAIL>>
Date:	Sun Apr 30 18:32:10 2017 -0700

    don't undef PERFTOOLS_DLL_DECL

    This is not necessary and will help adding more headers with
    dll-exported functions.

M	src/gperftools/tcmalloc.h.in
M	src/windows/gperftools/tcmalloc.h
M	src/windows/gperftools/tcmalloc.h.in

commit 491b1aca7edbbaed9ea6f7baaa547a35cf385130
Author: Aliaksey Kandratsenka <<EMAIL>>
Date:	Sat Apr 29 16:46:21 2017 -0700

    don't try to use pthread_atfork on windows

M	src/maybe_threads.cc
M	src/windows/mingw.h

commit 691045b95761bedb9170d758be82f9b2f15c673a
Author: Aliaksey Kandratsenka <<EMAIL>>
Date:	Sat Mar 12 14:41:28 2016 -0800

    suppress warnings from legacy headers while building legacy headers
    test

M	src/google/heap-checker.h
M	src/google/heap-profiler.h
M	src/google/malloc_extension.h
M	src/google/malloc_extension_c.h
M	src/google/malloc_hook.h
M	src/google/malloc_hook_c.h
M	src/google/profiler.h
M	src/google/stacktrace.h
M	src/google/tcmalloc.h
M	src/tests/simple_compat_test.cc

commit 22f7ceb97a63bcbc9d824a80916bb7a809fad1f2
Author: Aliaksey Kandratsenka <<EMAIL>>
Date:	Sat Apr 29 15:25:06 2017 -0700

    use unsigned for few flags in mini_disassembler_types.h

    We're initializing them with value that doesn't fit into signed 32-bit
    int and somehow mingw version that I have not just warns, but actually
    errors on that.

M	src/windows/mini_disassembler_types.h

commit 9b17a8a5ba1c8320d12c6e6df0ab54ff218b50e6
Author: Mostyn Bramley-Moore <<EMAIL>>
Date:	Thu May 11 20:40:18 2017 +0200

    remove superfluous size_t value >= 0 check

M	src/debugallocation.cc

commit 86ce69d77ff5f881c5701901bf7d1ef884f9e33a
Author: Eugene <<EMAIL>>
Date:	Sat Feb 4 17:57:34 2017 +0300

    Update binary_trees.cc

M	benchmark/binary_trees.cc

commit cd8586ed6c8bed550ad727ce8af83a1a6bd1ca8e
Author: Ola Olsson <<EMAIL>>
Date:	Tue Mar 21 14:07:16 2017 +0100

    Fix path names in README

M	README

commit 98753aa73772bb0fae8cee668e513d5754ab7737
Author: Aliaksey Kandratsenka <<EMAIL>>
Date:	Mon Feb 20 16:04:14 2017 -0800

    test that sized deallocation really works before enabling it

    Weirdly, some OSX compiler does allow -fsized-deallocation, yet barks
    on any attempt to actually use it at compile time (!). So lets detect
    this as well in configure and opt out as necessary.

    Should fix issue #870.

M	configure.ac

commit 5618ef78505a854bd0d2c145fd9cf7967226a20e
Author: Aliaksey Kandratsenka <<EMAIL>>
Date:	Mon Feb 20 15:31:41 2017 -0800

    Don't assume memalign exists in memalign vs nallocx test

    OSX and windows have issues with memalign. So test against tc_memalign
    instead.

    This should fix _memalign linker-time part of issue #870.

M	src/tests/tcmalloc_unittest.cc

commit bf640cd740fe38f6f10faa8683e8361fee971aba
Author: Aliaksey Kandratsenka <<EMAIL>>
Date:	Mon Feb 20 14:51:27 2017 -0800

    rename sys allocator's sys_alloc symbol to tcmalloc_sys_alloc

    Since we're not building with hidden visibility, symbols of other DSOs
    can interpose our symbols. And sys_alloc is just too generic name.

    And in fact erlang runtime has sys_alloc function. Which means we're
    trying to write to it's first bytes as part of initializing system
    allocator and crash.

    This should fix issue #843.

M	src/system-alloc.cc
M	src/system-alloc.h
M	src/tcmalloc.cc
M	src/windows/system-alloc.cc

commit 069e3b16551186f8da0a41c185550fdfd11af35b
Author: Aliaksey Kandratsenka <<EMAIL>>
Date:	Mon Feb 20 14:25:18 2017 -0800

    build malloc_bench_shared_full only when full tcmalloc is built

    I.e. because otherwise, when --enable-minimal is given, we're building
    empty libtcmalloc.la and linking it to malloc_bench_shared_full. Which
    has no effect at all and actually breaks builds on OSX.

    Should fix issue #869.

M	Makefile.am

commit b8f9d0d44f94177d34b069180618b7d002e85b69
Author: Aliaksey Kandratsenka <<EMAIL>>
Date:	Sun Dec 18 09:35:02 2016 -0800

    ported nallocx support from Google-internal tcmalloc

    nallocx is extension introduced by jemalloc. It returns effective size
    of allocaiton without allocating anything.

    We also support MALLOCX_LG_ALIGN flag. But all other jemalloc
    flags (which at the moment do nothing for nallocx anyways) are
    silently ignored, since there is no sensible way to return errors in
    this API.

    This was originally contributed by Dmitry Vyukov with input from
    Andrew Hunter. But due to significant divergence of Google-internal
    and free-software forks of tcmalloc, significant massaging was done by
    me. So all bugs are mine.

M	Makefile.am
A	src/gperftools/nallocx.h
M	src/tcmalloc.cc
M	src/tests/tcmalloc_unittest.cc

commit b0abefd93834d9d9c7ffaae2d23bd48ed8e96477
Author: Andrew Morrow <<EMAIL>>
Date:	Sat Dec 17 13:57:41 2016 -0500

    Fix a typo in the page fence flag declaration

M	src/debugallocation.cc

commit 855b3800064db49af823b85a54be269923eb6f4d
Author: Kirill Müller <<EMAIL>>
Date:	Tue Nov 15 09:58:11 2016 +0100

    replace docs by doc

M	Makefile.am
M	README
D	doc
M	packages/deb/docs
M	packages/deb/libgperftools0.manpages
M	src/gperftools/heap-checker.h
M	src/gperftools/heap-profiler.h
M	src/gperftools/profiler.h
M	src/profiledata.h
M	src/tcmalloc.cc

commit 664210ead806d700cdbe5eeaf75d7a066fdac541
Author: Kirill Müller <<EMAIL>>
Date:	Tue Nov 15 09:42:12 2016 +0100

    doc -> docs, with symlink

A	doc
R100	doc/cpuprofile-fileformat.html	docs/cpuprofile-fileformat.html
R100	doc/cpuprofile.html	docs/cpuprofile.html
R100	doc/designstyle.css	docs/designstyle.css
R100	doc/heap-example1.png	docs/heap-example1.png
R100	doc/heap_checker.html	docs/heap_checker.html
R100	doc/heapprofile.html	docs/heapprofile.html
R100	doc/index.html	docs/index.html
R100	doc/overview.dot	docs/overview.dot
R100	doc/overview.gif	docs/overview.gif
R100	doc/pageheap.dot	docs/pageheap.dot
R100	doc/pageheap.gif	docs/pageheap.gif
R100	doc/pprof-test-big.gif	docs/pprof-test-big.gif
R100	doc/pprof-test.gif	docs/pprof-test.gif
R100	doc/pprof-vsnprintf-big.gif	docs/pprof-vsnprintf-big.gif
R100	doc/pprof-vsnprintf.gif docs/pprof-vsnprintf.gif
R100	doc/pprof.1	docs/pprof.1
R100	doc/pprof.see_also	docs/pprof.see_also
R100	doc/pprof_remote_servers.html	docs/pprof_remote_servers.html
R100	doc/spanmap.dot docs/spanmap.dot
R100	doc/spanmap.gif docs/spanmap.gif
R100	doc/t-test1.times.txt	docs/t-test1.times.txt
R100	doc/tcmalloc-opspercpusec.vs.threads.1024.bytes.png
docs/tcmalloc-opspercpusec.vs.threads.1024.bytes.png
R100	doc/tcmalloc-opspercpusec.vs.threads.128.bytes.png
docs/tcmalloc-opspercpusec.vs.threads.128.bytes.png
R100	doc/tcmalloc-opspercpusec.vs.threads.131072.bytes.png
docs/tcmalloc-opspercpusec.vs.threads.131072.bytes.png
R100	doc/tcmalloc-opspercpusec.vs.threads.16384.bytes.png
docs/tcmalloc-opspercpusec.vs.threads.16384.bytes.png
R100	doc/tcmalloc-opspercpusec.vs.threads.2048.bytes.png
docs/tcmalloc-opspercpusec.vs.threads.2048.bytes.png
R100	doc/tcmalloc-opspercpusec.vs.threads.256.bytes.png
docs/tcmalloc-opspercpusec.vs.threads.256.bytes.png
R100	doc/tcmalloc-opspercpusec.vs.threads.32768.bytes.png
docs/tcmalloc-opspercpusec.vs.threads.32768.bytes.png
R100	doc/tcmalloc-opspercpusec.vs.threads.4096.bytes.png
docs/tcmalloc-opspercpusec.vs.threads.4096.bytes.png
R100	doc/tcmalloc-opspercpusec.vs.threads.512.bytes.png
docs/tcmalloc-opspercpusec.vs.threads.512.bytes.png
R100	doc/tcmalloc-opspercpusec.vs.threads.64.bytes.png
docs/tcmalloc-opspercpusec.vs.threads.64.bytes.png
R100	doc/tcmalloc-opspercpusec.vs.threads.65536.bytes.png
docs/tcmalloc-opspercpusec.vs.threads.65536.bytes.png
R100	doc/tcmalloc-opspercpusec.vs.threads.8192.bytes.png
docs/tcmalloc-opspercpusec.vs.threads.8192.bytes.png
R100	doc/tcmalloc-opspersec.vs.size.1.threads.png
docs/tcmalloc-opspersec.vs.size.1.threads.png
R100	doc/tcmalloc-opspersec.vs.size.12.threads.png
docs/tcmalloc-opspersec.vs.size.12.threads.png
R100	doc/tcmalloc-opspersec.vs.size.16.threads.png
docs/tcmalloc-opspersec.vs.size.16.threads.png
R100	doc/tcmalloc-opspersec.vs.size.2.threads.png
docs/tcmalloc-opspersec.vs.size.2.threads.png
R100	doc/tcmalloc-opspersec.vs.size.20.threads.png
docs/tcmalloc-opspersec.vs.size.20.threads.png
R100	doc/tcmalloc-opspersec.vs.size.3.threads.png
docs/tcmalloc-opspersec.vs.size.3.threads.png
R100	doc/tcmalloc-opspersec.vs.size.4.threads.png
docs/tcmalloc-opspersec.vs.size.4.threads.png
R100	doc/tcmalloc-opspersec.vs.size.5.threads.png
docs/tcmalloc-opspersec.vs.size.5.threads.png
R100	doc/tcmalloc-opspersec.vs.size.8.threads.png
docs/tcmalloc-opspersec.vs.size.8.threads.png
R100	doc/tcmalloc.html	docs/tcmalloc.html
R100	doc/threadheap.dot	docs/threadheap.dot
R100	doc/threadheap.gif	docs/threadheap.gif

commit 75dc9a6e1470fa82b828f9687edad48f53d740b1
Author: zmertens <<EMAIL>>
Date:	Fri Nov 18 15:27:32 2016 -0700

    Fix Post(s)cript tyos

M	doc/pprof.1
M	src/pprof

commit dde32f8bbc95312379f9f5a651799815bb6327c5
Author: Francis Ricci <<EMAIL>>
Date:	Tue Oct 11 11:14:06 2016 -0700

    Fix unaligned memory accesses in debug allocator

M	src/base/basictypes.h
M	src/debugallocation.cc

commit 02eeed29df112728564a5dde6417fa4622b57a06
Author: Ryan Macnak <<EMAIL>>
Date:	Tue Sep 27 14:03:05 2016 -0700

    Fix redefinition of mmap on aarch64.

M	src/base/linux_syscall_support.h

commit c07a15cff4b904ac45e4019f8e36eeffd3e8186c
Author: Aliaksey Kandratsenka <<EMAIL>>
Date:	Sat Sep 24 18:56:22 2016 -0700

    [windows] patch _free_base as well

    Looks like _free_base is used as generic free/delete, which is not
    checked for free/delete mismatch by their debug runtime. Issue #817
    occurs because something that is allocated by regular allocation
    functions (new or malloc) is freed by _free_base. Patch it as
    well fixes
    the problem.

    Closes #817.

M	src/windows/patch_functions.cc

commit acac6af26b0ef052b39f61a59507b23e9703bdfa
Author: Aliaksey Kandratsenka <<EMAIL>>
Date:	Sat Sep 24 16:19:27 2016 -0700

    Fix finding default zone on macOS sierra

    This is taken from jemalloc patch at
    https://github.com/jemalloc/jemalloc/pull/427/commits/19c9a3e828ed46f1576521c264640e60bd0cb01f
    by Mike Hommey (aka glandium).

    The issue was reported together with advise to take that jemalloc
    fix by
    Koichi Shiraishi and David Ribeiro Alves at gperftools issue #827.

    Closes issue #827.

M	src/libc_override_osx.h

commit 7822b5b0b9fa7e016e1f6b46ea86f26f4691a457
Author: Tulio Magno Quites Machado Filho <<EMAIL>>
Date:	Mon Aug 8 13:50:10 2016 -0300

    Stop using glibc malloc hooks

    glibc deprecated malloc hooks in 2011 and will be removing them soon.
    These hooks aren't safe and aren't necessary when the malloc API is
    fully exported.

M	configure.ac
M	src/libc_override_glibc.h

commit c92f0ed0891d69888cdc7e904c82048362b2dd8d
Author: Tulio Magno Quites Machado Filho <<EMAIL>>
Date:	Wed Jul 27 17:15:52 2016 -0300

    Remove references to __malloc_initialize_hook

    __malloc_initialize_hook has been deprecated in glibc since 2011
    and is
    being removed on glibc 2.24.

M	src/libc_override_glibc.h

commit 9709eef361aa8c46d6b14c08bebead7b8185e731
Merge: eb474c9 44f276e
Author: Aliaksey Kandratsenka (aka Aliaksei Kandratsenka)
<<EMAIL>>
Date:	Wed Aug 24 22:33:05 2016 -0700

    Merge pull request #821 from jtmcdole/patch-1

    Rename TCMALLOC_DEBUG to PERFTOOLS_VERBOSE

commit 44f276e132eef8ba013a04b4af92ce26464e2b54
Author: John McDole <<EMAIL>>
Date:	Wed Aug 24 17:47:28 2016 -0700

    Rename TCMALLOC_DEBUG to PERFTOOLS_VERBOSE

M	README

commit eb474c995eadef3edb47bc081f09693228832794
Author: Aliaksey Kandratsenka <<EMAIL>>
Date:	Sun Jul 3 18:20:32 2016 -0700

    Summary: support gcc atomic ops on clang too

    Clang actually does support __atomic_XXX atomic ops builtins but does
    not advertise itselt as gcc 4.7 or later. So we now detect clang
    separetely.

    We're enabling gcc atomic ops on clang >= 3.4 since this is the oldest
    version that I can test.

    This should fix issue #797.

M	src/base/atomicops.h

commit 7f86eab1f3e0636a79a96fd44e3bb51db0457a86
Author: Kenton Varda <<EMAIL>>
Date:	Thu Jun 30 18:32:39 2016 -0700

    Recognize .node files as shared libraries

    node.js extensions are regular shared libraries named with the `.node`
    extension. With this small change I was able to get a usable heap
    profile out of node to debug an extension I'm working on.

M	src/pprof

commit bf8eacce69df877d1cecdab8c7a6cc3f218ebcc2
Author: Bryan Chan <<EMAIL>>
Date:	Wed Jun 15 00:10:45 2016 -0400

    Add support for 31-bit s390; merge linux_syscall_support.h changes
    from upstream.

M	configure.ac
M	m4/pc_from_ucontext.m4
M	src/base/basictypes.h
M	src/base/linux_syscall_support.h
M	src/base/linuxthreads.h
M	src/getpc.h
M	src/malloc_hook_mmap_linux.h

commit c54218069b96e5a64e81186cd14107bf6660b61d
Author: savefromgoogle <<EMAIL>>
Date:	Sat May 14 17:50:34 2016 +0200

    Update README

    typo corrected and direct link to Google Group mailinglist added

M	README

commit 06f4ce65c226e2bed432119bd830a478012ea336
Author: iivlev <<EMAIL>>
Date:	Fri Apr 15 17:46:25 2016 +0300

    Small performance tweak: avoid calling time() if we don't need it

M	src/heap-profiler.cc

commit db8d4836091b93100b176e8cab4e842974234e4e
Author: Aliaksey Kandratsenka <<EMAIL>>
Date:	Sat Jun 25 16:24:40 2016 -0700

    Autogenerate ChangeLog from git on make dist

    This fixes build breakage introduced in preceding commit for issue
    #796.

A	ChangeLog
M	Makefile.am
M	configure.ac
A	m4/ax_generate_changelog.m4

commit 4a1359831938b5a71b2faf38c02003053af253d9
Author: Aliaksey Kandratsenka <<EMAIL>>
Date:	Sat Jun 25 16:12:48 2016 -0700

    renamed ChangeLog to ChangeLog.old

    I.e. to avoid confusion. This should fix issue #796.

R100	ChangeLog	ChangeLog.old

commit 7852eeb75b9375cf52a7da01be044da6e915dd08
Author: Aliaksey Kandratsenka <<EMAIL>>
Date:	Sat Apr 9 13:09:18 2016 -0700

    Use initial-exec tls for libunwind's recursion flag

    If we don't do it, then reading variable calls to __tls_get_addr,
    which
    uses malloc on first call. initial-exec makes dynamic linker
    reserve tls
    offset for recusion flag early and thus avoid unsafe calls to malloc.

    This fixes issue #786.

M	src/base/basictypes.h
M	src/stacktrace_libunwind-inl.h
M	src/thread_cache.h

commit a07f9fe75af25ce388af3d4ff4514b42513d766f
Author: Mike Gaffney <<EMAIL>>
Date:	Thu Mar 31 11:27:21 2016 -0700

    gerftools -> gperftools in readme

M	README

commit 9fd6d2687914a1f58a8ce457d6a1bd3d55ea0747
Author: Aliaksey Kandratsenka <<EMAIL>>
Date:	Sun Mar 20 12:29:40 2016 -0700

    added define to enable MADV_FREE usage on Linux

    Building with -DTCMALLOC_USE_MADV_FREE will enable usage of
    MADV_FREE on
    Linux if glibc copy of kernel headers has MADV_FREE defined.

    I.e. so that people can test this more easily.

    Affects ticket #780.

M	src/system-alloc.cc

commit 6f7a14f45e2e2e16fbbd250543f5758078e2f5f0
Author: Mathias Stearn <<EMAIL>>
Date:	Wed Mar 16 18:22:32 2016 -0400

    Don't use MADV_FREE on Linux

    Addresses #780.

M	src/system-alloc.cc

commit 55cf6e6281f2f19ed04a6b2a90e2dcaec08e16d0
Author: Aman Gupta <<EMAIL>>
Date:	Mon Mar 14 12:20:40 2016 -0700

    Fix symbol resolution on OSX

    The library mapping ranges on OSX and Linux are sorted in opposite
    orders

      7f71c3323000-7f71c3339000 r-xp 00000000 09:02 29099128
      /lib/x86_64-linux-gnu/libz.so.*******
      7f71c3339000-7f71c3538000 ---p 00016000 09:02 29099128
      /lib/x86_64-linux-gnu/libz.so.*******
      7f71c3538000-7f71c3539000 r--p 00015000 09:02 29099128
      /lib/x86_64-linux-gnu/libz.so.*******
      7f71c3539000-7f71c353a000 rw-p 00016000 09:02 29099128
      /lib/x86_64-linux-gnu/libz.so.*******

    vs

      108f8d000-108f95000 r-xp 00025000 00:00 0
      /usr/local/opt/libmemcached/lib/libmemcached.11.dylib
      108f8c000-108f8d000 r-xp 00024000 00:00 0
      /usr/local/opt/libmemcached/lib/libmemcached.11.dylib
      108f68000-108f8c000 r-xp 00000000 00:00 0
      /usr/local/opt/libmemcached/lib/libmemcached.11.dylib

M	src/pprof

commit 8e858436223db7729d41304ad29a29946d47382b
Author: Aliaksey Kandratsenka <<EMAIL>>
Date:	Sun Mar 13 15:11:50 2016 -0700

    added simple .travis.yml config

A	.travis.yml

commit 05e40d29c034c0d68322e589b8d6e234f562be2e
Author: Brian Silverman <<EMAIL>>
Date:	Tue Mar 8 15:58:02 2016 -0500

    Recognize modern Linux ARM

    Commit e580d7888154fa7f95b3cef9e18f1ce69182212b fixed the macros
    in some
    of the code but not other places.

    `make check` still fails in the same places on a Debian Jessie armhf
    system.

M	src/base/elfcore.h
M	src/base/linuxthreads.h

commit 632de2975e63f89613af9ab99bc1603a4a6332aa
Author: Aliaksey Kandratsenka <<EMAIL>>
Date:	Sat Mar 12 11:48:20 2016 -0800

    bumped version up to 2.5

M	NEWS
M	configure.ac
M	src/windows/config.h
M	src/windows/gperftools/tcmalloc.h

commit 6682016092c735feb29021acbb8c2067fdf1a209
Author: Aliaksey Kandratsenka <<EMAIL>>
Date:	Sat Mar 12 10:54:05 2016 -0800

    Unbreak profiling with CPUPROFILE_FREQUENCY=1

    This closes ticket #777.

    No test sadly, since it's not trivial to unittest this case. But
    fix with single-shot manual testing is better than nothing.

M	src/profile-handler.cc

commit 6ff86ff6a783a277956e45db68c6a2b729f27000
Author: Aliaksey Kandratsenka <<EMAIL>>
Date:	Sat Mar 5 16:17:28 2016 -0800

    bumped version to 2.4.91 for 2.5rc2

M	NEWS
M	configure.ac
M	src/windows/config.h
M	src/windows/gperftools/tcmalloc.h

commit 782165fa7f2c49d6a67c2415626a1f215cc21ac2
Author: Aliaksey Kandratsenka <<EMAIL>>
Date:	Sat Mar 5 15:47:25 2016 -0800

    build sized delete aliases even when sized-delete is disabled

    In this case we alias to regular delete. This is helpful because if we
    don't override sized delete, then apps will call version in libstdc++
    which delegates calls to regular delete, which is slower than calling
    regular delete directly.

M	configure.ac
M	src/libc_override_gcc_and_weak.h

commit 06811b3ae4c5365880898148d188ae91d20f52f2
Author: Aliaksey Kandratsenka <<EMAIL>>
Date:	Sat Mar 5 15:08:50 2016 -0800

    disable dynamic sized delete support by default

    IFUNC relocations don't support our advanced use case (calling
    application function or looking up environment variable).

    Particularly, it doesn't work on PPC and arm when tcmalloc is linked
    with -Wl,-z,now. See RedHat's bugzilla ticket
    https://bugzilla.redhat.com/show_bug.cgi?id=1312462 for more details.

M	configure.ac

commit d4d99eb608f3d778fa301137a85cc4903a1dd33e
Author: Aliaksey Kandratsenka <<EMAIL>>
Date:	Sat Mar 5 14:53:42 2016 -0800

    unbreak compilation with visual studio

    Specifically, this commit adds missing fake_stacktrace_scope.cc
    to msvc
    build and removes undef-ing of PERFTOOLS_DLL_DECL by tcmalloc.h.

M	src/gperftools/tcmalloc.h.in
M	src/windows/config.h
M	src/windows/gperftools/tcmalloc.h
M	src/windows/gperftools/tcmalloc.h.in
M	vsprojects/libtcmalloc_minimal/libtcmalloc_minimal.vcproj

commit 126d4582c18d053452d22ce12351c5429513aabe
Author: Brian Silverman <<EMAIL>>
Date:	Tue Mar 1 17:57:33 2016 -0500

    Call function pointers with the right type

    I think it's undefined behavior, but it's definitely weird. ubsan
    complains too.

M	src/tests/tcmalloc_unittest.cc

commit e0fa28ef7d50158b271da90f53ab3aee5ba441d8
Author: Brian Silverman <<EMAIL>>
Date:	Tue Mar 1 17:56:32 2016 -0500

    Don't shift a type by more than its width

    It's undefined behavior, so ubsan complains.

M	src/tests/sampler_test.cc

commit a1c764d2632cc0090f31d8d56effb94a8d049b54
Author: Brian Silverman <<EMAIL>>
Date:	Tue Mar 1 17:55:59 2016 -0500

    Initialize counters in test

M	src/tests/profile-handler_unittest.cc

commit 22123a37c236e26535d3f3fff7f31a5b6515d7d6
Author: Brian Silverman <<EMAIL>>
Date:	Tue Mar 1 17:55:53 2016 -0500

    Don't overflow a signed integer

    It's undefined behavior and ubsan catches it.

M	src/base/low_level_alloc.cc

commit 66e1e94f38467b5c7bbfb05e3c7267f3039a2c69
Author: Aliaksey Kandratsenka <<EMAIL>>
Date:	Mon Feb 29 20:09:39 2016 -0800

    added minimal "header section" to README

M	README

commit 2804b7cfee8d49d5ab98340cbf3050c0a5f57b98
Author: Aliaksey Kandratsenka <<EMAIL>>
Date:	Sun Feb 21 20:27:48 2016 -0800

    bumped version to 2.5rc

M	configure.ac
M	src/windows/config.h
M	src/windows/gperftools/tcmalloc.h

commit f47fefbfc1083e7efbada3e7e7d2859d31e151e7
Author: Aliaksey Kandratsenka <<EMAIL>>
Date:	Sun Feb 21 22:38:23 2016 -0800

    updated NEWS for 2.5rc

M	NEWS

commit cef60361741e858041f4bb4d46ce00c6db89b711
Author: Aliaksey Kandratsenka <<EMAIL>>
Date:	Sun Feb 21 23:03:41 2016 -0800

    alias same malloc/free variants to their canonical versions

    This is similar to what gcc 5 does anyways, except gcc 5 is placing
    jumps which adds a bit of overhead.

    Instead of letting gcc do it, we alias using ELF symbol aliasing. All
    free variants (tc_delete{,array}_{,nothrow}) are aliased to
    tc_free. There are 3 malloc variants that differ by oom
    handling. tc_newarray is aliased to tc_new. And tc_newarray_nothrow is
    aliased to tc_new_nothrow.

    This aliasing only happens in non-debug malloc, since debug malloc
    does
    distinguish between different variants since it needs checking for
    mismatch.

M	src/tcmalloc.cc

commit ea8d242061460309d174d9053a224cf94e65e6fe
Author: Aliaksey Kandratsenka <<EMAIL>>
Date:	Sun Feb 21 20:16:37 2016 -0800

    Re-enable MultipleIdleNonIdlePhases test

    Which was accidentally removed as part of MarkThreadTemporarilyIdle
    support.

M	src/tests/markidle_unittest.cc

commit c9962f698b0edb37315445b33a06820f9bcf5df9
Author: Aliaksey Kandratsenka <<EMAIL>>
Date:	Sun Feb 21 20:07:37 2016 -0800

    added maybe_emergency_malloc.h to Makefile.am

    Because without this reference it isn't packaged by make dist.

M	Makefile.am

commit 7dd4af65365d74a5d8d30d5811c26117a9192238
Author: Aliaksey Kandratsenka <<EMAIL>>
Date:	Sun Feb 21 19:26:37 2016 -0800

    don't round up sizes for large allocation when sampling

    This closes #723.

    Since rounding up prior to sampling is introducing possibility of
    arithmetic overflow, we're just not doing it.

    It introduces some error (up to 4k), but since we're dealing with at
    least 256k allocations, we're fine.

M	src/tcmalloc.cc

commit 4f3410e759ec42cb307429222d690a81e3cd37b0
Author: Aliaksey Kandratsenka <<EMAIL>>
Date:	Sun Feb 21 13:52:47 2016 -0800

    enable emergency malloc by default on arm when using libunwind

    Because by default libunwind on arm does fopen which mallocs
    which will
    deadlock unless we enable emergency malloc.

M	configure.ac

commit 7f12051dbe1f402771a747c1192a66e7571d94f9
Author: Aliaksey Kandratsenka <<EMAIL>>
Date:	Sun Jan 31 23:17:50 2016 -0800

    implemented emergency malloc

    Emergency malloc is enabled for cases when backtrace capturing
    needs to
    call malloc. In this case, we enable emergency malloc just prior to
    calling such code and disable it after it is done.

M	Makefile.am
M	configure.ac
M	src/debugallocation.cc
A	src/emergency_malloc.cc
A	src/emergency_malloc.h
A	src/emergency_malloc_for_stacktrace.cc
A	src/fake_stacktrace_scope.cc
M	src/malloc_hook.cc
A	src/maybe_emergency_malloc.h
M	src/stacktrace.cc
M	src/tcmalloc.cc
M	src/thread_cache.h

commit 3ee2360250c639f02b354820bc50efc6e104b754
Author: Aliaksey Kandratsenka <<EMAIL>>
Date:	Sat Feb 20 21:49:58 2016 -0800

    replaced invalid uses of __THROW

    We're now using it only when overriding glibc functions (such
    as malloc
    or mmap). In other cases (most importantly in public tcmalloc.h
    header)
    we're doing our own throw() to avoid possible breakage on future glibc
    changes.

M	src/debugallocation.cc
M	src/gperftools/tcmalloc.h.in
M	src/libc_override_glibc.h
M	src/libc_override_redefine.h
M	src/tcmalloc.cc
M	src/windows/gperftools/tcmalloc.h
M	src/windows/gperftools/tcmalloc.h.in

commit 013b82abcfeaa158da2490ff86d6bff5a5434d7f
Author: Aliaksey Kandratsenka <<EMAIL>>
Date:	Sat Feb 20 21:19:37 2016 -0800

    unbreak <malloc.h> inclusion in gperftools/tcmalloc.h

    We have shipped header which checked HAVE_XXX defines which we only
    defined in project-local config.h. So it could never work correctly.

    We're now doing #include <malloc.h> just like tc_mallinfo on constant
    which we detect at configure time and write into header that we
    install.

M	src/gperftools/tcmalloc.h.in

commit 19903e6f156d0ff8063880ddc5051b5dc261c1ff
Author: Aliaksey Kandratsenka <<EMAIL>>
Date:	Sat Feb 20 21:18:18 2016 -0800

    drop detection of sys/malloc.h and malloc/malloc.h

    Which we don't really need.

M	configure.ac

commit cdff090ebd7a3cef8de60109bef52251758181d7
Author: Aliaksey Kandratsenka <<EMAIL>>
Date:	Sat Feb 20 20:43:21 2016 -0800

    Fix several harmless clang warnings

M	src/common.cc
M	src/internal_logging.cc
M	src/malloc_hook.cc
M	src/tests/current_allocated_bytes_test.cc

commit 9095ed08402620b04591c2d27ca222594f0da6db
Author: Aliaksey Kandratsenka <<EMAIL>>
Date:	Sat Feb 20 20:33:27 2016 -0800

    implemented stacktrace capturing via libgcc's C++ ABI function

    Particularly _Unwind_Backtrace which seems to be gcc extension.

    This is what glibc's backtrace is commonly is using.

    Using _Unwind_Backtrace directly is better than glibc's backtrace,
    since
    it doesn't call into dlopen. While glibc does dlopen when it is
    built as
    shared library apparently to avoid link-time dependency on libgcc_s.so

M	Makefile.am
M	configure.ac
M	src/stacktrace.cc
A	src/stacktrace_libgcc-inl.h

commit 728cbe102108be8296f2485e384392a4408ce9db
Author: Aliaksey Kandratsenka <<EMAIL>>
Date:	Sat Feb 20 13:06:08 2016 -0800

    force profiler_unittest to do 'real' work

    'XOR loop' in profiler unittest wasn't 100% effective because
    it allowed
    compiler to avoid loading and storing to memory.

    After marking result variable as volatile, we're now forcing
    compiler to
    read and write memory, slowing this loops down sufficiently. And
    profiler_unittest is now passing more consistently.

    Closes #628

M	src/tests/profiler_unittest.cc

commit fff6b4fb8814b5f25d4f97889d5003537f7dcc92
Author: Aliaksey Kandratsenka <<EMAIL>>
Date:	Sun Jan 31 23:14:43 2016 -0800

    Extend low-level allocator to support custom pages allocator

M	src/base/low_level_alloc.cc
M	src/base/low_level_alloc.h

commit 32d992679589ea83e044d3f8263583ae960b0b16
Author: Aliaksey Kandratsenka <<EMAIL>>
Date:	Sun Jan 31 22:42:20 2016 -0800

    added malloc_bench_shared_full

M	.gitignore
M	Makefile.am

commit 00d8fa1ef8d6650f08e00f59baa22cd31b908432
Author: Aliaksey Kandratsenka <<EMAIL>>
Date:	Sat Feb 6 19:13:07 2016 -0800

    always use real throw() on operators new/delete

    Since non-glibc-s have no __THROW and lack of throw() on operators
    gives
    us warning.

M	src/libc_override_gcc_and_weak.h

commit 08e034ad5940f24f99a24630c7b71a0de728d05b
Author: Aliaksey Kandratsenka <<EMAIL>>
Date:	Sat Feb 6 16:19:54 2016 -0800

    Detect working ifunc before enabling dynamic sized delete support

    Particularly, on arm-linux and x86-64-debian-kfreebsd compilation
    fails
    due to lack of support for ifunc. So it is necessary to test at
    configure time whether ifunc is supported.

M	configure.ac

commit a788f354a0cd6de3d714a58ad3e6ae5baf806c3b
Author: Aliaksey Kandratsenka <<EMAIL>>
Date:	Sat Feb 6 16:01:25 2016 -0800

    include unistd.h for getpid in thread_lister.c

    This fixes warning produced on arm-linux.

M	src/base/thread_lister.c

commit 644a6bdbdb50b26a25e4428c43556467a6e8b5cc
Author: Bryan Chan <<EMAIL>>
Date:	Fri Jan 22 12:26:47 2016 -0500

    Add support for Linux s390x

    This resolves gperftools/gperftools#761.

M	configure.ac
M	m4/pc_from_ucontext.m4
M	src/base/basictypes.h
M	src/base/linux_syscall_support.h
M	src/base/linuxthreads.h
M	src/malloc_hook_mmap_linux.h

commit bab7753aad44b3395a063966f32c23f632fee174
Author: Bryan Chan <<EMAIL>>
Date:	Fri Jan 29 02:38:42 2016 -0500

    Fix typo in heap-checker-death_unittest.sh

M	src/tests/heap-checker-death_unittest.sh

commit 17182e1d3c324e8fe9cc02ce2822caa746d7d17a
Author: Simon Que <<EMAIL>>
Date:	Fri Jan 29 18:17:16 2016 -0800

    Fix include of malloc_hook_c.h in malloc_hook.h

    malloc_hook.h includes malloc_hook_c.h as
    <gperftools/malloc_hook_c.h>.
    This requires the compiler to have designated src/gperftools as a
    standard include directory (-I), which may not always be the case.

    Instead, include it as "malloc_hook_c.h", which will search in
    the same
    directory first. This will always work, regardless of whether it was
    designated a standard include directory.

M	src/gperftools/malloc_hook.h

commit c69721b2b2ceae426c36de191dd0a6fa443c5c7a
Author: Andrew Morrow <<EMAIL>>
Date:	Sun Nov 22 15:19:12 2015 -0500

    Add support for obtaining cache size of the current thread and
    softer idling

M	src/gperftools/malloc_extension.h
M	src/gperftools/malloc_extension_c.h
M	src/malloc_extension.cc
M	src/tcmalloc.cc
M	src/tests/markidle_unittest.cc
M	src/thread_cache.cc
M	src/thread_cache.h

commit 5ce42e535d6b3bf6c5e41ceb9cf876a70a7e3318
Author: Brian Silverman <<EMAIL>>
Date:	Tue Jan 26 16:53:48 2016 -0500

    Don't always arm the profiling timer.

    It causes a noticeable performance hit and can sometimes confuse GDB.

    Tested with CPUPROFILE_PER_THREAD_TIMERS=1.

    Based on an old <NAME_EMAIL>.

M	src/profile-handler.cc
M	src/profile-handler.h
M	src/tests/profile-handler_unittest.cc

commit 7f801ea091e03dae7b13160f76fecce15727c9c0
Author: Duncan Sands <<EMAIL>>
Date:	Fri Jan 15 18:12:51 2016 +0100

    Make sure the alias is not removed by link-time optimization when
    it can prove
    that it isn't used by the program, as it might still be needed to
    override the
    corresponding symbol in shared libraries (or inline assembler for
    that matter).
    For example, suppose the program uses malloc and free but not calloc
    and is
    statically linked against tcmalloc (built with -flto) and LTO
    is done.  Then
    before this patch the calloc alias would be deleted by LTO due to
    not being
    used, but the malloc/free aliases would be kept because they are
    used by the
    program.  Suppose the program is dynamically linked with a shared
    library that
    allocates memory using calloc and later frees it by calling free.
    Then calloc
    will use the libc memory allocator, because the calloc alias was
    deleted, but
    free will call into tcmalloc, resulting in a crash.

M	src/libc_override_gcc_and_weak.h

commit 6b3e6ef5e0ab65030d116dd55dac8d64f9c72d33
Author: Aliaksey Kandratsenka <<EMAIL>>
Date:	Sun Jan 24 19:45:16 2016 -0800

    don't retain compatibility with old docdir behavior

    Since it is not really needed. And since we don't care about too
    ancient
    autoconfs.

M	configure.ac

commit ccffcbd9e988a39654fe6ec5edd6d817f266ded9
Author: Chris Mayo <<EMAIL>>
Date:	Sun Dec 27 18:55:05 2015 +0000

    support use of configure --docdir argument

    Value of docdir was being overridden in Makefile.

    Retain compatibility with old Autoconf versions that do not provide
    docdir.

M	Makefile.am
M	configure.ac

commit 050f2d28be8a63836bd74558b82be7983770a654
Author: Aliaksey Kandratsenka <<EMAIL>>
Date:	Sat Dec 12 18:27:40 2015 -0800

    use alias attribute only for elf platforms

    It was reported that clang on OSX doesn't support alias
    attribute. Most
    likely because of executable format limitations.

    New code limits use of alias to gcc-compatible compilers on elf
    platforms (various gnu and *bsd systems). Elf format is known
    to support
    aliases.

M	src/tcmalloc.cc

commit 07b0b21ddd168d977c04b1395677de71bb3e402e
Author: cyshi <<EMAIL>>
Date:	Wed Dec 2 14:47:15 2015 +0800

    fix compilation error in spinlock

M	src/base/spinlock.cc

commit e14450366abc51c7a25256615179daf0bae1de34
Author: gshirishfree <<EMAIL>>
Date:	Mon Nov 23 11:34:13 2015 -0800

    Added better description for GetStats API

M	src/gperftools/malloc_extension.h

commit 64892ae730b704a7f3f23bd8eaeaf206901df201
Author: Aliaksey Kandratsenka <<EMAIL>>
Date:	Sat Nov 21 19:17:36 2015 -0800

    lower default transfer batch size down to 512

    Some workloads get much slower with too large batch size.

    This closes bug #678.

    binary_trees benchmark benefits from larger batch size. And I
    found that
    512 is not much slower than huge value that we had.

M	src/common.cc

commit 6fdfc5a7f40ebcff3fdaada1a2994ff54be2f9c7
Author: Aliaksey Kandratsenka <<EMAIL>>
Date:	Sat Oct 24 23:16:45 2015 -0700

    implemented enabling sized-delete support at runtime

    Under gcc 4.5 or greater we're using ifunc function attribute
    to resolve
    sized delete operator to either plain delete implementation
    (default) or
    to sized delete (if enabled via environment variable
    TCMALLOC_ENABLE_SIZED_DELETE).

M	configure.ac
M	src/libc_override_gcc_and_weak.h
M	src/tests/tcmalloc_unittest.sh

commit c2a79d063c949584170b3e7dd2939a4548c16079
Author: Aliaksey Kandratsenka <<EMAIL>>
Date:	Fri Oct 23 21:44:04 2015 -0700

    use x86 pause in spin loop

    This saves power and improves performance, particulary on SMT.

M	src/base/spinlock.cc

commit 0fb6dd8aa3c2fe3245c31c138ec4dfb05efd7f6d
Author: Aliaksey Kandratsenka <<EMAIL>>
Date:	Fri Oct 23 22:09:25 2015 -0700

    added binary_trees benchmark

M	.gitignore
M	Makefile.am
A	benchmark/binary_trees.cc

commit a8852489e54e915d22abbdad1688d1f25ccb7925
Author: Aliaksey Kandratsenka <<EMAIL>>
Date:	Sun Oct 18 00:40:15 2015 -0700

    drop unsupported allocation sampling code in tcmalloc_minimal

M	src/tcmalloc.cc
M	src/thread_cache.h

commit a9db0ae516c225543976280047b22b1e0ca08b93
Author: Aliaksey Kandratsenka <<EMAIL>>
Date:	Sun Oct 4 21:12:28 2015 -0700

    implemented (disabled by default) sized delete support

    gcc 5 and clang++-3.7 support sized deallocation from C++14. We are
    taking advantage of that by defining sized versions of operator
    delete.

    This is off by default so that if some existing programs that
    define own
    global operator delete without sized variant are not broken by
    tcmalloc's sized delete operator.

    There is also risk of breaking exiting code that deletes objects using
    wrong class (i.e. base class) without having virtual destructors.

M	configure.ac
M	src/gperftools/tcmalloc.h.in
M	src/libc_override_gcc_and_weak.h
M	src/libc_override_redefine.h
M	src/tcmalloc.cc
M	src/windows/gperftools/tcmalloc.h
M	src/windows/gperftools/tcmalloc.h.in

commit 88686972b9caf8fd132c4e30d89da78c3d324c27
Author: Aliaksey Kandratsenka <<EMAIL>>
Date:	Sun Oct 4 11:15:37 2015 -0700

    pass -fsized-deallocation to gcc 5

    Otherwise it gives warning for declaration of sized delete operator.

M	Makefile.am
M	configure.ac

commit 0a18fab3af2f1b59b851836d319a1eff0db5d44e
Author: Aliaksey Kandratsenka <<EMAIL>>
Date:	Sun Oct 4 21:07:54 2015 -0700

    implemented sized free support via tc_free_sized

M	src/debugallocation.cc
M	src/gperftools/tcmalloc.h.in
M	src/tcmalloc.cc
M	src/windows/gperftools/tcmalloc.h
M	src/windows/gperftools/tcmalloc.h.in
M	src/windows/patch_functions.cc

commit 464688ab6ddb82db22ea9b934ddea192478e4535
Author: Aliaksey Kandratsenka <<EMAIL>>
Date:	Sun Oct 4 21:27:56 2015 -0700

    speedup free code path by dropping "fast path allowed check"

M	src/tcmalloc.cc

commit 10f7e207166bd6652ed8803b9885917bfbbd90d0
Author: Aliaksey Kandratsenka <<EMAIL>>
Date:	Sat Oct 3 18:47:14 2015 -0700

    added SizeMap::MaybeSizeClass

    Because it allows us to first check for smaller sizes, which is most
    likely.

M	src/common.h

commit 436e1dea43dc097525e43a43e6d79810ba6921b5
Author: Aliaksey Kandratsenka <<EMAIL>>
Date:	Sat Oct 3 15:40:21 2015 -0700

    slightly faster GetCacheIfPresent

M	src/thread_cache.h

commit 04df911915ecfcbc5e6b13fdab1b86f825ed1938
Author: Aliaksey Kandratsenka <<EMAIL>>
Date:	Sat Oct 3 15:38:33 2015 -0700

    tell compiler that non-empty hooks are unlikely

M	src/malloc_hook-inl.h

commit 8cc75acd1f4024ca978858b1cf85b45204ed32b2
Author: Aliaksey Kandratsenka <<EMAIL>>
Date:	Sun Oct 4 11:04:12 2015 -0700

    correctly test for -Wno-unused-result support

    gcc is only giving warning for unknown -Wno-XXX flags so test never
    fails on gcc even if -Wno-XXX is not supported. By using
    -Wunused-result we're able to test if gcc actually supports it.

    This fixes issue #703.

M	configure.ac

commit 7753d8239b1e427d83729b6d5c0401e1d80308fd
Author: Aliaksey Kandratsenka <<EMAIL>>
Date:	Sun Oct 4 18:26:59 2015 -0700

    fixed clang warning about shifting negative values

M	src/base/basictypes.h

commit ae09ebb3833f3f207b7623245e360ddf122b823e
Author: Jens Rosenboom <<EMAIL>>
Date:	Thu Nov 19 17:45:44 2015 +0100

    Fix tmpdir usage in heap-profiler_unittest.sh

    Using a single fixed directory would break when tests were being
    run in
    parallel with "make -jN".

    Also, the cleanup at the end of the test didn't work because it
    referred
    to the wrong variable.

M	src/tests/heap-profiler_unittest.sh

commit df34e71b575daa6e4fa7588ae0deb9d529566f08
Author: Aliaksey Kandratsenka <<EMAIL>>
Date:	Sat Nov 21 16:03:09 2015 -0800

    use $0 when referring to pprof

    This fixed debian bug #805536. Debian ships pprof under google-pprof
    name so it is handy when google-pprof --help refers to itself
    correctly.

M	src/pprof

commit 7773ea64ee254700ef0ffc5673539f0e111a4f86
Author: Adhemerval Zanella <<EMAIL>>
Date:	Fri Nov 6 16:29:12 2015 -0200

    Alignment fix to static variables for system allocators

    This patch the placement new for some system allocator to force the
    static buffer to pointer value.

M	src/memfs_malloc.cc
M	src/system-alloc.cc

commit c46eb1f3d2f7a2bdc54a52ff7cf5e7392f5aa668
Author: Boris Sazonov <<EMAIL>>
Date:	Thu Oct 15 17:39:08 2015 +0300

    Fixed printf misuse in pprof - printed string was passed as
    format. Better use print instead

M	src/pprof

commit 9bbed8b1a8f1229cf0f1b4185683f309267ee1b9
Author: Boris Sazonov <<EMAIL>>
Date:	Wed Sep 23 19:46:13 2015 +0300

    Fixed assembler argument passing inside _syscall6 on MIPS - it was
    causing 'Expression too complex' compilation errors in spinlock

M	src/base/linux_syscall_support.h

commit 962aa53c55968f62f548f6f6f4849a384b505ca0
Author: Aliaksey Kandratsenka <<EMAIL>>
Date:	Sat Oct 10 20:31:50 2015 -0700

    added more fastpath microbenchmarks

    This also makes them output nicer results. I.e. every benchmark is
    run 3
    times and iteration duration is printed for every run.

    While this is still very synthetic and unrepresentave of malloc
    performance
    as a whole, it is exercising more situations in tcmalloc fastpath. So
    it a
    step forward.

M	Makefile.am
M	benchmark/malloc_bench.cc
A	benchmark/run_benchmark.c
A	benchmark/run_benchmark.h

commit 347a830689e4dba2adc1368d00fe4723ba726b4a
Author: Aliaksey Kandratsenka <<EMAIL>>
Date:	Sat Oct 10 15:58:11 2015 -0700

    Ensure that PPROF_PATH is set for debugallocation_test

    Which fixes issue #728.

M	src/tests/debugallocation_test.sh

commit a9059b7c30308a4f4ab25f00e55b67c649614ed3
Author: Aliaksey Kandratsenka <<EMAIL>>
Date:	Sat Oct 10 15:35:54 2015 -0700

    prevent clang from inlining Mallocer in heap checker unittest

    Looks like existing "trick" to avoid inlining doesn't really prevent
    sufficiently smart compiler from inlining Mallocer function. Which
    breaks tests, since test relies Mallocer having it's own separate
    stack
    frame.

    Making mallocer_addr variable volatile is seemingly enough to
    stop that.

M	src/tests/heap-checker_unittest.cc

commit 6627f9217d8897b297c6da038cfbcff6a3086cfa
Author: Aliaksey Kandratsenka <<EMAIL>>
Date:	Sat Sep 26 19:52:37 2015 -0700

    drop cycleclock

M	Makefile.am
D	src/base/cycleclock.h
M	src/base/sysinfo.cc
M	vsprojects/addressmap_unittest/addressmap_unittest.vcproj
M
vsprojects/low_level_alloc_unittest/low_level_alloc_unittest.vcproj
M	vsprojects/packed-cache_test/packed-cache_test.vcproj

commit f985abc29607b8a5662ddac7bd1ed99c71ceeb11
Author: Aliaksey Kandratsenka <<EMAIL>>
Date:	Sat Sep 26 19:50:03 2015 -0700

    amputate unportable and unused stuff from sysinfo

    We still check number of cpus in the system (in spinlock code),
    but old
    code was built under assumption of "no calls malloc" which is
    not needed
    in tcmalloc. Which caused it to be far more complicated than
    necessary (parsing procfs files, ifdefs for different OSes and
    arch-es).

    Also we don't need clock cycle frequency measurement.

    So I've removed all complexity of ald code and NumCPUs function and
    replaced it with GetSystemCPUsCount which is straightforward and
    portable call to sysconf.

    Renaming of cpus count function was made so that any further code that
    we might port from Google that depends on old semantics of NumCPUs
    will
    be detected at compile time. And has to be inspected for whether it
    really needs that semantics.

M	src/base/spinlock.cc
M	src/base/sysinfo.cc
M	src/base/sysinfo.h

commit 16408eb4d71ecbb1dd3b3c7ff22c94bd254d7b58
Author: Aliaksey Kandratsenka <<EMAIL>>
Date:	Tue Sep 29 11:59:25 2015 -0700

    amputated wait_cycles accounting in spinlocks

    This is not needed and pulls in CycleClock dependency that lowers
    code portability.

M	src/base/spinlock.cc
M	src/base/spinlock.h

commit fedceef40cd217ef406bc2522e9a8f879d60b0c0
Author: Aliaksey Kandratsenka <<EMAIL>>
Date:	Tue Sep 29 11:55:35 2015 -0700

    drop cycleclock reference in ThreadCache

M	src/thread_cache.cc

commit d7fdc3fc9ddc0937eb7961b1d8c864cc8182f0d9
Author: Aliaksey Kandratsenka <<EMAIL>>
Date:	Tue Sep 29 11:43:09 2015 -0700

    dropped unused and unsupported synchronization profiling facility

    Spinlock usage of cycle counter is due do tracking of time it's spent
    waiting for lock. But this tracking is only useful we actually have
    synchronization profiling working, which dont have. Thus I'm dropping
    calls to this facility with eye towards further removal of cycle clock
    usage.

M	Makefile.am
M	src/base/spinlock.cc
D	src/base/synchronization_profiling.h

commit 3a054d37c1f5323462bd77f55be02c5b0d764611
Author: Aliaksey Kandratsenka <<EMAIL>>
Date:	Mon Sep 28 15:40:15 2015 -0700

    dropped unused SpinLockWait function

M	src/base/spinlock.cc
M	src/base/spinlock_internal.cc
M	src/base/spinlock_internal.h

commit 5b62d38329f04ee30fb475a36b552c1ebc4e1e79
Author: Aliaksey Kandratsenka <<EMAIL>>
Date:	Mon Sep 28 10:27:49 2015 -0700

    avoid checking for dup. entries on empty backtrace

    This might fix issue #721. But it is right thing to do
    regardless. Since
    if depth is 0 we'll be reading random "garbage" on the stack.

M	src/profiler.cc

commit 7b9ded722e4cef9a44b8d8bfa80d3e1e108cf590
Author: Aliaksey Kandratsenka <<EMAIL>>
Date:	Sat Sep 12 16:26:54 2015 -0700

    fixed compiler warning in memory_region_map.cc

M	src/memory_region_map.cc

commit 4194e485cbb5d8c59f65aba49da63c08ecc573da
Author: Aliaksey Kandratsenka <<EMAIL>>
Date:	Sat Sep 12 16:20:53 2015 -0700

    Don't link libtcmalloc_minimal.so to libpthread.so

    So that LD_PRELOAD-ing doesn't force loading libpthread.so which may
    slow down some single-threaded apps.

    tcmalloc already has maybe_threads facility that can detect if
    libpthread.so is loaded (via weak symbols) and provide 'simulations'
    of
    some pthread functions that tcmalloc needs.

M	Makefile.am
M	src/maybe_threads.cc
M	src/maybe_threads.h
M	src/static_vars.cc

commit 121038308d8c5b34707614c44de265816a322563
Author: Fredrik Mellbin <<EMAIL>>
Date:	Sun Sep 27 01:18:31 2015 +0200

    Check if _MSC_VER is defined to avoid warnings

M	src/windows/port.h

commit 73673229955cf35c5b3046ee3100d94e82d33bc2
Author: Fredrik Mellbin <<EMAIL>>
Date:	Sun Sep 27 01:11:14 2015 +0200

    Make default config.h work with VS2015

M	src/windows/config.h

commit ae0a444db06b2327441e6160eafa33d8b7b95629
Author: Dair Grant <<EMAIL>>
Date:	Thu Oct 1 13:46:22 2015 +0100

    Ensure ThreadCache objects are CACHELINE_ALIGNED.

M	src/base/basictypes.h
M	src/common.cc
M	src/system-alloc.cc

commit ea0b1d315497771d9d077c754fb9d6a92d1c01dc
Author: Aliaksey Kandratsenka <<EMAIL>>
Date:	Sat Sep 26 11:13:13 2015 -0700

    unbreak TestErrno again

    Somehow in previous commit I managed to break assignment of memalign
    result to variable being tested. Luckily gcc gave me warning.

M	src/tests/tcmalloc_unittest.cc

commit e53aef24add50bdee5ab2943d96e5c5b2b3bf596
Author: Aliaksey Kandratsenka <<EMAIL>>
Date:	Sat Sep 26 09:58:45 2015 -0700

    don't try to test memalign on windows

    This unbreaks tcmalloc_unittest.cc on windows.

M	src/tests/tcmalloc_unittest.cc

commit 7707582448d5bdf311d88726c5f77fcbb0bedd09
Merge: 5078abd 9eb63bd
Author: Aliaksey Kandratsenka (aka Aliaksei Kandratsenka)
<<EMAIL>>
Date:	Sat Sep 26 09:43:50 2015 -0700

    Merge pull request #717 from myrsloik/master

    Use correct mangled new and delete symbols on windows x64

commit 9eb63bddfb7a8a7b086bb3daceeeaa2e3b1d8f67
Author: Fredrik Mellbin <<EMAIL>>
Date:	Thu Sep 24 19:57:26 2015 +0200

    Use correct mangled new and delete symbols on windows x64

M	src/windows/patch_functions.cc

commit 5078abdb331e63d7a216994f186eb736861f8df7
Author: fdeweerdt <<EMAIL>>
Date:	Tue Aug 18 09:27:39 2015 -0700

    Don't discard curl options if timeout is not defined.

    Editing the options passed to curl via 'my @URL_FETCHER = ("curl",
    "-s");' (in particular to add a -k to ignore self signed certs) fails
    for some invocations of curl. In FetchDynamicProfile, 'my @fetcher =
    AddFetchTimeout($fetch_timeout, @URL_FETCHER);' ends up being just
    'curl' if timeout is not defined.

    This happens because AddFetchTimeout doesn't retrieve all the
    arguments
    from the caller.

    [<EMAIL>: updated commit message]
    Signed-off-by: Aliaksey Kandratsenka <<EMAIL>>

M	src/pprof

commit 54505f1d50c2d1f4676f5e87090b64a117fd980e
Author: Aliaksey Kandratsenka <<EMAIL>>
Date:	Sun Aug 2 19:28:03 2015 -0700

    help clang with inlining important fast-path functions

    Clang's recent focus on code size doesn't help us in malloc fast-path
    because somehow clang completely ignores inline directives.

    In order to help clang generate code that was actually intended by
    original authors, we're adding always_inline attribute to key
    fast-path functions.

    Clang also guessed likely branch "wrong" in couple places. Which is
    now addressed by UNLIKELY declarations there.

M	src/tcmalloc.cc
M	src/thread_cache.h

commit 73c0c8c61b84e268bafd961bf304b2e4d296142f
Author: Aliaksey Kandratsenka <<EMAIL>>
Date:	Sun Aug 2 19:09:02 2015 -0700

    moved do_mallor_or_cpp_alloc in better place

M	src/tcmalloc.cc

commit 41aca070e85258d9d47b0ac47f5eddece8bf45ba
Author: Aliaksey Kandratsenka <<EMAIL>>
Date:	Sun Aug 2 10:53:32 2015 -0700

    always set errno to ENOMEM on OOM condition and in single place

    While standards do not require us to set errno to ENOMEM in certain
    places (like posix_memalign), existing code may sometimes set it
    (i.e. because mmap or sbrk couldn't get memory from kernel)
    anyways. And from my reading of glibc, it's malloc is doing more or
    less same by just always setting ENOMEM on OOM condition.

    This commit also eliminates some functions (XXX_no_errno) that are not
    needed anymore.

M	src/debugallocation.cc
M	src/page_heap.cc
M	src/tcmalloc.cc

commit c4493874cd3b662d2778f3b79a3096ae61569b67
Author: Aliaksey Kandratsenka <<EMAIL>>
Date:	Sat Aug 1 22:20:15 2015 -0700

    deal with OOM handling in one place and prior to returning result

    This commit removes 4 (four!) duplicates of C++ OOM handling. And
    introduces one helper for that.

    Other change is that malloc doesn't have to check tc_new_mode anymore
    until it _actually_ deals with OOM condition. Which shaves off couple
    instructions from fast-path.

M	src/debugallocation.cc
M	src/tcmalloc.cc

commit 09448a8fe977eaa083340c2504caac8820832179
Author: Aliaksey Kandratsenka <<EMAIL>>
Date:	Sun Aug 2 18:11:04 2015 -0700

    added tcmalloc_unittest path with TCMALLOC_HEAP_LIMIT_MB=512

    I.e. so that we can exercise "voluntary" OOM conditions better.

M	src/tests/tcmalloc_unittest.sh

commit 73fb7c7eb37e488bab8298be4110e30d83af2184
Author: Aliaksey Kandratsenka <<EMAIL>>
Date:	Sun Aug 2 01:47:55 2015 -0700

    added test on errno = ENOMEM on out of memory

M	src/tests/tcmalloc_unittest.cc

commit eb725ff26371dfb5ae2523802c6abe75833cacef
Author: Aliaksey Kandratsenka <<EMAIL>>
Date:	Sat Aug 1 20:54:39 2015 -0700

    unbreak heap-profiler-unittest on gcc 5

    gcc 5 has got nice new optimization (-fipa-icf) which merges identical
    functions into one. And that causes heap-profiler_unittest to fail
    since it expects to see both Allocate and Allocate2 in heap
    profiles. And smart GCC detects that they are same function and makes
    one function out of two and thus breaks this test.

    New code simply adds (disabled) logging calls to make those functions
    non-identical.

M	src/tests/heap-profiler_unittest.cc

commit 53833298f3822b2b8b78c9dc85160d65d78a6857
Author: Aliaksey Kandratsenka <<EMAIL>>
Date:	Sat Aug 1 19:46:34 2015 -0700

    unbreak heap_checker_unittest on gcc 5

    GCC 5 ended up too smart and optimized out assignment of allocated
    block to global variable. Which caused test to fail since it triggered
    unexpected "leak".

M	src/tests/heap-checker_unittest.cc

commit 024bae96ce8e1591993fc0da191ce0a92d609481
Author: Aliaksey Kandratsenka <<EMAIL>>
Date:	Sat Aug 1 17:41:42 2015 -0700

    dropped support for PREANSINEW define which nobody needs anymore

M	src/debugallocation.cc
M	src/tcmalloc.cc

commit 64e0133901a20f83c41adb36748fd19d21228515
Author: Aliaksey Kandratsenka <<EMAIL>>
Date:	Sun May 3 12:55:47 2015 -0700

    added trivial malloc fast-path benchmark

    While this is not good representation of real-world production malloc
    behavior, it is representative of length (instruction-wise and well as
    cycle-wise) of fast-path. So this is better than nothing.

M	.gitignore
M	Makefile.am
A	benchmark/malloc_bench.cc

commit e1d1311cfb6312cd44e086c879f3e95cbfa0eb9d
Author: Tom Conerly <<EMAIL>>
Date:	Mon Jul 27 11:35:29 2015 -0700

    issue-699: Free list on error paths

M	src/heap-profile-table.cc

commit b5b79860fd2b8e1a9b0573e93f942695f2992b59
Author: Aliaksey Kandratsenka <<EMAIL>>
Date:	Sat Aug 1 11:24:56 2015 -0700

    issue-702: correctly declare arg-less functions in profiler.h

    This is patch by user mitchblank.

    From his words:

    The problem is pretty simple.  Ancient C code allowed declarations
    without argument prototypes, i.e.

      int foo();

    For compatibility this is still accepted.  If you want to declare a
    function with zero prototypes the correct way to do it is:

       int foo(void);

    C++ also accepts this syntax, but it's not needed there.

    Normally compilers still accept the old-style entries, but with
    sufficient warning flags gcc will complain about them.  It is good for
    header files to have the explicit "void" argument so all compilers are
    kept happy.

    I'm attaching a simple patch to add the "void" parameter to that file.
    I haven't checked if other headers have the same problem (I'm just
    using the profiler at the moment)

    <end of quote>

    In fact "int foo()" means "foo accepts any args" and we really want
    "foo has no args". For which int foo (void) is right declaration.

M	src/gperftools/profiler.h

commit 7df7f14c949d89d9c3f5c7c339bbdda81fb8abc7
Author: Aliaksey Kandratsenka <<EMAIL>>
Date:	Sat Jun 13 21:35:06 2015 -0700

    issue-693: enable futex usage on arm

    This patch was contributed by user spotrh.

M	src/base/linux_syscall_support.h
M	src/base/spinlock_linux-inl.h

commit cb998e56d763cfe901cf30a692d4cfd4f85259ae
Author: Aliaksey Kandratsenka <<EMAIL>>
Date:	Sat Jun 13 21:28:28 2015 -0700

    issue-693: convert sys_futex to it's 6-arg form

    Because sys_futex actually takes 6 args in more recent kernels (even
    though last two args are unused for FUTEX_{WAKE,WAIT}.

    This is patch contributed by user spotrh.

M	src/base/linux_syscall_support.h

commit 36066b8df4bc516ade5209a1f60bd84d6448b531
Author: Aliaksey Kandratsenka <<EMAIL>>
Date:	Sat Jun 27 21:15:30 2015 -0700

    issue-695: implementated TCMALLOC_TRACE_FILE variable

    This is contributed by Paolo Bonzini.

    This commit adds TCMALLOC_TRACE_FILE environment variable, which if
    defined overrides location of malloc trace file.

M	src/debugallocation.cc

commit c4069d2d37b67296d675c2d1de42a46dc6d43efc
Author: Brian Silverman <<EMAIL>>
Date:	Tue Jun 16 16:56:48 2015 -0700

    Add empty virtual destructor to class with virtual methods.

    Clang 3.5 has a warning about deleting objects with virtual methods
    through non-virtual destructors which was triggered. I'm not sure
    whether this actually creates any undefined or otherwise incorrect
    behavior, but it seems like a good thing to fix regardless.

    Example compiler warning:
    third_party/gperftools/src/tests/profile-handler_unittest.cc:282:5:
    error:
      delete called on '(anonymous namespace)::BusyThread' that has
      virtual
      functions but non-virtual destructor [-Wdelete-non-virtual-dtor]
	delete busy_worker_;
	^

M	src/tests/profile-handler_unittest.cc

commit 019362fefcdca39a46d1a831ed46266c24b9a273
Author: Patrick LoPresti <<EMAIL>>
Date:	Mon May 25 11:56:51 2015 -0700

    Add support for CPUPROFILE_TIMER_SIGNAL environment variable.

    Which both enables per-thread timers and allows the signal number for
    the timer to be selected.

    [<EMAIL>: reformatted commit message for subject line length]
    Signed-off-by: Aliaksey Kandratsenka <<EMAIL>>

M	src/profile-handler.cc
M	src/tests/profile-handler_unittest.cc

commit 81d8d2a9e7f941a2051781fd0fe62c683c32f1ef
Author: Milton Chiang <<EMAIL>>
Date:	Wed May 13 21:53:05 2015 +0800

    Add "ARMv8-A" to the supporting list of ARM architecture.

M	src/base/arm_instruction_set_select.h

commit 64d1a86cb8da245c982d470a7dfdd635197e6e5e
Author: Aliaksey Kandratsenka <<EMAIL>>
Date:	Sat May 9 15:38:12 2015 -0700

    include time.h for struct timespec on Visual Studio 2015

    This patch was submitted by user wmamrak.

M	src/windows/port.h

commit 7013b219970a329d1db58fbd7fa7c907bec8dbba
Author: Aliaksey Kandratsenka <<EMAIL>>
Date:	Sat May 9 12:48:11 2015 -0700

    hook mi_force_{un,}lock on OSX instead of pthread_atfork

    This is patch by Anton Samokhvalov.

    Apparently it helps with locking around forking on OSX.

M	src/libc_override_osx.h
M	src/static_vars.cc

commit f25f8e0bf2d361f852929848d79d7ba54586c352
Author: Angus Gratton <<EMAIL>>
Date:	Mon May 4 08:18:43 2015 +1000

    Clarify that only tcmalloc_minimal is supported on Windows.

M	INSTALL
M	README_windows.txt

commit 772a686c45455893708178d3b59b1d3b571015aa
Author: Aliaksey Kandratsenka <<EMAIL>>
Date:	Sun May 3 13:15:16 2015 -0700

    issue-683: fix compile error in clang with -m32 and 64-bit off_t

M	src/malloc_hook_mmap_linux.h

commit 0a3bafd645764250732f3c0627534142568f6f1f
Author: Aliaksey Kandratsenka <<EMAIL>>
Date:	Sat Apr 11 10:35:53 2015 -0700

    fix typo in PrintAvailability code

    This is patch contributed by user ssubotin.

M	src/windows/nm-pdb.c

commit 6ce10a2a05f13803061538d5c77e89695de59be4
Author: Matt Cross <<EMAIL>>
Date:	Thu Mar 26 16:13:56 2015 -0400

    Add support for printing collapsed stacks for generating flame graphs.

M	src/pprof

commit 2c1a165fa56a6f8dff8fa2662ceda971ad676ead
Author: Matt Cross <<EMAIL>>
Date:	Thu Mar 26 12:10:23 2015 -0400

    Add support for reading debug symbols automatically on systems
    where shared libraries with debug symbols are installed at
    "/usr/lib/debug/<originalpath>.debug", such as RHEL and CentOS.

M	src/pprof

commit 2e654956287043a30aeaec20e5c19650358af618
Author: Jonathan Lambrechts <<EMAIL>>
Date:	Fri Feb 13 18:52:21 2015 +0100

    callgrind : handle inlined functions

M	src/pprof

commit 90d7408d381cf2ad68ce4974cbccd51ed5222ca3
Author: Jonathan Lambrechts <<EMAIL>>
Date:	Fri Feb 13 18:51:33 2015 +0100

    pprof : callgrind : fix unknown files

M	src/pprof

commit aa963a24ae7a74b095a631ea6a86cd071c453911
Author: Aliaksey Kandratsenka <<EMAIL>>
Date:	Mon Feb 9 08:35:03 2015 -0800

    issue-672: fixed date of news entry of gperftools 2.4 release

    It is 2015 and not 2014. Spotted and reported by Armin Rigo.

M	NEWS

commit c66aeabdbacbfd3aff7a6633f34526ca32642f67
Author: Aliaksey Kandratsenka <<EMAIL>>
Date:	Sat Jan 10 14:35:54 2015 -0800

    fixed default value of HEAP_PROFILER_TIME_INTERVAL in .html doc

M	doc/heapprofile.html

commit 689e4a5bb4b2a8afecb85e83b8e4f294f80b6124
Author: Aliaksey Kandratsenka <<EMAIL>>
Date:	Sat Jan 10 12:26:51 2015 -0800

    bumped version to 2.4

M	NEWS
M	configure.ac
M	src/windows/config.h
M	src/windows/gperftools/tcmalloc.h

commit 3f5f1bba0c2cb4862c38b0c9050f1cede8a5c344
Author: Aliaksey Kandratsenka <<EMAIL>>
Date:	Sun Dec 28 18:18:27 2014 -0800

    bumped version to 2.4rc

M	configure.ac
M	src/windows/config.h
M	src/windows/gperftools/tcmalloc.h

commit c4dfdebc796c2e802db686a7eea483f3d31edbcf
Author: Aliaksey Kandratsenka <<EMAIL>>
Date:	Sun Dec 28 17:53:02 2014 -0800

    updated NEWS for gperftools 2.4rc

M	NEWS

commit 0096be5f6f0bb2a01438ee78b7bb6158ffa5a1fb
Author: Aliaksey Kandratsenka <<EMAIL>>
Date:	Sat Dec 20 17:17:41 2014 -0800

    pprof: allow disabling auto-removal of "constant 2nd frame"

    "constand 2nd frame" feature is supposed to detect and workaround
    incorrect cpu profile stack captures where parts of or whole cpu
    profiling signal handler frames are not skipped.

    I've seen programs where this feature incorrectly removes non-signal
    frames.

    Plus it actually hides bugs in stacktrace capturing which we want be
    able to spot.

    There is now --no-auto-signal-frm option for disabling it.

M	src/pprof

commit 4859d8020579cd8db8b1f8bc6af382104b38f96f
Author: Aliaksey Kandratsenka <<EMAIL>>
Date:	Sat Dec 20 19:56:13 2014 -0800

    cpuprofiler: drop correct number of signal handler frames

    We actually have 3 and not 2 of them.

M	src/profiler.cc

commit 812ab1ee7e4365e6a9568834c7e8f4aef10018fb
Author: Aliaksey Kandratsenka <<EMAIL>>
Date:	Sat Dec 20 19:53:12 2014 -0800

    pprof: eliminate duplicate top frames if dropping signal frames

    In cpu profiles that had parts of signal handler we could have
    situation like that:

    * PC
    * signal handler frame
    * PC

    Specifically when capturing stacktraces via libunwind.

    For such stacktraces pprof used to draw self-cycle in functions
    confusing everybody. Given that me might have a number of such
    profiles in the wild it makes sense to treat that duplicate PC issue.

M	src/pprof

commit e6e78315e4761ad121a5eeb4fdffe3571d81ac17
Author: Aliaksey Kandratsenka <<EMAIL>>
Date:	Sat Dec 20 19:04:43 2014 -0800

    cpuprofiler: better explain deduplication of top stacktrace entry

M	src/profiler.cc

commit 24b8ec28464712bc124af5655ebf877fb3f79032
Author: Aliaksey Kandratsenka <<EMAIL>>
Date:	Sat Dec 20 17:10:38 2014 -0800

    cpuprofiler: disable capturing stacktrace from signal's ucontext

    This was reported to cause problems due to libunwind occasionally
    returning top level pc that is 1 smaller than real pc which causes
    problems.

M	src/stacktrace_libunwind-inl.h

commit 83588de7204124c8d60703f169c2f3af8aa0ce5f
Author: Aliaksey Kandratsenka <<EMAIL>>
Date:	Sat Dec 20 17:04:10 2014 -0800

    pprof: added support for dumping stacks in --text mode

    Which is very useful for diagnosing stack capturing and processing
    bugs.

M	src/pprof

commit 2f29c9b06220ce6ad75d5ab2b98e9f63ade79eea
Author: Aliaksey Kandratsenka <<EMAIL>>
Date:	Sat Dec 20 17:03:36 2014 -0800

    pprof: made --show-addresses work

M	src/pprof

commit b8b027d09a53dd6292d2f7c45aec40198628a808
Author: Raphael Moreira Zinsly <<EMAIL>>
Date:	Tue Dec 23 10:55:22 2014 -0200

    Make PPC64 use 64K of internal page size for tcmalloc by default

    This patch set the default tcmalloc internal page size to 64K when
    built on PPC.

M	configure.ac

commit 3f55d874be8812aef9f0f567048188584962b4c1
Author: Raphael Moreira Zinsly <<EMAIL>>
Date:	Tue Dec 23 10:29:49 2014 -0200

    New configure flags to set the alignment and page size of tcmalloc

    Added two new configure flags, --with-tcmalloc-pagesize and
    --with-tcmalloc-alignment, in order to set the tcmalloc internal page
    size and tcmalloc allocation alignment without the need of a compiler
    directive and to make the choice of the page size independent of the
    allocation alignment.

M	INSTALL
M	configure.ac
M	src/common.h

commit 1035d5c18f64d114ac790b92a96f3b3a1a301eb9
Author: Aliaksey Kandratsenka <<EMAIL>>
Date:	Sun Dec 21 19:52:34 2014 -0800

    start building malloc_extension_c_test even with static linking

    Comment in Makefile.am stating that it doesn't work with static
    linking is not accurate anymore.

M	Makefile.am

commit d570a6391cf4c5a5570e22ada5cf0b324c7b8dfd
Author: Aliaksey Kandratsenka <<EMAIL>>
Date:	Sun Dec 21 19:33:25 2014 -0800

    unbreak malloc_extension_c_test on clang

    Looks like even force_malloc trick was not enough to force clang to
    actually call malloc. I'm now calling tc_malloc directly to prevent
    that smartness.

M	src/tests/malloc_extension_c_test.c

commit 4ace8dbbe2e04da1029a539c72b90dee1724c33f
Author: Aliaksey Kandratsenka <<EMAIL>>
Date:	Sun Dec 21 18:49:47 2014 -0800

    added subdir-objects automake options

    This is suggested by automake itself regarding future-compat.

M	.gitignore
M	Makefile.am

commit f72e37c3f99d942dd648e392abc65f294aa94fa8
Author: Aliaksey Kandratsenka <<EMAIL>>
Date:	Sun Dec 21 18:27:03 2014 -0800

    fixed C++ comment warning in malloc_extension_c.h from C compiler

M	src/gperftools/malloc_extension_c.h

commit f94ff0cc0943c7b7bb9390f9d83a57c9d9e11fd0
Author: Aliaksey Kandratsenka <<EMAIL>>
Date:	Sun Nov 9 14:41:36 2014 -0800

    made AtomicOps_x86CPUFeatureStruct hidden

    So that access to has_sse2 is faster under -fPIC.

M	src/base/atomicops-internals-x86.h

commit 987a724c23df4b29eb457e96bb5c1bbab15dc90e
Author: Aliaksey Kandratsenka <<EMAIL>>
Date:	Sun Nov 9 14:20:20 2014 -0800

    dropped atopmicops workaround for irrelevant Opteron locking bug

    It's not cheap at all when done in this way (i.e. without runtime
    patching) and apparently useless.

    It looks like Linux kernel never got this workaround at all. See
    bugzilla ticket: https://bugzilla.kernel.org/show_bug.cgi?id=11305

    And I see no traces of this workaround in glibc either.

    On the other hand, opensolaris folks apparently still have it (or
    something similar, based on comments on linux bugzilla) in their code:
    https://github.com/illumos/illumos-gate/blob/32842aabdc7c6f8f0c6140a256cf42cf5404fefb/usr/src/uts/i86pc/os/mp_startup.c#L1136

    And affected CPUs (if any) are from year 2008 (that's 6 years now).

    Plus even if somebody still uses those cpus (which is unlikely), they
    won't have working kernel and glibc anyways.

M	src/base/atomicops-internals-x86.cc
M	src/base/atomicops-internals-x86.h

commit 7da5bd014d77ddaf694054b1e3ae0a3ef92ab384
Author: Aliaksey Kandratsenka <<EMAIL>>
Date:	Sat Dec 20 21:18:07 2014 -0800

    enabled aggressive decommit by default

    TCMALLOC_AGGRESSIVE_DECOMMIT=f is one way to disable it and
    SetNumericProperty is another.

M	src/static_vars.cc
M	src/tests/tcmalloc_unittest.sh

commit 51b0ad55b3267caff6cd2d25815bfb913179b526
Author: Aliaksey Kandratsenka <<EMAIL>>
Date:	Sun Nov 9 17:36:49 2014 -0800

    added basic unit test for singular malloc hooks

M	src/tests/malloc_extension_c_test.c

commit bce72dda078d2cb3e9745077f9903e642a966131
Author: Aliaksey Kandratsenka <<EMAIL>>
Date:	Sun Nov 9 17:12:05 2014 -0800

    inform compiler that tcmalloc allocation sampling is unlikely

    Now compiler generates slightly better code which produces jump-less
    code for common case of not sampling allocations.

M	src/tcmalloc.cc

commit 4f051fddcd7af53e1607b6a4866ffa461a0033ef
Author: Aliaksey Kandratsenka <<EMAIL>>
Date:	Sat Mar 15 13:18:28 2014 -0700

    eliminated CheckIfKernelSupportsTLS

    We don't care about pre-2.6.0 kernels anymore. So we can assume that
    if compile time check worked, then at runtime it'll work.

M	src/tcmalloc.cc
M	src/thread_cache.cc
M	src/thread_cache.h
M	src/windows/port.cc

commit 81291ac3992ec7500faf2026c72feb80c9634dc3
Author: Aliaksey Kandratsenka <<EMAIL>>
Date:	Sun Nov 9 16:48:45 2014 -0800

    set elf visibility to hidden for malloc hooks

    To speed up access to them under -fPIC.

M	src/malloc_hook-inl.h

commit 105c004d0c84137f32cc71b8d3f7899fcc8c2e72
Author: Aliaksey Kandratsenka <<EMAIL>>
Date:	Sun Nov 9 14:40:30 2014 -0800

    introduced ATTRIBUTE_VISIBILITY_HIDDEN

    So that we can disable elf symbol interposition for certain
    perf-sensitive symbols.

M	src/base/basictypes.h

commit 6a6c49e1f53df3d00a0661a86090534146ce686c
Author: Aliaksey Kandratsenka <<EMAIL>>
Date:	Sun Nov 9 16:23:06 2014 -0800

    replaced separate singular malloc hooks with faster HookList

    Specifically, we can now check in one place if hooks are set at all,
    instead of two places. Which makes fast path shorter.

M	src/malloc_hook-inl.h
M	src/malloc_hook.cc

commit ba0441785bae214566008e69adcd915800d9dbb3
Author: Aliaksey Kandratsenka <<EMAIL>>
Date:	Sun Nov 9 16:13:54 2014 -0800

    removed extra barriers in malloc hooks mutation methods

    Because those are already done under spinlock and read-only and
    lockless Traverse is already tolerant to slight inconsistencies.

M	src/malloc_hook.cc

commit 890f34c77ef79dfe1e00ce36a3f91aee7fe759b7
Author: Aliaksey Kandratsenka <<EMAIL>>
Date:	Sun Nov 9 16:08:59 2014 -0800

    introduced support for deprecated singular hooks into HookList

    So that we can later drop separate singular hooks.

M	src/malloc_hook-inl.h
M	src/malloc_hook.cc

commit 81ed7dff11de915b12c4111d403e52c81c786f82
Author: Aliaksey Kandratsenka <<EMAIL>>
Date:	Sun Dec 7 13:33:40 2014 -0800

    returned date of 2.3rc in NEWS back

M	NEWS

commit 463a619408219fff8955d47a71de1aab31ebd129
Author: Aliaksey Kandratsenka <<EMAIL>>
Date:	Sun Dec 7 12:53:35 2014 -0800

    bumped version to 2.3

M	configure.ac
M	src/windows/config.h
M	src/windows/gperftools/tcmalloc.h

commit 76e8138e12567e2ee1f638e8fcffadc2ca1de83d
Author: Aliaksey Kandratsenka <<EMAIL>>
Date:	Sun Dec 7 12:46:49 2014 -0800

    updated NEWS for gperftools 2.3

M	NEWS

commit 8eb4ed785ae883acb6425fd980e9d3e6bdcab89d
Author: Raphael Moreira Zinsly <<EMAIL>>
Date:	Thu Nov 27 14:11:09 2014 -0200

    Added option to disable libunwind linking

    This patch adds a configure option to enable or disable libunwind
    linking.
    The patch also disables libunwind on ppc by default.

M	configure.ac

commit 3b94031d21fac39ce5be820f4f8bc37626ed4c08
Author: Aliaksey Kandratsenka <<EMAIL>>
Date:	Thu Nov 27 11:48:08 2014 -0800

    compile libunwind unwinder only of __thread is supported

    This fixed build on certain OSX that I have access to.

M	src/stacktrace.cc

commit 3ace468202a8647a16134d0bb42fa497fa3ec0d4
Author: Aliaksey Kandratsenka <<EMAIL>>
Date:	Thu Nov 27 10:43:11 2014 -0800

    issue-658: correctly close socketpair fds when socketpair fails

    This applies patch by glider.

M	src/symbolize.cc

commit e7d5e512b068fb76f5eeed7985bebd9c0d14e226
Author: Aliaksey Kandratsenka <<EMAIL>>
Date:	Sun Nov 2 20:02:12 2014 -0800

    bumped version to 2.3rc

M	configure.ac
M	src/windows/config.h
M	src/windows/gperftools/tcmalloc.h

commit 1d44d378513a8782c058833118b664f38214810b
Author: Aliaksey Kandratsenka <<EMAIL>>
Date:	Sun Nov 2 19:38:29 2014 -0800

    updated NEWS for gperftools 2.3rc

M	NEWS

commit 1108d83cf4a1692fce3b736e16d3e98c33329177
Author: Aliaksey Kandratsenka <<EMAIL>>
Date:	Sun Sep 7 13:09:14 2014 -0700

    implemented cpu-profiling mode that profiles threads separately

    Default mode of operation of cpu profiler uses itimer and
    SIGPROF. This timer is by definition per-process and no spec defines
    which thread is going to receive SIGPROF. And it provides correct
    profiles only if we assume that probability of picking threads will be
    proportional to cpu time spent by threads.

    It is easy to see, that recent Linux (at least on common SMP hardware)
    doesn't satisfy that assumption. Quite big skews of SIGPROF ticks
    between threads is visible. I.e. I could see as big as 70%/20%
    division instead of 50%/50% for pair of cpu-hog threads. (And I do see
    it become 50/50 with new mode)

    Fortunately POSIX provides mechanism to track per-thread cpu time via
    posix timers facility. And even more fortunately, Linux also provides
    mechanism to deliver timer ticks to specific threads.

    Interestingly, it looks like FreeBSD also has very similar facility
    and seems to suffer from same skew.  But due to difference in a way
    how threads are identified, I haven't bothered to try to support this
    mode on FreeBSD.

    This commit implements new profiling mode where every thread creates
    posix timer which tracks thread's cpu time. Threads also also set up
    signal delivery to itself on overflows of that timer.

    This new mode requires every thread to be registered in cpu
    profiler. Existing ProfilerRegisterThread function is used for that.

    Because registering threads requires application support (or suitable
    LD_PRELOAD-able wrapper for thread creation API), new mode is off by
    default. And it has to be manually activated by setting environment
    variable CPUPROFILE_PER_THREAD_TIMERS.

    New mode also requires librt symbols to be available. Which we do not
    link to due to librt's dependency on libpthread.  Which we avoid due
    to perf impact of bringing in libpthread to otherwise single-threaded
    programs. So it has to be either already loaded by profiling program
    or LD_PRELOAD-ed.

M	Makefile.am
M	configure.ac
M	src/maybe_threads.cc
M	src/maybe_threads.h
M	src/profile-handler.cc
M	src/tests/profile-handler_unittest.cc

commit 714bd93e42535e759716324a90fbb395506499d2
Author: Aliaksey Kandratsenka <<EMAIL>>
Date:	Sun Aug 10 20:28:11 2014 -0700

    drop workaround for too old redhat 7

    Note that this is _not_ RHEL7 but original redhat 7 from early 2000s.

M	configure.ac

commit 8de46e66fcd2577758ab297b553bb0f468d8a97a
Author: Aliaksey Kandratsenka <<EMAIL>>
Date:	Sun Aug 10 19:38:07 2014 -0700

    don't add leaf function twice to profile under libunwind

M	src/profiler.cc

commit 2e5ee0488996437aeef2028ad95d969b56abcad1
Author: Aliaksey Kandratsenka <<EMAIL>>
Date:	Sun Aug 10 12:19:45 2014 -0700

    pprof: indicate if using remote profile

    Missing profile file is common source of confusion. So a bit more
    clarify is useful.

M	src/pprof

commit 6efe96b41c9531f68f806faa0464445f884178ce
Author: Aliaksey Kandratsenka <<EMAIL>>
Date:	Sat Oct 11 15:09:18 2014 -0700

    issue-493: correctly detect __ARM_ARCH_6ZK__ for MemoryBarrier

    Which should fix issue reported by user pedronavf

M	src/base/atomicops-internals-arm-v6plus.h

commit 8e97626378d5c4151a480dea1964e25969c9311d
Author: Aliaksey Kandratsenka <<EMAIL>>
Date:	Sun Nov 2 11:28:30 2014 -0800

    issue-655: use safe getenv for aggressive decommit mode flag

    Because otherwise we risk deadlock due to too early use of getenv on
    windows.

M	src/base/commandlineflags.h
M	src/static_vars.cc

commit 8c3dc52fcfe02412a529769a22cbc75388a5d368
Author: Aliaksey Kandratsenka <<EMAIL>>
Date:	Sat Oct 18 16:35:57 2014 -0700

    issue-654: [pprof] handle split text segments

    This applies patch by user simonb.

    Quoting:

    Relocation packing splits a single executable load segment into two.
    Before:

      LOAD	     0x000000 0x00000000 0x00000000 0x2034d28 0x2034d28
      R E 0x1000
      LOAD	     0x2035888 0x02036888 0x02036888 0x182d38 0x1a67d0
      RW  0x1000

    After:
      LOAD	     0x000000 0x00000000 0x00000000 0x14648 0x14648 R
      E 0x1000
      LOAD	     0x014648 0x0020c648 0x0020c648 0x1e286e0 0x1e286e0
      R E 0x1000
      ...
      LOAD	     0x1e3d888 0x02036888 0x02036888 0x182d38 0x1a67d0
      RW  0x1000

    The .text section is in the second LOAD, and this is not at
    offset/address zero.  The result is that this library shows up in
    /proc/self/maps as multiple executable entries, for example (note:
    this trace is not from the library dissected above, but rather from an
    earlier version of it):

      73b0c000-73b21000 r-xp 00000000 b3:19 786460
      /data/.../libchrome.2160.0.so
      73b21000-73d12000 ---p 00000000 00:00 0
      73d12000-75a90000 r-xp 00014000 b3:19 786460
      /data/.../libchrome.2160.0.so
      75a90000-75c0d000 rw-p 01d91000 b3:19 786460
      /data/.../libchrome.2160.0.so

    When parsing this, pprof needs to merge the two r-xp entries above
    into a single entry, otherwise the addresses it prints are incorrect.

    The following fix against 2.2.1 was sufficient to make pprof --text
    print the correct output.  Untested with other pprof options.

M	src/pprof

commit 44c61ce6c4c713b194330641f400bbf64fd2abec
Author: Ricardo M. Correia <<EMAIL>>
Date:	Wed Oct 8 04:39:14 2014 +0200

    Fix parsing /proc/pid/maps dump in CPU profile data file

    When trying to use pprof on my machine, the symbols of my program were
    not being recognized.

    It turned out that pprof, when calculating the offset of the text list
    of mapped objects (the last section of the CPU profile data file), was
    assuming that the slot size was always 4 bytes, even on 64-bit
    machines.

    This led to ParseLibraries() reading a lot of garbage data at the
    beginning of the map, and consequently the regex was failing to
    match on
    the first line of the real (non-garbage) map.

M	src/pprof

commit 2a28ef24ddf8013bff59914b10902f1fb07bf9b2
Author: Aliaksey Kandratsenka <<EMAIL>>
Date:	Sat Sep 6 16:49:24 2014 -0700

    Added remaining memory allocated info to 'Exiting' dump message

    This applies patch by user yurivict.

M	src/heap-profiler.cc

commit bbf346a856d4a7c5c2ab0e65d7cccf3dc1f23f13
Author: Adam McNeeney <<EMAIL>>
Date:	Fri Aug 22 10:01:24 2014 +0100

    Cope with new addr2line outputs for DWARF4

    Copes with ? for line number (converts to 0).
    Copes with (discriminator <num>) suffixes to file/linenum (removes).

    Change-Id: I96207165e4852c71d3512157864f12d101cdf44a

M	src/pprof

commit b08d760958dbacc8da822f7f3db76d58c95a114d
Author: Aliaksey Kandratsenka <<EMAIL>>
Date:	Sat Aug 23 14:45:08 2014 -0700

    issue-641: Added --show_addresses option

    This applies patch by user yurivict.

M	src/pprof

commit 3c326d9f200a527bba45f1d222aa6aff20d42bc3
Author: Aliaksey Kandratsenka <<EMAIL>>
Date:	Tue Aug 19 08:14:08 2014 -0700

    issue-644: fix possible out-of-bounds access in GetenvBeforeMain

    As suggested by user Ivan L.

M	src/base/sysinfo.cc

commit f1ae3c446f34f6e8ac901caba281051ec269e4df
Author: jiakai <<EMAIL>>
Date:	Mon Jul 28 11:28:03 2014 -0700

    Add an option to allow disabling stripping template argument in pprof

M	src/pprof

commit a12890df2519d254d1c497b8e0a65bb8fc9e1ab2
Author: Aliaksey Kandratsenka <<EMAIL>>
Date:	Sat Jul 26 14:12:42 2014 -0700

    issue-635: allow whitespace in libraries paths

    This applies change suggested <NAME_EMAIL>

M	src/pprof

commit d5e36788d8bc626403dc020a86213cfc740ee73d
Author: Aliaksey Kandratsenka <<EMAIL>>
Date:	Sat Jul 26 13:59:22 2014 -0700

    issue-636: fix prof/web command on Windows/MinGW

    This applies patch sent by user chaishushan.

M	src/pprof

commit 4b788656bb9c480640d917d27d8a94a5eae436f5
Author: Michael Pasieka <<EMAIL>>
Date:	Sun Jul 13 18:09:35 2014 -0700

    added option to display stack traces in output for heap checker

    Quoting from email:

    I had the same question as William posted to stack overflow back on
    Dec 9,2013: How to display symbols in stack trace of google-perftools
    heap profiler (*).	I dug into the source and realized the
    functionality was not there but could be added. I am hoping that
    someone else will find this useful/helpful.

    The patch I created will not attach so I am adding below.

    Enjoy!

    -- Michael

    *
    http://stackoverflow.com/questions/20476918/how-to-display-symbols-in-stack-trace-of-google-perftools-heap-profiler

M	src/pprof

commit 3abb5cb819bafe7004363f041c194afd827cb053
Author: WenSheng He <<EMAIL>>
Date:	Mon Jun 30 14:39:13 2014 +0800

    issue-630: The env var should be "CPUPROFILE"

    To enable cpu profile, the env var should be "CPUPROFILE", not
    "PROFILE"
    actually.

    Signed-off-by: Aliaksey Kandratsenka <<EMAIL>>

M	src/gperftools/profiler.h

commit fd81ec257883c6d5486e4568c955dff86dbed5c8
Author: Aliaksey Kandratsenka <<EMAIL>>
Date:	Sat Jun 28 13:05:12 2014 -0700

    issue-631: fixed miscompilation of debugallocation without mmap

    This applies patch sent by user iamxujian.

    Clearly, when I updated debugallocation to fix issue-464 I've broken
    no-mmap path by forgetting closing brace.

M	src/debugallocation.cc

commit 2e90b6fd72fec33aedf547d1977bdee6b77645b9
Author: Aliaksey Kandratsenka <<EMAIL>>
Date:	Sat Jun 21 15:45:44 2014 -0700

    bumped version to 2.2.1

M	configure.ac
M	src/windows/config.h
M	src/windows/gperftools/tcmalloc.h

commit 577b940cc0a0ef207115d071a81127f93f02c083
Author: Aliaksey Kandratsenka <<EMAIL>>
Date:	Sat Jun 21 15:39:46 2014 -0700

    updated NEWS for 2.2.1

M	NEWS

commit 2fe4b329ad4d3f242b0bd73295375e70be79187b
Author: Aliaksey Kandratsenka <<EMAIL>>
Date:	Sun May 18 10:59:06 2014 -0700

    applied chromium patch fixing some build issue on android

    This applies patch from: https://codereview.chromium.org/284843002/ by
    <EMAIL>

M	src/libc_override_gcc_and_weak.h

commit c009398e3239be8ae6185ad83685caf7458c49bb
Author: Aliaksey Kandratsenka <<EMAIL>>
Date:	Sun Jun 15 12:53:14 2014 -0700

    issue-628:package missing stacktrace_powerpc-{linux,darwin}-inl.h

    This headers were missing in .tar.gz because they were not mentioned
    anywhere in Makefile.am.

M	Makefile.am

commit 81d99f21ede78ab8d5fec15d0055416ac1b581f3
Author: Adhemerval Zanella <<EMAIL>>
Date:	Tue Jun 3 07:50:56 2014 -0500

    issue-626: Fix SetupAggressiveDecommit initialization

    This patch fixes the SetupAggressiveDecommit initialization to
    run after
    pageheap_ creation.  Current code it not enforcing it, since
    InitStaticVars is being called outside the static_vars module.

M	src/static_vars.cc

commit 846b775dfadb77901202ae7ddbac30ad1de7df01
Author: Aliaksey Kandratsenka <<EMAIL>>
Date:	Sat May 3 17:50:11 2014 -0700

    bumped version to 2.2

M	configure.ac
M	src/windows/config.h
M	src/windows/gperftools/tcmalloc.h

commit cdf8e1e932016bd5f1737e1f10bce07226228d15
Author: Aliaksey Kandratsenka <<EMAIL>>
Date:	Sat May 3 17:44:38 2014 -0700

    updated NEWS for 2.2

M	NEWS

commit 0807476f56b9dc024375467be0f0b07fae3ee7fb
Author: Aliaksey Kandratsenka <<EMAIL>>
Date:	Sat May 3 17:38:14 2014 -0700

    issue-620: windows dll patching: fixed delete of old stub code

    After code for issue 359 was applied PreamblePatcher started using
    it's own code to manage memory of stub code fragments. It's not using
    new[] anymore. And it automatically frees stub code memory on
    Unpatch.

    Clearly, author of that code forgot to remote that no more needed
    delete call. With that delete call we end up trying to free memory
    that was never allocated with any of known allocators and crash.

M	src/windows/patch_functions.cc

commit facd7e83b341e069875b7c1cd7774cf671d932b5
Author: Aliaksey Kandratsenka <<EMAIL>>
Date:	Sat Apr 19 11:08:51 2014 -0700

    bumped version to 2.1.90

M	configure.ac
M	src/windows/config.h
M	src/windows/gperftools/tcmalloc.h

commit e8e082af25952e9bd10c37c3f028c91fcbbd1cbd
Author: Aliaksey Kandratsenka <<EMAIL>>
Date:	Sat Apr 19 13:16:02 2014 -0700

    updated NEWS for 2.2rc

M	NEWS

commit 802fdb739e7aabcb15443030842a2137a5559338
Author: Aliaksey Kandratsenka <<EMAIL>>
Date:	Sat Apr 12 18:07:11 2014 -0700

    issue-610: use TCMallocGetenvSafe from inside malloc

    Instead of plain getenv. So that windows getenv implementation that
    may call malloc does not deadlock.

M	src/common.cc
M	src/thread_cache.cc

commit 6b83516adefcf0806825f6dba2eb2232615d744b
Author: Aliaksey Kandratsenka <<EMAIL>>
Date:	Sat Apr 12 18:06:32 2014 -0700

    issue-610: made dynamic_annotations.c use TCMallocGetenvSafe

M	src/base/dynamic_annotations.c

commit aeef3b44201ba663c19ca1c97792b724346e84a9
Author: Aliaksey Kandratsenka <<EMAIL>>
Date:	Sat Apr 12 18:05:59 2014 -0700

    issue-610: introduced TCMallocGetenvSafe

    This is version of GetenvBeforeMain that's available to C code.

M	Makefile.am
M	src/base/sysinfo.cc
A	src/getenv_safe.h

commit 125e5ed58b72550e6b74e0139c2c3dd5e262a6ec
Author: Aliaksey Kandratsenka <<EMAIL>>
Date:	Sat Apr 12 12:38:19 2014 -0700

    don't enable backtrace() for stacktrace capturing by default

    Because we don't yet have a treatment for deadlocks that are caused by
    (recursive) use of malloc from within that facility.

M	configure.ac

commit 75b65f96b0bb44d70e0a461a03a3f8c928390283
Author: Raphael Moreira Zinsly <<EMAIL>>
Date:	Tue Apr 8 17:45:13 2014 -0300

    PowerPC: stacktrace function refactor and fixes

    This patch fixes the stacktrace creating when the function is
    interrupted by a signal. For Linux, the vDSO signal trampoline
    symbol is
    compared against LR from stack backchain and handled different in that
    case (since the signal trampoline layout a different stack frame).

    Because of this extensive change the PowerPC stacktrace code has now
    been refactored to split in Linux and Darwin specific codes.

M	src/stacktrace.cc
A	src/stacktrace_powerpc-darwin-inl.h
A	src/stacktrace_powerpc-linux-inl.h

commit 8deea9ff2a3e6eef8af64ea0727d6cb840c13769
Author: Raphael Moreira Zinsly <<EMAIL>>
Date:	Tue Apr 8 17:44:08 2014 -0300

    VDSOsupport cleanup

    This patch cleans up unused VDSO getcpu racking from VDSOsupport
    class,
    since the code is not used anywhere in gperftools and symbol name
    is not
    architecture independent.

M	src/base/vdso_support.cc
M	src/base/vdso_support.h

commit 9d5e1a0aa5a6ad1c7af18b65016fa5c9b7fccb47
Author: Raphael Moreira Zinsly <<EMAIL>>
Date:	Tue Apr 8 17:43:11 2014 -0300

    Fixed issues with heap checker on PPC64 LE.

    Fixed the wrapper for the syscall sys_clone and the test for heap
    checker on PPC64 LE. Both use the ODP structure, which is only
    used on BE architectures.

M	src/base/linux_syscall_support.h
M	src/tests/heap-checker_unittest.cc

commit 49237462c8ae7920332c1034c623e57b50a6109c
Author: Raphael Moreira Zinsly <<EMAIL>>
Date:	Tue Apr 8 17:42:17 2014 -0300

    Fixed the way that pprof packed profile data in BE.

    pprof was writing profile data in a way that only works for
    little-endian
    files, this patch verifies if the system is big-endian and writes
    packed
    data correctly.

M	src/pprof

commit a1ae66ef110bd87ff97903e86fd84c745db24646
Author: Raphael Moreira Zinsly <<EMAIL>>
Date:	Tue Apr 8 17:37:29 2014 -0300

    Fixed the use of addr2line to discover the separator symbol.

    In systems where addr2line has a version greater than 2.22 pprof fails
    in discover the separator symbol (_fini). This patch identifies if
    addr2line can find the symbol, otherwise pprof uses objdump to recover
    a address that addr2line's newer versions can recognize as the
    separator
    function.

M	src/pprof

commit 8b2e5ee831760a94bc407dc255a522eda242b04f
Author: Aliaksey Kandratsenka <<EMAIL>>
Date:	Tue Apr 1 22:29:11 2014 -0700

    issue-614: use tc_memalign in ReallocAfterMemalloc test

    Because some OSes lack plain memalign. And we really need to test our
    implementation which is always available via tc_malloc.

M	configure.ac
M	src/tests/debugallocation_test.cc

commit 0399af1019240e2d9127a588ddc8e31ff4656df0
Author: Aliaksey Kandratsenka <<EMAIL>>
Date:	Tue Apr 1 21:30:27 2014 -0700

    added tc_malloc_skip_new_handler

    This is port of corresponding chromium change at:
    https://codereview.chromium.org/55333002/

    Basic idea is that sometimes apps that use tc_set_new_mode in order to
    have C++ out-of-memory handler catch OOMs in malloc, need to invoke
    usual malloc that returns 0 on OOM.

    That new API is exactly for that. It'll always return NULL on OOM even
    if tc_new_mode is set to true.

M	src/debugallocation.cc
M	src/gperftools/tcmalloc.h.in
M	src/tcmalloc.cc
M	src/tests/tcmalloc_unittest.cc
M	src/windows/gperftools/tcmalloc.h
M	src/windows/gperftools/tcmalloc.h.in

commit d77317247e1c8de1ea4e0419318b26f636e64431
Author: Aliaksey Kandratsenka <<EMAIL>>
Date:	Sat Mar 1 12:13:02 2014 -0800

    issue deprecation warning on use of google/ headers

M	src/google/heap-checker.h
M	src/google/heap-profiler.h
M	src/google/malloc_extension.h
M	src/google/malloc_extension_c.h
M	src/google/malloc_hook.h
M	src/google/malloc_hook_c.h
M	src/google/profiler.h
M	src/google/stacktrace.h
M	src/google/tcmalloc.h

commit e7297f0c14b0c09ec52707ed7477c7aee43a1882
Author: Aliaksey Kandratsenka <<EMAIL>>
Date:	Sat Mar 15 12:44:11 2014 -0700

    speed up MallocExtension::instance()

    It was reported that pthread_once is expensive, especially on ppc.

    In new implementation in hot path instead of doing potentially
    expensive atomic read with barrier, we do just plain read.

    It's slightly less robust than older implementation, but it should be
    faster.

    New code is making assumption that programs do not spawn threads
    before main() is called. And therefore all variables & modules are
    initialized before threads are created. Which looks like pretty safe
    assumption. With that assumption, doing plain read is safe, because
    current_instance is initialized as part of module init and therefore
    before threads are spawned.

    This patch is based on feedback of Adhemerval Zanella.

M	src/malloc_extension.cc

commit df227794361f9725eca7420d95e65ab997c79716
Author: Adhemerval Zanella <<EMAIL>>
Date:	Sun Feb 2 19:32:37 2014 -0200

    Fix getpc_test for PPC64v2 LE

    This patch fix the PPC64 guard to get the function address for
    PPC64v2.
    It removes the use of an indirection (to get the ODP text address),
    since the PPCv2 does not have function descriptors.

M	src/tests/getpc_test.cc

commit e3deb42d5dcdeeb8a340d03f207f2488ef3fb2eb
Author: Aliaksey Kandratsenka <<EMAIL>>
Date:	Sat Mar 29 13:28:01 2014 -0700

    issue-613: remove friend declaration from HeapLeakChecker

    This applies <NAME_EMAIL>:

    heap-checker.h contains the following friend declaration of main:
    friend int main(int, char**).

    C99 allows another declaration of main, i.e. int main(int, char**,
    char**), and if code uses it and includes the heap-checker header,
    this might result in a conflict, e.g.

    error: declaration of C function 'int main(int, char**, char**)'
    conflicts with
     int main(int argc, char* argv[], char** envp)

    Actually the comment above the friend declaration of main() mentions
    that this is required to get the unittest working and for other
    internal usage, but I'm not completely sure if this is true as long as
    I'm able to build and run the unittest removing the declaration.

M	src/gperftools/heap-checker.h

commit 1a28754656bd6dfc1297d62a4c5811b0a5f03180
Author: Aliaksey Kandratsenka <<EMAIL>>
Date:	Sat Mar 29 13:23:00 2014 -0700

    issue-612: added missing include for std::min

    Otherwise Visual Studio 2013 rightfully complains

M	src/windows/system-alloc.cc

commit fe566314267fc3f67c5068d0afe004b1733ff19a
Author: Aliaksey Kandratsenka <<EMAIL>>
Date:	Sat Mar 1 12:38:08 2014 -0800

    unbreak building with libunwind

    Caused by premature merging of previous patch.

    When we're searching for backtrace in libexecinfo and don't find it,
    we should not reset UNWIND_LIBS to empty value.

    Correct fix is to first search for backtrace in libunwind and then to
    search for it in libexecinfo.

M	configure.ac

commit 91179961490a601f2c611889b3075e995fa5437e
Author: Thomas Klausner <<EMAIL>>
Date:	Tue Feb 25 21:41:28 2014 +0100

    Look for backtrace() in libexecinfo as well.

    Signed-off-by: Aliaksey Kandratsenka <<EMAIL>>

M	configure.ac

commit fd3379a213b2a99a72a7544b6208056daf15d0d2
Author: Thomas Klausner <<EMAIL>>
Date:	Tue Feb 25 21:41:07 2014 +0100

    Fix unportable test(1) construct.

    Signed-off-by: Aliaksey Kandratsenka <<EMAIL>>

M	configure.ac

commit a7223c2a14431ffd783117c55f84a2c84423d313
Author: Thomas Klausner <<EMAIL>>
Date:	Tue Feb 25 21:40:11 2014 +0100

    Test for memalign in configure.ac. Disable a test that uses memalign
    if it is not found.

    Signed-off-by: Aliaksey Kandratsenka <<EMAIL>>

M	configure.ac
M	src/tests/debugallocation_test.cc

commit bd9665ebbe1aca5e1ba993de4214e64ee8bbe43a
Author: Aliaksey Kandratsenka <<EMAIL>>
Date:	Sat Feb 22 13:46:42 2014 -0800

    issue-489: added tcmalloc test pass with chromium-style decommit

M	src/tests/tcmalloc_unittest.sh

commit 6a000d6dd5968ac29f8fc43f7dfc736338e11781
Author: Aliaksey Kandratsenka <<EMAIL>>
Date:	Sat Feb 22 13:46:11 2014 -0800

    issue-489: added unit test for chromium-style decommitting

M	src/tests/tcmalloc_unittest.cc

commit eb2d69014cb3e163f2ab3ed676fbedf5c3a97caa
Author: Aliaksey Kandratsenka <<EMAIL>>
Date:	Sat Feb 22 13:10:08 2014 -0800

    issue-489: made tests pass on enabled chromium-style decommitting

M	src/tests/tcmalloc_unittest.cc

commit a92fc76f72318f7a46e91d9ef6dd24f2bcf44802
Author: Aliaksey Kandratsenka <<EMAIL>>
Date:	Sat Feb 22 13:09:05 2014 -0800

    issue-489: enable chromium-style decommitting on env variable

    TCMALLOC_AGGRESSIVE_DECOMMIT=t now enables aggressive decommitting by
    default.

M	src/static_vars.cc

commit c7ce50cd04ea08bd20d4ea4b2924e6a4451d2565
Author: Aliaksey Kandratsenka <<EMAIL>>
Date:	Sat Nov 9 17:35:24 2013 -0800

    issue-489: implemented API to set chromium-style de-committing

    Chrome has code to decommit (release back to OS) every span that's
    released. I don't want to make it default, but indeed some
    applications may want to enable this mode.

    The code itself is taken from 2-way-merging of code from Chromium
    fork.

M	src/page_heap.cc
M	src/page_heap.h
M	src/tcmalloc.cc

commit 1d707cd4a3dfe6f238a530f945291acfd5995042
Author: Aliaksey Kandratsenka <<EMAIL>>
Date:	Sat Nov 9 17:19:34 2013 -0800

    issue-489: fixed warning

    Computing certain values just for ASSERT raises just warning from
    compiler because if NDEBUG is set those are dead code.

M	src/page_heap.cc

commit 91bffcbad60d84beebe8b69a1db6c85c10fc04bf
Author: Aliaksey Kandratsenka <<EMAIL>>
Date:	Sat Nov 9 16:22:35 2013 -0800

    issue-489: ported chromium windows decommitting code

    I tried to do it cleanly with merges but chromium code has so many
    relevant commits (with frequent reverts) that makes it near
    impossible.

    Simpler 2-way emerge-files worked in the end. I've removed chromium's
    aggressive 'always decommit' behavior which I want to make optional
    later.

    Majority of this work is the following commits (but there are more,
    particularly against port.cc):

    commit 9c92338c5f8770c440799d24387c3733fd6d826b
    Author: <EMAIL>
    <<EMAIL>@0039d316-1c4b-4281-b951-d872f2087c98>
    Date:   Tue Oct 6 18:33:31 2009 +0000

	Tracks the amount of committed vs uncommitted memory in tcmalloc's
	page heap's freelists

	Keeps track of the number of reserved but not committed pages in
	the freelist and uses that to calculate a waste metric, which is
	the ratio of committed pages vs pages used by the application.
	This is exposed in the GetStats() call (which is used for
	about:tcmalloc) and through GetNumericalProperty() in Malloc

	BUG=none
	TEST=open about:tcmalloc and monitor 'WASTE' columns while using
	the browser

	Review URL: http://codereview.chromium.org/251065

	git-svn-id: svn://svn.chromium.org/chrome/trunk/src@28133
	0039d316-1c4b-4281-b951-d872f2087c98

    commit aef4f1be3eec2059a7c6e2c106050a5f3d6ccf12
    Author: <EMAIL>
    <<EMAIL>@0039d316-1c4b-4281-b951-d872f2087c98>
    Date:   Mon Oct 5 17:58:51 2009 +0000

	Revert further back to MBelshe's baseline forking TCMalloc

	This changes to decommitting in all paths through the
	page_heap delete method (which adds spans to the free lists).

	r=mbelshe,jamesr
	Review URL: http://codereview.chromium.org/255067

	git-svn-id: svn://svn.chromium.org/chrome/trunk/src@28006
	0039d316-1c4b-4281-b951-d872f2087c98

    commit e94afbb913b95f512cb8745a2729c73f82b15ae7
    Author: <EMAIL>
    <<EMAIL>@0039d316-1c4b-4281-b951-d872f2087c98>
    Date:   Thu Oct 1 00:25:41 2009 +0000

	Rollback Scavenge implemetation and rely on existing functionality
	to free

	This is a landing of a patch provided by antonm.  See:
	http://codereview.chromium.org/235022

	Also included change to browser_about_handler.cc to fix build,
	and I set
	TCMALLOC_RELEASE_RATE to 1.0 on line 40 of page_heap.cc (I
	think this
	was an inadvertent rollback element).

	r=antonm
	Review URL: http://codereview.chromium.org/257009

	git-svn-id: svn://svn.chromium.org/chrome/trunk/src@27692
	0039d316-1c4b-4281-b951-d872f2087c98

    commit c585892d2c42a47c95d06a684a6685156c545403
    Author: <EMAIL>
    <<EMAIL>@0039d316-1c4b-4281-b951-d872f2087c98>
    Date:   Wed Sep 2 17:33:23 2009 +0000

	Landing for Anton Muhin's tcmalloc patch:
	http://codereview.chromium.org/180021/show

	Restore decommitting in IncrementalScavenge and draft Scavenge
	method to
	be invoked periodically
	to reduce amount of committed pages.

	BUG=none
	TEST=none

	Review URL: http://codereview.chromium.org/187008

	git-svn-id: svn://svn.chromium.org/chrome/trunk/src@25188
	0039d316-1c4b-4281-b951-d872f2087c98

    commit 14239acc00731e94736ac62e80fc6b17c31ea131
    Author: <EMAIL>
    <<EMAIL>@0039d316-1c4b-4281-b951-d872f2087c98>
    Date:   Wed Aug 12 02:17:14 2009 +0000

	Major changes to the Chrome allocator.

	Changes include:
	* Fix tcmalloc to release memory.  Implements the
	TCMalloc_SystemCommit()
	  mechanism so that tcmalloc can implement SystemRelease()
	  and later
	  reuse that memory.
	* Enable dynamic switching of allocators based on an environment
	variable.
	  Users can now switch between tcmalloc, jemalloc, the default
	  windows
	  heap, and the windows low-fragmentation heap.
	* Implements set_new_mode() across all allocators so that we
	can be sure
	  that out-of-memory conditions are handled safely.

	BUG=18345
	TEST=none; plan to get all unit tests running through these
	allocators.

	Review URL: http://codereview.chromium.org/165275

	git-svn-id: svn://svn.chromium.org/chrome/trunk/src@23140
	0039d316-1c4b-4281-b951-d872f2087c98

M	src/page_heap.cc
M	src/page_heap.h
M	src/system-alloc.cc
M	src/system-alloc.h
M	src/windows/system-alloc.cc

commit 7be2edfe7f09e7e8c123e958742815784a718880
Author: Aliaksey Kandratsenka <<EMAIL>>
Date:	Sat Feb 22 12:14:11 2014 -0800

    issue-525: only warn if --enable-frame-pointers are not given

    This fixes issue when frame pointers warning is given even if frame
    pointers are enabled

M	configure.ac

commit 7e24b6ca2aa2c15a24504c02fad09f47e1ece8b5
Author: Aliaksey Kandratsenka <<EMAIL>>
Date:	Sun Feb 16 17:04:26 2014 -0800

    added debugallocation check for offset_ corruption

    It was previously possible (although unlikely) for damaged offset_
    field
    to lead FromRawPointer implementation into different MallocBlock.

    As is usual with any damage, it's best to catch errors at earliest
    possible time.

M	src/debugallocation.cc

commit 6dcd73f1eb5ce2cc44ab918c53cd42c472f44c52
Author: Aliaksey Kandratsenka <<EMAIL>>
Date:	Sun Feb 16 16:59:43 2014 -0800

    avoid crash in DebugMallocImplementation::GetOwnership

    It was possible that if GetOwnership is passed pointer to memory not
    owned by tcmalloc, it would crash. Or incorrectly return
    owned. I.e. due to indirection in FromRawPointer.

    New implementation prevents that, but introduces different bug
    instead. New implementation incorrectly returns "not owned" for
    memalign chunks with big alignment. But in can be argued that passing
    pointer returned from different memalign implementation did not work
    previously too.

M	src/debugallocation.cc

commit 33280ffb71fc0e4eb75e455d53824c344d011e35
Author: Aliaksey Kandratsenka <<EMAIL>>
Date:	Sun Feb 16 16:25:14 2014 -0800

    removed unused "using" in malloc_extension_test.cc

M	src/tests/malloc_extension_test.cc

commit 066e524d6e33e9e3364bb6819f98a02b347c14ef
Author: Aliaksey Kandratsenka <<EMAIL>>
Date:	Sun Feb 16 14:58:35 2014 -0800

    eliminated useless BASE_XXX defines in debugallocation.cc

    And closed TODO entry for that.

M	src/debugallocation.cc

commit a2375a1f360c0451ec2a2b852ea26a71fb731727
Author: Aliaksey Kandratsenka <<EMAIL>>
Date:	Sun Feb 16 14:49:57 2014 -0800

    issue-464: correctly handle realloc after memalign in debugalloc

    debug memalign is creating special header block to allow us to find
    real allocated block. And previous implementation of data copying
    wasn't taking that into account and was copying that "alignment
    header" into newly allocated block.

M	src/debugallocation.cc
M	src/tests/debugallocation_test.cc

commit d31f522f0e1b0e87ee3d5941e331ba745a0f5c23
Author: Riku Voipio <<EMAIL>>
Date:	Mon Feb 3 16:31:32 2014 +0200

    Add aarch64 defines

    With atomic operations and system call support in place, enable
    with __aarch64__ defines Aarch64 support in other files around
    the google-perftools header files. After these, google-perftools
    testsuite (make check) results:

    8 of 46 tests failed.

    FAIL: sampling_test.sh
    FAIL: heap-profiler_unittest.sh
    FAIL: heap-checker_unittest.sh
    FAIL: heap-checker-death_unittest.sh
    FAIL: sampling_debug_test.sh
    FAIL: heap-profiler_debug_unittest.sh
    FAIL: heap-checker_debug_unittest.sh
    FAIL: profiler_unittest.sh

    While it indicates that there is still work to do, This is still
    better than the result I get on ARMv7:

    12 of 46 tests failed.

M	src/base/basictypes.h
M	src/base/cycleclock.h
M	src/base/linuxthreads.h
M	src/malloc_hook_mmap_linux.h

commit 15b5e7a35c83ce5d38fa523f2c291a2ac30bb8ed
Author: Riku Voipio <<EMAIL>>
Date:	Tue Feb 4 11:09:47 2014 +0200

    linux_syscall_support.h: add aarch64 support

    Aarch64 support for linux_syscall_support.h. Since Aarch64 is a brand
    new architecture, none of the legacy system calls are neccesarily
    available. Thus some changes were neccesary affect other architectures
    as well:

    1) use getdents64 where available and else getdents (for ppc64)

    2) other legacy system calls, pipe, waitpid and open replaced
    by pipe2,
       wait4 and openat where available.

    3) use fstatat if stat is not available.

    The aarch64 system call interface follows the Aarch64 calling
    convention
    (regs x0-x5 for arguments and x8 system call number - return in x0).
    Clone implementation is adapted from glibc.

    v2: step back in getdents removal due to ppc64

M	src/base/linux_syscall_support.h
M	src/base/linuxthreads.cc

commit b5e584dfdc22328f713488311707f502aa77ff5f
Author: Aliaksey Kandratsenka <<EMAIL>>
Date:	Sun Feb 16 19:41:37 2014 -0800

    issue-525: warn user on lack of libunwind but don't fail

    Because we now have access to other backtrace capturing method(s) at
    runtime.

M	configure.ac

commit 90ba15d1f2f6704af96f62ce1e8c5f214697bab1
Author: Aliaksey Kandratsenka <<EMAIL>>
Date:	Sat Feb 8 15:30:36 2014 -0800

    issue-604: implement runtime-selectable stacktrace capturing

    We're now building all supported stacktrace capturing methods. And
    there's now a way to select at runtime which method is used.

M	Makefile.am
M	configure.ac
M	src/stacktrace.cc
M	src/stacktrace_arm-inl.h
D	src/stacktrace_config.h
M	src/stacktrace_generic-inl.h
C055	src/stacktrace.cc	src/stacktrace_impl_setup-inl.h
M	src/stacktrace_instrument-inl.h
M	src/stacktrace_libunwind-inl.h
M	src/stacktrace_powerpc-inl.h
M	src/stacktrace_win32-inl.h
M	src/stacktrace_x86-inl.h

commit 33f6781d64af88ea23698a084188d8c2ab94ecb1
Author: Aliaksey Kandratsenka <<EMAIL>>
Date:	Sun Feb 16 18:47:04 2014 -0800

    issue-605: avoid compilation errors if pthread_key_t is pointer

    Which seems to be the case on later cygwin

M	src/maybe_threads.cc

commit 100f310088aa30d347cd5d07bbe9d9f80cf2c2d0
Author: Aliaksey Kandratsenka <<EMAIL>>
Date:	Sun Feb 16 18:27:14 2014 -0800

    unbreak make dist

M	Makefile.am

commit a0ed9ace534372fbeee9bfefd6e26b922f0a1835
Author: Wang YanQing <<EMAIL>>
Date:	Tue Feb 11 23:20:31 2014 +0800

    debugallocation: fix bus error on mipsel-linux platform when enable
    use_malloc_page_fence

    Fix below "BUS ERROR" issue:

    a0 hold start address of memory block allocated by DebugAllocate
    in debugallocation.cc

    gdb) info registers
	      zero	 at	  v0	   v1	    a0	     a1
	      a2       a3
     R0   00000000 10008700 772f62a0 00084d40 766dcfef 7fb5f420 00000000
     004b4dd8
		t0	 t1	  t2	   t3	    t4	     t5
		t6	 t7
     R8   7713c1a0 7712dbc0 ffffffff 777bc000 f0000000 00000001 00000000
     00403d10
		s0	 s1	  s2	   s3	    s4	     s5
		s6	 s7
     R16  7fb5ff1c 00401b9c 77050020 7fb5fb18 00000000 004cb008 004ca748
     ffffffff
		t8	 t9	  k0	   k1	    gp	     sp
		s8	 ra
     R24  0000002f 771adcd4 00000000 00000000 771f4140 7fb5f408 7fb5f430
     771add6c
		sr	 lo	  hi	  bad	 cause	     pc
	  00008713 0000e9fe 00000334 766dcff7 00800010 771adcfc
	       fsr	fir
	  00000004 00000000

    (gdb) disassemble
    Dump of assembler code for function _ZNSs4_Rep10_M_disposeERKSaIcE:
       0x771adcd4 <+0>:     lui     gp,0x4
       0x771adcd8 <+4>:     addiu   gp,gp,25708
       0x771adcdc <+8>:     addu    gp,gp,t9
       0x771adce0 <+12>:    lw	    v0,-28696(gp)
       0x771adce4 <+16>:    beq     a0,v0,0x771add38
       <_ZNSs4_Rep10_M_disposeERKSaIcE+100>
       0x771adce8 <+20>:    nop
       0x771adcec <+24>:    lw	    v0,-30356(gp)
       0x771adcf0 <+28>:    beqzl   v0,0x771add1c
       <_ZNSs4_Rep10_M_disposeERKSaIcE+72>
       0x771adcf4 <+32>:    lw	    v0,8(a0)
       0x771adcf8 <+36>:    sync
    => 0x771adcfc <+40>:    ll	    v0,8(a0)
       0x771add00 <+44>:    addiu   at,v0,-1
       0x771add04 <+48>:    sc	    at,8(a0)
       0x771add08 <+52>:    beqz    at,0x771adcfc
       <_ZNSs4_Rep10_M_disposeERKSaIcE+40>
       0x771add0c <+56>:    nop
       0x771add10 <+60>:    sync
       0x771add14 <+64>:    b	    0x771add24
       <_ZNSs4_Rep10_M_disposeERKSaIcE+80>
       0x771add18 <+68>:    nop
       0x771add1c <+72>:    addiu   v1,v0,-1
       0x771add20 <+76>:    sw	    v1,8(a0)
       0x771add24 <+80>:    bgtz    v0,0x771add38
       <_ZNSs4_Rep10_M_disposeERKSaIcE+100>
       0x771add28 <+84>:    nop
       0x771add2c <+88>:    lw	    t9,-27072(gp)
       0x771add30 <+92>:    jr	    t9
       0x771add34 <+96>:    nop
       0x771add38 <+100>:   jr	    ra
       0x771add3c <+104>:   nop
    End of assembler dump.

    ll instruction manual:
    Load Linked:
    Loads the destination register with the contents of the word
    that is at the memory location. This instruction implicity performs
    a SYNC operation; all loads and stores to shared memory fetched prior
    to the ll must access memory before the ll, and loads and stores to
    shared memory fetched subsequent to the ll must access memory
    after ll.
    Load Linked and Store Conditional can be use to automatically update
    memory locations. *This instruction is not valid in the mips1
    architectures.
    The machine signals an address exception when the effective address
    is not
    divisible by four.

    Signed-off-by: Wang YanQing <<EMAIL>>
    Signed-off-by: Aliaksey Kandratsenka <<EMAIL>>
    [<EMAIL>: removed addition of unused #include]

M	src/debugallocation.cc

commit 38bfc7a1c2f9ba718bcaa9b82fdcd9c429c8f85f
Author: Aliaksey Kandratsenka <<EMAIL>>
Date:	Sat Feb 8 14:10:11 2014 -0800

    removed irrelevant comment

M	src/base/atomicops.h

commit d03c467a3446088b229f0106d9f47fab6d7c52a4
Author: Aliaksey Kandratsenka <<EMAIL>>
Date:	Sat Feb 8 13:35:16 2014 -0800

    allow asking for gcc atomics on all platforms

    I.e. by doing ./configure CPPFLAGS=-DTCMALLOC_PREFER_GCC_ATOMICS

M	src/base/atomicops.h

commit 6de1f38b687e3974d67adad45217f35a2c376049
Author: Aliaksey Kandratsenka <<EMAIL>>
Date:	Sat Feb 8 13:43:04 2014 -0800

    chmod -x configure.ac

    Because configure.ac is not really executable. And because it
    interferes with tab completion of configure.

M	configure.ac

commit e8fe990fa06e337a07059c55adc6ed2a0888de95
Author: Riku Voipio <<EMAIL>>
Date:	Wed Jan 29 10:54:29 2014 +0200

    implement atomics with gcc intrinsics

    Gcc after 4.7 provides atomic builtins[1]. Use these instead of adding
    yet-another-assembly port for Aarch64 (64-bit ARM). This patch enables
    succesfully building and running atomicops unittest on Aarch64.

    This patch enables using gcc builtins only when no assembly
    implementation is provided. But as a quick check, atomicops_unittest
    and rest of testsuite passes with atomicops-internals-gcc also
    ARMv7 and X86_64 if the ifdef in atomicops is adjusted to prefer
    the generic implementation.

    [1] http://gcc.gnu.org/onlinedocs/gcc/_005f_005fatomic-Builtins.html

A	src/base/atomicops-internals-gcc.h
M	src/base/atomicops.h

commit fa4b1c401da1ac381d4d72172825231b3d5518d9
Author: Aliaksey Kandratsenka <<EMAIL>>
Date:	Sun Jan 19 22:37:44 2014 -0800

    issue-599: fixing FreeBSD issue with sbrk

    Applied patch by yurivict.

    It was wrong assembly specifically for FreeBSD in sbrk overriding
    code.

M	src/malloc_hook_mmap_freebsd.h

commit 71a239e559c9ea2300ad0511f8de7077db5369c3
Author: Aliaksey Kandratsenka <<EMAIL>>
Date:	Sun Jan 19 12:30:53 2014 -0800

    check debug_malloc_implementation_space via COMPILE_ASSERT

    Because we can and because compile-time is always better.

M	src/debugallocation.cc

commit 54568e32fc2321e0adef15fb1eab4e3a7f8ce5b0
Author: Aliaksey Kandratsenka <<EMAIL>>
Date:	Sat Sep 21 18:14:00 2013 -0700

    issue-565: don't pollute global namespace with thread lister API

    Instead those functions that are original taken from google's "base"
    code now have prefix TCMalloc_. So that they don't conflict with other
    google's libraries having same functions.

M	src/base/linuxthreads.cc
M	src/base/thread_lister.c
M	src/base/thread_lister.h
M	src/gperftools/heap-checker.h
M	src/heap-checker.cc
M	src/memory_region_map.h

commit 64bc1baa1f4723d73ba40cd730b72896bd45a810
Author: Aliaksey Kandratsenka <<EMAIL>>
Date:	Sat May 18 17:11:58 2013 -0700

    issue-{66,547}: use signal's ucontext when unwinding backtrace

    In issue-66 (and readme) it is pointed out that sometimes there are
    some issues grabbing backtrace across signal handler boundary.

    This code attempts to fix it by grabbing backtrace from signal's
    ucontext which clearly does not include signal handler boundary.

    We're using "feature" of libunwind that for some important platforms
    libunwind's context is same as libc's ucontext_t which is given to us
    as part of calling signal handler.

M	src/stacktrace_libunwind-inl.h

commit 185bf3fcc36f8cb3839abdfe652f615bfb5306d1
Author: Aliaksey Kandratsenka <<EMAIL>>
Date:	Sat Jan 11 12:46:02 2014 -0800

    issue-581: avoid destructing DebugMallocImplementation

    Because otherwise destructor might be invoked well before other places
    that might touch malloc extension instance.

    We're using placement new to initialize it and pass pointer to
    MallocExtension::Register. Which ensures that destructor for it is
    never run.

    Based on idea suggested by Andrew C. Morrow.

M	src/debugallocation.cc

commit 48a0d131c1aa088c6075e9c4676ee430f81d8600
Author: Aliaksey Kandratsenka <<EMAIL>>
Date:	Sat Jan 18 13:27:41 2014 -0800

    issue-548: pass -fno-builtin to compiler for unittests

    Because clang doesn't understand -fno-builtin-malloc and friends. And
    otherwise new/delete pairs get optimized away causing our tests that
    expect hooks to be called to fail.

M	Makefile.am

commit e98371540d63dde53ce4b7c772d78c1da6b59ea8
Author: Aliaksey Kandratsenka <<EMAIL>>
Date:	Sat Jan 11 16:28:15 2014 -0800

    eliminated gcc warning on __thread configure snippet

    gcc complained about lack of matching ' in code that force-fails
    __thread detection on mingw

M	configure.ac

commit 60b12171bc73117c0108b847bb310af095cd2778
Author: xiaoyur347 <<EMAIL>>
Date:	Sat Jan 11 11:39:53 2014 +0800

    fix GCC version detect for platforms other than X86/X64

    [<EMAIL>: commented why we're disabling __thread not just for x86]

    Signed-off-by: Aliaksey Kandratsenka <<EMAIL>>

M	configure.ac

commit 764d304222f0c3057ab99babd06246016cbfa505
Author: Aliaksey Kandratsenka <<EMAIL>>
Date:	Sun Jan 5 12:49:23 2014 -0800

    don't re-define strtoq for VS2013

    Which is part of previous change that wasn't correctly applied.

M	src/windows/port.h

commit 1fc768864d506466b45f3f38474912bcb69bb772
Author: Aliaksey Kandratsenka <<EMAIL>>
Date:	Sat Jan 4 18:28:36 2014 -0800

    fix compilation under VS 2013

    This is essentially a copy of corresponding chromium change from:
    https://codereview.chromium.org/27017003

M	src/windows/port.cc
M	src/windows/port.h

commit 4c274b9e20132230e62117ff583ebadd83081d90
Author: Aliaksey Kandratsenka <<EMAIL>>
Date:	Sat Jan 4 18:28:36 2014 -0800

    issue-592: handle recent mingw with C++11 threads

    Somehow it's c++ headers (like string) define pthread symbols without
    even us asking for. That breaks old assumption that pthread symbols
    are not available on windows.

    In order to fix that we detect this condition in configure.ac and
    avoid defining windows versions of pthread symbols.

M	Makefile.am
M	configure.ac
M	src/windows/mingw.h
M	src/windows/port.h

commit 1458ee2239e0791567e69112931dc17eb0456cf8
Author: Aliaksey Kandratsenka <<EMAIL>>
Date:	Sat Jan 4 13:54:24 2014 -0800

    issue-596: removed unused AtomicIncrement operation

    There's no need for us to attempt to maintain Google's atomic ops code
    in era of C++11.

M	src/base/atomicops-internals-arm-generic.h
M	src/base/atomicops-internals-arm-v6plus.h
M	src/base/atomicops-internals-linuxppc.h
M	src/base/atomicops-internals-macosx.h
M	src/base/atomicops-internals-mips.h
M	src/base/atomicops-internals-windows.h
M	src/base/atomicops-internals-x86.h
M	src/base/atomicops.h
M	src/tests/atomicops_unittest.cc

commit 6630b24e27c6a62727fe73aaae21dcc7364b8fee
Author: Aliaksey Kandratsenka <<EMAIL>>
Date:	Sat Jan 4 13:07:35 2014 -0800

    Removed unused AtomicPtr::CompareAndSwap

M	src/malloc_hook-inl.h
M	src/malloc_hook.cc

commit a15115271cc475509b17bf7fecbe1ac4966baf2e
Author: xiaoyur347 <<EMAIL>>
Date:	Fri Dec 20 09:41:08 2013 +0800

    add "-finstrument-functions" support for MIPS uclibc.
    should configure with CXXFLAGS="-finstrument-functions"

M	src/stacktrace_config.h
A	src/stacktrace_instrument-inl.h

commit 7c4888515ed93347d4793fc066cd6048e519a197
Author: xiaoyur347 <<EMAIL>>
Date:	Fri Dec 20 09:02:49 2013 +0800

    add uclibc support
    * some variables defined with "char *" should be modified to
    "const char*"
    * For uclibc, glibc's "void malloc_stats(void)" should be "void
    malloc_stats(FILE *)", is commented now.
    * For uclibc, __sbrk is with attribute "hidden", so we use mmap
    allocator for uclibc.

M	Makefile.am
M	src/heap-checker.cc
M	src/heap-profiler.cc
M	src/libc_override_gcc_and_weak.h
M	src/malloc_hook_mmap_linux.h
M	src/memory_region_map.cc
M	src/symbolize.cc
M	src/system-alloc.cc

commit 7bd193bca97d93b43ff6c824bc9f39227329312f
Author: Aliaksey Kandratsenka <<EMAIL>>
Date:	Sat Dec 14 12:03:02 2013 -0800

    issue-586: detect main executable even if PIE is active

    Previous logic of detecting main program addresses is to assume that
    main executable is at least addressess. With PIE (active by default on
    Ubuntus) it doesn't work.

    In order to deal with that, we're attempting to find main executable
    mapping in /proc/[pid]/maps. And old logic is preserved too just in
    case.

M	src/pprof

commit f8a2163b5131050765ea877e2573f4930e41f630
Author: Aliaksey Kandratsenka <<EMAIL>>
Date:	Fri Dec 6 12:23:48 2013 -0800

    Added AM_MAINTAINER_MODE to disable Makefile rebuild rules

    Some people might want to check-in unpacked result on make dist into
    git. But because git doesn't preserve timestamps it would cause those
    automatic "auto-retool" rules to trigger. Sometimes even causing build
    breakage if system's autotools version don't match autotools version
    used for make dist.

    Easiest way around this problem is to simply disable those unnecessary
    "maintainer" rebuild rules. Especially given that source is always
    freely available via git and therefore there should be no reason to
    regenerate any of autotools products in 'make dist'-produced sources.

M	configure.ac

commit 925bbaea76b91bd307634908cfd6902f99804544
Author: Aliaksey Kandratsenka <<EMAIL>>
Date:	Sat Nov 16 14:01:38 2013 -0800

    actually check result of CheckAddressBits

    Previously call to CheckAddressBits was made but nothing was done to
    it's result.

    I've also make sure that actual size is used in checks and in bumping
    up of TCMalloc_SystemTaken.

M	src/system-alloc.cc

commit f216317a879e972ceafe77e61b2d66fd5f29922e
Author: Aliaksey Kandratsenka <<EMAIL>>
Date:	Sat Nov 16 15:05:45 2013 -0800

    use AC_PROG_LIBTOOL to summon libtool

    So that older autotools of rhel 5 can be used

M	configure.ac

commit d4f4c5a3104e30b14b1090241fb4d0fac6a0e357
Author: Aliaksey Kandratsenka <<EMAIL>>
Date:	Sat Nov 16 14:00:19 2013 -0800

    assert that ClassSize(0) is 0 instead >=0

    Because it's return value being size_t cannot be negative
    anyways. This fixes clang warning

M	src/common.cc

commit 946203d60e50488a0b9e0fe003c13662873fa17d
Author: Aliaksey Kandratsenka <<EMAIL>>
Date:	Sat Nov 16 13:35:59 2013 -0800

    assert key size in way that is clearer to gcc

    Both new and old asserts are checking same condition, however new
    assert helps gcc see that out of bounds access is not possible in
    root_ array.

M	src/pagemap.h

commit bf2d7bd3f8c1fb6a0843e55e652e37f4ce0fae3d
Author: Aliaksey Kandratsenka <<EMAIL>>
Date:	Sat Nov 16 13:31:34 2013 -0800

    fixed gcc warning

    We've recently changed old_signal_handler to by integer, so comparing
    it with NULL is not good idea.

M	src/heap-profiler.cc

commit dd5f979c5e6e7e3127835a659a5af89ac21597d5
Author: Aliaksey Kandratsenka <<EMAIL>>
Date:	Sat Nov 16 13:31:08 2013 -0800

    fixed -Wreorder warning in HeapProfileTable constructor

M	src/heap-profile-table.cc

commit e4ea98f147a7602979f70be022de068b04e68060
Author: Aliaksey Kandratsenka <<EMAIL>>
Date:	Sat Nov 9 14:19:16 2013 -0800

    issue-585: fixed use of TCMALLOC_MAX_TOTAL_THREAD_CACHE_BYTES

    In order to apply that, we're now doing explicit EnvToInt64 call as
    part of initializing thread cache module.

M	src/thread_cache.cc

commit e0102230ec7e8304155798bf7f03d6abcd5991ee
Author: Aliaksey Kandratsenka <<EMAIL>>
Date:	Sat Nov 16 12:03:35 2013 -0800

    issue-588: Fix profiler_unittest.cc fork()

    As suggested by Hannes Weisbach.

    Call heap-profiler_unittest with the arguments 1 -2 (one iteration, 2
    fork()ed children).

    Instead of running the test, the program crashes with a std::bad_alloc
    exception.	This is caused by unconditionally passing the
    number-of-threads-argument (0 or positive for threads, negative for
    fork()s) in RunManyThreads(), thus allocating an array of pthread_t of
    size -2.  Depending on the sign of the thread number argument either
    RunManyThreads or fork() should be called.

M	src/tests/profiler_unittest.cc

commit 2bf83af65664a2badbaebdb722ad498e8b38548c
Author: Aliaksey Kandratsenka <<EMAIL>>
Date:	Sat Nov 16 11:35:32 2013 -0800

    issue-587: fix typos in unit test scripts

    As proposed by Hannes Weisbach.

    The argument will be garbled because of a misplaced brace, for example
    (heap-checker_unittest.sh):

    HEAP_CHECKER="${1:-$BINDIR}/heap-checker_unittest"
    which should be:
    HEAP_CHECKER="${1:-$BINDIR/heap-checker_unittest}"

    This unit test is used to check the binaries heap-checker_unittest and
    heap-checker_debug_unittest.  With the typo, the executable
    heap-checker_debug_unittest is never actually run.

M	src/tests/heap-checker-death_unittest.sh
M	src/tests/heap-checker_unittest.sh
M	src/tests/heap-profiler_unittest.sh
M	src/tests/tcmalloc_unittest.sh

commit b3b19269783cf1ed163bdb447cef9ca11b10851c
Author: Aliaksey Kandratsenka <<EMAIL>>
Date:	Sat Nov 9 12:28:55 2013 -0800

    issue-584: added license note to files without explicit license

    As suggested at corresponding chromium issue discussion it's seemingly
    sufficient to simply refer to project-wide LICENSE file.

M	src/tests/page_heap_test.cc
M	src/tests/profile-handler_unittest.cc
M	src/tests/raw_printer_test.cc
M	src/tests/stack_trace_table_test.cc
M	src/windows/config.h

commit 7be35fb0d844c73f5c5301f62074294df5acbc5c
Author: Joonsoo Kim <<EMAIL>>
Date:	Thu Oct 10 14:26:47 2013 +0900

    central_freelist: change fetch ordering

    When we fetch objects from the span for thread cache, we make
    reverse-ordered list against original list on the span and suppy
    this list
    to thread cache. This algorithm has trouble with newly created span.
    Newly created span has ascending ordered objects list. Since thread
    cache
    will get reverse-ordered list against it, user gets objects as
    descending order.

    Following example shows what occurs in this algorithm.

    new span: object list: 1 -> 2 -> 3 -> 4 -> 5 -> ...
    fetch N items: N -> N-1 -> N-2 -> ... -> 2 -> 1 -> NULL
    thread cache: N -> N-1 -> N-2 -> ... -> 2 -> 1 -> NULL

    user's 1st malloc: N
    user's 2nd malloc: N-1
    ...
    user's Nth malloc: 1

    In general, access memory with ascending order is better than
    descending
    order in terms of the performance. So this patch fix this situation.

    I run below program to measure performance effect.

	    #define MALLOC_SIZE (512)
	    #define CACHE_SIZE (64)
	    #define TOUCH_SIZE (512 / CACHE_SIZE)

	    array = malloc(sizeof(void *) * count);

	    for (i = 0; i < 1; i++) {
		    for (j = 0; j < count; j++) {
			    x = malloc(MALLOC_SIZE);
			    array[j] = x;
		    }
	    }

	    repeat = 10;
	    for (i = 0; i < repeat; i++) {
		    for (j = 0; j < count; j++) {
			    x = array[j];
			    for (k = 0; k < TOUCH_SIZE; k++) {
				    *(x + (k * CACHE_SIZE)) = '1';
			    }
		    }
	    }

    LD_PRELOAD=libtcmalloc_minimal.so perf stat -r 10 ./a.out 1000000

    **** Before ****
     Performance counter stats for './a.out 1000000' (10 runs):

	   2.715161299 seconds time elapsed
	   ( +-  0.07% )

    **** After ****
     Performance counter stats for './a.out 1000000' (10 runs):

	   2.259366428 seconds time elapsed
	   ( +-  0.08% )

M	src/central_freelist.cc

commit 7315b45c28564afdc2699beff934a3b45457edc6
Author: Joonsoo Kim <<EMAIL>>
Date:	Thu Oct 10 11:46:12 2013 +0900

    central_freelist: fetch objects as much as possible during each trial

    It is better to reduce function call if possible. If we try to fetch
    objects from one span as much as possible during each function call,
    number of function call would be reduced and this would help
    performance.

M	src/central_freelist.cc
M	src/central_freelist.h

commit cc002ea19363e1ebbd7f3e809d116ab81a6862cd
Author: Joonsoo Kim <<EMAIL>>
Date:	Thu Oct 10 10:06:33 2013 +0900

    skip unnecessary check during double-check SizeClass intergrity

    On initialization step, tcmalloc double-checks SizeClass integrity
    with
    all possible size values, 0 to kMaxSize. This causes tremendous
    overhead
    for short-lived applications.

    For example, consider following command.
    'find -exec grep something {} \;'

    Actual work of each grep is really small, but double-check requires
    more work. To reduce this overhead, it is best to remove double-check
    entirely. But we cannot be sure the integrity without double-checking,
    so alternative is needed.

    This patch doesn't remove double-check, instead, try to skip
    unnecessary
    check based on ClassIndex() implementation. This reduce much
    overhead and
    the code has same coverage as previous double-check. Following is
    the result of this patch.

    time LD_PRELOAD=libtcmalloc_minimal.so find ./ -exec grep "SOMETHING"
    {} \;

    * Before
    real    0m3.675s
    user    0m1.000s
    sys     0m0.640s

    * This patch
    real    0m2.833s
    user    0m0.056s
    sys     0m0.220s

    * Remove double-check entirely
    real    0m2.675s
    user    0m0.072s
    sys     0m0.184s

M	src/common.cc

commit 3e9a33e8c708ccf3ec91e3a3b14e924f5f79e4a6
Author: Aliaksey Kandratsenka <<EMAIL>>
Date:	Sat Oct 26 16:54:03 2013 -0700

    issue-583: include pthread.h into static_var.cc

    Because we're doing pthread_atfork.

    Fix suggested by user named drussel.

M	src/static_vars.cc

commit db0d5730ee059d72b895fbead5237f9cb5bbf98a
Author: Aliaksey Kandratsenka <<EMAIL>>
Date:	Sat Jun 22 13:48:11 2013 -0700

    issue-579: ensure order between memory region and libunwind locks

    I.e. to prevent possible deadlock when this locks are taked by
    different threads in different order.

    This particular problem was also reported as part of issue 66.

M	src/memory_region_map.cc

commit 42ddc8d42c82ba6f5137c26b4e7f752b1a022831
Author: Aliaksey Kandratsenka <<EMAIL>>
Date:	Sat Sep 21 19:03:52 2013 -0700

    added emacs -*- mode lines for google coding style

M	src/addressmap-inl.h
M	src/base/atomicops-internals-arm-generic.h
M	src/base/atomicops-internals-arm-v6plus.h
M	src/base/atomicops-internals-linuxppc.h
M	src/base/atomicops-internals-macosx.h
M	src/base/atomicops-internals-mips.h
M	src/base/atomicops-internals-windows.h
M	src/base/atomicops-internals-x86.cc
M	src/base/atomicops-internals-x86.h
M	src/base/atomicops.h
M	src/base/basictypes.h
M	src/base/commandlineflags.h
M	src/base/cycleclock.h
M	src/base/elf_mem_image.cc
M	src/base/elf_mem_image.h
M	src/base/elfcore.h
M	src/base/googleinit.h
M	src/base/linux_syscall_support.h
M	src/base/linuxthreads.cc
M	src/base/logging.cc
M	src/base/logging.h
M	src/base/low_level_alloc.cc
M	src/base/low_level_alloc.h
M	src/base/simple_mutex.h
M	src/base/spinlock.cc
M	src/base/spinlock.h
M	src/base/spinlock_internal.cc
M	src/base/spinlock_internal.h
M	src/base/spinlock_linux-inl.h
M	src/base/spinlock_posix-inl.h
M	src/base/spinlock_win32-inl.h
M	src/base/stl_allocator.h
M	src/base/synchronization_profiling.h
M	src/base/sysinfo.cc
M	src/base/sysinfo.h
M	src/base/thread_lister.h
M	src/central_freelist.cc
M	src/central_freelist.h
M	src/common.cc
M	src/common.h
M	src/config_for_unittests.h
M	src/debugallocation.cc
M	src/getpc.h
M	src/gperftools/heap-checker.h
M	src/gperftools/heap-profiler.h
M	src/gperftools/malloc_extension.h
M	src/gperftools/malloc_hook.h
M	src/gperftools/profiler.h
M	src/gperftools/stacktrace.h
M	src/gperftools/tcmalloc.h.in
M	src/heap-checker-bcad.cc
M	src/heap-checker.cc
M	src/heap-profile-stats.h
M	src/heap-profile-table.cc
M	src/heap-profile-table.h
M	src/heap-profiler.cc
M	src/internal_logging.cc
M	src/internal_logging.h
M	src/libc_override.h
M	src/libc_override_gcc_and_weak.h
M	src/libc_override_glibc.h
M	src/libc_override_osx.h
M	src/libc_override_redefine.h
M	src/linked_list.h
M	src/malloc_extension.cc
M	src/malloc_hook-inl.h
M	src/malloc_hook.cc
M	src/malloc_hook_mmap_freebsd.h
M	src/malloc_hook_mmap_linux.h
M	src/maybe_threads.cc
M	src/maybe_threads.h
M	src/memfs_malloc.cc
M	src/memory_region_map.cc
M	src/memory_region_map.h
M	src/packed-cache-inl.h
M	src/page_heap.cc
M	src/page_heap.h
M	src/page_heap_allocator.h
M	src/pagemap.h
M	src/profile-handler.cc
M	src/profile-handler.h
M	src/profiledata.cc
M	src/profiledata.h
M	src/profiler.cc
M	src/raw_printer.cc
M	src/raw_printer.h
M	src/sampler.cc
M	src/sampler.h
M	src/span.cc
M	src/span.h
M	src/stack_trace_table.cc
M	src/stack_trace_table.h
M	src/stacktrace.cc
M	src/stacktrace_arm-inl.h
M	src/stacktrace_config.h
M	src/stacktrace_generic-inl.h
M	src/stacktrace_libunwind-inl.h
M	src/stacktrace_powerpc-inl.h
M	src/stacktrace_win32-inl.h
M	src/stacktrace_x86-inl.h
M	src/static_vars.cc
M	src/static_vars.h
M	src/symbolize.cc
M	src/symbolize.h
M	src/system-alloc.cc
M	src/system-alloc.h
M	src/tcmalloc.cc
M	src/tcmalloc.h
M	src/tcmalloc_guard.h
M	src/tests/addressmap_unittest.cc
M	src/tests/atomicops_unittest.cc
M	src/tests/current_allocated_bytes_test.cc
M	src/tests/debugallocation_test.cc
M	src/tests/frag_unittest.cc
M	src/tests/getpc_test.cc
M	src/tests/heap-checker_unittest.cc
M	src/tests/heap-profiler_unittest.cc
M	src/tests/low_level_alloc_unittest.cc
M	src/tests/malloc_extension_c_test.c
M	src/tests/malloc_extension_test.cc
M	src/tests/malloc_hook_test.cc
M	src/tests/markidle_unittest.cc
M	src/tests/memalign_unittest.cc
M	src/tests/packed-cache_test.cc
M	src/tests/page_heap_test.cc
M	src/tests/pagemap_unittest.cc
M	src/tests/profile-handler_unittest.cc
M	src/tests/profiledata_unittest.cc
M	src/tests/profiler_unittest.cc
M	src/tests/raw_printer_test.cc
M	src/tests/realloc_unittest.cc
M	src/tests/sampler_test.cc
M	src/tests/sampling_test.cc
M	src/tests/simple_compat_test.cc
M	src/tests/stack_trace_table_test.cc
M	src/tests/system-alloc_unittest.cc
M	src/tests/tcmalloc_large_unittest.cc
M	src/tests/tcmalloc_unittest.cc
M	src/tests/testutil.cc
M	src/tests/testutil.h
M	src/tests/thread_dealloc_unittest.cc
M	src/thread_cache.cc
M	src/thread_cache.h
M	src/windows/auto_testing_hook.h
M	src/windows/get_mangled_names.cc
M	src/windows/gperftools/tcmalloc.h
M	src/windows/gperftools/tcmalloc.h.in
M	src/windows/mingw.h
M	src/windows/mini_disassembler.cc
M	src/windows/mini_disassembler.h
M	src/windows/mini_disassembler_types.h
M	src/windows/override_functions.cc
M	src/windows/port.cc
M	src/windows/port.h
M	src/windows/preamble_patcher.cc
M	src/windows/preamble_patcher.h
M	src/windows/preamble_patcher_test.cc
M	src/windows/preamble_patcher_with_stub.cc

commit 799a22624c85e8749f8bed0bfa63282b03e75bdd
Author: Aliaksey Kandratsenka <<EMAIL>>
Date:	Sat Sep 28 19:32:20 2013 -0700

    issue-575: do not use cycle count register on arm6

    Apparently not all arm6 implementations implement it in this
    particular way.

    This applies patch by Ben Avison.

M	src/base/cycleclock.h

commit 2a2d6596f8c3d62b7ec444761f4edf0c85d10d92
Author: Petr Hosek <<EMAIL>>
Date:	Fri Aug 23 23:58:47 2013 -0700

    Adds system-alloc_unittest Visual Studio project

M	gperftools.sln
A	vsprojects/system-alloc_unittest/system-alloc_unittest.vcproj

commit 83aed118e009b92ea88645ef1f7b842a921612c5
Author: Petr Hosek <<EMAIL>>
Date:	Fri Aug 23 13:53:35 2013 -0700

    issue-567: Allows for overriding system allocator on Windows

    [<EMAIL>: minor changes to make mingw build work]
    Signed-off-by: Aliaksey Kandratsenka <<EMAIL>>

M	Makefile.am
M	src/windows/port.cc
A	src/windows/system-alloc.cc
M	vsprojects/libtcmalloc_minimal/libtcmalloc_minimal.vcproj
M	vsprojects/tmu-static/tmu-static.vcproj

commit 4ad16873a0a2d8861a0bfe8234d45e31cc70ee90
Author: Petr Hosek <<EMAIL>>
Date:	Sat Aug 24 14:24:47 2013 -0700

    Exports SysAllocator class to avoid .dll build errors

M	src/gperftools/malloc_extension.h

commit 326990b5c30d249c3cf4688a88fc415b05494aca
Author: Aliaksey Kandratsenka <<EMAIL>>
Date:	Sat Sep 14 14:38:53 2013 -0700

    issue-557: added support for dumping heap profile via signal

    This applies patch from Jean Lee.

    I've reformatted it to match surronding code style and changed
    validation logic a bit. I.e. we're not checking signal for range
    anymore given we're not sure what different platforms support, but
    we're checking return value of signal() for SIG_ERR instead.

M	src/heap-profiler.cc

commit cb65e49b83c84bc205203c12793f2dd00c4a7721
Author: Aliaksey Kandratsenka <<EMAIL>>
Date:	Sat Sep 14 16:45:42 2013 -0700

    issue-536: do not PrintStats if running under valgrind

    When we detect running under valgrind we do not initialize our own
    malloc. So trying to print malloc stats when asked via MALLOCSTATS
    cannot work.

    This does fix proposed by Philippe Waroquiers. In which we detect
    running under valgrind prior to checking MALLOCSTATS environment
    variable and refuse printing stats if we detect valgrind.

M	src/tcmalloc.cc

commit 6979583592df555a369a2c975f5117a1f61911af
Author: Aliaksey Kandratsenka <<EMAIL>>
Date:	Mon Sep 9 07:59:25 2013 -0700

    issue-564: added atomic ops support for mips{,64}

    This merges patch contributed by Jovan Zelincevic.

    And with that patch tcmalloc build with --enable-minimal (just malloc
    replacement) appears to work (passes unit tests).

M	Makefile.am
A	src/base/atomicops-internals-mips.h
M	src/base/atomicops.h
M	src/base/basictypes.h
M	src/base/linux_syscall_support.h
M	src/stacktrace_config.h

commit 28dd85e2825af71138621a4417e6ab004631924d
Author: Aliaksey Kandratsenka <<EMAIL>>
Date:	Fri Aug 30 16:57:14 2013 +0300

    implement pc from ucontext access for mips

M	m4/pc_from_ucontext.m4

commit 819a2b051f1dba9526f2338098fff6dd1700bdb6
Author: Aliaksey Kandratsenka <<EMAIL>>
Date:	Thu Aug 29 19:00:31 2013 +0300

    issue-413: disable __thread usage on OSX

    Because it was found that __thread variables access is compiled into
    calls to tlv_get_addr which was found to call malloc. Because we
    actually use thread-local storage from inside malloc it leads to stack
    overflow. So we'll continue using pthreads API for that which is known
    to work on OSX.

M	configure.ac

commit 43809080931127037ce6e748f37a28ce7489387d
Author: Aliaksey Kandratsenka <<EMAIL>>
Date:	Thu Aug 29 18:51:48 2013 +0300

    lowered autoconf requirement

    Autoconf 2.59 works. And most notably it will not affect our releases
    which are all prepared with newer autoconf.

M	configure.ac

commit 7a178d472737d988583bade3f0345201651cd43f
Author: Joon-Sung Um <<EMAIL>>
Date:	Tue Aug 27 22:16:18 2013 +0900

    Update document for tcmalloc

    Update tcmalloc.html for new parameters.

     * kMaxSize = 256k
     * kNumClasses = 88
     * kPageShift = 13

    Signed-off-by: Aliaksey Kandratsenka <<EMAIL>>

M	doc/tcmalloc.html

commit 313e08b5a1a951f710b0323b3ca4e1e8d869520e
Author: Aliaksey Kandratsenka <<EMAIL>>
Date:	Sun Aug 4 20:44:06 2013 +0300

    issue-560: Revert "issue-481: ...

    ...Replaced test mechanism for distinct address spaces with a more
    reliable mechanism"

    This reverts commit 5dd53ab6cbf9d98f2d60546835e84785a104da46 (svn
    revision 167)

    With this commit rhel 6.2 fails heap-checker-death_unittest and
    without it passes.

    Ticket refers to 2 things and both are invalid:

    * that ptrace PEEKDATA ignores data argument. I've checked kernel
      source and found it to be wrong

    * something about distinct address spaces

    And in addition to all that original ticket admits that it doesn't fix
    anything.

    It looks like, compared to original code that "fix" is not succesfully
    wait-ing on parent's ptrace request. I.e. by adding some additional
    diagnostics I'm seeing this sys_waitpid returning ECHILD.

M	src/base/linuxthreads.cc

commit 6d00cbce92c5576b4bcf07a2b5634b4dcfa14f8a
Author: Aliaksey Kandratsenka <<EMAIL>>
Date:	Sat Aug 17 15:52:53 2013 +0300

    issue-561: don't cast function pointer to void *

    Which gcc-3.4 (as shipped in rhel 4) doesn't like.

    Cast to void * was originally added to avoid issue on OSX which
    doesn't have sighandler_t.

    In that place we only need to know if it's null or not. So casting to
    intptr_t looks like simplest possible way to achieve that.

M	src/profiler.cc

commit 7d8d522d737f6c55169264b35ebe0be1f9b23efd
Author: Aliaksey Kandratsenka <<EMAIL>>
Date:	Sat Aug 17 15:52:22 2013 +0300

    add heap-profile-stats.h to dist .tar.gz

M	Makefile.am

commit d76cfa6d6ca5944d6300765fcb9160e889423750
Author: Aliaksey Kandratsenka <<EMAIL>>
Date:	Sun Aug 4 18:35:55 2013 +0300

    issue-502: Count m(un)map for each stacktrace in MemoryRegionMap

    ..instead of HeapProfileTable

    This upstreams chromium commit reviewed at:
    https://codereview.chromium.org/12388070

    Original and upstreaming author is: Dai MIKURUBE

    This patch fixes a bug that gperftools(TCMalloc)'s mmap profiler
    (HEAP_PROFILE_MMAP) doesn't hook some memory pages used by the
    profiler itself.

    This problem has been lived in gperftools for a long time.
    It is discussed in gperftools' issue 502.
    https://code.google.com/p/gperftools/issues/detail?id=502

    Some bugs in the mmap profiler were fixed by
    https://code.google.com/p/gperftools/issues/detail?id=383,
    but the patch in the issue 383 didn't fix the bug mentioned in
    the issue 502.

    This change reverts the previous patch and http://crrev.com/132771
    at first. Then, it modifies MemoryRegionMap to count m(un)map
    calls for each stacktrace in itself instead of merging the counts
    for each stacktrace in HeapProfileTable.

    This change also cleans up heap-profiler, heap-profile-table and
    deep-heap-profile.

    Chromium-BUG=https://code.google.com/p/chromium/issues/detail?id=181517
    Chromium-Committed:
    https://src.chromium.org/viewvc/chrome?view=rev&revision=188176

M	src/heap-checker.cc
A	src/heap-profile-stats.h
M	src/heap-profile-table.cc
M	src/heap-profile-table.h
M	src/heap-profiler.cc
M	src/memory_region_map.cc
M	src/memory_region_map.h

commit 89b163a0883d40a612331ed8565b8ab1219fdbd0
Author: Aliaksey Kandratsenka <<EMAIL>>
Date:	Sun Aug 4 18:27:07 2013 +0300

    added .gitignore

A	.gitignore

commit 18fbc316eebea9db2d7ec41e161c0a3fbb09fa42
Author: <EMAIL>
<<EMAIL>@6b5cf1ce-ec42-a296-1ba9-69fdba395a50>
Date:	Wed Jul 31 04:02:19 2013 +0000

    Fix a C++11 error.

    This merges patch by Nico Weber.

    New clang versions complain in C++11 mode that:

    error: cannot initialize a variable of type 'void *' with an rvalue
    of type 'uintptr_t' (aka 'unsigned long')

    This same change was done for the google-internal version of
    tcmalloc too.

    Reviewed-at: https://codereview.appspot.com/12132043



    git-svn-id: http://gperftools.googlecode.com/svn/trunk@238
    6b5cf1ce-ec42-a296-1ba9-69fdba395a50

M	src/stack_trace_table.cc

commit 674fcd94a8a0a3595f64e13762ba3a6529e09926
Author: <EMAIL>
<<EMAIL>@6b5cf1ce-ec42-a296-1ba9-69fdba395a50>
Date:	Tue Jul 30 09:14:20 2013 +0000

    bump version to 2.1



    git-svn-id: http://gperftools.googlecode.com/svn/trunk@236
    6b5cf1ce-ec42-a296-1ba9-69fdba395a50

M	NEWS
M	configure.ac
M	packages/deb/changelog
M	src/windows/config.h
M	src/windows/gperftools/tcmalloc.h

commit 805a6601939edd9bca60a8911e56b217e477c75e
Author: <EMAIL>
<<EMAIL>@6b5cf1ce-ec42-a296-1ba9-69fdba395a50>
Date:	Tue Jul 30 08:30:45 2013 +0000

    issue-559: don't setup fork handler in InitStaticVars

    Because on OSX it calls malloc which leads to deadlock.

    Given that we don't really need that fork handler _that_ early, it's
    fine to change it to normal static initializer



    git-svn-id: http://gperftools.googlecode.com/svn/trunk@235
    6b5cf1ce-ec42-a296-1ba9-69fdba395a50

M	src/static_vars.cc

commit c583a5babb54aedf6ab6e7746fd976ef2469839d
Author: <EMAIL>
<<EMAIL>@6b5cf1ce-ec42-a296-1ba9-69fdba395a50>
Date:	Tue Jul 30 08:30:19 2013 +0000

    issue-559: don't depend on sighandler_t

    Which is not available on OSX.

    I've also fixed style around this place.



    git-svn-id: http://gperftools.googlecode.com/svn/trunk@234
    6b5cf1ce-ec42-a296-1ba9-69fdba395a50

M	src/profiler.cc

commit 1af20041bf5821167e69bf905596b2d45d1d94da
Author: <EMAIL>
<<EMAIL>@6b5cf1ce-ec42-a296-1ba9-69fdba395a50>
Date:	Tue Jul 30 08:29:46 2013 +0000

    issue-559: AtomicWord xxx_AtomicExchange are all inline

    Otherwise OSX correctly complains about duplicate definitions



    git-svn-id: http://gperftools.googlecode.com/svn/trunk@233
    6b5cf1ce-ec42-a296-1ba9-69fdba395a50

M	src/base/atomicops.h

commit 7a9c4e075fe2572307cddd11bc2f5c7d6b3d29ee
Author: <EMAIL>
<<EMAIL>@6b5cf1ce-ec42-a296-1ba9-69fdba395a50>
Date:	Fri Jul 26 19:36:08 2013 +0000

    issue-556: update windows PACKAGE_{VERSION,STRING}



    git-svn-id: http://gperftools.googlecode.com/svn/trunk@232
    6b5cf1ce-ec42-a296-1ba9-69fdba395a50

M	src/windows/config.h

commit 11d46f0aac5ef941b74114412bda4747a349c60d
Author: <EMAIL>
<<EMAIL>@6b5cf1ce-ec42-a296-1ba9-69fdba395a50>
Date:	Fri Jul 26 19:35:38 2013 +0000

    issue-556: dll-export getpagesize

    Because page_heap_test needs this.



    git-svn-id: http://gperftools.googlecode.com/svn/trunk@231
    6b5cf1ce-ec42-a296-1ba9-69fdba395a50

M	src/windows/port.cc
M	src/windows/port.h

commit 6d4ad4428bb8f587d890fa5b7a76ba65dcf48dcb
Author: <EMAIL>
<<EMAIL>@6b5cf1ce-ec42-a296-1ba9-69fdba395a50>
Date:	Fri Jul 26 19:35:20 2013 +0000

    issue-556: drop malloc limit at the end of page_heap_test

    Which otherwise causes somewhat weird stack overflow on release
    windows builds.



    git-svn-id: http://gperftools.googlecode.com/svn/trunk@230
    6b5cf1ce-ec42-a296-1ba9-69fdba395a50

M	src/tests/page_heap_test.cc

commit be54c3f3ed192eabab9b075ec924d2479a956b9d
Author: <EMAIL>
<<EMAIL>@6b5cf1ce-ec42-a296-1ba9-69fdba395a50>
Date:	Fri Jul 26 19:34:54 2013 +0000

    issue-556: dll-export TCMalloc_System{Alloc,Release}

    because page_heap_test is using this stuff



    git-svn-id: http://gperftools.googlecode.com/svn/trunk@229
    6b5cf1ce-ec42-a296-1ba9-69fdba395a50

M	src/system-alloc.h
M	src/windows/port.cc

commit e3716145cbfa1247fa37dd627b2136a061944255
Author: <EMAIL>
<<EMAIL>@6b5cf1ce-ec42-a296-1ba9-69fdba395a50>
Date:	Fri Jul 26 18:19:05 2013 +0000

    issue-552: Fix page_heap_test for system with different page size

    This is patch by Adhemerval Zanella.

    PowerPC uses 64K page size instead of 4k for x86 and x86_64. It
    makes the
    page_heap_test fails because the following test:

     static bool HaveSystemRelease =
	TCMalloc_SystemRelease(TCMalloc_SystemAlloc(kPageSize, NULL,
	0), kPageSize);

    will always fail if kPageSize is less than getpagesize() (the default
    configuration).

    The following patch fixes it by trying to allocate/deallocate
    an entire
    page instead.



    git-svn-id: http://gperftools.googlecode.com/svn/trunk@228
    6b5cf1ce-ec42-a296-1ba9-69fdba395a50

M	src/tests/page_heap_test.cc

commit f45133e75c09ca7d5e86bda2db16e30c6fa348c0
Author: <EMAIL>
<<EMAIL>@6b5cf1ce-ec42-a296-1ba9-69fdba395a50>
Date:	Fri Jul 26 18:04:03 2013 +0000

    issue-553: Fix syscall wrapper for PowerPC

    This is patch by Adhemerval Zanella.

    * src/stacktrace_powerpc-inl.h: It is just a cleanup for the
    stacktrace
      functions for PowerPC. The idea is to simplify the code.
    * src/tests/heap-checker_unittest.cc: Handles the PPC64 function
    descriptor
      correctly in malloc tracers. Different from other architecture,
      for PPC64
      the address returned in function pointers are the ODP entry, not the
      symbol address in .text segment. This leads the comparison bogus,
      since
      it will compare a ODP entry with a .text address.
    * src/heap-checker.cc: Add support for PPC in ptrace.
    * src/base/elfcore.h: Likewise.
    * src/base/linuxthreads.cc: Fix the thread creation using the clone
    wrapper.
    * src/base/linux_syscall_support.h: Various fixes for PPC32 and PPC64:
      fixes the kernel_stat[64] struct layout, and sys_clone and
      sys_socket
      implementation.



    git-svn-id: http://gperftools.googlecode.com/svn/trunk@227
    6b5cf1ce-ec42-a296-1ba9-69fdba395a50

M	src/base/elfcore.h
M	src/base/linux_syscall_support.h
M	src/base/linuxthreads.cc
M	src/heap-checker.cc
M	src/stacktrace_powerpc-inl.h
M	src/tests/heap-checker_unittest.cc

commit ee2bf097133b115e3da249e43507e02645e46e59
Author: <EMAIL>
<<EMAIL>@6b5cf1ce-ec42-a296-1ba9-69fdba395a50>
Date:	Sat Jul 20 21:37:26 2013 +0000

    updated NEWS and packages/ for 2.1rc



    git-svn-id: http://gperftools.googlecode.com/svn/trunk@226
    6b5cf1ce-ec42-a296-1ba9-69fdba395a50

M	NEWS
M	packages/deb/changelog
M	packages/deb/control
M	packages/deb/copyright
M	packages/rpm/rpm.spec

commit 60ab178aeb827378d78cae8f1c6f893b14deaaf0
Author: <EMAIL>
<<EMAIL>@6b5cf1ce-ec42-a296-1ba9-69fdba395a50>
Date:	Sat Jul 20 21:37:01 2013 +0000

    bump version number for 2.1rc



    git-svn-id: http://gperftools.googlecode.com/svn/trunk@225
    6b5cf1ce-ec42-a296-1ba9-69fdba395a50

M	configure.ac
M	src/windows/gperftools/tcmalloc.h

commit 7c2aa2f7c2cf901c17b9b9215224f725e798d1f2
Author: <EMAIL>
<<EMAIL>@6b5cf1ce-ec42-a296-1ba9-69fdba395a50>
Date:	Sat Jul 20 21:35:56 2013 +0000

    issue-546: fixed return type of Release_AtomicExchange on arm6+



    git-svn-id: http://gperftools.googlecode.com/svn/trunk@224
    6b5cf1ce-ec42-a296-1ba9-69fdba395a50

M	src/base/atomicops-internals-arm-v6plus.h

commit d8e12e94ea09eaf8b040c6887f75cdf832b5619b
Author: <EMAIL>
<<EMAIL>@6b5cf1ce-ec42-a296-1ba9-69fdba395a50>
Date:	Sat Jul 20 21:35:14 2013 +0000

    issue-549: handle most recent mingw that has sleep and nanosleep

    I.e. we have to check their presence in configure and in case of their
    presence we have to avoid re-defining then in window's port.h



    git-svn-id: http://gperftools.googlecode.com/svn/trunk@223
    6b5cf1ce-ec42-a296-1ba9-69fdba395a50

M	configure.ac
M	src/windows/port.h

commit ac354636de8a4f11e4fde679b52e9f58fda0e079
Author: <EMAIL>
<<EMAIL>@6b5cf1ce-ec42-a296-1ba9-69fdba395a50>
Date:	Sun Jul 14 04:19:02 2013 +0000

    issue-550: remove config.h.in

    Which is autoconf product and thus is not needed in source repository



    git-svn-id: http://gperftools.googlecode.com/svn/trunk@222
    6b5cf1ce-ec42-a296-1ba9-69fdba395a50

D	src/config.h.in

commit e54971d58641853a9515d9f0313645729eab113a
Author: <EMAIL>
<<EMAIL>@6b5cf1ce-ec42-a296-1ba9-69fdba395a50>
Date:	Sat Jul 6 21:54:34 2013 +0000

    issue-534: fixed a number of gcc warnings

    This applies patch from Adhemerval Zanella.



    git-svn-id: http://gperftools.googlecode.com/svn/trunk@221
    6b5cf1ce-ec42-a296-1ba9-69fdba395a50

M	src/base/basictypes.h
M	src/heap-profiler.cc
M	src/profiler.cc
M	src/tests/heap-checker_unittest.cc
M	src/tests/page_heap_test.cc

commit 7dd038d7c58c9de889e3fcc552161533ea1baab1
Author: <EMAIL>
<<EMAIL>@6b5cf1ce-ec42-a296-1ba9-69fdba395a50>
Date:	Sat Jul 6 20:48:18 2013 +0000

    issue-545: corrected README for CPUPROFILE_FREQUENCY variable

    Applied patch by Mikhail Veltishchev



    git-svn-id: http://gperftools.googlecode.com/svn/trunk@220
    6b5cf1ce-ec42-a296-1ba9-69fdba395a50

M	README

commit a833a146b7de93774f0a9428edc5eda410d9a7dc
Author: <EMAIL>
<<EMAIL>@6b5cf1ce-ec42-a296-1ba9-69fdba395a50>
Date:	Sat Jul 6 20:46:50 2013 +0000

    issue-528: fixed spelling

    This simply applies patch by Lajos Veres



    git-svn-id: http://gperftools.googlecode.com/svn/trunk@219
    6b5cf1ce-ec42-a296-1ba9-69fdba395a50

M	doc/heapprofile.html
M	doc/pprof_remote_servers.html
M	src/base/spinlock.cc
M	src/base/sysinfo.cc
M	src/base/sysinfo.h
M	src/gperftools/profiler.h
M	src/pprof
M	src/stacktrace_x86-inl.h
M	src/windows/patch_functions.cc
M	src/windows/preamble_patcher.cc

commit 205abf1e7cb860224cbf391c7f69a6192ccc5076
Author: <EMAIL>
<<EMAIL>@6b5cf1ce-ec42-a296-1ba9-69fdba395a50>
Date:	Sun May 12 19:15:13 2013 +0000

    issue-518: add support for x32 ABI to linux_syscall_support.h

    As pointed out in the ticket this is taken from chromium review system
    here: https://codereview.chromium.org/13648012



    git-svn-id: http://gperftools.googlecode.com/svn/trunk@218
    6b5cf1ce-ec42-a296-1ba9-69fdba395a50

M	src/base/linux_syscall_support.h

commit 376a3107aa06063d72132318ac5ad266466e4325
Author: <EMAIL>
<<EMAIL>@6b5cf1ce-ec42-a296-1ba9-69fdba395a50>
Date:	Tue May 7 22:28:05 2013 +0000

    issue-368: allocate enough bytes for large metadata allocations

    During issue-368 review it was correctly pointed out then in place
    where I compare metadata allocation size to threshold I should pass
    that size down to TCMalloc_SystemAlloc instead of threshold.



    git-svn-id: http://gperftools.googlecode.com/svn/trunk@217
    6b5cf1ce-ec42-a296-1ba9-69fdba395a50

M	src/common.cc

commit 58d39a1b2190b9d0b719ee33f7bc5383a9b27462
Author: <EMAIL>
<<EMAIL>@6b5cf1ce-ec42-a296-1ba9-69fdba395a50>
Date:	Tue May 7 22:23:37 2013 +0000

    issue-368: added missing large_heap_fragmentation_unittest.cc



    git-svn-id: http://gperftools.googlecode.com/svn/trunk@216
    6b5cf1ce-ec42-a296-1ba9-69fdba395a50

A	src/tests/large_heap_fragmentation_unittest.cc

commit c45bb7d6033952ea33bb181c1f96999317b535a1
Author: <EMAIL>
<<EMAIL>@6b5cf1ce-ec42-a296-1ba9-69fdba395a50>
Date:	Tue May 7 19:22:45 2013 +0000

    issue-511: fixed negative offset handling for conditional jumps

    While doing and testing issue-511 I've found one subtle bug which is
    incorrect handling of short offsets. They are defined to be signed but
    previous code used unsigned char for them which caused negative
    offsets to look like larger positive offsets. Fix is trivial.



    git-svn-id: http://gperftools.googlecode.com/svn/trunk@215
    6b5cf1ce-ec42-a296-1ba9-69fdba395a50

M	src/windows/preamble_patcher.cc

commit 3567b1701aa55a6421aefa5f3de1ca5507cbdf79
Author: <EMAIL>
<<EMAIL>@6b5cf1ce-ec42-a296-1ba9-69fdba395a50>
Date:	Tue May 7 19:15:35 2013 +0000

    issue-511: recognise rex.w jmpq *<literal>(%rip) as iat jump

    Apparently Windows Server 2012 (and presumably windows 8) now has this
    form of iat jump. Which is quite useless (rex.w is according to my
    understanding is not needed at all) but because of rex.w our code to
    recognize jumps like that didn't work.

    Fix is just skip this prefix.



    git-svn-id: http://gperftools.googlecode.com/svn/trunk@214
    6b5cf1ce-ec42-a296-1ba9-69fdba395a50

M	src/windows/preamble_patcher.cc

commit 7fcb5ac0696e7ef7f7e7e51c18745af25de7da96
Author: <EMAIL>
<<EMAIL>@6b5cf1ce-ec42-a296-1ba9-69fdba395a50>
Date:	Tue May 7 19:13:29 2013 +0000

    issue-511: implemented rel8 jump patching

    I've found that Visual Studio 2012 release 32-bit C runtime library
    patching fails because array new has rel8 jmp which previous code
    could not handle.

    Implementation is largely copied from conditional jumps handling code.



    git-svn-id: http://gperftools.googlecode.com/svn/trunk@213
    6b5cf1ce-ec42-a296-1ba9-69fdba395a50

M	src/windows/preamble_patcher.cc
M	src/windows/preamble_patcher.h
M	src/windows/preamble_patcher_with_stub.cc

commit 8cb4086a0aee539869bd087a85881788545b23d6
Author: <EMAIL>
<<EMAIL>@6b5cf1ce-ec42-a296-1ba9-69fdba395a50>
Date:	Mon May 6 19:50:59 2013 +0000

    issue-368: test that we don't fragment large spans too badly

    This adds unit test that does essentially same things as code to
    reproduce bug in
    https://code.google.com/p/gperftools/issues/detail?id=368




    git-svn-id: http://gperftools.googlecode.com/svn/trunk@212
    6b5cf1ce-ec42-a296-1ba9-69fdba395a50

M	Makefile.am

commit f25ac4421f9efb635b88105e9c0830293ce19f9d
Author: <EMAIL>
<<EMAIL>@6b5cf1ce-ec42-a296-1ba9-69fdba395a50>
Date:	Mon May 6 19:50:23 2013 +0000

    issue-{368,443}: allocate metadata in big batches

    It uses same approach as PageHeapAllocator. Namely allocates big chunk
    which is then used to satisfy smaller allocations.

    In issue-443 gradual heap grows causes old code that allocates
    metadata in smaller pieces and thus more frequently to fragment the
    heap. It's also causing most of 368 heap fragmentation too.

    New code allocates 8 megs of address space at once for metadata
    allocations. Most OSes will allocate actual memory only when
    corresponding pages are touched. Thus this change should not cause
    increased memory usage.

    I've also made sure metadata is always properly aligned in case we
    ever allocate something that breaks natural alignment. E.g. strings.



    git-svn-id: http://gperftools.googlecode.com/svn/trunk@211
    6b5cf1ce-ec42-a296-1ba9-69fdba395a50

M	src/common.cc

commit 4fd762cead660d4661359ad507083ac4f4967ee4
Author: <EMAIL>
<<EMAIL>@6b5cf1ce-ec42-a296-1ba9-69fdba395a50>
Date:	Mon May 6 19:49:48 2013 +0000

    issue-368: unmap free spans and retry before growing heap

    Because unmapped spans are not coalesced with normal spans it's
    possible that we indeed have a large enough free span, but we fail to
    see that because we always consider unmapped and normal spans
    separately. That behavior is more likely for larger spans.

    In order to protect programs that grow heap frequently and by small
    amounts from much more frequent minor page faults, there's limit of
    running that force pages unmap path once per 128 megs of heap growth.



    git-svn-id: http://gperftools.googlecode.com/svn/trunk@210
    6b5cf1ce-ec42-a296-1ba9-69fdba395a50

M	src/page_heap.cc

commit 99fe9944de32046fd954399e60415fba7d03eeb0
Author: <EMAIL>
<<EMAIL>@6b5cf1ce-ec42-a296-1ba9-69fdba395a50>
Date:	Mon Apr 1 05:32:22 2013 +0000

    issue-510: remove duplicate arm #elif



    git-svn-id: http://gperftools.googlecode.com/svn/trunk@209
    6b5cf1ce-ec42-a296-1ba9-69fdba395a50

M	src/base/basictypes.h

commit 6354e2c8cdaaaeffdfe7d5b347b125394e2d55fa
Author: <EMAIL>
<<EMAIL>@6b5cf1ce-ec42-a296-1ba9-69fdba395a50>
Date:	Mon Mar 11 19:24:49 2013 +0000

    issue-506: fixed bogus unit test failure

    Looks like my version of GCC is aware that free(malloc(X)) is a
    no-op. So it optimizes that away completely ignoring simple fact that
    we're observing malloc hooks invocations. By adding check that malloc
    succeeded we force gcc to actually preserve that malloc call.



    git-svn-id: http://gperftools.googlecode.com/svn/trunk@208
    6b5cf1ce-ec42-a296-1ba9-69fdba395a50

M	src/tests/malloc_extension_c_test.c

commit 7896dcb9db2375fcd1d8d19052985a12ad113ba9
Author: <EMAIL>
<<EMAIL>@6b5cf1ce-ec42-a296-1ba9-69fdba395a50>
Date:	Mon Mar 11 19:16:47 2013 +0000

    issue-504: disable tls on mingw

    Which is known to fail.



    git-svn-id: http://gperftools.googlecode.com/svn/trunk@207
    6b5cf1ce-ec42-a296-1ba9-69fdba395a50

M	configure.ac

commit 8bf4522aa53a7b612a865bc10716192f0f05314b
Author: <EMAIL>
<<EMAIL>@6b5cf1ce-ec42-a296-1ba9-69fdba395a50>
Date:	Mon Mar 11 19:15:46 2013 +0000

    issue-504: do not define HAVE_MMAP on mingw

    Because, clearly, windows doesn't have one



    git-svn-id: http://gperftools.googlecode.com/svn/trunk@206
    6b5cf1ce-ec42-a296-1ba9-69fdba395a50

M	configure.ac

commit 9c24d255cdd8dab7482d968acc69d493680233ce
Author: <EMAIL>
<<EMAIL>@6b5cf1ce-ec42-a296-1ba9-69fdba395a50>
Date:	Mon Mar 11 19:15:23 2013 +0000

    issue-504: add AM_LDFLAGS to all *_la targets

    Because automake will not automatically add AM_LDFLAGS if there's
    per-target LDFLAGS. See their good info manual.

    This fixes .dll compilation of tcmalloc



    git-svn-id: http://gperftools.googlecode.com/svn/trunk@205
    6b5cf1ce-ec42-a296-1ba9-69fdba395a50

M	Makefile.am

commit a2275fc6b48b98d5265388bb8586faa7c8cc581f
Author: <EMAIL>
<<EMAIL>@6b5cf1ce-ec42-a296-1ba9-69fdba395a50>
Date:	Mon Mar 11 19:14:18 2013 +0000

    issue-504: don't try to typedef pid_t on mingw

    Because recent mingws (more then few years ago seemingly) do that
    already.



    git-svn-id: http://gperftools.googlecode.com/svn/trunk@204
    6b5cf1ce-ec42-a296-1ba9-69fdba395a50

M	src/windows/mingw.h
M	src/windows/port.h

commit f00977533de6fd27a2f796b2e8e9adb7fcd965e8
Author: <EMAIL>
<<EMAIL>@6b5cf1ce-ec42-a296-1ba9-69fdba395a50>
Date:	Mon Mar 11 19:13:53 2013 +0000

    issue-504: use gcc inline assembly atomic ops on mingw

    Because those are well tested and can be trusted



    git-svn-id: http://gperftools.googlecode.com/svn/trunk@203
    6b5cf1ce-ec42-a296-1ba9-69fdba395a50

M	Makefile.am
M	src/base/atomicops.h

commit 34cdd6821b637b124722687578aa119e8714266c
Author: <EMAIL>
<<EMAIL>@6b5cf1ce-ec42-a296-1ba9-69fdba395a50>
Date:	Mon Mar 11 19:13:13 2013 +0000

    issue-504: use lowercase windows includes for mingw x-compilation

    I.e. so that I can build tcmalloc.dll using comfortable environment of
    my GNU/Linux box and without having to touch actual windows box or VM.



    git-svn-id: http://gperftools.googlecode.com/svn/trunk@202
    6b5cf1ce-ec42-a296-1ba9-69fdba395a50

M	Makefile.am
M	src/base/sysinfo.h
M	src/windows/patch_functions.cc

commit 0971d80d0ef0c881dcf46453e576a7e289d86975
Author: <EMAIL>
<<EMAIL>@6b5cf1ce-ec42-a296-1ba9-69fdba395a50>
Date:	Mon Mar 11 18:35:16 2013 +0000

    issue-503: updated svn:ignore to ignore generated files



    git-svn-id: http://gperftools.googlecode.com/svn/trunk@201
    6b5cf1ce-ec42-a296-1ba9-69fdba395a50

commit 05b100d9d4ffeb1342eb5ecf50bd0d2339e626c5
Author: <EMAIL>
<<EMAIL>@6b5cf1ce-ec42-a296-1ba9-69fdba395a50>
Date:	Mon Mar 11 18:32:05 2013 +0000

    issue-503: removed checked in configure and other auto* products

    So that it's easier to contribute and review contributions.

    People wishing to build gperftools from svn checkout should run
    autogen.sh first which will create those files and then ./configure &&
    make as usual

    INSTALL file has updated instructions too.



    git-svn-id: http://gperftools.googlecode.com/svn/trunk@200
    6b5cf1ce-ec42-a296-1ba9-69fdba395a50

M	INSTALL
D	Makefile.in
D	aclocal.m4
M	autogen.sh
D	compile
D	config.guess
D	config.sub
D	configure
M	configure.ac
D	depcomp
D	install-sh
D	ltmain.sh
D	m4/libtool.m4
D	m4/ltoptions.m4
D	m4/ltsugar.m4
D	m4/ltversion.m4
D	m4/lt~obsolete.m4
D	missing
D	mkinstalldirs

commit dcf55b976f19a186ac04d60d95e03ed2795285c4
Author: <EMAIL>
<<EMAIL>@6b5cf1ce-ec42-a296-1ba9-69fdba395a50>
Date:	Mon Mar 11 18:29:00 2013 +0000

    issue-503: recover original INSTALL file

    That was accidently overwritten by autotools files update in r196



    git-svn-id: http://gperftools.googlecode.com/svn/trunk@199
    6b5cf1ce-ec42-a296-1ba9-69fdba395a50

M	INSTALL

commit 5037f687d1de80722f634e84763572caf2725f98
Author: <EMAIL>
<<EMAIL>@6b5cf1ce-ec42-a296-1ba9-69fdba395a50>
Date:	Mon Mar 11 01:35:37 2013 +0000

    issue-425: Fixed tcmalloc unittest crashes for windows builds

    Missing use of volatile was causing vs2010 to perform unwanted
    optimization resulting in the crash. See issue for more details.


    git-svn-id: http://gperftools.googlecode.com/svn/trunk@198
    6b5cf1ce-ec42-a296-1ba9-69fdba395a50

M	src/tests/tcmalloc_unittest.cc

commit beb78cc05babf0a49d21aed0ec789f19fc0f2d28
Author: <EMAIL>
<<EMAIL>@6b5cf1ce-ec42-a296-1ba9-69fdba395a50>
Date:	Mon Mar 11 00:23:03 2013 +0000

    issue-493: Fix for building against ARM targets

    gperftools was failing to build for arm targets for the following
    reasons:
    1. Some ARMv7 instructions used when the target is ARMv6 so those
    fail to assemble
    2. The cache line length is undefined for ARM architectures


    git-svn-id: http://gperftools.googlecode.com/svn/trunk@197
    6b5cf1ce-ec42-a296-1ba9-69fdba395a50

M	src/base/atomicops-internals-arm-v6plus.h
M	src/base/basictypes.h

commit bd3b3a7e9a67fac846cf574f5bfd241157bdfe3c
Author: <EMAIL>
<<EMAIL>@6b5cf1ce-ec42-a296-1ba9-69fdba395a50>
Date:	Sun Mar 10 20:17:21 2013 +0000

    issue-496: Fixes an issue where tcmalloc might deadlock in a thread
    calling fork

    tcmalloc contained a bug where some internal locks were left in a
    undefined state
    between fork, leaving the child process in a deadlock state. This
    patch fixes the
    issue by introducing stricter locking between the parent nd child
    while forking.


    git-svn-id: http://gperftools.googlecode.com/svn/trunk@196
    6b5cf1ce-ec42-a296-1ba9-69fdba395a50

M	INSTALL
M	Makefile.in
M	aclocal.m4
M	config.guess
M	config.sub
M	configure
M	configure.ac
M	depcomp
M	install-sh
M	missing
M	mkinstalldirs
M	src/central_freelist.h
M	src/config.h.in
M	src/static_vars.cc

commit 560ca8650c8a9d2971420970f0ed5e17848150eb
Author: <EMAIL>
<<EMAIL>@6b5cf1ce-ec42-a296-1ba9-69fdba395a50>
Date:	Sun Mar 10 20:02:46 2013 +0000

    issue-491: Significant performance improvement for spin lock
    contention

    This patch fixes issues where spinlocks under contention were
    failing to
    wakeup waiters, sometimes resulting in blow ups from 13ns to as high
    as 256ms.
    Under heavy contention, applications were observed sleeping for
    minutes at a
    time giving the appearance of a hang.


    git-svn-id: http://gperftools.googlecode.com/svn/trunk@195
    6b5cf1ce-ec42-a296-1ba9-69fdba395a50

M	src/base/spinlock.h

commit b591d53af951eac60683237204464ebfec2c3afa
Author: <EMAIL>
<<EMAIL>@6b5cf1ce-ec42-a296-1ba9-69fdba395a50>
Date:	Sun Mar 10 19:54:39 2013 +0000

    issue-490: Added support for acquire/release atomic exchange

    The atomic ops API was missing support for all of the various
    flavors of Acquire_AtomicExchange and Release_AtomicExchange
    which have now been added.


    git-svn-id: http://gperftools.googlecode.com/svn/trunk@194
    6b5cf1ce-ec42-a296-1ba9-69fdba395a50

M	src/base/atomicops-internals-arm-generic.h
M	src/base/atomicops-internals-arm-v6plus.h
M	src/base/atomicops-internals-linuxppc.h
M	src/base/atomicops-internals-macosx.h
M	src/base/atomicops-internals-windows.h
M	src/base/atomicops-internals-x86.h
M	src/base/atomicops.h
M	src/tests/atomicops_unittest.cc

commit 687207b5fcfadc821a3d5b94f2ea112ca933e476
Author: <EMAIL>
<<EMAIL>@6b5cf1ce-ec42-a296-1ba9-69fdba395a50>
Date:	Sun Mar 10 19:44:43 2013 +0000

    issue-443: Increase the number of objects transferred between thread
    cache and central free list

    This fix is a result of a performance degradation observed in
    multi-threaded programs where large
    amounts of memory (30GB) are consumed, released by a pool of threads
    in a cyclic manner. This was
    mainly due to the amount of time we were spending in the slow path
    consolidating memory between
    the thread cache and central free list. The default has been bumped
    up to 32768 and is now also
    controllable through the TCMALLOC_TRANSFER_NUM_OBJ environment
    setting.


    git-svn-id: http://gperftools.googlecode.com/svn/trunk@193
    6b5cf1ce-ec42-a296-1ba9-69fdba395a50

M	Makefile.am
M	Makefile.in
M	src/common.cc
M	src/common.h
A	src/tests/tcmalloc_unittest.sh

commit 19eff86f850e54683c8868316e362af8919df18a
Author: <EMAIL>
<<EMAIL>@6b5cf1ce-ec42-a296-1ba9-69fdba395a50>
Date:	Sat Mar 9 01:16:17 2013 +0000

    actually made svn:ignore work



    git-svn-id: http://gperftools.googlecode.com/svn/trunk@192
    6b5cf1ce-ec42-a296-1ba9-69fdba395a50

commit 177a896a8273ae73affc48a126fcf9a3ba91f502
Author: <EMAIL>
<<EMAIL>@6b5cf1ce-ec42-a296-1ba9-69fdba395a50>
Date:	Sat Mar 9 01:06:58 2013 +0000

    added proper svn:ignore



    git-svn-id: http://gperftools.googlecode.com/svn/trunk@191
    6b5cf1ce-ec42-a296-1ba9-69fdba395a50

commit b96203b591f63d24cd2979241b18534d22358bca
Author: <EMAIL>
<<EMAIL>@6b5cf1ce-ec42-a296-1ba9-69fdba395a50>
Date:	Sat Dec 22 20:14:08 2012 +0000

    issue-461: Fix to malloc_extension.h so that it builds with
    -std=gnu++98


    git-svn-id: http://gperftools.googlecode.com/svn/trunk@190
    6b5cf1ce-ec42-a296-1ba9-69fdba395a50

M	src/gperftools/malloc_extension.h

commit 84b983c8d43f43a3c7f71d45d51fc4adcc688cd9
Author: <EMAIL>
<<EMAIL>@6b5cf1ce-ec42-a296-1ba9-69fdba395a50>
Date:	Sat Dec 22 20:06:47 2012 +0000

    issue-465: Adding automagic support for __builtin_expect

    Previously __builtin_ expect was based on a macro check against
    gcc version.
    Now we perform the check via AM which is a cleaner approach. There
    are also
    a number of code changes here to utilize LIKELY/UNLIKELY macros
    based on
    __builtin_expect to improve performance.


    git-svn-id: http://gperftools.googlecode.com/svn/trunk@189
    6b5cf1ce-ec42-a296-1ba9-69fdba395a50

M	configure
M	configure.ac
M	src/base/basictypes.h
M	src/common.h
M	src/config.h.in
M	src/tcmalloc.cc

commit a5dacccd6ae4cbfedb5263bfe0f325f03c7f0db8
Author: <EMAIL>
<<EMAIL>@6b5cf1ce-ec42-a296-1ba9-69fdba395a50>
Date:	Sat Dec 22 19:06:35 2012 +0000

    issue-466: Clarified stats output and comments for ExtractStats()
    and GetThreadStats()


    git-svn-id: http://gperftools.googlecode.com/svn/trunk@188
    6b5cf1ce-ec42-a296-1ba9-69fdba395a50

M	src/tcmalloc.cc
M	src/thread_cache.h

commit 09d97533b09e473c0cdd269e8cf4e9a9737e49fa
Author: <EMAIL>
<<EMAIL>@6b5cf1ce-ec42-a296-1ba9-69fdba395a50>
Date:	Sat Dec 22 19:02:52 2012 +0000

    issue-467: Fixed issue with allocation size being narrowed to 32-bit



    git-svn-id: http://gperftools.googlecode.com/svn/trunk@187
    6b5cf1ce-ec42-a296-1ba9-69fdba395a50

M	src/tcmalloc.cc

commit 990889e6232ff3787f1d42d4091a0478ffb93988
Author: <EMAIL>
<<EMAIL>@6b5cf1ce-ec42-a296-1ba9-69fdba395a50>
Date:	Sat Dec 22 18:53:18 2012 +0000

    issue-470: Allows the default system allocator to be overidden

    This is useful when you run into cases where you need an allocator
    before tcmalloc has been fully initialized.


    git-svn-id: http://gperftools.googlecode.com/svn/trunk@186
    6b5cf1ce-ec42-a296-1ba9-69fdba395a50

M	src/system-alloc.cc

commit 3bf5f55d78769d2f8525626dd662457d2f014151
Author: <EMAIL>
<<EMAIL>@6b5cf1ce-ec42-a296-1ba9-69fdba395a50>
Date:	Sat Dec 22 18:38:48 2012 +0000

    issue-482: Adds support for CACHELINE_ALIGNED value for arm based
    devices

    Hardcoded the cacheline size to 32 because this value is supported
    by several
    arm devices, however a better logic needs to be implemented at some
    point to
    handle more arm devices.


    git-svn-id: http://gperftools.googlecode.com/svn/trunk@185
    6b5cf1ce-ec42-a296-1ba9-69fdba395a50

M	src/base/basictypes.h

commit 6856d1d1b2a807efd84dcafa10b865b6de22bc28
Author: <EMAIL>
<<EMAIL>@6b5cf1ce-ec42-a296-1ba9-69fdba395a50>
Date:	Sat Dec 22 18:34:43 2012 +0000

    issue-488: Performance improvement for initialization checks

    These came in from the Google-internal version of tcmalloc. They
    saw some
    decent speed improvements, both on microbenchmarks and big
    programs. The
    idea is to improve the speed of the "Is everything initialized?" type
    of
    code, that's at the start of all allocations and deallocations.


    git-svn-id: http://gperftools.googlecode.com/svn/trunk@184
    6b5cf1ce-ec42-a296-1ba9-69fdba395a50

M	src/static_vars.h
M	src/tcmalloc.cc
M	src/thread_cache.cc
M	src/thread_cache.h

commit ad5aa05838121d52ad1fde5463a796c3320fe067
Author: <EMAIL>
<<EMAIL>@6b5cf1ce-ec42-a296-1ba9-69fdba395a50>
Date:	Sat Dec 22 18:25:58 2012 +0000

    issue-483: Speed up accesses to ClassIndex()

    Making its return type unsigned can save a conversion from
    signed to unsigned, and getting rid of the ternary operators
    seems to help a little bit as well. Various gcc versions weren't
    generating conditional moves for them as one would expect.


    git-svn-id: http://gperftools.googlecode.com/svn/trunk@183
    6b5cf1ce-ec42-a296-1ba9-69fdba395a50

M	src/base/basictypes.h
M	src/common.h

commit 8de78fd85b69bc569ac8fc9e75144e02f5cae851
Author: <EMAIL>
<<EMAIL>@6b5cf1ce-ec42-a296-1ba9-69fdba395a50>
Date:	Mon Nov 5 04:45:01 2012 +0000

    issue-452: Adds a control mechanism to the cpu profiler to be switched
    on and off using a user defined signal.

    CPUPROFILESIGNAL - Takes a signal number between the value of 1 and
    64 inclusive which represents a signal
		       number as defined by signal.h. The signal must
		       not be in use by the program. Sending this
		       signal to the program turns profiling on and off
		       like a switch. By default the switch is
		       off when the program starts. Successive profiles
		       are suffixed with a monotonically increasing
		       number.


    git-svn-id: http://gperftools.googlecode.com/svn/trunk@182
    6b5cf1ce-ec42-a296-1ba9-69fdba395a50

M	doc/cpuprofile.html
M	src/profiler.cc

commit 46f06ca0db41d3f598750caefdaf3c6063969e64
Author: <EMAIL>
<<EMAIL>@6b5cf1ce-ec42-a296-1ba9-69fdba395a50>
Date:	Sun Nov 4 23:08:17 2012 +0000

    issue-451: Fixed incorrect assembly for 64-bit barrier load and
    store on windows platforms.


    git-svn-id: http://gperftools.googlecode.com/svn/trunk@181
    6b5cf1ce-ec42-a296-1ba9-69fdba395a50

M	src/base/atomicops-internals-windows.h

commit 5fe91d5623c2351ba4675db71822fc6be5e2cbce
Author: <EMAIL>
<<EMAIL>@6b5cf1ce-ec42-a296-1ba9-69fdba395a50>
Date:	Sun Nov 4 22:53:01 2012 +0000

    issue-450: Move includes for struct mallinfo from tcmalloc.cc to
    tcmalloc.h to fix compiler warnings from clang.


    git-svn-id: http://gperftools.googlecode.com/svn/trunk@180
    6b5cf1ce-ec42-a296-1ba9-69fdba395a50

M	src/gperftools/tcmalloc.h.in
M	src/tcmalloc.cc

commit 86a55316baf2a7c0718670fd7342b648e0ef4a87
Author: <EMAIL>
<<EMAIL>@6b5cf1ce-ec42-a296-1ba9-69fdba395a50>
Date:	Sun Nov 4 19:12:42 2012 +0000

    issue-449: Disables mmap check when cross compiling since it is
    meaningless anyways

    git-svn-id: http://gperftools.googlecode.com/svn/trunk@179
    6b5cf1ce-ec42-a296-1ba9-69fdba395a50

M	configure
M	configure.ac

commit 6f6c2bf68576e43491884364fc05c9ed752adb4f
Author: <EMAIL>
<<EMAIL>@6b5cf1ce-ec42-a296-1ba9-69fdba395a50>
Date:	Sun Nov 4 19:02:15 2012 +0000

    issue-448: New environment setting that allows you to set an explicit
    heap limit

    TCMALLOC_HEAP_LIMIT_MB - The maximum amount of heap memory that
    tcmalloc can use.
    TCMALLOC_DISABLE_MEMORY_RELEASE - emulate platform with no
    MADV_DONTNEED support (generally for testing purposes).


    git-svn-id: http://gperftools.googlecode.com/svn/trunk@178
    6b5cf1ce-ec42-a296-1ba9-69fdba395a50

M	src/page_heap.cc
M	src/page_heap.h
M	src/system-alloc.cc
M	src/system-alloc.h
M	src/tests/page_heap_test.cc
M	src/tests/tcmalloc_unittest.cc
M	src/windows/port.cc

commit 644b1c6e355a0f5dd948ca482a575f49a4bd2032
Author: <EMAIL>
<<EMAIL>@6b5cf1ce-ec42-a296-1ba9-69fdba395a50>
Date:	Sun Nov 4 18:30:05 2012 +0000

    issue-445: Fix for wrong cmpx instruction in OSAtomicCompareAndSwap64
    for ppc64


    git-svn-id: http://gperftools.googlecode.com/svn/trunk@177
    6b5cf1ce-ec42-a296-1ba9-69fdba395a50

M	src/base/atomicops-internals-linuxppc.h

commit e32bb2d9a76dc5ee42ee41a2ca2c6e5caece0150
Author: <EMAIL>
<<EMAIL>@6b5cf1ce-ec42-a296-1ba9-69fdba395a50>
Date:	Sun Nov 4 18:24:46 2012 +0000

    issue-444: Fix for invalid conversion build error in signal handler
    code


    git-svn-id: http://gperftools.googlecode.com/svn/trunk@176
    6b5cf1ce-ec42-a296-1ba9-69fdba395a50

M	src/base/linux_syscall_support.h

commit abeaf46028c8dfab7e7867ee7a3a49ebe21cf129
Author: <EMAIL>
<<EMAIL>@6b5cf1ce-ec42-a296-1ba9-69fdba395a50>
Date:	Sun Nov 4 18:15:11 2012 +0000

    issue-430: Introduces 8-byte alignment support for tcmalloc


    git-svn-id: http://gperftools.googlecode.com/svn/trunk@175
    6b5cf1ce-ec42-a296-1ba9-69fdba395a50

M	src/common.cc
M	src/common.h
M	src/tests/tcmalloc_unittest.cc

commit baaf0188295582ca68df03f70baa13d96a88e2eb
Author: <EMAIL>
<<EMAIL>@6b5cf1ce-ec42-a296-1ba9-69fdba395a50>
Date:	Sun Nov 4 17:41:47 2012 +0000

    issue-473: Fix for make install error regarding
    src/windows/google/tcmalloc.h


    git-svn-id: http://gperftools.googlecode.com/svn/trunk@174
    6b5cf1ce-ec42-a296-1ba9-69fdba395a50

M	Makefile.am
M	Makefile.in

commit 1363bc69550f43af693f6455118432988042a8dd
Author: <EMAIL>
<<EMAIL>@6b5cf1ce-ec42-a296-1ba9-69fdba395a50>
Date:	Sun Nov 4 17:21:00 2012 +0000

    issue-475: Re-applying changes made originally for r150 and r151

    - Used aclocal, autoupdate, autoconf, and automake to
    correctly apply changes made to Makefile.am. Detailed
    instructions on this procedure can be found here
    http://inti.sourceforge.net/tutorial/libinti/autotoolsproject.html.
    - Fixed a number of error/warning messages due to use of newer
    aclocal, autoconf, and automake utilities.
    - Directly and indirectly related to issue-385 and issue-480.



    git-svn-id: http://gperftools.googlecode.com/svn/trunk@173
    6b5cf1ce-ec42-a296-1ba9-69fdba395a50

M	Makefile.am
M	Makefile.in
M	aclocal.m4
M	configure
M	configure.ac
M	m4/libtool.m4

commit 1243295cdcc20f2f47df9bdc9b8bb50b69c085b0
Author: <EMAIL>
<<EMAIL>@6b5cf1ce-ec42-a296-1ba9-69fdba395a50>
Date:	Sun Nov 4 15:59:08 2012 +0000

    issue-475: Amend of r151 and r150

    In revisions 151 and 150 an attempt was made to enable frame pointers
    by default for i386. However, in the process of doing so a number of
    files were inadvertently touched as a result of running autogen.sh. As
    a result, I have needed to roll back these revisions so that I can
    reattempt the change.


    git-svn-id: http://gperftools.googlecode.com/svn/trunk@172
    6b5cf1ce-ec42-a296-1ba9-69fdba395a50

M	INSTALL
M	Makefile.am
M	Makefile.in
M	aclocal.m4
M	config.guess
M	config.sub
M	configure
M	configure.ac
M	depcomp
M	install-sh
M	ltmain.sh
M	m4/libtool.m4
M	m4/ltoptions.m4
M	m4/ltversion.m4
M	m4/lt~obsolete.m4
M	missing
M	mkinstalldirs

commit 5c17459c5e7b4133a2dec5ffe6a9237a114f5378
Author: <EMAIL>
<<EMAIL>@6b5cf1ce-ec42-a296-1ba9-69fdba395a50>
Date:	Sat Nov 3 16:14:25 2012 +0000

    issue-476: Amendment to r152:issue-437 to fix a build error due to
    PR_SET_TRACER not being defined in older glibc releases.

    git-svn-id: http://gperftools.googlecode.com/svn/trunk@171
    6b5cf1ce-ec42-a296-1ba9-69fdba395a50

M	src/base/linuxthreads.cc

commit 6287bbbbad8730712cfd1ee28ecc0648cbaa9f94
Author: <EMAIL>
<<EMAIL>@6b5cf1ce-ec42-a296-1ba9-69fdba395a50>
Date:	Sat Nov 3 15:56:27 2012 +0000

    issue-477: Fix clang compilation errors regarding format specifiers

    git-svn-id: http://gperftools.googlecode.com/svn/trunk@170
    6b5cf1ce-ec42-a296-1ba9-69fdba395a50

M	src/base/sysinfo.cc
M	src/debugallocation.cc
M	src/getpc.h
M	src/heap-checker.cc
M	src/heap-profile-table.cc
M	src/heap-profiler.cc
M	src/malloc_extension.cc
M	src/memory_region_map.cc
M	src/symbolize.cc
M	src/tcmalloc.cc
M	src/tests/markidle_unittest.cc
M	src/tests/tcmalloc_unittest.cc

commit 87699c97cc07b30fc2765415853ae685f7bc45f2
Author: <EMAIL>
<<EMAIL>@6b5cf1ce-ec42-a296-1ba9-69fdba395a50>
Date:	Sat Nov 3 15:40:01 2012 +0000

    issue-478: Added the heap profiler tunnable setting
    HEAP_PROFILER_TIME_INTERVAL used to dump heap profiles on a specified
    time interval.

    git-svn-id: http://gperftools.googlecode.com/svn/trunk@169
    6b5cf1ce-ec42-a296-1ba9-69fdba395a50

M	doc/heapprofile.html
M	src/heap-profiler.cc

commit 4eb6946d689bbaa74f8be78265f6d6ff9ae04ab2
Author: <EMAIL>
<<EMAIL>@6b5cf1ce-ec42-a296-1ba9-69fdba395a50>
Date:	Sat Nov 3 15:27:58 2012 +0000

    issue-479: Replaced sscanf usage while iterating proc maps entries
    to fix a deadlock introduced by glibc-2.15.

    git-svn-id: http://gperftools.googlecode.com/svn/trunk@168
    6b5cf1ce-ec42-a296-1ba9-69fdba395a50

M	src/base/sysinfo.cc

commit 5dd53ab6cbf9d98f2d60546835e84785a104da46
Author: <EMAIL>
<<EMAIL>@6b5cf1ce-ec42-a296-1ba9-69fdba395a50>
Date:	Sat Nov 3 14:52:42 2012 +0000

    issue-481: Replaced test mechanism for distinct address spaces with
    a more reliable mechanism

    Rather than using sys_ptrace+PTRACE_PEEK_DATA to determine whether
    address spaces are distinct, we now use sys_waitpid+__WCLONE. See
    issue-481 for a more detailed rationale.


    git-svn-id: http://gperftools.googlecode.com/svn/trunk@167
    6b5cf1ce-ec42-a296-1ba9-69fdba395a50

M	src/base/linuxthreads.cc

commit 6c436203522156699ef11c6792ed481e02cef366
Author: <EMAIL>
<<EMAIL>@6b5cf1ce-ec42-a296-1ba9-69fdba395a50>
Date:	Sat Nov 3 14:36:33 2012 +0000

    amend-r165: forgot to regenrate configure and Makefile.in

    git-svn-id: http://gperftools.googlecode.com/svn/trunk@166
    6b5cf1ce-ec42-a296-1ba9-69fdba395a50

M	Makefile.in
M	configure

commit c566266b10d2fc4e0c67fec25e2a45ebad803196
Author: <EMAIL>
<<EMAIL>@6b5cf1ce-ec42-a296-1ba9-69fdba395a50>
Date:	Sat Nov 3 14:13:21 2012 +0000

    issue-480: duplicate of issue-385 fixed in r150 however some of the
    comments in the code
    regarding frame pointers needed to be clarified.


    git-svn-id: http://gperftools.googlecode.com/svn/trunk@165
    6b5cf1ce-ec42-a296-1ba9-69fdba395a50

M	Makefile.am
M	configure.ac

commit 4f325fbcc9f0db17fbf108188a32b2c67bcd38e0
Author: <EMAIL>
<<EMAIL>@6b5cf1ce-ec42-a296-1ba9-69fdba395a50>
Date:	Sun Oct 28 19:45:03 2012 +0000

    issue-424: Updated heap profiler documentation that had
    mispelling for the heap profiler mmap only environment setting
    (HEAP_PROFILE_ONLY_MMAP)

    git-svn-id: http://gperftools.googlecode.com/svn/trunk@164
    6b5cf1ce-ec42-a296-1ba9-69fdba395a50

M	doc/heapprofile.html

commit a828c0a8ff59300fffce466cc617d40fa39dedea
Author: <EMAIL>
<<EMAIL>@6b5cf1ce-ec42-a296-1ba9-69fdba395a50>
Date:	Sun Oct 28 19:30:23 2012 +0000

    issue-417: This commit removed the fatal error message printed along
    with the pprof usage information when pprof is run with no arguments.

    git-svn-id: http://gperftools.googlecode.com/svn/trunk@163
    6b5cf1ce-ec42-a296-1ba9-69fdba395a50

M	src/pprof

commit e5b095abdc2368111f53cedf376cd1505a2c7583
Author: <EMAIL>
<<EMAIL>@6b5cf1ce-ec42-a296-1ba9-69fdba395a50>
Date:	Sun Oct 28 18:28:21 2012 +0000

    issue-411: This commit adds additional logging to the cpu profiler
    to warn when the profiler is run and no CPUPROFILE environment
    setting can be found. It also adds a new environment variable
    PERFTOOLS_UNITTEST to allow certain modules to take action when
    running under the umbrella of a unit test.

    git-svn-id: http://gperftools.googlecode.com/svn/trunk@162
    6b5cf1ce-ec42-a296-1ba9-69fdba395a50

M	src/profiler.cc
M	src/tests/profiler_unittest.sh

commit 57c48e9b5ffdb0f177c07ea8420c4072e816e1a7
Author: <EMAIL>
<<EMAIL>@6b5cf1ce-ec42-a296-1ba9-69fdba395a50>
Date:	Sun Oct 28 14:51:41 2012 +0000

    issue-405: backed out original commit made in revision 147 due to
    the fact that it didn't work as intended

    git-svn-id: http://gperftools.googlecode.com/svn/trunk@161
    6b5cf1ce-ec42-a296-1ba9-69fdba395a50

M	src/heap-checker.cc

commit 3e296c28c3c2b67df624d372b75650ae65c87b04
Author: <EMAIL>
<<EMAIL>@6b5cf1ce-ec42-a296-1ba9-69fdba395a50>
Date:	Tue Sep 18 02:47:24 2012 +0000

    issue-442: Fixes an issue encountered on PPC with undefined
    conversions from 'base::subtle::Atomic64*' to 'const volatile
    Atomic32*'

    git-svn-id: http://gperftools.googlecode.com/svn/trunk@160
    6b5cf1ce-ec42-a296-1ba9-69fdba395a50

M	src/base/spinlock_internal.cc

commit 1f59bb34d3ce03df51c8356dd44fcb0b15de4769
Author: <EMAIL>
<<EMAIL>@6b5cf1ce-ec42-a296-1ba9-69fdba395a50>
Date:	Tue Sep 18 02:33:00 2012 +0000

    Adding support for CPU frequencies for Linux on PowerPC

    git-svn-id: http://gperftools.googlecode.com/svn/trunk@159
    6b5cf1ce-ec42-a296-1ba9-69fdba395a50

M	src/base/sysinfo.cc

commit a2fec7c694ce992a3b7d3fe4205bb8710b017bab
Author: <EMAIL>
<<EMAIL>@6b5cf1ce-ec42-a296-1ba9-69fdba395a50>
Date:	Tue Sep 18 01:43:02 2012 +0000

    issue-446: Fixes compatability issues in malloc_hook_test for PPC64

    git-svn-id: http://gperftools.googlecode.com/svn/trunk@158
    6b5cf1ce-ec42-a296-1ba9-69fdba395a50

M	src/tests/malloc_hook_test.cc

commit b05b6fc7655ba7fee8397da7013db007f0c47e30
Author: <EMAIL>
<<EMAIL>@6b5cf1ce-ec42-a296-1ba9-69fdba395a50>
Date:	Tue Sep 18 01:27:34 2012 +0000

    issue-458: Optimizes malloc hooks for weakly consistent memory
    architectures

    git-svn-id: http://gperftools.googlecode.com/svn/trunk@157
    6b5cf1ce-ec42-a296-1ba9-69fdba395a50

M	src/malloc_hook-inl.h

commit 00edf667f62e17a268f03c6aaef19946add75dcf
Author: <EMAIL>
<<EMAIL>@6b5cf1ce-ec42-a296-1ba9-69fdba395a50>
Date:	Tue Sep 18 01:18:15 2012 +0000

    issue-456: Adds support for CACHELINE_ALIGNED values for PPC

    git-svn-id: http://gperftools.googlecode.com/svn/trunk@156
    6b5cf1ce-ec42-a296-1ba9-69fdba395a50

M	src/base/basictypes.h

commit 731d0932a081d521a36462df7dcbc45fa4221011
Author: <EMAIL>
<<EMAIL>@6b5cf1ce-ec42-a296-1ba9-69fdba395a50>
Date:	Tue Sep 18 00:59:16 2012 +0000

    issue-455: Adds another size for large pages in tcmalloc through
    the new define TCMALLOC_LARGE_PAGES64K

    git-svn-id: http://gperftools.googlecode.com/svn/trunk@155
    6b5cf1ce-ec42-a296-1ba9-69fdba395a50

M	src/common.h

commit 771b6bcf689bd089b802ddcdb27a40b4fb4a8e3b
Author: <EMAIL>
<<EMAIL>@6b5cf1ce-ec42-a296-1ba9-69fdba395a50>
Date:	Tue Sep 18 00:52:13 2012 +0000

    issue-457: Fixed an issue where the 'getpc' testcase was using the
    wrong PPC64 preprocessor directive

    git-svn-id: http://gperftools.googlecode.com/svn/trunk@154
    6b5cf1ce-ec42-a296-1ba9-69fdba395a50

M	src/tests/getpc_test.cc

commit 711232a1ef4997305a681287191de74f6597dabc
Author: <EMAIL>
<<EMAIL>@6b5cf1ce-ec42-a296-1ba9-69fdba395a50>
Date:	Tue Sep 18 00:42:23 2012 +0000

    issue-453 Added support to get the timebase register value using
    just one instruction and also adjusts the PPC32 code to the recent
    GLIBC one that implements the same functionality

    git-svn-id: http://gperftools.googlecode.com/svn/trunk@153
    6b5cf1ce-ec42-a296-1ba9-69fdba395a50

M	src/base/cycleclock.h

commit fa0209f261c5e065d523bb1858f84fd91eb2f39a
Author: <EMAIL>
<<EMAIL>@6b5cf1ce-ec42-a296-1ba9-69fdba395a50>
Date:	Tue Sep 18 00:00:20 2012 +0000

    issue-437 Fixed issues related to new glibc shipped with Ubuntu 10.10

    1. ptrace permissions were modifed to be a bit more strict which
    required
       us to programatically set the permissions while syncing up to
       the profiling
       thread.

    2. Order of destructors registered with atexit changed which was
    casuing us to
       miss generating the backtrace when heap checker was finished. Seems
       that we
       initially fixed this for FreeBSD and now linux has changed their
       behaviour
       to be the same. We are now a bit stricter on the rules here
       accross all
       platforms.


    git-svn-id: http://gperftools.googlecode.com/svn/trunk@152
    6b5cf1ce-ec42-a296-1ba9-69fdba395a50

M	src/base/linuxthreads.cc
M	src/heap-checker.cc

commit cd723b43ff783a05321f0c0ba79a82494185b23c
Author: <EMAIL>
<<EMAIL>@6b5cf1ce-ec42-a296-1ba9-69fdba395a50>
Date:	Wed May 16 12:24:13 2012 +0000

    issue-385: updates to automake to pick up the explicit setting of
    the -fno-omit-frame-pointer for i386

    git-svn-id: http://gperftools.googlecode.com/svn/trunk@151
    6b5cf1ce-ec42-a296-1ba9-69fdba395a50

M	INSTALL
M	Makefile.am
M	Makefile.in
M	aclocal.m4
M	config.guess
M	config.sub
M	configure
M	configure.ac
M	depcomp
M	install-sh
M	ltmain.sh
M	m4/libtool.m4
M	m4/ltoptions.m4
M	m4/ltversion.m4
M	m4/lt~obsolete.m4
M	missing
M	mkinstalldirs

commit 0c0382234d5812474b070b6eccdfd99c98490548
Author: <EMAIL>
<<EMAIL>@6b5cf1ce-ec42-a296-1ba9-69fdba395a50>
Date:	Wed May 16 01:28:08 2012 +0000

    issue-385: as of gcc 4.6 we now need to enable frame pointers for
    i386 platforms since the default is to now omit frame pointers

    git-svn-id: http://gperftools.googlecode.com/svn/trunk@150
    6b5cf1ce-ec42-a296-1ba9-69fdba395a50

M	Makefile.am

commit bafd0f8b7cec4dd0b74a4a8e5456b96744640679
Author: <EMAIL>
<<EMAIL>@6b5cf1ce-ec42-a296-1ba9-69fdba395a50>
Date:	Sat Apr 21 17:27:54 2012 +0000

    Changing internal behaviour of tcmalloc to return an
    allocated size of 0 when the target pointer is null (refer to
    TCMallocImplementation::GetAllocatedSize). This change was reviewed
    as https://codereview.appspot.com/5833050/.

    git-svn-id: http://gperftools.googlecode.com/svn/trunk@149
    6b5cf1ce-ec42-a296-1ba9-69fdba395a50

M	src/tcmalloc.cc

commit 9be2f8d802a19ab6e4b2abed3f7cd84780628d16
Author: <EMAIL>
<<EMAIL>@6b5cf1ce-ec42-a296-1ba9-69fdba395a50>
Date:	Sat Apr 21 16:15:20 2012 +0000

    Adding in further support for 64-bit NoBarrier_Load on windows

    git-svn-id: http://gperftools.googlecode.com/svn/trunk@148
    6b5cf1ce-ec42-a296-1ba9-69fdba395a50

M	src/base/atomicops-internals-windows.h

commit 68df7fa6057a29b3130d41a27592f74d58c2df57
Author: <EMAIL>
<<EMAIL>@6b5cf1ce-ec42-a296-1ba9-69fdba395a50>
Date:	Sat Mar 3 19:45:49 2012 +0000

    issue-405: Added a tunable to the heap checker that allows the heap
    checker error exit code to be overridden. For larger projects this
    allows certain heap checked programs that have intentional/known
    leaks to be managed.

    git-svn-id: http://gperftools.googlecode.com/svn/trunk@147
    6b5cf1ce-ec42-a296-1ba9-69fdba395a50

M	src/heap-checker.cc

commit 0a9d3f8af373c71e5fabdc8c01239c47df4743ed
Author: <EMAIL>
<<EMAIL>@6b5cf1ce-ec42-a296-1ba9-69fdba395a50>
Date:	Fri Mar 2 06:04:08 2012 +0000

    issue-410: This set of changes exposes the central cache free list
    size, transfer cache free list size, and the meta data overhead size
    through the getNumericProperty() function.

    git-svn-id: http://gperftools.googlecode.com/svn/trunk@146
    6b5cf1ce-ec42-a296-1ba9-69fdba395a50

M	src/gperftools/malloc_extension.h
M	src/tcmalloc.cc

commit 1233f0f9101c3f4e85e04711c7223dc7f73ec4fb
Author: csilvers <csilvers@6b5cf1ce-ec42-a296-1ba9-69fdba395a50>
Date:	Tue Feb 21 03:02:43 2012 +0000

    This file is in the tarball, but I forgot to put it in svn as well.
    (It's tricky because it's auto-generated from tcmalloc.h.in at
    tarball-making time.)


    git-svn-id: http://gperftools.googlecode.com/svn/trunk@145
    6b5cf1ce-ec42-a296-1ba9-69fdba395a50

A	src/windows/gperftools/tcmalloc.h

commit ce2891234584069f90e6e0a446e28bf7ca3e031d
Author: csilvers <csilvers@6b5cf1ce-ec42-a296-1ba9-69fdba395a50>
Date:	Sat Feb 4 00:10:11 2012 +0000

    Url-typo in the latest NEWS message.


    git-svn-id: http://gperftools.googlecode.com/svn/trunk@144
    6b5cf1ce-ec42-a296-1ba9-69fdba395a50

M	NEWS

commit 4e9432c5090d5b8354b0b57fca8b96b2efef37e3
Author: csilvers <csilvers@6b5cf1ce-ec42-a296-1ba9-69fdba395a50>
Date:	Sat Feb 4 00:07:36 2012 +0000

    Fri Feb 03 15:40:45 2012  Google
    Inc. <<EMAIL>>

	    * gperftools: version 2.0
	    * Renamed the project from google-perftools to gperftools
	    (csilvers)
	    * Renamed the .deb/.rpm packagse from google-perftools to
	    gperftools too
	    * Renamed include directory from google/ to gperftools/
	    (csilvers)
	    * Changed the 'official' perftools email in setup.py/etc
	    * Renamed google-perftools.sln to gperftools.sln
	    * PORTING: Removed bash-isms & grep -q in
	    heap-checker-death_unittest.sh
	    * Changed copyright text to reflect Google's relinquished
	    ownership


    git-svn-id: http://gperftools.googlecode.com/svn/trunk@142
    6b5cf1ce-ec42-a296-1ba9-69fdba395a50

M	AUTHORS
M	ChangeLog
M	INSTALL
M	Makefile.am
M	Makefile.in
M	NEWS
M	README
M	README_windows.txt
M	aclocal.m4
M	config.guess
M	config.sub
M	configure
M	configure.ac
M	depcomp
M	doc/cpuprofile.html
M	doc/designstyle.css
M	doc/heap_checker.html
M	doc/heapprofile.html
M	doc/index.html
M	doc/pprof.1
M	doc/pprof.see_also
M	doc/pprof_remote_servers.html
R100	google-perftools.sln	gperftools.sln
M	install-sh
M	missing
M	mkinstalldirs
M	packages/deb/changelog
M	packages/deb/control
M	packages/deb/copyright
R071	packages/deb/libgoogle-perftools-dev.dirs
packages/deb/libgperftools-dev.dirs
R079	packages/deb/libgoogle-perftools-dev.install
packages/deb/libgperftools-dev.install
R100	packages/deb/libgoogle-perftools0.dirs
packages/deb/libgperftools0.dirs
R100	packages/deb/libgoogle-perftools0.install
packages/deb/libgperftools0.install
R100	packages/deb/libgoogle-perftools0.manpages
packages/deb/libgperftools0.manpages
M	packages/rpm/rpm.spec
M	src/base/low_level_alloc.cc
M	src/config.h.in
M	src/debugallocation.cc
M	src/google/heap-checker.h
M	src/google/heap-profiler.h
M	src/google/malloc_extension.h
M	src/google/malloc_extension_c.h
M	src/google/malloc_hook.h
M	src/google/malloc_hook_c.h
M	src/google/profiler.h
M	src/google/stacktrace.h
A	src/google/tcmalloc.h
C099	src/google/heap-checker.h	src/gperftools/heap-checker.h
C100	src/google/heap-profiler.h	src/gperftools/heap-profiler.h
C100	src/google/malloc_extension.h	src/gperftools/malloc_extension.h
C100	src/google/malloc_extension_c.h
src/gperftools/malloc_extension_c.h
C099	src/google/malloc_hook.h	src/gperftools/malloc_hook.h
C100	src/google/malloc_hook_c.h	src/gperftools/malloc_hook_c.h
C100	src/google/profiler.h	src/gperftools/profiler.h
C100	src/google/stacktrace.h src/gperftools/stacktrace.h
R098	src/google/tcmalloc.h.in	src/gperftools/tcmalloc.h.in
M	src/heap-checker-bcad.cc
M	src/heap-checker.cc
M	src/heap-profile-table.cc
M	src/heap-profiler.cc
M	src/internal_logging.cc
M	src/libc_override.h
M	src/libc_override_gcc_and_weak.h
M	src/libc_override_glibc.h
M	src/libc_override_osx.h
M	src/malloc_extension.cc
M	src/malloc_hook-inl.h
M	src/malloc_hook.cc
M	src/memfs_malloc.cc
M	src/memory_region_map.cc
M	src/page_heap.cc
M	src/page_heap.h
M	src/pprof
M	src/profiler.cc
M	src/stacktrace.cc
M	src/stacktrace_arm-inl.h
M	src/stacktrace_generic-inl.h
M	src/stacktrace_libunwind-inl.h
M	src/stacktrace_powerpc-inl.h
M	src/stacktrace_win32-inl.h
M	src/stacktrace_x86-inl.h
M	src/system-alloc.cc
M	src/tcmalloc.cc
M	src/tests/current_allocated_bytes_test.cc
M	src/tests/debugallocation_test.cc
M	src/tests/frag_unittest.cc
M	src/tests/heap-checker-death_unittest.sh
M	src/tests/heap-checker_unittest.cc
M	src/tests/heap-profiler_unittest.cc
M	src/tests/low_level_alloc_unittest.cc
M	src/tests/malloc_extension_c_test.c
M	src/tests/malloc_extension_test.cc
M	src/tests/malloc_hook_test.cc
M	src/tests/markidle_unittest.cc
M	src/tests/profiler_unittest.cc
M	src/tests/sampling_test.cc
C059	src/tests/current_allocated_bytes_test.cc
src/tests/simple_compat_test.cc
M	src/tests/stacktrace_unittest.cc
M	src/tests/system-alloc_unittest.cc
M	src/tests/tcmalloc_unittest.cc
M	src/tests/thread_dealloc_unittest.cc
A	src/windows/auto_testing_hook.h
M	src/windows/config.h
A	src/windows/google/tcmalloc.h
R098	src/windows/google/tcmalloc.h.in
src/windows/gperftools/tcmalloc.h.in
M	src/windows/patch_functions.cc
M
vsprojects/current_allocated_bytes_test/current_allocated_bytes_test.vcproj
M	vsprojects/libtcmalloc_minimal/libtcmalloc_minimal.vcproj
M
vsprojects/low_level_alloc_unittest/low_level_alloc_unittest.vcproj
M	vsprojects/malloc_extension_test/malloc_extension_test.vcproj
M	vsprojects/malloc_hook_test/malloc_hook_test.vcproj
M	vsprojects/tmu-static/tmu-static.vcproj

commit e10b720797359e1855a5e4769424988b4e76c3e7
Author: csilvers <csilvers@6b5cf1ce-ec42-a296-1ba9-69fdba395a50>
Date:	Sat Feb 4 00:06:48 2012 +0000

    Last of the remnant empty directories that I don't know why they're
    there.


    git-svn-id: http://gperftools.googlecode.com/svn/trunk@141
    6b5cf1ce-ec42-a296-1ba9-69fdba395a50

commit e55949bba69b7a6eede3468765e86c5ed000be52
Author: csilvers <csilvers@6b5cf1ce-ec42-a296-1ba9-69fdba395a50>
Date:	Sat Feb 4 00:06:06 2012 +0000

    This directory got renamed tmu-static a long time ago.  Get rid of
    this ancient remnant.


    git-svn-id: http://gperftools.googlecode.com/svn/trunk@140
    6b5cf1ce-ec42-a296-1ba9-69fdba395a50

commit 0f588815f979721346a6cb05a1cbd83d670b975a
Author: csilvers <csilvers@6b5cf1ce-ec42-a296-1ba9-69fdba395a50>
Date:	Sat Feb 4 00:05:39 2012 +0000

    Apparently I'm not good at deleting files from svn after they're no
    longer used anymore.  Get rid of a few more that fall into this
    category.


    git-svn-id: http://gperftools.googlecode.com/svn/trunk@139
    6b5cf1ce-ec42-a296-1ba9-69fdba395a50

D	src/stacktrace_nacl-inl.h
D	src/stacktrace_x86_64-inl.h

commit 2b2016f6844a88b9448d445f90ad1c849a56ea23
Author: csilvers <csilvers@6b5cf1ce-ec42-a296-1ba9-69fdba395a50>
Date:	Sat Feb 4 00:03:35 2012 +0000

    I have no idea why I created this directory, but it's never been
    used.  Delete it.


    git-svn-id: http://gperftools.googlecode.com/svn/trunk@138
    6b5cf1ce-ec42-a296-1ba9-69fdba395a50

commit ace2da06732ee549b6d64c1f59b62181f5110230
Author: csilvers <csilvers@6b5cf1ce-ec42-a296-1ba9-69fdba395a50>
Date:	Sat Feb 4 00:02:15 2012 +0000

    This file has been obsolete for almost a year now; I just forgot to
    delete it from svn.


    git-svn-id: http://gperftools.googlecode.com/svn/trunk@137
    6b5cf1ce-ec42-a296-1ba9-69fdba395a50

D	src/base/atomicops-internals-arm-gcc.h

commit 0afb078b34700859cef8d708463fc5c955e26c31
Author: csilvers <csilvers@6b5cf1ce-ec42-a296-1ba9-69fdba395a50>
Date:	Tue Jan 31 19:11:26 2012 +0000

    Tue Jan 31 10:43:50 2012	Google Inc. <<EMAIL>>

	    * google-perftools: version 1.10 release
	    * PORTING: Support for patching assembly on win
	    x86_64! (scott.fr...)
	    * PORTING: Work around atexit-execution-order bug on freebsd
	    (csilvers)
	    * PORTING: Patch _calloc_crt for windows (roger orr)
	    * PORTING: Add C++11 compatibility method for stl allocator
	    (jdennett)
	    * PORTING: use MADV_FREE, not MADV_DONTNEED, on freebsd
	    (csilvers)
	    * PORTING: Don't use SYS_open when not supported on solaris
	    (csilvers)
	    * PORTING: Do not assume uname() returns 0 on success
	    (csilvers)
	    * LSS: Improved ARM support in linux-syscall-support
	    (dougkwan)
	    * LSS: Get rid of unused syscalls in linux-syscall-support
	    (csilvers)
	    * LSS: Fix broken mmap wrapping for ppc (markus)
	    * LSS: Emit .cfi_adjust_cfa_offset when appropriate
	    (ppluzhnikov)
	    * LSS: Be more accurate in register use in __asm__ (markus)
	    * LSS: Fix __asm__ calls to compile under clang (chandlerc)
	    * LSS: Fix ARM inline assembly bug around r7 and swi (lcwu)
	    * No longer log when an allocator fails (csilvers)
	    * void* -> const void* for MallocExtension methods (llib)
	    * Improve HEAP_PROFILE_MMAP and fix bugs with it (dmikurube)
	    * Replace int-based abs with more correct fabs in a test
	    (pmurin)


    git-svn-id: http://gperftools.googlecode.com/svn/trunk@135
    6b5cf1ce-ec42-a296-1ba9-69fdba395a50

M	ChangeLog
M	Makefile.am
M	Makefile.in
M	NEWS
M	README_windows.txt
M	configure
M	configure.ac
M	google-perftools.sln
M	packages/deb/changelog
M	packages/deb/copyright
M	src/pprof
M	src/system-alloc.cc
M	src/tcmalloc.cc
M	src/windows/config.h
M	src/windows/mini_disassembler.h
M	src/windows/patch_functions.cc
M	src/windows/preamble_patcher.h
M	src/windows/preamble_patcher_test.cc
M	src/windows/shortproc.asm
A	vsprojects/preamble_patcher_test/preamble_patcher_test.vcproj

commit e0eaf5981eede6311e311ac5054489b85015c5d7
Author: csilvers <csilvers@6b5cf1ce-ec42-a296-1ba9-69fdba395a50>
Date:	Sat Jan 28 00:13:43 2012 +0000

	    * PORTING: Support for patching assembly on win
	    x86_64! (scott.fr...)
	    * PORTING: Work around atexit-execution-order bug on freebsd
	    (csilvers)
	    * PORTING: Patch _calloc_crt for windows (roger orr)
	    * PORTING: Add C++11 compatibility method for stl allocator
	    (jdennett)
	    * PORTING: use MADV_FREE, not MADV_DONTNEED, on freebsd
	    (csilvers)
	    * PORTING: Don't use SYS_open when not supported on solaris
	    (csilvers)
	    * PORTING: Do not assume uname() returns 0 on success
	    (csilvers)
	    * LSS: Improved ARM support in linux-syscall-support
	    (dougkwan)
	    * LSS: Get rid of unused syscalls in linux-syscall-support
	    (csilvers)
	    * LSS: Fix broken mmap wrapping for ppc (markus)
	    * LSS: Emit .cfi_adjust_cfa_offset when appropriate
	    (ppluzhnikov)
	    * LSS: Be more accurate in register use in __asm__ (markus)
	    * LSS: Fix __asm__ calls to compile under clang (chandlerc)
	    * LSS: Fix ARM inline assembly bug around r7 and swi (lcwu)
	    * No longer log when an allocator fails (csilvers)
	    * void* -> const void* for MallocExtension methods (llib)
	    * Improve HEAP_PROFILE_MMAP and fix bugs with it (dmikurube)
	    * Replace int-based abs with more correct fabs in a test
	    (pmurin)


    git-svn-id: http://gperftools.googlecode.com/svn/trunk@134
    6b5cf1ce-ec42-a296-1ba9-69fdba395a50

M	NEWS
M	configure
M	configure.ac
M	doc/cpuprofile.html
M	src/base/atomicops-internals-arm-v6plus.h
M	src/base/linux_syscall_support.h
M	src/base/stl_allocator.h
M	src/base/sysinfo.cc
M	src/debugallocation.cc
M	src/google/malloc_extension.h
M	src/google/malloc_extension_c.h
M	src/heap-checker.cc
M	src/heap-profile-table.cc
M	src/heap-profile-table.h
M	src/heap-profiler.cc
M	src/malloc_extension.cc
M	src/memory_region_map.cc
M	src/memory_region_map.h
M	src/system-alloc.cc
M	src/tcmalloc.cc
M	src/tests/sampler_test.cc
M	src/thread_cache.cc
M	src/windows/ia32_opcode_map.cc
M	src/windows/mini_disassembler.cc
M	src/windows/mini_disassembler.h
M	src/windows/mini_disassembler_types.h
M	src/windows/patch_functions.cc
M	src/windows/preamble_patcher.cc
M	src/windows/preamble_patcher.h
A	src/windows/preamble_patcher_test.cc
M	src/windows/preamble_patcher_with_stub.cc
A	src/windows/shortproc.asm

commit 4fa02db374683d3c69c1b36158534c011513ed31
Author: csilvers <csilvers@6b5cf1ce-ec42-a296-1ba9-69fdba395a50>
Date:	Sat Dec 24 01:00:32 2011 +0000

    Ready to release perftools 1.9.1  :-/

    Added the ARM stacktrace file to the tarball (for 'make dist')


    git-svn-id: http://gperftools.googlecode.com/svn/trunk@132
    6b5cf1ce-ec42-a296-1ba9-69fdba395a50

M	Makefile.am
M	Makefile.in
M	NEWS
M	configure
M	configure.ac

commit 2a7b3b88371c2f78ab31be202f384fd0ed466890
Author: csilvers <csilvers@6b5cf1ce-ec42-a296-1ba9-69fdba395a50>
Date:	Fri Dec 23 00:45:49 2011 +0000

    Thu Dec 22 16:22:45 2011	Google Inc. <<EMAIL>>

	    * google-perftools: version 1.9 release
	    * Lightweight check for double-frees (blount)
	    * BUGFIX: Fix pprof to exit properly if run with no args
	    (dagitses)
	    * Suggest ASan as a way to diagnose buggy code (ppluzhnikov)
	    * Get rid of unused CACHELINE_SIZE (csilvers)
	    * Replace atexit() calls with global dtors; helps freebsd
	    (csilvers)
	    * Disable heap-checker under AddressSanitizer (kcc)
	    * Fix bug in powerpc stacktracing (ppluzhnikov)
	    * PERF: Use exponential backoff waiting for spinlocks (m3b)
	    * Fix 64-bit nm on 32-bit binaries in pprof (csilvers)
	    * Add ProfileHandlerDisallowForever (rsc)
	    * BUGFIX: Shell escape when forking in pprof (csilvers)
	    * No longer combine overloaded functions in pprof (csilvers)
	    * Fix address-normalizing bug in pprof (csilvers)
	    * More consistently call abort() instead of exit() on failure
	    (csilvers)
	    * Allow NoGlobalLeaks to be safely called more than once
	    (csilvers)
	    * PORTING/BUGFIX: Fix ARM cycleclock to use volatile asm
	    (dougkwan)
	    * PORTING: 64-bit atomic ops for ARMv7 (dougkwan)
	    * PORTING: Implement stacktrace for ARM (dougkwan)
	    * PORTING: Fix malloc_hook_mmap_linux for ARM (dougkwan)
	    * PORTING: Update linux_syscall_support.h for ARM/etc
	    (evannier, sanek)
	    * PORTING: Fix freebsd to work on x86_64 (<EMAIL>)
	    * PORTING: Added additional SYS_mmap fixes for FreeBSD
	    (chappedm)
	    * PORTING: Allow us to compile on OS X 10.6 and run on 10.5
	    (raltherr)
	    * PORTING: Check for mingw compilers that *do* define timespec
	    * PORTING: Add "support" for MIPS cycletimer
	    * PORTING: Fix fallback cycle-timer to work with Now
	    (dougkwan)
	    * PERF: Move stack trace collecting out of the mutex (taylorc)
	    * PERF: Get the deallocation stack trace outside the mutex
	    (sean)
	    * Make PageHeap dynamically allocated for leak checks (maxim)
	    * BUGFIX: Fix probing of nm -f behavior in pprof (dpeng)
	    * BUGFIX: Fix a race with the CentralFreeList lock before main
	    (sanjay)
	    * Support /pprof/censusprofile url arguments (rajatjain)
	    * Change IgnoreObject to return its argument (nlewycky)
	    * Update malloc-hook files to support more CPUs
	    * BUGFIX: write our own strstr to avoid libc problems
	    (csilvers)
	    * Use simple callgrind compression facility in pprof
	    * Print an error message when we can't run pprof to symbolize
	    (csilvers)
	    * Die in configure when g++ is't installed (csilvers)
	    * DOC: Beef up the documentation a bit about using libunwind
	    (csilvers)


    git-svn-id: http://gperftools.googlecode.com/svn/trunk@130
    6b5cf1ce-ec42-a296-1ba9-69fdba395a50

M	ChangeLog
M	INSTALL
M	NEWS
M	configure
M	configure.ac
M	packages/deb/changelog
M	src/pprof
M	src/windows/config.h

commit 75f5a791bc1c0af0ddf43453c18f44723884f37e
Author: csilvers <csilvers@6b5cf1ce-ec42-a296-1ba9-69fdba395a50>
Date:	Fri Dec 23 00:45:14 2011 +0000

	    Add the magical incantations to get perftools to compile
	    cleanly under windows.  The only non-trivial one is the #undef
	    of small, which I needed to compile under the latest cygwin --
	    I ran g++ with -dD under cygwin and saw the '#define small
	    char' with my own eyes.  I wouldn't have thought it...


    git-svn-id: http://gperftools.googlecode.com/svn/trunk@129
    6b5cf1ce-ec42-a296-1ba9-69fdba395a50

M	src/internal_logging.h
M	src/tcmalloc.cc
M	src/windows/port.h

commit 76131b04cf41a7f0fed99503f985847fc261b930
Author: csilvers <csilvers@6b5cf1ce-ec42-a296-1ba9-69fdba395a50>
Date:	Fri Dec 23 00:44:53 2011 +0000

	    In my testing for a new release, I was noticing hangs on
	    cygwin, that went away when I turned off tls support.  Since I
	    didn't use to have such problems, I am being conservative and
	    only turning off TLS for the particular version of cygwin I'm
	    testing on (as returned by uname()).


    git-svn-id: http://gperftools.googlecode.com/svn/trunk@128
    6b5cf1ce-ec42-a296-1ba9-69fdba395a50

M	src/thread_cache.cc

commit 3d30fbcc85021b267f7e3faf4c2280d70681920c
Author: csilvers <csilvers@6b5cf1ce-ec42-a296-1ba9-69fdba395a50>
Date:	Fri Dec 23 00:44:30 2011 +0000

	    We use mmx instructions now in perftools, so specify -xmms for
	    gcc on i386, where it's not on by default (it is for
	    gcc/x86_64, in my tests).  This could potentially cause an
	    error for embedded systems, which can have i386 but no mms,
	    but the code wouldn't run properly on them anyway without
	    tweaks.


    git-svn-id: http://gperftools.googlecode.com/svn/trunk@127
    6b5cf1ce-ec42-a296-1ba9-69fdba395a50

M	Makefile.am
M	Makefile.in

commit f622d261e9a4331724d222cc2a41b517607c07d7
Author: csilvers <csilvers@6b5cf1ce-ec42-a296-1ba9-69fdba395a50>
Date:	Tue Dec 20 18:43:38 2011 +0000

	    * Fix ARM cycleclock to use volatile asm (dougkwan)
	    * BUGFIX: Define BASE_HAS_ATOMIC64 for ARMv7 (dougkwan)
	    * Fix fallback cycle-timer to work with Now (dougkwan)
	    * Fix pprof to exit properly if run with no args (dagitses)
	    * Suggest ASan as a way to diagnose buggy code (ppluzhnikov)
	    * Get rid of unused CACHELINE_SIZE (csilvers)



    git-svn-id: http://gperftools.googlecode.com/svn/trunk@126
    6b5cf1ce-ec42-a296-1ba9-69fdba395a50

M	src/base/atomicops-internals-arm-v6plus.h
M	src/base/basictypes.h
M	src/base/cycleclock.h
M	src/debugallocation.cc
M	src/malloc_hook_mmap_freebsd.h
M	src/pprof
M	src/windows/port.h

commit 7da02003014644f73893df8530ee788dc0e626de
Author: <EMAIL>
<<EMAIL>@6b5cf1ce-ec42-a296-1ba9-69fdba395a50>
Date:	Wed Nov 30 02:54:35 2011 +0000

    Added additional SYS_mmap fixes for FreeBSD due to syscall differences
    between os versions (pulled from differences between FreeBSD6 and
    FreeBSD8 mmap implementations)

    git-svn-id: http://gperftools.googlecode.com/svn/trunk@125
    6b5cf1ce-ec42-a296-1ba9-69fdba395a50

M	src/malloc_hook_mmap_freebsd.h

commit 8c8329390cd05a1bf1bf9613712521a9ee9fe260
Author: csilvers <csilvers@6b5cf1ce-ec42-a296-1ba9-69fdba395a50>
Date:	Tue Nov 22 01:06:33 2011 +0000

	    * Eliminate some TSAN warnings (wilsonh)
	    * Better fix for freebsd on x86_64 (<EMAIL>)
	    * Lightweight check for double-frees (blount)
	    * 64-bit atomic ops for ARMv7 (dougkwan)
	    * Remove dynamic annotations in some cases (dvyukov)
	    * Update linux_syscall_support.h for ARM, etc (evannier)


    git-svn-id: http://gperftools.googlecode.com/svn/trunk@124
    6b5cf1ce-ec42-a296-1ba9-69fdba395a50

M	src/base/atomicops-internals-arm-v6plus.h
M	src/base/dynamic_annotations.c
M	src/malloc_hook_mmap_freebsd.h
M	src/profile-handler.cc
M	src/thread_cache.h

commit ca23f57679e2bd4e0390d6891aa765b7e9daca22
Author: csilvers <csilvers@6b5cf1ce-ec42-a296-1ba9-69fdba395a50>
Date:	Thu Nov 17 00:11:42 2011 +0000

    Fix bad #include paths.


    git-svn-id: http://gperftools.googlecode.com/svn/trunk@123
    6b5cf1ce-ec42-a296-1ba9-69fdba395a50

M	src/stacktrace.cc
M	src/stacktrace_config.h

commit e580d7888154fa7f95b3cef9e18f1ce69182212b
Author: csilvers <csilvers@6b5cf1ce-ec42-a296-1ba9-69fdba395a50>
Date:	Wed Nov 16 21:20:45 2011 +0000

    Some linux_syscall_support.h improvements:

	 * Fix socketcall functions (mec)
	 * Add ARM related defs and syscall asembly code (sanek)
	 * Use __arm__ instead of particular ARM arch macro (dougkwan)


    git-svn-id: http://gperftools.googlecode.com/svn/trunk@122
    6b5cf1ce-ec42-a296-1ba9-69fdba395a50

M	src/base/linux_syscall_support.h

commit 6c3eaabd7306173b6b71b63797ebf050675046cf
Author: csilvers <csilvers@6b5cf1ce-ec42-a296-1ba9-69fdba395a50>
Date:	Wed Nov 16 05:21:54 2011 +0000

	    * Check for mingw compilers that *do* define timespec
	    * Replace atexit() calls with global dtors; helps freebsd
	    (csilvers)
	    * Fix malloc_hook_mmap_linux for ARM (dougkwan)
	    * Disalbe heap-checker under AddressSanitizer (kcc)
	    * Fix bug in powerpc stacktracing (ppluzhnikov)
	    * Use exponential backoff waiting for spinlocks (m3b)
	    * Fix 64-bit nm on 32-bit binaries in pprof (csilvers)
	    * Implement stacktrace for ARM (dougkwan)
	    * Add ProfileHandlerDisallowForever (rsc)
	    * Shell escape when forking in pprof (csilvers)
	    * Fix freebsd to work on x86_64 (<EMAIL>)
	    * No longer combine overloaded functions in pprof (csilvers)
	    * Fix address-normalizing bug in pprof (csilvers)
	    * More consistently call abort() instead of exit() on failure
	    (csilvers)
	    * Allow NoGlobalLeaks to be safely called more than once
	    (csilvers)
	    * Beef up the documentation a bit about using libunwind
	    (csilvers)


    git-svn-id: http://gperftools.googlecode.com/svn/trunk@121
    6b5cf1ce-ec42-a296-1ba9-69fdba395a50

M	INSTALL
M	Makefile.am
M	Makefile.in
M	src/base/googleinit.h
M	src/base/linux_syscall_support.h
M	src/base/logging.h
M	src/base/spinlock_internal.cc
M	src/base/spinlock_linux-inl.h
M	src/base/spinlock_posix-inl.h
M	src/base/spinlock_win32-inl.h
M	src/debugallocation.cc
M	src/google/heap-checker.h
M	src/heap-checker.cc
M	src/malloc_hook_mmap_freebsd.h
M	src/malloc_hook_mmap_linux.h
M	src/pprof
M	src/profile-handler.cc
M	src/profile-handler.h
M	src/stacktrace.cc
C059	src/stacktrace_powerpc-inl.h	src/stacktrace_arm-inl.h
M	src/stacktrace_config.h
M	src/stacktrace_powerpc-inl.h
M	src/stacktrace_x86-inl.h
M	src/tests/heap-checker_unittest.cc
M	src/tests/profile-handler_unittest.cc

commit a6076edd177d59e67207753b799ce047a3663cb0
Author: csilvers <csilvers@6b5cf1ce-ec42-a296-1ba9-69fdba395a50>
Date:	Tue Oct 18 20:57:45 2011 +0000

	    * Get the deallocation stack trace outside the lock (sean)
	    * Make PageHeap dynamically allocated for leak checks (maxim)
	    * BUGFIX: Fix probing of nm -f behavior in pprof (dpeng)
	    * PORTING: Add "support" for MIPS cycletimer
	    * BUGFIX: Fix a race with the CentralFreeList lock (sanjay)
	    * Allow us to compile on OS X 10.6 and run on 10.5 (raltherr)
	    * Support /pprof/censusprofile url arguments (rajatjain)
	    * Die in configure when g++ is't installed (csilvers)
	    * Change IgnoreObject to return its argument (nlewycky)
	    * Update malloc-hook files to support more CPUs
	    * Move stack trace collecting out of the mutex (taylorc)
	    * BUGFIX: write our own strstr to avoid libc problems
	    (csilvers)
	    * use simple callgrind compression facility in pprof
	    * print an error message when we can't run pprof to symbolize
	    (csilvers)


    git-svn-id: http://gperftools.googlecode.com/svn/trunk@120
    6b5cf1ce-ec42-a296-1ba9-69fdba395a50

M	INSTALL
M	Makefile.am
M	Makefile.in
M	configure
M	configure.ac
M	packages/rpm.sh
M	src/base/basictypes.h
M	src/base/cycleclock.h
M	src/base/dynamic_annotations.h
M	src/base/spinlock_internal.cc
M	src/base/spinlock_linux-inl.h
M	src/central_freelist.cc
M	src/central_freelist.h
M	src/common.cc
M	src/common.h
M	src/debugallocation.cc
M	src/google/heap-checker.h
M	src/heap-checker.cc
M	src/heap-profile-table.cc
M	src/heap-profile-table.h
M	src/heap-profiler.cc
M	src/internal_logging.cc
M	src/internal_logging.h
M	src/libc_override_osx.h
M	src/malloc_hook.cc
M	src/malloc_hook_mmap_linux.h
M	src/memfs_malloc.cc
M	src/page_heap.cc
M	src/page_heap.h
M	src/page_heap_allocator.h
M	src/pprof
M	src/profile-handler.cc
M	src/profiler.cc
M	src/span.cc
M	src/span.h
M	src/stack_trace_table.cc
M	src/stacktrace.cc
M	src/stacktrace_config.h
M	src/static_vars.cc
M	src/static_vars.h
M	src/symbolize.cc
M	src/system-alloc.cc
M	src/tcmalloc.cc
M	src/tests/tcmalloc_unittest.cc
M	src/thread_cache.cc
M	src/thread_cache.h
M	src/windows/config.h

commit c2eedce2a718913ed6264ac8e96571c233761e3b
Author: csilvers <csilvers@6b5cf1ce-ec42-a296-1ba9-69fdba395a50>
Date:	Fri Aug 26 21:08:59 2011 +0000

    Fri Aug 26 13:29:25 2011	Google Inc. <<EMAIL>>

	    * google-perftools: version 1.8.3 release
	    * Added back the 'pthreads unsafe early' #define, needed
	    for FreeBSD


    git-svn-id: http://gperftools.googlecode.com/svn/trunk@117
    6b5cf1ce-ec42-a296-1ba9-69fdba395a50

M	ChangeLog
M	NEWS
M	configure
M	configure.ac
M	src/config.h.in
M	src/pprof
M	src/windows/config.h

commit f633b40ba5d3785c7e31fd9b575c5cf5e7c174f3
Author: csilvers <csilvers@6b5cf1ce-ec42-a296-1ba9-69fdba395a50>
Date:	Thu Aug 11 22:06:22 2011 +0000

    Thu Aug 11 15:01:47 2011	Google Inc. <<EMAIL>>

	    * google-perftools: version 1.8.2 release
	    * Fixed calculation of patchlevel, 'make check' should all
	    pass again


    git-svn-id: http://gperftools.googlecode.com/svn/trunk@115
    6b5cf1ce-ec42-a296-1ba9-69fdba395a50

M	ChangeLog
M	NEWS
M	configure
M	configure.ac
M	src/pprof
M	src/windows/config.h

commit ffcdf7bf5a7e97428f7521b3c9e6d854e3a424a9
Author: csilvers <csilvers@6b5cf1ce-ec42-a296-1ba9-69fdba395a50>
Date:	Thu Aug 11 22:05:09 2011 +0000

    Fix #includes for ARM.


    git-svn-id: http://gperftools.googlecode.com/svn/trunk@114
    6b5cf1ce-ec42-a296-1ba9-69fdba395a50

M	src/base/atomicops-internals-arm-generic.h

commit d2faf4646dc79486babfbd63cf5f658b917dd6ab
Author: csilvers <csilvers@6b5cf1ce-ec42-a296-1ba9-69fdba395a50>
Date:	Wed Jul 27 04:18:01 2011 +0000

    Tue Jul 26 20:57:51 2011	Google Inc. <<EMAIL>>

	    * google-perftools: version 1.8 release
	    * Added an #include to fix compile breakage on latest gcc's
	    * Removed an extra , in the configure.ac script


    git-svn-id: http://gperftools.googlecode.com/svn/trunk@112
    6b5cf1ce-ec42-a296-1ba9-69fdba395a50

M	ChangeLog
M	NEWS
M	configure
M	configure.ac
M	src/base/elf_mem_image.cc
M	src/config.h.in
M	src/pprof
M	src/tests/tcmalloc_unittest.cc
M	src/windows/config.h

commit 100c38c1a225446c1bbeeaac117902d0fbebfefe
Author: csilvers <csilvers@6b5cf1ce-ec42-a296-1ba9-69fdba395a50>
Date:	Sat Jul 16 01:07:10 2011 +0000

    Fri Jul 15 16:10:51 2011	Google Inc. <<EMAIL>>

	    * google-perftools: version 1.8 release
	    * PORTING: (Disabled) support for patching mmap on freebsd
	    (chapp...)
	    * PORTING: Support volatile __malloc_hook for glibc 2.14
	    (csilvers)
	    * PORTING: Use _asm rdtsc and __rdtsc to get cycleclock in
	    windows (koda)
	    * PORTING: Fix fd vs. HANDLE compiler error on cygwin
	    (csilvers)
	    * PORTING: Do not test memalign or double-linking on OS X
	    (csilvers)
	    * PORTING: Actually enable TLS on windows (jontra)
	    * PORTING: Some work to compile under Native Client (krasin)
	    * PORTING: deal with pthread_once w/o -pthread on freebsd
	    (csilvers)
	    * Rearrange libc-overriding to make it easier to port
	    (csilvers)
	    * Display source locations in pprof disassembly (sanjay)
	    * BUGFIX: Actually initialize allocator name (mec)
	    * BUGFIX: Keep track of 'overhead' bytes in malloc reporting
	    (csilvers)
	    * Allow ignoring one object twice in the leak checker (glider)
	    * BUGFIX: top10 in pprof should print 10 lines, not 11 (rsc)
	    * Refactor vdso source files (tipp)
	    * Some documentation cleanups
	    * Document MAX_TOTAL_THREAD_CACHE_SIZE <= 1Gb (nsethi)
	    * Add MallocExtension::GetOwnership(ptr) (csilvers)
	    * BUGFIX: We were leaving out a needed $(top_srcdir) in
	    the Makefile
	    * PORTING: Support getting argv0 on OS X
	    * Add 'weblist' command to pprof: like 'list' but html
	    (sanjay)
	    * Improve source listing in pprof (sanjay)
	    * Cap cache sizes to reduce fragmentation (ruemmler)
	    * Improve performance by capping or increasing sizes
	    (ruemmler)
	    * Add M{,un}mapReplacmenet hooks into MallocHook (ribrdb)
	    * Refactored system allocator logic (gangren)
	    * Include cleanups (csilvers)
	    * Add TCMALLOC_SMALL_BUT_SLOW support (ruemmler)
	    * Clarify that tcmalloc stats are MiB (robinson)
	    * Remove support for non-tcmalloc debugallocation (blount)
	    * Add a new test: malloc_hook_test (csilvers)
	    * Change the configure script to be more crosstool-friendly
	    (mcgrathr)
	    * PORTING: leading-underscore changes to support win64
	    (csilvers)
	    * Improve debugallocation tc_malloc_size (csilvers)
	    * Extend atomicops.h and cyceclock to use ARM V6+ optimized
	    code (sanek)
	    * Change malloc-hook to use a list-like structure (llib)
	    * Add flag to use MAP_PRIVATE in memfs_malloc (gangren)
	    * Windows support for pprof: nul and /usr/bin/file (csilvers)
	    * TESTING: add test on strdup to tcmalloc_test (csilvers)
	    * Augment heap-checker to deal with no-inode maps (csilvers)
	    * Count .dll/.dylib as shared libs in heap-checker (csilvers)
	    * Disable sys_futex for arm; it's not always reliable (sanek)
	    * PORTING: change lots of windows/port.h macros to functions
	    * BUGFIX: Generate correct version# in tcmalloc.h on windows
	    (csilvers)
	    * PORTING: Some casting to make solaris happier about types
	    (csilvers)
	    * TESTING: Disable debugallocation_test in 'minimal' mode
	    (csilvers)
	    * Rewrite debugallocation to be more modular (csilvers)
	    * Don't try to run the heap-checker under valgrind
	    (ppluzhnikov)
	    * BUGFIX: Make focused stat %'s relative, not absolute
	    (sanjay)
	    * BUGFIX: Don't use '//' comments in a C file (csilvers)
	    * Quiet new-gcc compiler warnings via -Wno-unused-result,
	    etc (csilvers)


    git-svn-id: http://gperftools.googlecode.com/svn/trunk@110
    6b5cf1ce-ec42-a296-1ba9-69fdba395a50

M	ChangeLog
M	Makefile.am
M	Makefile.in
M	NEWS
M	README
M	configure
M	configure.ac
M	doc/heapprofile.html
M	doc/tcmalloc.html
M	google-perftools.sln
M	packages/deb/changelog
M	src/base/atomicops-internals-windows.h
M	src/base/atomicops.h
M	src/base/cycleclock.h
C067	src/base/vdso_support.cc	src/base/elf_mem_image.cc
A	src/base/elf_mem_image.h
M	src/base/low_level_alloc.cc
M	src/base/spinlock_internal.cc
M	src/base/vdso_support.cc
M	src/base/vdso_support.h
M	src/central_freelist.cc
M	src/central_freelist.h
M	src/common.cc
M	src/common.h
M	src/config.h.in
M	src/debugallocation.cc
M	src/google/heap-checker.h
M	src/google/malloc_extension.h
M	src/google/malloc_extension_c.h
M	src/google/malloc_hook.h
M	src/heap-checker.cc
A	src/libc_override.h
A	src/libc_override_gcc_and_weak.h
A	src/libc_override_glibc.h
A	src/libc_override_osx.h
A	src/libc_override_redefine.h
M	src/malloc_extension.cc
M	src/malloc_hook-inl.h
M	src/malloc_hook.cc
A	src/malloc_hook_mmap_freebsd.h
A	src/malloc_hook_mmap_linux.h
M	src/maybe_threads.cc
M	src/memfs_malloc.cc
M	src/memory_region_map.cc
M	src/pprof
M	src/profile-handler.cc
M	src/profiler.cc
M	src/stack_trace_table.h
M	src/stacktrace.cc
M	src/stacktrace_config.h
C053	src/stack_trace_table.h src/stacktrace_nacl-inl.h
M	src/symbolize.cc
M	src/system-alloc.cc
M	src/system-alloc.h
M	src/tcmalloc.cc
M	src/tests/debugallocation_test.cc
M	src/tests/malloc_extension_c_test.c
M	src/tests/malloc_extension_test.cc
M	src/tests/malloc_hook_test.cc
M	src/tests/profiler_unittest.cc
M	src/tests/system-alloc_unittest.cc
M	src/tests/tcmalloc_unittest.cc
M	src/thread_cache.cc
M	src/windows/config.h
M	src/windows/google/tcmalloc.h.in
M	src/windows/mingw.h
M	src/windows/port.cc
M	src/windows/port.h
M
vsprojects/current_allocated_bytes_test/current_allocated_bytes_test.vcproj
M	vsprojects/malloc_hook_test/malloc_hook_test.vcproj

commit 8c7d2289d24f7a49f1f6f60d4a6eaee06fa04c60
Author: csilvers <csilvers@6b5cf1ce-ec42-a296-1ba9-69fdba395a50>
Date:	Thu May 19 21:37:12 2011 +0000

	    * Fix typos in comment in profiler.h (nrhodes)
	    * #include fixes (jyrki)
	    * Add missing stddef.h for ptrdiff_t (mec)
	    * Add M{,un}mapReplacement hooks into MallocHook (ribrdb)
	    * Force big alloc in frag test (ruemmler)
	    * PERF: Increase the size class cache to 64K entries
	    (ruemmler)
	    * PERF: Increase the transfer cache by 16x (ruemmler)
	    * Use windows intrinsic to get the tsc (csilvers)
	    * Rename atomicops-internals-x86-msvc.h->windows.h (csilvers)
	    * Remove flaky DEATH test in malloc_hook_test (ppluzhnikov)
	    * Expose internal ReadStackTraces()/etc (lantran)
	    * Refactored system allocator logic (gangren)
	    * Include-what-you-use: cleanup tcmalloc #includes (csilvers)
	    * Don't set kAddressBits to 48 on 32-bit systems (csilvers)
	    * Add declaration for __rdtsc() for windows (koda)
	    * Don't revert to system alloc for expected errors (gangren)
	    * Add TCMALLOC_SMALL_BUT_SLOW support (ruemmler)
	    * Clarify that tcmalloc stats are MiB (robinson)
	    * Avoid setting cpuinfo_cycles_per_second to 0 (koda)
	    * Fix frag_unittest memory calculations (ruemmler)
	    * Remove support for non-tcmalloc debugallocation (blount)
	    * Add malloc_hook_test (llib)
	    * Change the objcopy -W test to be cross-friendly (mcgrathr)
	    * Export __tcmalloc in addition to _tcmalloc, for 86_64
	    (csilvers)


    git-svn-id: http://gperftools.googlecode.com/svn/trunk@109
    6b5cf1ce-ec42-a296-1ba9-69fdba395a50

M	INSTALL
M	Makefile.am
M	Makefile.in
M	README
M	README_windows.txt
M	configure
M	configure.ac
M	google-perftools.sln
R096	src/base/atomicops-internals-x86-msvc.h
src/base/atomicops-internals-windows.h
M	src/base/atomicops.h
M	src/base/cycleclock.h
M	src/base/sysinfo.cc
M	src/base/sysinfo.h
M	src/base/vdso_support.h
M	src/central_freelist.cc
M	src/central_freelist.h
M	src/common.cc
M	src/common.h
M	src/config.h.in
M	src/debugallocation.cc
M	src/google/malloc_extension.h
M	src/google/malloc_hook.h
M	src/google/malloc_hook_c.h
M	src/google/profiler.h
M	src/google/tcmalloc.h.in
M	src/heap-profiler.cc
M	src/internal_logging.cc
M	src/internal_logging.h
M	src/malloc_extension.cc
M	src/malloc_hook-inl.h
M	src/malloc_hook.cc
M	src/memfs_malloc.cc
M	src/memory_region_map.cc
M	src/memory_region_map.h
M	src/packed-cache-inl.h
M	src/page_heap.cc
M	src/page_heap.h
M	src/page_heap_allocator.h
M	src/pagemap.h
M	src/sampler.cc
M	src/sampler.h
M	src/span.cc
M	src/stack_trace_table.cc
M	src/stack_trace_table.h
M	src/static_vars.cc
M	src/system-alloc.cc
M	src/system-alloc.h
M	src/tcmalloc.cc
M	src/tests/frag_unittest.cc
M	src/tests/heap-checker_unittest.cc
A	src/tests/malloc_hook_test.cc
M	src/tests/memalign_unittest.cc
M	src/tests/page_heap_test.cc
M	src/tests/realloc_unittest.cc
M	src/tests/system-alloc_unittest.cc
M	src/tests/tcmalloc_large_unittest.cc
M	src/thread_cache.cc
M	src/thread_cache.h
M	src/windows/patch_functions.cc
M	src/windows/port.cc
A	vsprojects/malloc_hook_test/malloc_hook_test.vcproj

commit 1d30e525ae6ac38ae381bb3118f7f47998af2942
Author: csilvers <csilvers@6b5cf1ce-ec42-a296-1ba9-69fdba395a50>
Date:	Mon Mar 21 21:41:55 2011 +0000

	    * Improve debugallocation tc_malloc_size (csilvers)
	    * Extend atomicops.h to use ARM V6+ optimized code (sanek)
	    * Fix failure in Ranges test (ppluzhnikov)
	    * Change malloc-hook to use a list-like structure (llib)
	    * Update tcmalloc_regtest to use new malloc hooks (llib)
	    * PARTIAL: Keep track of 'overhead' bytes in the page cache
	    (csilvers)


    git-svn-id: http://gperftools.googlecode.com/svn/trunk@108
    6b5cf1ce-ec42-a296-1ba9-69fdba395a50

M	Makefile.am
M	Makefile.in
M	google-perftools.sln
A	src/base/atomicops-internals-arm-generic.h
A	src/base/atomicops-internals-arm-v6plus.h
M	src/base/atomicops.h
M	src/central_freelist.cc
M	src/central_freelist.h
M	src/google/malloc_hook.h
M	src/google/malloc_hook_c.h
M	src/heap-checker-bcad.cc
M	src/heap-checker.cc
M	src/heap-profiler.cc
M	src/malloc_hook-inl.h
M	src/malloc_hook.cc
M	src/memory_region_map.cc
M	src/memory_region_map.h
M	src/tcmalloc.cc
A	src/tests/current_allocated_bytes_test.cc
M	src/tests/low_level_alloc_unittest.cc
M	src/tests/malloc_extension_c_test.c
M	src/tests/tcmalloc_unittest.cc
A
vsprojects/current_allocated_bytes_test/current_allocated_bytes_test.vcproj

commit 6fe07cd2c0527e18276cc79a57e2212a4b048746
Author: csilvers <csilvers@6b5cf1ce-ec42-a296-1ba9-69fdba395a50>
Date:	Fri Mar 4 23:52:33 2011 +0000

	    * add a flag to use MAP_PRIVATE in memfs_malloc (gangren)
	    * pthread_self() is now safe to use early (ppluzhnikov)
	    * windows support for pprof: nul and /usr/bin/file (csilvers)
	    * fix tc_malloc_size for debugallocation (csilvers)
	    * add test on strdup to tcmalloc_test (csilvers)
	    * augment heap-checker to deal with no-inode maps (csilvers)
	    * Get rid of -Wno-unused-result: not all gcc's support it
	    (csilvers)
	    * /bin/true -> ':', which is faster and more portable
	    (csilvers)


    git-svn-id: http://gperftools.googlecode.com/svn/trunk@107
    6b5cf1ce-ec42-a296-1ba9-69fdba395a50

M	Makefile.am
M	Makefile.in
M	doc/tcmalloc.html
M	src/debugallocation.cc
M	src/heap-checker.cc
M	src/memfs_malloc.cc
M	src/pprof
M	src/tcmalloc.cc
M	src/tests/debugallocation_test.sh
M	src/tests/memalign_unittest.cc
M	src/tests/tcmalloc_unittest.cc
M	src/thread_cache.cc

commit 75584139e40c9d6c952d9c5339c52e5b58302fc8
Author: csilvers <csilvers@6b5cf1ce-ec42-a296-1ba9-69fdba395a50>
Date:	Wed Mar 2 08:10:05 2011 +0000

	    * Enhance cycleclock on ARM v6 and above (sanek)
	    * Reduce object copying by using a reference (nherring)
	    * Modified lock annotations a bit (lcwu)
	    * Make debugallocation test a bit more forgiving (csilvers)
	    * Count .dll/.dylib as shared libs in heapchecker (csilvers)
	    * Disable sys_futex for arm (sanek)
	    * Don't use macros as much in windows/port.h (andrey.s...)
	    * Update #includes in case malloc.h is in weird places
	    (csilvers)
	    * Turn off some not-so-useful warnings in gcc 4 (csilvers)
	    * Do some casting to make solaris happier about types
	    (csilvers)
	    * Disable debugallocation_test in 'minimal' mode (csilvers)
	    * Rewrite debugallocation to be more modular (csilvers)
	    * We can't run the heap-checker under valgrind (ppluzhnikov)


    git-svn-id: http://gperftools.googlecode.com/svn/trunk@106
    6b5cf1ce-ec42-a296-1ba9-69fdba395a50

M	Makefile.am
M	Makefile.in
M	configure
M	configure.ac
C055	src/raw_printer.cc	src/base/arm_instruction_set_select.h
M	src/base/cycleclock.h
M	src/base/logging.h
M	src/base/low_level_alloc.cc
M	src/base/spinlock_linux-inl.h
M	src/base/stl_allocator.h
M	src/base/sysinfo.cc
M	src/config.h.in
M	src/debugallocation.cc
M	src/google/tcmalloc.h.in
M	src/heap-checker.cc
M	src/internal_logging.cc
M	src/raw_printer.cc
M	src/tcmalloc.cc
M	src/tests/debugallocation_test.cc
M	src/tests/heap-checker_unittest.cc
M	src/tests/memalign_unittest.cc
M	src/tests/sampler_test.cc
M	src/windows/config.h
R094	src/windows/google/tcmalloc.h	src/windows/google/tcmalloc.h.in
M	src/windows/port.cc
M	src/windows/port.h

commit c1abbfae802af5bf949c78e0bfdfd58d5c669a86
Author: csilvers <csilvers@6b5cf1ce-ec42-a296-1ba9-69fdba395a50>
Date:	Sat Feb 19 00:11:09 2011 +0000

	    * avoid unused-var warnings with annotalysis (lcwu)
	    * make focused stats %'s relative, not absolute (sanjay)


    git-svn-id: http://gperftools.googlecode.com/svn/trunk@105
    6b5cf1ce-ec42-a296-1ba9-69fdba395a50

M	src/base/dynamic_annotations.h
M	src/pprof

commit dd3d9d969e9b889e96c2af497e436856fac1a2a7
Author: csilvers <csilvers@6b5cf1ce-ec42-a296-1ba9-69fdba395a50>
Date:	Tue Feb 8 01:03:37 2011 +0000

	    * Fix tcmalloc_unittest on MSVC 10 in release mode (csilvers)
	    * Fix malloc_hook_c.h to compile with -ansi under gcc
	    (csilvers)


    git-svn-id: http://gperftools.googlecode.com/svn/trunk@104
    6b5cf1ce-ec42-a296-1ba9-69fdba395a50

M	NEWS
M	src/google/malloc_hook_c.h
M	src/tests/tcmalloc_unittest.cc

commit 7375b4f3cb3ab4471d0016017be5e18ba5451c5f
Author: csilvers <csilvers@6b5cf1ce-ec42-a296-1ba9-69fdba395a50>
Date:	Sat Feb 5 00:19:37 2011 +0000

    Fri Feb 04 15:54:31 2011	Google Inc. <<EMAIL>>

	    * google-perftools: version 1.7 release
	    * Reduce page map key size under x86_64 by 4.4MB (rus)
	    * Remove a flaky malloc-extension test (fdabek)
	    * Improve the performance of PageHeap::New (ond..., csilvers)
	    * Improve sampling_test with no-inline additions/etc (fdabek)
	    * 16-byte align debug allocs (jyasskin)
	    * Change FillProcSelfMaps to detect out-of-buffer-space
	    (csilvers)
	    * Document the need for sampling to use GetHeapSample
	    (csilvers)
	    * Try to read TSC frequency from tsc_freq_khs (adurbin)
	    * Do better at figuring out if tests are running under gdb
	    (ppluzhnikov)
	    * Improve spinlock contention performance (ruemmler)
	    * Better internal-function list for pprof's /contention
	    (ruemmler)
	    * Speed up GoogleOnce (m3b)
	    * Limit number of incoming/outgoing edges in pprof (sanjay)
	    * Add pprof --evince to go along with --gv (csilvers)
	    * Document the various ways to get heap-profiling information
	    (csilvers)
	    * Separate out synchronization profiling routines (ruemmler)
	    * Improve malloc-stats output to be more understandable
	    (csilvers)
	    * Add support for census profiler in pporf (nabeelmian)
	    * Document how pprof's /symbol must support GET requests
	    (csilvers)
	    * Improve acx_pthread.m4 (ssuomi, liujisi)
	    * Speed up pprof's ExtractSymbols (csilvers)
	    * Ignore some known-leaky (java) libraries in the heap checker
	    (davidyu)
	    * Make kHideMask use all 64 bits in tests (ppluzhnikov)
	    * Clean up pprof input-file handling (csilvers)
	    * BUGFIX: Don't crash if __environ is NULL (csilvers)
	    * BUGFIX: Fix totally broken debugallocation tests (csilvers)
	    * BUGFIX: Fix up fake_VDSO handling for unittest (ppluzhnikov)
	    * BUGFIX: Suppress all large allocs when report threshold is 0
	    (lexie)
	    * BUGFIX: mmap2 on i386 takes an off_t, not off64_t (csilvers)
	    * PORTING: Add missing PERFTOOLS_DLL_DECL (csilvers)
	    * PORTING: Add stddef.h to make newer gcc's happy (csilvers)
	    * PORTING: Document some tricks for working under OS X
	    (csilvers)
	    * PORTING: Don't try to check valgrind for windows (csilvers)
	    * PORTING: Make array-size a var to compile under clang
	    (chandlerc)
	    * PORTING: No longer hook _aligned_malloc and _aligned_free
	    (csilvers)
	    * PORTING: Quiet some gcc warnings (csilvers)
	    * PORTING: Replace %PRIxPTR with %p to be more portable
	    (csilvers)
	    * PORTING: Support systems that capitalize /proc weirdly
	    (sanek)
	    * PORTING: Treat arm3 the same as arm5t in cycletimer
	    (csilvers)
	    * PORTING: Update windows logging to not allocate memory
	    (csilvers)
	    * PORTING: avoid double-patching newer windows DLLs
	    (roger.orr)
	    * PORTING: get dynamic_annotations.c to work on windows
	    (csilvers)
	    * Add pkg-config .pc files for the 5 libraries we produce
	    (csilvers)
	    * Added proper libtool versioning, so this lib will be 0.1.0
	    (csilvers)
	    * Moved from autoconf 2.64 to 2.65


    git-svn-id: http://gperftools.googlecode.com/svn/trunk@102
    6b5cf1ce-ec42-a296-1ba9-69fdba395a50

M	ChangeLog
M	Makefile.am
M	Makefile.in
M	NEWS
M	README
M	configure
M	configure.ac
M	m4/acx_pthread.m4
M	packages/deb/changelog
M	src/base/cycleclock.h
M	src/base/dynamic_annotations.c
M	src/base/logging.h
M	src/base/stl_allocator.h
M	src/base/vdso_support.cc
M	src/debugallocation.cc
M	src/heap-checker.cc
M	src/page_heap.cc
M	src/page_heap.h
M	src/pprof
M	src/symbolize.h
M	src/system-alloc.cc
M	src/tests/debugallocation_test.cc
M	src/tests/debugallocation_test.sh
M	src/tests/malloc_extension_test.cc
M	src/windows/port.h

commit 3d77cbf7d569a7c7f0ce39a83f6c98da1718f1c4
Author: csilvers <csilvers@6b5cf1ce-ec42-a296-1ba9-69fdba395a50>
Date:	Wed Jan 19 21:37:15 2011 +0000

	    * Make kHideMask use all 64 bits (ppluzhnikov)
	    * Add new IsDebuggerAttached method (ppluzhnikov)
	    * Document some tricks for maybe getting perftools to work
	    on OS X
	    * Redo file-top pprof commands (csilvers)
	    * Clean up pprof input-file handling (csilvers)
	    * 16-byte align debug allocs (jyasskin)
	    * Ignore JVM memory leakage in the heap checker (davidyu,
	    kkurimoto)
	    * Better internal-function list for contentionz (ruemmler)
	    * mmap2 on i386 takes an off_t, not an off64_t (csilvers)
	    * Fix up fake-VDSO handling for unittest (ppluzhnikov)
	    * Don't try to check valgrind for windows (csilvers)


    git-svn-id: http://gperftools.googlecode.com/svn/trunk@101
    6b5cf1ce-ec42-a296-1ba9-69fdba395a50

M	README
M	src/base/dynamic_annotations.c
M	src/base/linux_syscall_support.h
M	src/base/vdso_support.cc
M	src/base/vdso_support.h
M	src/debugallocation.cc
M	src/heap-checker.cc
M	src/malloc_hook.cc
M	src/pprof
M	src/tcmalloc.cc
M	src/tests/heap-checker_unittest.cc
M	src/tests/tcmalloc_unittest.cc
M	src/windows/config.h

commit a0a2ff3b493481ff52e4b4deec2fcc494756b64a
Author: csilvers <csilvers@6b5cf1ce-ec42-a296-1ba9-69fdba395a50>
Date:	Wed Nov 24 00:33:20 2010 +0000

	    * PORTING: Add PERFTOOLS_DLL_DECL to malloc_hook_c.h
	    (csilvers)
	    * Add new .h files to Makefile (csilvers)


    git-svn-id: http://gperftools.googlecode.com/svn/trunk@100
    6b5cf1ce-ec42-a296-1ba9-69fdba395a50

M	Makefile.am
M	Makefile.in
M	src/google/malloc_hook_c.h

commit 3014cf142e5a2409c88ab4559f3274434ed9a29b
Author: csilvers <csilvers@6b5cf1ce-ec42-a296-1ba9-69fdba395a50>
Date:	Thu Nov 18 01:07:25 2010 +0000

	    * Suppress all large allocs when report threshold==0
	    * Clarified meaning of various malloc stats
	    * Change from ATTRIBUTED_DEPRECATED to comments
	    * Make array-size a var to compile under clang
	    * Reduce page map key size under x86_64 by 4.4MB
	    * Added full qualification to MemoryBarrier
	    * Support systems that capitalize /proc weirdly
	    * Avoid gcc warning: exporting type in unnamed ns
	    * Add some dynamic annotations for gcc attributes
	    * Add support for census profiler in pprof
	    * Speed up pprof's ExtractSymbols
	    * Speed up GoogleOnce
	    * Add pkg-config (.pc) files
	    * Detect when __environ exists but is NULL
	    * Improve spinlock contention performance
	    * Add GetFreeListSizes
	    * Improve sampling_test, eg by adding no-inline
	    * Relax malloc_extension test-check for big pages
	    * Add proper library version number information
	    * Update from autoconf 2.64 to 2.65
	    * Better document how to write a server that works with pprof
	    * Change FillProcSelfMaps to better handle out-of-space
	    * No longer hook _aligned_malloc/free in windows
	    * Handle function-forwarding in DLLs when patching (in
	    windows)
	    * Update .vcproj files that had wrong .cc files in them (!)
	    * get rid of unnecessary 'size < 0'
	    * fix comments a bit in sysinfo.cc
	    * another go at improving malloc-stats output
	    * fix comment typo in profiler.cc
	    * Add a few more thread annotations
	    * Try to read TSC frequency from 'tsc_freq_khz'
	    * Fix annotalysis/TSAN incompatibility
	    * Add pprof --evince to go along with --gv
	    * Document need for sampling to use GetHeapSample
	    * Fix flakiness in malloc_extension_test
	    * Separate out synchronization profiling routines


    git-svn-id: http://gperftools.googlecode.com/svn/trunk@99
    6b5cf1ce-ec42-a296-1ba9-69fdba395a50

M	Makefile.am
M	Makefile.in
M	NEWS
M	README
M	aclocal.m4
M	configure
M	configure.ac
M	doc/pprof_remote_servers.html
M	ltmain.sh
A	m4/libtool.m4
A	m4/ltoptions.m4
A	m4/ltsugar.m4
A	m4/ltversion.m4
A	m4/lt~obsolete.m4
M	packages/deb/libgoogle-perftools-dev.dirs
M	packages/deb/libgoogle-perftools-dev.install
M	packages/rpm/rpm.spec
A	src/base/atomicops-internals-arm-gcc.h
M	src/base/basictypes.h
M	src/base/dynamic_annotations.h
M	src/base/logging.h
M	src/base/low_level_alloc.cc
M	src/base/spinlock.cc
M	src/base/spinlock.h
A	src/base/spinlock_internal.cc
C057	src/base/spinlock_win32-inl.h	src/base/spinlock_internal.h
M	src/base/spinlock_linux-inl.h
M	src/base/spinlock_posix-inl.h
M	src/base/spinlock_win32-inl.h
C067	src/base/spinlock_win32-inl.h
src/base/synchronization_profiling.h
M	src/base/sysinfo.cc
M	src/base/sysinfo.h
M	src/base/thread_annotations.h
M	src/base/vdso_support.cc
M	src/common.h
M	src/config.h.in
M	src/debugallocation.cc
M	src/google/heap-checker.h
M	src/google/malloc_extension.h
M	src/heap-profile-table.cc
M	src/malloc_extension.cc
M	src/memory_region_map.cc
M	src/page_heap.cc
M	src/page_heap.h
M	src/pprof
M	src/profiler.cc
M	src/system-alloc.cc
M	src/tcmalloc.cc
M	src/tests/debugallocation_test.cc
M	src/tests/malloc_extension_test.cc
M	src/tests/sampling_test.cc
M	src/tests/sampling_test.sh
M	src/tests/system-alloc_unittest.cc
M	src/tests/tcmalloc_unittest.cc
M	src/windows/config.h
M	src/windows/patch_functions.cc
M	src/windows/port.cc
M	src/windows/port.h
M	vsprojects/addressmap_unittest/addressmap_unittest.vcproj
M	vsprojects/libtcmalloc_minimal/libtcmalloc_minimal.vcproj
M
vsprojects/low_level_alloc_unittest/low_level_alloc_unittest.vcproj
M	vsprojects/packed-cache_test/packed-cache_test.vcproj
M	vsprojects/tmu-static/tmu-static.vcproj

commit 682ff7da1205398376ee725b4ce3219c107b3f8a
Author: csilvers <csilvers@6b5cf1ce-ec42-a296-1ba9-69fdba395a50>
Date:	Thu Aug 5 20:36:47 2010 +0000

    Thu Aug  5 12:48:03 PDT 2010

	    * google-perftools: version 1.6 release
	    * Add tc_malloc_usable_size for compatibility with glibc
	    (csilvers)
	    * Override malloc_usable_size with tc_malloc_usable_size
	    (csilvers)
	    * Default to no automatic heap sampling in tcmalloc (csilvers)
	    * Add -DTCMALLOC_LARGE_PAGES, a possibly faster tcmalloc (rus)
	    * Make some functions extern "C" to avoid false ODR warnings
	    (jyasskin)
	    * pprof: Add SVG-based output (rsc)
	    * pprof: Extend pprof --tools to allow per-tool configs
	    (csilvers)
	    * pprof: Improve support of 64-bit and big-endian profiles
	    (csilvers)
	    * pprof: Add interactive callgrind suport (weidenri...)
	    * pprof: Improve address->function mapping a bit (dpeng)
	    * Better detection of when we're running under valgrind
	    (csilvers)
	    * Better CPU-speed detection under valgrind (saito)
	    * Use, and recommend, -fno-builtin-malloc when compiling
	    (csilvers)
	    * Avoid false-sharing of memory between caches (bmaurer)
	    * BUGFIX: Fix heap sampling to use correct alloc size (bmauer)
	    * BUGFIX: Avoid gcc 4.0.x bug by making hook-clearing atomic
	    (csilvers)
	    * BUGFIX: Avoid gcc 4.5.x optimization bug (csilvers)
	    * BUGFIX: Work around deps-determining bug in libtool 1.5.26
	    (csilvers)
	    * BUGFIX: Fixed test to use HAVE_PTHREAD, not HAVE_PTHREADS
	    (csilvers)
	    * BUGFIX: Fix tls callback behavior on windows when using wpo
	    (wtc)
	    * BUGFIX: properly align allocation sizes on Windows (antonm)
	    * BUGFIX: Fix prototypes for tcmalloc/debugalloc wrt throw()
	    (csilvers)
	    * DOC: Updated heap-checker doc to match reality better
	    (fischman)
	    * DOC: Document ProfilerFlush, ProfilerStartWithOptions
	    (csilvers)
	    * DOC: Update docs for heap-profiler functions (csilvers)
	    * DOC: Clean up documentation around tcmalloc.slack_bytes
	    (fikes)
	    * DOC: Renamed README.windows to README_windows.txt (csilvers)
	    * DOC: Update the NEWS file to be non-empty (csilvers)
	    * PORTING: Fix windows addr2line and nm with proper rc code
	    (csilvers)
	    * PORTING: Add CycleClock and atomicops support for arm 5
	    (sanek)
	    * PORTING: Improve PC finding on cygwin and redhat 7
	    (csilvers)
	    * PORTING: speed up function-patching under windows (csilvers)


    git-svn-id: http://gperftools.googlecode.com/svn/trunk@97
    6b5cf1ce-ec42-a296-1ba9-69fdba395a50

M	ChangeLog
M	INSTALL
M	NEWS
M	README
M	configure
M	configure.ac
M	doc/heap_checker.html
M	m4/pc_from_ucontext.m4
M	packages/deb/changelog
M	src/base/atomicops.h
M	src/base/cycleclock.h
M	src/base/dynamic_annotations.c
M	src/base/dynamic_annotations.h
M	src/base/sysinfo.cc
M	src/base/thread_annotations.h
M	src/base/vdso_support.h
M	src/common.cc
M	src/common.h
M	src/google/malloc_extension.h
M	src/google/tcmalloc.h.in
M	src/heap-checker.cc
M	src/heap-profiler.cc
M	src/malloc_hook-inl.h
M	src/malloc_hook.cc
M	src/pprof
M	src/stacktrace_x86-inl.h
M	src/tcmalloc.cc
M	src/tests/sampler_test.cc
M	src/tests/tcmalloc_unittest.cc
M	src/windows/google/tcmalloc.h
M	src/windows/port.cc
M	vsprojects/addressmap_unittest/addressmap_unittest.vcproj
M	vsprojects/libtcmalloc_minimal/libtcmalloc_minimal.vcproj
M
vsprojects/low_level_alloc_unittest/low_level_alloc_unittest.vcproj
M	vsprojects/tmu-static/tmu-static.vcproj

commit 488eee994d571da216ef105d4144282c801f0eee
Author: csilvers <csilvers@6b5cf1ce-ec42-a296-1ba9-69fdba395a50>
Date:	Wed Jun 23 16:42:31 2010 +0000

    Oops, I thought I had done this last time, but maybe not.  Submit the
    name change.


    git-svn-id: http://gperftools.googlecode.com/svn/trunk@96
    6b5cf1ce-ec42-a296-1ba9-69fdba395a50

R092	README.windows	README_windows.txt

commit cb7393cbe2d737009001fd9d30dad568bac7a3d8
Author: csilvers <csilvers@6b5cf1ce-ec42-a296-1ba9-69fdba395a50>
Date:	Mon Jun 21 15:59:56 2010 +0000

	    * Default to not sampling in tcmalloc (csilvers)
	    * Add -DTCMALLOC_LARGE_PAGES: better perf for some workloads
	    (rus)
	    * Extend pprof --tools to allow per-tool configs (csilvers)
	    * Have STL_Allocator pass on # bytes to free (richardfang)
	    * Add a header guard to config.h (csilvers)
	    * DOC: Clean up documentation around tcmalloc.slack_bytes
	    (fikes)
	    * DOC: Document ProfilerFlush, ProfilerStartWithOptions
	    (csilvers)
	    * PORTING: Work around a gcc 4.5.0 optimization bug (csilvers)
	    * PORTING: Use -fno-builtin-malloc and friends when compiling
	    tcmalloc
	    * PORTING: Define _WIN32_WINNT high enough for mingw
	    (csilvers)
	    * PORTING: Work around libtool bug getting deps wrong in
	    some cases
	    * Update README.windows to emphasize $IncludeDir more
	    (csilvers)
	    * Rename README.windows to README_windows.txt (csilvers)


    git-svn-id: http://gperftools.googlecode.com/svn/trunk@95
    6b5cf1ce-ec42-a296-1ba9-69fdba395a50

M	Makefile.am
M	Makefile.in
M	README
M	configure
M	configure.ac
M	doc/cpuprofile.html
M	doc/tcmalloc.html
M	src/base/dynamic_annotations.c
M	src/base/dynamic_annotations.h
M	src/base/stl_allocator.h
M	src/central_freelist.cc
M	src/common.h
M	src/config.h.in
M	src/google/malloc_extension.h
M	src/heap-checker.cc
M	src/internal_logging.h
M	src/linked_list.h
M	src/memory_region_map.h
M	src/page_heap.cc
M	src/page_heap.h
M	src/pprof
M	src/sampler.cc
M	src/span.h
M	src/tcmalloc.cc
M	src/tests/frag_unittest.cc
M	src/tests/page_heap_test.cc
M	src/tests/testutil.cc
M	src/thread_cache.cc
M	src/thread_cache.h
M	src/windows/config.h
M	src/windows/mingw.h

commit d8c02761689ba909f474b85618f99ac6dfc9a168
Author: csilvers <csilvers@6b5cf1ce-ec42-a296-1ba9-69fdba395a50>
Date:	Fri May 7 21:53:24 2010 +0000

	    * Update docs for heap-profiler fns (csilvers)
	    * In pprof, accept URLs without ports but with http:// (rsc)
	    * Refactor sizeclass handling in tcmalloc (bmaurer)
	    * Always log failed calls to FindAllocDetails (mec)
	    * Clarify comments for ProfilerStart* (malcolmr)
	    * Add #include guards to stacktrace_win32-inl.h (glider)
	    * Add ANNOTATE_ENABLE_RACE_DETECTION(enable) (kcc)
	    * Make a contentful NEWS file (csilvers)
	    * Fix addr2line --help (which pprof relies on) for windows
	    (csilvers)
	    * Fixes a bug in tcmalloc's TLS callback on windows -static
	    (wtc)


    git-svn-id: http://gperftools.googlecode.com/svn/trunk@94
    6b5cf1ce-ec42-a296-1ba9-69fdba395a50

M	NEWS
M	doc/heapprofile.html
M	packages/deb/control
M	packages/rpm/rpm.spec
M	src/base/dynamic_annotations.c
M	src/base/dynamic_annotations.h
M	src/base/vdso_support.cc
M	src/central_freelist.cc
M	src/common.h
M	src/google/profiler.h
M	src/heap-checker.cc
M	src/internal_logging.h
M	src/page_heap.cc
M	src/page_heap.h
M	src/pprof
M	src/span.h
M	src/stacktrace_win32-inl.h
M	src/tcmalloc.cc
M	src/tests/page_heap_test.cc
M	src/windows/addr2line-pdb.c
M	src/windows/nm-pdb.c
M	src/windows/port.cc

commit b0fe220d503eb23830e622939c2e14f084392d1e
Author: csilvers <csilvers@6b5cf1ce-ec42-a296-1ba9-69fdba395a50>
Date:	Thu Apr 22 17:29:02 2010 +0000

	    * Fix pthread_once extern declarations (willchan)
	    * Add missing closing brace that resulted in compile failure
	    (willchan)
	    * Handle closed stdin/out/err properly when forking (glider)


    git-svn-id: http://gperftools.googlecode.com/svn/trunk@93
    6b5cf1ce-ec42-a296-1ba9-69fdba395a50

M	src/maybe_threads.cc
M	src/profile-handler.cc
M	src/symbolize.cc
M	src/tcmalloc.cc

commit 21c65ecb037d7d41dcd574c70cd7b7e9d5045462
Author: csilvers <csilvers@6b5cf1ce-ec42-a296-1ba9-69fdba395a50>
Date:	Mon Apr 12 21:18:48 2010 +0000

	    * Speed up IsSymbolizedProfile by checking for NUL (csilvers)
	    * Fix heap sampling to use correct alloc size (bmaurer)
	    * Make pprof ignore tc_new{,array}_nothrow (csilvers)
	    * PORTING: Have IsHeapProfilerRunning return an int, for C
	    (csilvers)
	    * Avoid false-sharing of memory between caches (bmaurer)
	    * Fix some variable shadowing (rt)
	    * SVG-based ouptut in pprof; also, wget->curl (rsc)
	    * Allow arbitrary prefix before obvious handler (rsc)
	    * Advertise when using libunwind (ppluzhnikov)

    Also, the install file seems to have reverted back to the default at
    some previous point in time (autotools will do that occasionally).
    Change that back to have the perftools-specific text in there.


    git-svn-id: http://gperftools.googlecode.com/svn/trunk@92
    6b5cf1ce-ec42-a296-1ba9-69fdba395a50

M	INSTALL
M	src/base/basictypes.h
M	src/google/heap-profiler.h
M	src/heap-checker.cc
M	src/heap-profiler.cc
M	src/page_heap_allocator.h
M	src/pprof
M	src/stacktrace_config.h
M	src/system-alloc.cc
M	src/system-alloc.h
M	src/tcmalloc.cc
M	src/thread_cache.h

commit 92beff88437b31f4a618640b88487e0f8dfb7017
Author: csilvers <csilvers@6b5cf1ce-ec42-a296-1ba9-69fdba395a50>
Date:	Tue Mar 23 20:39:55 2010 +0000

	    * Add new (std::nothrow) to debugallocation (corrado)
	    * Add a flag to ingore unaligned-ptr leaks (archanakannan)
	    * PORTING: Add get-pc capabilities for a new OS (csilvers)
	    * Don't register malloc extension under valgrind (csilvers)
	    * Fix throw specs for our global operator new (chandlerc)
	    * PORTING: link to instructions on windows static overrides
	    (mbelshe)
	    * Fix prototype differences in debugalloc (chandlerc,
	    csilvers, wan)
	    * Change pprof to handle big-endian input files (csilvers)
	    * Properly align allocation sizes on Windows (antonm)
	    * Improve IsRunningOnValgrind, using valgrind.h (csilvers,
	    kcc)
	    * Improve the accuracy of system_alloc actual_size (csilvers)
	    * Add interactive callgrind support to pprof (weidenri...)
	    * Fix off-by-one problems when symbolizing in pprof (dpeng)
	    * Be more permissive in allowed library names, in pprof
	    (csilvers)
	    * PORTING: Fix pc_from_ucontext to handle cygwin and redhat7
	    (csilvers)
	    * Fix stacktrace to avoid inlining (ppluzhnikov)


    git-svn-id: http://gperftools.googlecode.com/svn/trunk@91
    6b5cf1ce-ec42-a296-1ba9-69fdba395a50

M	Makefile.am
M	Makefile.in
M	README.windows
M	configure
M	configure.ac
M	doc/heap_checker.html
M	m4/pc_from_ucontext.m4
A	src/base/dynamic_annotations.c
D	src/base/dynamic_annotations.cc
M	src/base/dynamic_annotations.h
M	src/base/low_level_alloc.cc
M	src/config.h.in
M	src/debugallocation.cc
M	src/google/stacktrace.h
M	src/google/tcmalloc.h.in
M	src/heap-checker.cc
M	src/heap-profile-table.cc
M	src/heap-profile-table.h
M	src/malloc_extension.cc
M	src/malloc_hook.cc
M	src/memory_region_map.cc
M	src/pprof
M	src/stacktrace.cc
M	src/stacktrace_generic-inl.h
M	src/stacktrace_libunwind-inl.h
M	src/stacktrace_powerpc-inl.h
D	src/stacktrace_with_context.cc
M	src/stacktrace_x86-inl.h
M	src/system-alloc.cc
M	src/tcmalloc.cc
M	src/tests/debugallocation_test.cc
M	src/tests/heap-checker-death_unittest.sh
M	src/tests/profiler_unittest.cc
M	src/tests/profiler_unittest.sh
M	src/tests/tcmalloc_unittest.cc
A	src/third_party/valgrind.h
M	src/windows/google/tcmalloc.h
M	src/windows/port.cc

commit 23dd124970bc11636feaa240394063ba5889ca54
Author: csilvers <csilvers@6b5cf1ce-ec42-a296-1ba9-69fdba395a50>
Date:	Thu Feb 11 01:32:42 2010 +0000

	    * Speed up patching by not patching modules that are already
	    loaded


    git-svn-id: http://gperftools.googlecode.com/svn/trunk@88
    6b5cf1ce-ec42-a296-1ba9-69fdba395a50

M	src/windows/config.h
M	src/windows/patch_functions.cc

commit 8f8a010cab7d3070069a31990cb0e269f03dceb0
Author: csilvers <csilvers@6b5cf1ce-ec42-a296-1ba9-69fdba395a50>
Date:	Wed Jan 20 23:00:51 2010 +0000

    Oops, I submitted the wrong version of patch_functions.cc with the
    previous commit.  This is really the one that goes with release 1.5.
    .
    ?\027[D

    git-svn-id: http://gperftools.googlecode.com/svn/trunk@86
    6b5cf1ce-ec42-a296-1ba9-69fdba395a50

M	src/windows/patch_functions.cc

commit fef86cfeba8915e34575f09499e18b184d7254a9
Author: csilvers <csilvers@6b5cf1ce-ec42-a296-1ba9-69fdba395a50>
Date:	Wed Jan 20 22:47:29 2010 +0000

	    * google-perftools: version 1.5 release
	    * Add tc_set_new_mode (willchan)
	    * Make memalign functions + realloc respect tc_set_new_mode
	    (willchan)
	    * Add ReleaseToSystem(num_bytes) (kash)
	    * Handle zero-length symbols a bit better in pprof (csilvers)
	    * Prefer __environ to /proc/self/environ in cpu profiler
	    (csilvers)
	    * Add HEAP_CHECK_MAX_LEAKS flag to control #leaks to report
	    (glider)
	    * Add two new numeric pageheap properties to MallocExtension
	    (fikes)
	    * Print alloc size when mmap fails (hakon)
	    * Add ITIMER_REAL support to cpu profiler (csilvers,
	    nabeelmian)
	    * Speed up symbolizer in heap-checker reporting (glider)
	    * Speed up futexes with FUTEX_PRIVATE_FLAG (m3b)
	    * Speed up tcmalloc but doing better span coalescing (sanjay)
	    * Better support for different wget's and addr2maps in pprof
	    (csilvres)
	    * Implement a nothrow version of delete and delete[]
	    (csilvers)
	    * BUGFIX: fix a race on module_libcs[i] in windows patching
	    (csilvers)
	    * BUGFIX: Fix debugallocation to call cpp_alloc for new
	    (willchan)
	    * BUGFIX: A simple bugfix for --raw mode (mrabkin)
	    * BUGFIX: Fix C shims to actually be valid C (csilvers)
	    * BUGFIX: Fix recursively-unmapped-region accounting
	    (ppluzhnikov)
	    * BUGFIX: better distinguish real and fake vdso (ppluzhnikov)
	    * WINDOWS: replace debugmodule with more reliable psai
	    (andrey)
	    * PORTING: Add .bundle as another shared library extension
	    (csilvers)
	    * PORTING: Fixed a typo bug in the ocnfigure PRIxx m4 macro
	    (csilvers)
	    * PORTING: Augment sysinfo to work on 64-bit OS X (csilvers)
	    * PORTING: Use sys/ucontext.h to fix compiing on OS X 10.6
	    (csilvers)
	    * PORTING: Fix sysinfo libname reporting for solaris x86
	    (jeffrey)
	    * PORTING: Use libunwind for i386 when using --omitfp
	    (ppluzhnikov)

    NOTE: This release uses an older version of
    src/windows/patch_functions.cc
    because I decided the latest optimizations were not well enough tested
    for a release.  I'll aim to get them into next release.


    git-svn-id: http://gperftools.googlecode.com/svn/trunk@84
    6b5cf1ce-ec42-a296-1ba9-69fdba395a50

M	ChangeLog
M	INSTALL
M	autogen.sh
M	configure
M	configure.ac
M	packages/deb/changelog
M	src/pprof
M	src/windows/patch_functions.cc

commit 63b8d63beb7d771713774f9a5d57381cbd29bf19
Author: csilvers <csilvers@6b5cf1ce-ec42-a296-1ba9-69fdba395a50>
Date:	Thu Jan 14 16:26:05 2010 +0000

	    * PORTING: Revised patch_functions to avoid deadlock
	    (csilvers, andrey)
	    * PORTING: Revised patch_functions to speed up .dll loads
	    (csilvers)
	    * PORTING: Build and run sampling_test for windows (csilvers)
	    * Correctly init tc structs even when libc isn't patched
	    (csilvers)
	    * Make low-level allocs async-signal-safe (saito)


    git-svn-id: http://gperftools.googlecode.com/svn/trunk@83
    6b5cf1ce-ec42-a296-1ba9-69fdba395a50

M	Makefile.am
M	Makefile.in
M	README.windows
M	src/base/low_level_alloc.cc
M	src/base/low_level_alloc.h
M	src/google/malloc_hook.h
M	src/malloc_hook.cc
M	src/pprof
M	src/stacktrace_x86-inl.h
M	src/tcmalloc.cc
M	src/tests/sampling_test.sh
M	src/thread_cache.cc
M	src/windows/patch_functions.cc
M	vsprojects/libtcmalloc_minimal/libtcmalloc_minimal.vcproj
M	vsprojects/tmu-static/tmu-static.vcproj

commit eeeacd5ec4fa36256091f45e5b3af81cee2a4d86
Author: csilvers <csilvers@6b5cf1ce-ec42-a296-1ba9-69fdba395a50>
Date:	Wed Jan 6 00:34:23 2010 +0000

	    * PORTING: Fix a race condition in windows patching
	    * PORTING: Use Psapi instead of debugmodule to get windows
	    module info


    git-svn-id: http://gperftools.googlecode.com/svn/trunk@82
    6b5cf1ce-ec42-a296-1ba9-69fdba395a50

M	README.windows
M	google-perftools.sln
M	src/page_heap.h
M	src/windows/patch_functions.cc
M	vsprojects/libtcmalloc_minimal/libtcmalloc_minimal.vcproj
M	vsprojects/tmu-static/tmu-static.vcproj

commit 6e7479331c751bdfe04d272dbb1bbbe877f0e86a
Author: csilvers <csilvers@6b5cf1ce-ec42-a296-1ba9-69fdba395a50>
Date:	Tue Dec 15 01:41:30 2009 +0000

	    * Fix a memory leak with repeated Patch() calls on windows
	    (csilvers)
	    * Fix a bug when we re-Patch() a previously unpatched lib
	    (csilvers)
	    * Add .bundle as another .so extension in pprof (csilvers)


    git-svn-id: http://gperftools.googlecode.com/svn/trunk@81
    6b5cf1ce-ec42-a296-1ba9-69fdba395a50

M	src/base/spinlock.h
M	src/heap-checker.cc
M	src/heap-profile-table.h
M	src/memfs_malloc.cc
M	src/memory_region_map.h
M	src/packed-cache-inl.h
M	src/pprof
M	src/profile-handler.cc
M	src/profiledata.h
M	src/windows/patch_functions.cc
M	src/windows/preamble_patcher.cc

commit 1d981b6997f73e6d92b647d042cfe8649d4728c1
Author: csilvers <csilvers@6b5cf1ce-ec42-a296-1ba9-69fdba395a50>
Date:	Wed Dec 2 21:42:10 2009 +0000

	    * Make memalign && posix_memalign respect tc_set_new_mode
	    (willchan)
	    * Fix windows patch functions to respect tc_set_new_mode
	    (willchan)


    git-svn-id: http://gperftools.googlecode.com/svn/trunk@80
    6b5cf1ce-ec42-a296-1ba9-69fdba395a50

M	src/tcmalloc.cc
M	src/tests/tcmalloc_unittest.cc
M	src/windows/patch_functions.cc

commit a94d5f797412bac7b811bf8d69d9298fec54f5cc
Author: csilvers <csilvers@6b5cf1ce-ec42-a296-1ba9-69fdba395a50>
Date:	Wed Dec 2 18:15:13 2009 +0000

	    * Prefer __environ to /proc/self/environ (csilvers)
	    * Add HEAP_CHECK_MAX_LEAKS envvar (glider)
	    * BUGFIX: debugallocation now calls cpp_alloc for new
	    (willchan)
	    * BUGFIX: tc_set_new_mode() respected for realloc and calloc
	    (willchan)
	    * BUGFIX: fix opt-mode maybe-crash on debugallocation_test
	    (csilvers)
	    * Print alloc size when mmap fails (hakon)
	    * Add ITIMER_REAL support (csilvers, nabeelmian)
	    * BUGFIX: correctly report double-frees (csilvers)
	    * Export tc_set_new_mode() from the .h file (willchan)
	    * Restructure Symbolize to make it more efficient (glider)
	    * PORTING: Augment sysinfo to work on 64-bit OS X (csilvers)
	    * Add two numeric pageheap properties to MallocExtension
	    (fikes)
	    * PORTING: Use libunwind for i386 when using --omitfp
	    (ppluzhnikov)
	    * Add ReleaseToSystem(num_bytes) (kash)
	    * Provide correct library filenames under solaris (jeffrey)
	    * BUGFIX: simple fix in pprof --raw mode (mrabkin)
	    * PORTING: Prefer sys/ucontext.h to fix OS 10.6 builds
	    (csilvers)
	    * Improve support for inlined functions in pprof (sanjay)
	    * Update wget code to not use keepalive (mrabkin, csilvers)
	    * PORTING: correctly handle x86_64 machines that use fp's
	    (csilvers)


    git-svn-id: http://gperftools.googlecode.com/svn/trunk@79
    6b5cf1ce-ec42-a296-1ba9-69fdba395a50

M	configure
M	configure.ac
M	doc/cpuprofile.html
M	doc/heap_checker.html
M	m4/compiler_characteristics.m4
M	src/base/logging.h
M	src/base/sysinfo.cc
M	src/config.h.in
M	src/debugallocation.cc
M	src/google/malloc_extension.h
M	src/google/malloc_extension_c.h
M	src/google/tcmalloc.h.in
M	src/heap-profile-table.cc
M	src/malloc_extension.cc
M	src/memfs_malloc.cc
M	src/pprof
M	src/profile-handler.cc
M	src/symbolize.cc
M	src/symbolize.h
M	src/tcmalloc.cc
M	src/tests/atomicops_unittest.cc
M	src/tests/debugallocation_test.cc
M	src/tests/malloc_extension_test.cc
M	src/tests/profile-handler_unittest.cc
M	src/tests/profiler_unittest.sh
M	src/tests/tcmalloc_unittest.cc
M	src/windows/config.h
M	src/windows/google/tcmalloc.h

commit 5b80f01df1137337131b4c50ce97faaff9973e90
Author: csilvers <csilvers@6b5cf1ce-ec42-a296-1ba9-69fdba395a50>
Date:	Tue Nov 10 16:24:57 2009 +0000

	    * Replace usleep() and poll() with nanosleep() (glider)
	    * Document problems with _recalloc (csilvers)
	    * Detect when x86_64 doesn't turn off frame pointers
	    (csilvers)
	    * Fix sysinfo.cc/etc to work with 64-bit os x (csilvers)
	    * BUGFIX: Use __TEXT instead of __DATA to store tcmalloc fns
	    (csilvers)
	    * Added two numeric pageheap properties to tcmalloc (fikes)
	    * Support for mallocranges stats visualization (sanjay)
	    * Use libunwind for i386, not just x86_64 (ppluzhnikov)
	    * Add ReleaseToSystem(num_bytes) (kash)
	    * Provide corect library filenames under solaris (jeffrey)
	    * BUGFIX: a simple bug in pprof --raw mode (mrabkin)
	    * Prfer sys/ucontext.h to ucontext.h, to fix OS X 10.6
	    (csilvers)
	    * Improve supprot for inlined functions in pprof (sanjay)
	    * Document inaccuracies in profiling mmap calls (csilvers)
	    * Update wget code to not use keepalive (mrabkin, csilvers)


    git-svn-id: http://gperftools.googlecode.com/svn/trunk@78
    6b5cf1ce-ec42-a296-1ba9-69fdba395a50

M	Makefile.am
M	Makefile.in
M	README.windows
M	configure
M	configure.ac
M	doc/heapprofile.html
M	google-perftools.sln
M	m4/pc_from_ucontext.m4
M	src/base/basictypes.h
M	src/base/dynamic_annotations.h
M	src/base/sysinfo.cc
M	src/base/sysinfo.h
M	src/debugallocation.cc
M	src/google/heap-checker.h
M	src/google/malloc_extension.h
M	src/google/malloc_extension_c.h
M	src/heap-checker.cc
M	src/malloc_extension.cc
M	src/page_heap.cc
M	src/page_heap.h
M	src/pagemap.h
M	src/pprof
M	src/stacktrace_config.h
M	src/symbolize.cc
M	src/symbolize.h
M	src/tcmalloc.cc
M	src/tests/malloc_extension_c_test.c
A	src/tests/page_heap_test.cc
M	src/tests/pagemap_unittest.cc
M	src/tests/profile-handler_unittest.cc
M	src/tests/tcmalloc_unittest.cc
A	vsprojects/page_heap_test/page_heap_test.vcproj

commit 25eed16e1b042a80c9a3e83bbf7ed227d04fb45a
Author: csilvers <csilvers@6b5cf1ce-ec42-a296-1ba9-69fdba395a50>
Date:	Tue Oct 27 17:30:52 2009 +0000

	    * Fix Symbolize() to call pprof once, rather than once/symbol
	    (glider)
	    * Fix unsetting of hooks before forking, in debug mode (maxim)
	    * Add some documention for pmuprofile (aruns)
	    * Speed up futex with FUTEX_PRIVATE_FLAG (m3b)
	    * Fix os x 10.6: prefer sys/ucontext.h to ucontext.h
	    (csilvers)
	    * Fix C shims to be actually valid C: malloc_extension/etc
	    (csilvers)
	    * Fix a longtime memset bug (csilvers)
	    * Implement nothrow versions of delete (csilvers)
	    * Fix recursively-unmapped-region accounting (ppluzhnikov)
	    * Better distinguish between real and fake VDSO (ppluzhnikov)
	    * Modify span coalescing to improve performance (sanjay)
	    * WINDOWS: Remove unnecessary lock around VirtualAlloc
	    (mbelshe)
	    * Remove performance tests for ptmalloc2 (csilvers)


    git-svn-id: http://gperftools.googlecode.com/svn/trunk@77
    6b5cf1ce-ec42-a296-1ba9-69fdba395a50

M	Makefile.am
M	Makefile.in
M	configure
M	configure.ac
M	doc/pprof_remote_servers.html
M	src/base/spinlock_linux-inl.h
M	src/base/vdso_support.cc
M	src/config.h.in
M	src/debugallocation.cc
M	src/getpc.h
M	src/google/malloc_extension_c.h
M	src/google/tcmalloc.h.in
M	src/heap-checker.cc
M	src/heap-profile-table.cc
M	src/malloc_extension.cc
M	src/memory_region_map.cc
M	src/page_heap.cc
M	src/page_heap.h
M	src/pprof
M	src/profiler.cc
M	src/sampler.cc
M	src/stacktrace_libunwind-inl.h
M	src/stacktrace_x86-inl.h
M	src/symbolize.cc
M	src/symbolize.h
M	src/tcmalloc.cc
M	src/tests/heap-checker-death_unittest.sh
M	src/tests/heap-checker_unittest.cc
A	src/tests/malloc_extension_c_test.c
D	src/tests/ptmalloc/COPYRIGHT
D	src/tests/ptmalloc/lran2.h
D	src/tests/ptmalloc/malloc-machine.h
D	src/tests/ptmalloc/t-test.h
D	src/tests/ptmalloc/t-test1.c
D	src/tests/ptmalloc/t-test2.c
D	src/tests/ptmalloc/thread-m.h
D	src/tests/ptmalloc/thread-st.h
M	src/tests/tcmalloc_unittest.cc
M	src/windows/config.h
M	src/windows/google/tcmalloc.h
M	src/windows/port.cc

commit 19dfa9e3733155e57406fbd082273eb53cb2750e
Author: csilvers <csilvers@6b5cf1ce-ec42-a296-1ba9-69fdba395a50>
Date:	Fri Sep 11 18:42:32 2009 +0000

    Thu Sep 10 13:51:15 2009  Google Inc. <<EMAIL>>

	    * google-perftools: version 1.4 release
	    * Add debugallocation library, to catch memory leaks,
	    stomping, etc
	    * Add --raw mode to allow for delayed processing of pprof
	    files
	    * Use less memory when reading CPU profiles
	    * New environment variables to control kernel-allocs (sbrk,
	    memfs, etc)
	    * Add MarkThreadBusy(): performance improvement
	    * Remove static thread-cache-size code; all is dynamic now
	    * Add new HiddenPointer class to heap checker
	    * BUGFIX: pvalloc(0) allocates now (found by new debugalloc
	    library)
	    * BUGFIX: valloc test (not implementation) no longer
	    overruns memory
	    * BUGFIX: GetHeapProfile no longer deadlocks
	    * BUGFIX: Support unmapping memory regions before main
	    * BUGFIX: Fix some malloc-stats formatting
	    * BUGFIX: Don't crash as often when freeing libc-allocated
	    memory
	    * BUGFIX: Deal better with incorrect PPROF_PATH when
	    symbolizing
	    * BUGFIX: weaken new/delete/etc in addition to malloc/free/etc
	    * BUGFIX: Fix return value of GetAllocatedSize
	    * PORTING: Fix mmap-#define problem on some 64-bit systems
	    * PORTING: Call ranlib again (some OS X versions need it)
	    * PORTING: Fix a leak when building with LLVM
	    * PORTING: Remove some unneeded bash-ishs from testing scripts
	    * WINDOWS: Support library unloading as well as loading
	    * WINDOWS/BUGFIX: Set page to 'xrw' instead of 'rw' when
	    patching


    git-svn-id: http://gperftools.googlecode.com/svn/trunk@76
    6b5cf1ce-ec42-a296-1ba9-69fdba395a50

M	ChangeLog
M	Makefile.am
M	Makefile.in
M	aclocal.m4
M	configure
M	configure.ac
M	doc/tcmalloc.html
M	google-perftools.sln
M	m4/install_prefix.m4
A	m4/pc_from_ucontext.m4
M	packages/deb/changelog
M	src/base/atomicops-internals-macosx.h
M	src/base/atomicops-internals-x86.cc
M	src/base/atomicops-internals-x86.h
M	src/base/basictypes.h
M	src/base/dynamic_annotations.cc
M	src/base/dynamic_annotations.h
M	src/base/logging.h
M	src/base/low_level_alloc.cc
M	src/base/low_level_alloc.h
M	src/base/simple_mutex.h
M	src/base/spinlock.cc
M	src/base/spinlock.h
A	src/base/spinlock_linux-inl.h
A	src/base/spinlock_posix-inl.h
A	src/base/spinlock_win32-inl.h
M	src/base/sysinfo.cc
M	src/base/sysinfo.h
M	src/base/vdso_support.cc
M	src/base/vdso_support.h
M	src/common.cc
M	src/common.h
M	src/config.h.in
A	src/debugallocation.cc
M	src/getpc.h
M	src/google/heap-checker.h
M	src/google/malloc_extension.h
M	src/google/malloc_extension_c.h
M	src/google/tcmalloc.h.in
M	src/heap-checker.cc
M	src/heap-profile-table.cc
M	src/heap-profiler.cc
M	src/malloc_extension.cc
M	src/malloc_hook.cc
M	src/memfs_malloc.cc
M	src/memory_region_map.cc
M	src/packed-cache-inl.h
M	src/page_heap.h
M	src/pagemap.h
M	src/pprof
M	src/profiler.cc
M	src/sampler.h
M	src/stacktrace_win32-inl.h
M	src/stacktrace_with_context.cc
M	src/stacktrace_x86-inl.h
A	src/symbolize.cc
C060	src/stacktrace_with_context.cc	src/symbolize.h
M	src/system-alloc.cc
M	src/tcmalloc.cc
M	src/tests/addressmap_unittest.cc
A	src/tests/debugallocation_test.cc
A	src/tests/debugallocation_test.sh
M	src/tests/heap-checker_unittest.cc
M	src/tests/heap-profiler_unittest.cc
M	src/tests/heap-profiler_unittest.sh
M	src/tests/low_level_alloc_unittest.cc
M	src/tests/malloc_extension_test.cc
M	src/tests/markidle_unittest.cc
M	src/tests/memalign_unittest.cc
M	src/tests/pagemap_unittest.cc
M	src/tests/profile-handler_unittest.cc
M	src/tests/profiledata_unittest.cc
M	src/tests/profiler_unittest.cc
M	src/tests/profiler_unittest.sh
M	src/tests/sampler_test.cc
M	src/tests/stacktrace_unittest.cc
M	src/tests/tcmalloc_unittest.cc
M	src/thread_cache.cc
M	src/thread_cache.h
M	src/windows/config.h
M	src/windows/google/tcmalloc.h
M	src/windows/patch_functions.cc
M	src/windows/port.h
M	src/windows/preamble_patcher.cc
M	vsprojects/addr2line-pdb/addr2line-pdb.vcproj
M	vsprojects/addressmap_unittest/addressmap_unittest.vcproj
M	vsprojects/frag_unittest/frag_unittest.vcproj
M	vsprojects/libtcmalloc_minimal/libtcmalloc_minimal.vcproj
M
vsprojects/low_level_alloc_unittest/low_level_alloc_unittest.vcproj
M	vsprojects/malloc_extension_test/malloc_extension_test.vcproj
M	vsprojects/markidle_unittest/markidle_unittest.vcproj
M	vsprojects/nm-pdb/nm-pdb.vcproj
M	vsprojects/packed-cache_test/packed-cache_test.vcproj
M	vsprojects/pagemap_unittest/pagemap_unittest.vcproj
M	vsprojects/realloc_unittest/realloc_unittest.vcproj
M	vsprojects/sampler_test/sampler_test.vcproj
M	vsprojects/stack_trace_table_test/stack_trace_table_test.vcproj
M
vsprojects/tcmalloc_minimal_large/tcmalloc_minimal_large_unittest.vcproj
M
vsprojects/tcmalloc_minimal_unittest/tcmalloc_minimal_unittest.vcproj
M	vsprojects/thread_dealloc_unittest/thread_dealloc_unittest.vcproj
M	vsprojects/tmu-static/tmu-static.vcproj

commit 2197cc670204c583bba3903b765c77620f349609
Author: csilvers <csilvers@6b5cf1ce-ec42-a296-1ba9-69fdba395a50>
Date:	Wed Jun 10 02:04:26 2009 +0000

    Tue Jun  9 18:19:06 2009  Google Inc. <<EMAIL>>

	    * google-perftools: version 1.3 release
	    * Provide our own name for memory functions: tc_malloc, etc
	    (csilvers)
	    * Weaken memory-alloc functions so user can override them
	    (csilvers)
	    * Remove meaningless delete(nothrow) and delete[](nothrow)
	    (csilvers)
	    * BUILD: replace clever libtcmalloc/profiler.a with a new .a
	    (csilvers)
	    * PORTING: improve windows port  by using google spinlocks
	    (csilvers)
	    * PORTING: Fix RedHat 9 memory allocation in heapchecker
	    (csilvers)
	    * PORTING: Rename OS_WINDOWS macro to PLATFORM_WINDOWS
	    (mbelshe)
	    * PORTING/BUGFIX: Make sure we don't clobber GetLastError
	    (mbelshe)
	    * BUGFIX: get rid of useless data for callgrind (weidenrinde)
	    * BUGFIX: Modify windows patching to deadlock sometimes
	    (csilvers)
	    * BUGFIX: an improved fix for hook handling during fork
	    (csilvers)
	    * BUGFIX: revamp profiler_unittest.sh, which was very broken
	    (csilvers)


    git-svn-id: http://gperftools.googlecode.com/svn/trunk@74
    6b5cf1ce-ec42-a296-1ba9-69fdba395a50

M	ChangeLog
M	Makefile.am
M	Makefile.in
M	README
M	aclocal.m4
M	configure
M	configure.ac
D	m4/stl_hash.m4
M	packages/deb/changelog
M	packages/rpm/rpm.spec
M	src/base/atomicops-internals-linuxppc.h
M	src/base/dynamic_annotations.cc
M	src/base/dynamic_annotations.h
M	src/base/spinlock.cc
M	src/base/sysinfo.cc
M	src/base/sysinfo.h
M	src/base/vdso_support.cc
M	src/base/vdso_support.h
M	src/config.h.in
A	src/google/tcmalloc.h.in
M	src/heap-checker.cc
M	src/pprof
M	src/profile-handler.cc
M	src/tcmalloc.cc
M	src/tests/heap-checker-death_unittest.sh
M	src/tests/profiler_unittest.sh
M	src/tests/stacktrace_unittest.cc
M	src/windows/config.h
A	src/windows/google/tcmalloc.h
M	src/windows/override_functions.cc
M	src/windows/patch_functions.cc

commit 104bf697fbd2a0b90b5f01344ee01c8caa1745d0
Author: csilvers <csilvers@6b5cf1ce-ec42-a296-1ba9-69fdba395a50>
Date:	Mon May 18 22:50:20 2009 +0000

    Use the google spinlock code instead of the built-in windows code.
    The main benefit for perftools is that google spinlocks allow for
    link-time (static) initialization, which we had to simulate before,
    yielding bugs and worse performance.


    git-svn-id: http://gperftools.googlecode.com/svn/trunk@73
    6b5cf1ce-ec42-a296-1ba9-69fdba395a50

M	src/base/atomicops-internals-x86-msvc.h
M	src/base/spinlock.cc
M	src/base/spinlock.h
M	src/windows/config.h
M	src/windows/patch_functions.cc
M	src/windows/port.cc
M	src/windows/port.h
M	vsprojects/addressmap_unittest/addressmap_unittest.vcproj
M	vsprojects/libtcmalloc_minimal/libtcmalloc_minimal.vcproj
M
vsprojects/low_level_alloc_unittest/low_level_alloc_unittest.vcproj
M	vsprojects/packed-cache_test/packed-cache_test.vcproj
M	vsprojects/tmu-static/tmu-static.vcproj

commit ad03b009ef2046cee9dc38afe022b487de37db5c
Author: csilvers <csilvers@6b5cf1ce-ec42-a296-1ba9-69fdba395a50>
Date:	Sat Apr 25 01:01:23 2009 +0000

    In the case of windows with HAS_EXCEPTIONS turned off we weren't able
    to use the std::set_new_handler correctly.	Rework the #ifdefs to
    allow use of the std_new_handler, but ignore the exceptions.

    Patch submitted by mbelshe.


    git-svn-id: http://gperftools.googlecode.com/svn/trunk@72
    6b5cf1ce-ec42-a296-1ba9-69fdba395a50

M	src/tcmalloc.cc

commit 1894763f57b00f72a720c4c126815c073d9ed0f3
Author: csilvers <csilvers@6b5cf1ce-ec42-a296-1ba9-69fdba395a50>
Date:	Wed Apr 22 22:53:41 2009 +0000

    Make sure we don't clobber GetLastError() (for windows).


    git-svn-id: http://gperftools.googlecode.com/svn/trunk@71
    6b5cf1ce-ec42-a296-1ba9-69fdba395a50

M	src/windows/port.h

commit a63235c4331445d718d151a2a659ec9687a0b8c8
Author: csilvers <csilvers@6b5cf1ce-ec42-a296-1ba9-69fdba395a50>
Date:	Tue Apr 21 17:10:29 2009 +0000

    1) Change #include "config.h" to <config.h>.  This is what automake
    recommends, and makes it easier to override a config file.

    2) Rename OS_WINDOWS in sysinfo.cc, to not conflict with a macro
    defined in a windows SDK somewhere.


    git-svn-id: http://gperftools.googlecode.com/svn/trunk@70
    6b5cf1ce-ec42-a296-1ba9-69fdba395a50

M	src/base/atomicops.h
M	src/base/basictypes.h
M	src/base/commandlineflags.h
M	src/base/elfcore.h
M	src/base/logging.cc
M	src/base/logging.h
M	src/base/low_level_alloc.h
M	src/base/simple_mutex.h
M	src/base/spinlock.cc
M	src/base/spinlock.h
M	src/base/stl_allocator.h
M	src/base/sysinfo.cc
M	src/base/sysinfo.h
M	src/base/vdso_support.h
M	src/heap-checker.cc
M	src/heap-profile-table.cc
M	src/heap-profiler.cc
M	src/internal_logging.cc
M	src/internal_logging.h
M	src/malloc_extension.cc
M	src/malloc_hook.cc
M	src/memfs_malloc.cc
M	src/memory_region_map.cc
M	src/memory_region_map.h
M	src/page_heap.cc
M	src/page_heap.h
M	src/profiledata.cc
M	src/profiledata.h
M	src/raw_printer.cc
M	src/raw_printer.h
M	src/span.cc
M	src/span.h
M	src/stack_trace_table.cc
M	src/stack_trace_table.h
M	src/stacktrace.cc
M	src/stacktrace_with_context.cc
M	src/static_vars.h
M	src/system-alloc.cc
M	src/system-alloc.h
M	src/tcmalloc.cc
M	src/tcmalloc.h
M	src/thread_cache.cc
M	src/thread_cache.h
M	src/windows/override_functions.cc
M	src/windows/patch_functions.cc
M	src/windows/port.cc

commit beb6a9a183c1ca25c99e4401b58266ce73b8c846
Author: csilvers <csilvers@6b5cf1ce-ec42-a296-1ba9-69fdba395a50>
Date:	Sat Apr 18 00:02:25 2009 +0000

    Fri Apr 17 16:40:48 2009  Google Inc. <<EMAIL>>

	    * google-perftools: version 1.2 release
	    * Allow large_alloc_threshold=0 to turn it off entirely
	    (csilvers)
	    * Die more helpfully when out of memory for internal data
	    (csilvers)
	    * Refactor profile-data gathering, add a new unittest (cgd,
	    nabeelmian)
	    * BUGFIX: fix rounding errors with static thread-size caches
	    (addi)
	    * BUGFIX: disable hooks better when forking in leak-checker
	    (csilvers)
	    * BUGFIX: fix realloc of crt pointers on windows (csilvers)
	    * BUGFIX: do a better job of finding binaries in .sh tests
	    (csilvers)
	    * WINDOWS: allow overriding malloc/etc instead of patching
	    (mbelshe)
	    * PORTING: fix compilation error in a ppc-specific file
	    (csilvers)
	    * PORTING: deal with quirks in cygwin's /proc/self/maps
	    (csilvers)
	    * PORTING: use 'A' version of functions for ascii input
	    (mbelshe)
	    * PORTING: generate .so's on cygwin and mingw (ajenjo)
	    * PORTING: disable profiler methods on cygwin (jperkins)
	    * Updated autoconf version to 2.61 and libtool version
	    to 1.5.26


    git-svn-id: http://gperftools.googlecode.com/svn/trunk@68
    6b5cf1ce-ec42-a296-1ba9-69fdba395a50

M	ChangeLog
M	INSTALL
M	Makefile.am
M	Makefile.in
M	README.windows
M	aclocal.m4
M	config.guess
M	config.sub
M	configure
M	configure.ac
M	ltmain.sh
M	packages/deb/changelog
M	packages/rpm.sh
M	packages/rpm/rpm.spec
M	src/base/atomicops-internals-linuxppc.h
M	src/base/logging.cc
M	src/base/logging.h
M	src/base/simple_mutex.h
M	src/base/sysinfo.cc
M	src/base/sysinfo.h
M	src/config.h.in
M	src/google/heap-checker.h
M	src/google/profiler.h
M	src/heap-checker.cc
M	src/heap-profile-table.cc
M	src/heap-profile-table.h
M	src/page_heap_allocator.h
M	src/pprof
A	src/profile-handler.cc
A	src/profile-handler.h
M	src/profiledata.cc
M	src/profiledata.h
M	src/profiler.cc
M	src/sampler.cc
M	src/stacktrace_win32-inl.h
M	src/stacktrace_x86-inl.h
M	src/tcmalloc.cc
M	src/tests/frag_unittest.cc
A	src/tests/profile-handler_unittest.cc
M	src/tests/profiledata_unittest.cc
M	src/tests/sampling_test.cc
M	src/tests/sampling_test.sh
M	src/tests/testutil.cc
M	src/thread_cache.cc
M	src/windows/addr2line-pdb.c
M	src/windows/config.h
M	src/windows/mingw.h
M	src/windows/nm-pdb.c
A	src/windows/override_functions.cc
M	src/windows/patch_functions.cc
M	src/windows/port.cc
M	src/windows/port.h

commit edd03a831f350bc72d76d4fad2b390d43faccb79
Author: csilvers <csilvers@6b5cf1ce-ec42-a296-1ba9-69fdba395a50>
Date:	Wed Mar 11 20:50:03 2009 +0000

    Wed Mar 11 11:25:34 2009  Google Inc. <<EMAIL>>

	    * google-perftools: version 1.1 release
	    * Dynamically resize thread caches -- nice perf. improvement
	    (kash)
	    * Add VDSO support to give better stacktraces in linux
	    (ppluzhnikov)
	    * Improve heap-profiling sampling algorithm (ford)
	    * Rewrite leak-checking code: should be faster and more robust
	    (sanjay)
	    * Use ps2 instead of ps for dot: better page cropping for gv
	    (csilvers)
	    * Disable malloc-failure warning messages by default
	    (csilvers)
	    * Update config/Makefile to disable tests on a per-OS basis
	    (csilvers)
	    * PORTING: Get perftools compiling under MSVC 7.1 again
	    (csilvers)
	    * PORTING: Get perftools compiling under cygwin again
	    (csilvers)
	    * PORTING: automatically set library flags for solaris x86
	    (csilvers)
	    * Add TCMALLOC_SKIP_SBRK to mirror TCMALLOC_SKIP_MMAP
	    (csilvers)
	    * Add --enable flags to allow selective building (csilvers)
	    * Put addr2line-pdb and nm-pdb in proper output directory
	    (csilvers)
	    * Remove deprecated DisableChecksIn (sanjay)
	    * DOCUMENTATION: Document most MallocExtension routines
	    (csilvers)


    git-svn-id: http://gperftools.googlecode.com/svn/trunk@66
    6b5cf1ce-ec42-a296-1ba9-69fdba395a50

M	ChangeLog
M	INSTALL
M	Makefile.am
M	Makefile.in
M	README
M	README.windows
M	aclocal.m4
M	configure
M	configure.ac
M	doc/pprof_remote_servers.html
M	doc/tcmalloc.html
M	google-perftools.sln
A	m4/acx_nanosleep.m4
M	packages/deb.sh
M	packages/deb/changelog
M	src/base/basictypes.h
M	src/base/cycleclock.h
M	src/base/dynamic_annotations.cc
M	src/base/dynamic_annotations.h
M	src/base/linux_syscall_support.h
M	src/base/simple_mutex.h
M	src/base/spinlock.h
M	src/base/sysinfo.cc
M	src/base/sysinfo.h
M	src/base/thread_annotations.h
A	src/base/vdso_support.cc
A	src/base/vdso_support.h
M	src/central_freelist.cc
M	src/common.cc
M	src/common.h
M	src/config.h.in
M	src/google/heap-checker.h
M	src/google/malloc_extension.h
M	src/google/malloc_extension_c.h
M	src/google/stacktrace.h
M	src/heap-checker.cc
M	src/heap-profile-table.cc
M	src/heap-profile-table.h
M	src/heap-profiler.cc
M	src/internal_logging.cc
M	src/internal_logging.h
M	src/malloc_extension.cc
M	src/malloc_hook.cc
M	src/memory_region_map.cc
M	src/page_heap.cc
M	src/pprof
M	src/profiler.cc
C061	src/static_vars.cc	src/raw_printer.cc
A	src/raw_printer.h
A	src/sampler.cc
A	src/sampler.h
M	src/span.cc
A	src/stack_trace_table.cc
C056	src/static_vars.cc	src/stack_trace_table.h
M	src/stacktrace.cc
C057	src/stacktrace.cc	src/stacktrace_config.h
M	src/stacktrace_libunwind-inl.h
M	src/stacktrace_win32-inl.h
C059	src/static_vars.cc	src/stacktrace_with_context.cc
M	src/stacktrace_x86-inl.h
M	src/stacktrace_x86_64-inl.h
M	src/static_vars.cc
M	src/static_vars.h
M	src/system-alloc.cc
M	src/tcmalloc.cc
M	src/tests/heap-checker-death_unittest.sh
M	src/tests/heap-checker_unittest.cc
M	src/tests/heap-profiler_unittest.sh
C060	src/tests/sampling_test.cc	src/tests/malloc_extension_test.cc
A	src/tests/pagemap_unittest.cc
A	src/tests/raw_printer_test.cc
A	src/tests/realloc_unittest.cc
A	src/tests/sampler_test.cc
M	src/tests/sampling_test.cc
M	src/tests/sampling_test.sh
A	src/tests/stack_trace_table_test.cc
M	src/tests/tcmalloc_unittest.cc
M	src/thread_cache.cc
M	src/thread_cache.h
M	src/windows/addr2line-pdb.c
M	src/windows/config.h
M	src/windows/patch_functions.cc
M	src/windows/port.h
M	vsprojects/addr2line-pdb/addr2line-pdb.vcproj
M	vsprojects/libtcmalloc_minimal/libtcmalloc_minimal.vcproj
M
vsprojects/low_level_alloc_unittest/low_level_alloc_unittest.vcproj
C061	vsprojects/nm-pdb/nm-pdb.vcproj
vsprojects/malloc_extension_test/malloc_extension_test.vcproj
M	vsprojects/nm-pdb/nm-pdb.vcproj
C064	vsprojects/nm-pdb/nm-pdb.vcproj
vsprojects/pagemap_unittest/pagemap_unittest.vcproj
C065	vsprojects/nm-pdb/nm-pdb.vcproj
vsprojects/realloc_unittest/realloc_unittest.vcproj
C063	vsprojects/nm-pdb/nm-pdb.vcproj
vsprojects/sampler_test/sampler_test.vcproj
C061	vsprojects/nm-pdb/nm-pdb.vcproj
vsprojects/stack_trace_table_test/stack_trace_table_test.vcproj
M	vsprojects/tmu-static/tmu-static.vcproj

commit c75de4d1e91c339fb5142a8a21be8b3ba5224ef7
Author: csilvers <csilvers@6b5cf1ce-ec42-a296-1ba9-69fdba395a50>
Date:	Tue Jan 6 19:41:15 2009 +0000

    Tue Jan  6 13:58:56 2009  Google Inc. <<EMAIL>>
	    * google-perftools: version 1.0 release
	    * Exactly the same as 1.0rc2


    git-svn-id: http://gperftools.googlecode.com/svn/trunk@64
    6b5cf1ce-ec42-a296-1ba9-69fdba395a50

M	ChangeLog
M	configure
M	configure.ac
M	packages/deb/changelog
M	src/pprof

commit 49b02736109f9fe3383971e62bda2192ae86a36d
Author: csilvers <csilvers@6b5cf1ce-ec42-a296-1ba9-69fdba395a50>
Date:	Mon Dec 15 01:20:31 2008 +0000

    Sun Dec 14 17:10:35 2008  Google Inc. <<EMAIL>>
	    * google-perftools: version 1.0rc2 release
	    * Fix compile error on 64-bit systems (casting ptr to int)
	    (csilvers)

    Thu Dec 11 16:01:32 2008  Google Inc. <<EMAIL>>


    git-svn-id: http://gperftools.googlecode.com/svn/trunk@62
    6b5cf1ce-ec42-a296-1ba9-69fdba395a50

M	ChangeLog
M	configure
M	configure.ac
M	src/heap-checker.cc

commit 6fa2a2574ce1c15ac12293e24691d69a41972e54
Author: csilvers <csilvers@6b5cf1ce-ec42-a296-1ba9-69fdba395a50>
Date:	Sat Dec 13 01:35:42 2008 +0000

    Thu Dec 11 16:01:32 2008  Google Inc. <<EMAIL>>

	    * google-perftools: version 1.0rc1 release
	    * Replace API for selectively disabling heap-checker in code
	    (sanjay)
	    * Add a pre-mmap hook (daven, adlr)
	    * Add MallocExtension interface to set memory-releasing rate
	    (fikes)
	    * Augment pprof to allow any string ending in /pprof/profile
	    (csilvers)
	    * PORTING: Rewrite -- and fix --  malloc patching for windows
	    (dvitek)
	    * PORTING: Add nm-pdb and addr2line-pdb for use by pprof
	    (dvitek)
	    * PORTING: Improve cygwin and mingw support (jperkins,
	    csilvers)
	    * PORTING: Fix pprof for mac os x, other pprof improvements
	    (csilvers)
	    * PORTING: Fix some PPC bugs in our locking code
	    (anton.blanchard)
	    * A new unittest, smapling_test, to verify tcmalloc-profiles
	    (csilvers)
	    * Turn off TLS for gcc < 4.1.2, due to a TLS + -fPIC bug
	    (csilvers)
	    * Prefer __builtin_frame_address to assembly for stacktraces
	    (nlewycky)
	    * Separate tcmalloc.cc out into multiple files --
	    finally! (kash)
	    * Make our locking code work with -fPIC on 32-bit x86 (aruns)
	    * Fix an initialization-ordering bug for tcmalloc/profiling
	    (csilvers)
	    * Use "initial exec" model of TLS to speed up tcmalloc
	    (csilvers)
	    * Enforce 16-byte alignment for tcmalloc, for SSE (sanjay)


    git-svn-id: http://gperftools.googlecode.com/svn/trunk@60
    6b5cf1ce-ec42-a296-1ba9-69fdba395a50

M	ChangeLog
M	INSTALL
M	Makefile.am
M	Makefile.in
M	README.windows
M	configure
M	configure.ac
M	doc/heap_checker.html
M	doc/pprof_remote_servers.html
M	google-perftools.sln
M	packages/deb.sh
M	packages/deb/changelog
M	src/base/atomicops-internals-linuxppc.h
M	src/base/atomicops-internals-macosx.h
M	src/base/atomicops-internals-x86.h
M	src/base/basictypes.h
M	src/base/linux_syscall_support.h
M	src/base/logging.cc
M	src/base/logging.h
M	src/base/simple_mutex.h
M	src/base/sysinfo.cc
M	src/base/sysinfo.h
A	src/base/thread_annotations.h
M	src/base/thread_lister.c
A	src/central_freelist.cc
A	src/central_freelist.h
A	src/common.cc
A	src/common.h
M	src/getpc.h
M	src/google/heap-checker.h
M	src/google/heap-profiler.h
M	src/google/malloc_extension.h
M	src/google/malloc_hook.h
M	src/google/malloc_hook_c.h
M	src/google/profiler.h
M	src/google/stacktrace.h
M	src/heap-checker.cc
M	src/heap-profile-table.cc
M	src/heap-profile-table.h
M	src/heap-profiler.cc
A	src/linked_list.h
M	src/malloc_extension.cc
M	src/malloc_hook-inl.h
M	src/malloc_hook.cc
M	src/memory_region_map.cc
M	src/memory_region_map.h
A	src/page_heap.cc
A	src/page_heap.h
A	src/page_heap_allocator.h
M	src/pprof
M	src/profiler.cc
A	src/span.cc
A	src/span.h
M	src/stacktrace.cc
M	src/stacktrace_libunwind-inl.h
A	src/stacktrace_win32-inl.h
M	src/stacktrace_x86-inl.h
C059	src/base/logging.cc	src/static_vars.cc
A	src/static_vars.h
M	src/tcmalloc.cc
C071	src/base/logging.cc	src/tcmalloc_guard.h
M	src/tests/atomicops_unittest.cc
M	src/tests/frag_unittest.cc
M	src/tests/heap-checker_unittest.cc
M	src/tests/low_level_alloc_unittest.cc
C052	src/base/logging.cc	src/tests/sampling_test.cc
A	src/tests/sampling_test.sh
M	src/tests/stacktrace_unittest.cc
M	src/tests/tcmalloc_unittest.cc
M	src/tests/testutil.cc
A	src/thread_cache.cc
A	src/thread_cache.h
A	src/windows/addr2line-pdb.c
A	src/windows/get_mangled_names.cc
M	src/windows/mingw.h
M	src/windows/mini_disassembler.cc
M	src/windows/mini_disassembler.h
M	src/windows/mini_disassembler_types.h
A	src/windows/nm-pdb.c
M	src/windows/patch_functions.cc
M	src/windows/port.cc
M	src/windows/port.h
M	src/windows/preamble_patcher.cc
M	src/windows/preamble_patcher.h
M	src/windows/preamble_patcher_with_stub.cc
D	src/windows/vc7and8.def
R059	vsprojects/memalign_unittest/memalign_unittest.vcproj
vsprojects/addr2line-pdb/addr2line-pdb.vcproj
M	vsprojects/frag_unittest/frag_unittest.vcproj
M	vsprojects/libtcmalloc_minimal/libtcmalloc_minimal.vcproj
M
vsprojects/low_level_alloc_unittest/low_level_alloc_unittest.vcproj
M	vsprojects/markidle_unittest/markidle_unittest.vcproj
C068	vsprojects/frag_unittest/frag_unittest.vcproj
vsprojects/nm-pdb/nm-pdb.vcproj
M
vsprojects/tcmalloc_minimal_large/tcmalloc_minimal_large_unittest.vcproj
M
vsprojects/tcmalloc_minimal_unittest/tcmalloc_minimal_unittest.vcproj
M	vsprojects/thread_dealloc_unittest/thread_dealloc_unittest.vcproj
R074
vsprojects/tcmalloc_minimal_unittest-static/tcmalloc_minimal_unittest-static.vcproj
vsprojects/tmu-static/tmu-static.vcproj

commit 16191f87ff8dc78295c0f617060460664fc444bd
Author: csilvers <csilvers@6b5cf1ce-ec42-a296-1ba9-69fdba395a50>
Date:	Tue Sep 23 17:51:05 2008 +0000

    Tue Sep 23 08:56:31 2008  Google Inc. <<EMAIL>>

	    * google-perftools: version 0.99.2 release
	    * COMPILE FIX: add #include needed for FreeBSD and OS X
	    (csilvers)


    git-svn-id: http://gperftools.googlecode.com/svn/trunk@58
    6b5cf1ce-ec42-a296-1ba9-69fdba395a50

M	ChangeLog
M	configure
M	configure.ac
M	src/tests/heap-profiler_unittest.cc

commit 8b2dd25dc9d1523ab9a86bd39c4fb798c89db899
Author: csilvers <csilvers@6b5cf1ce-ec42-a296-1ba9-69fdba395a50>
Date:	Sat Sep 20 17:15:23 2008 +0000

    Sat Sep 20 09:37:18 2008  Google Inc. <<EMAIL>>

	    * google-perftools: version 0.99.1 release
	    * BUG FIX: look for nm, etc in /usr/bin, not /usr/crosstool
	    (csilvers)


    git-svn-id: http://gperftools.googlecode.com/svn/trunk@56
    6b5cf1ce-ec42-a296-1ba9-69fdba395a50

M	ChangeLog
M	configure
M	configure.ac
M	packages/deb/changelog
M	src/pprof

commit 106aef86ce5697cf44bdbec90ab6833b9254d273
Author: csilvers <csilvers@6b5cf1ce-ec42-a296-1ba9-69fdba395a50>
Date:	Fri Sep 19 20:06:40 2008 +0000

    Thu Sep 18 16:00:27 2008  Google Inc. <<EMAIL>>

	    * google-perftools: version 0.99 release
	    * Add IsHeapProfileRunning (csilvers)
	    * Add C shims for some of the C++ header files (csilvers)
	    * Fix heap profile file clean-up logic (maxim)
	    * Rename linuxthreads.c to .cc for better compiler support
	    (csilvers)
	    * Add source info to disassembly in pprof (sanjay)
	    * Use open instead of fopen to avoid memory alloc (csilvers)
	    * Disable malloc extensions when running under valgrind (kcc)
	    * BUG FIX: Fix out-of-bound error by reordering a check
	    (larryz)
	    * Add Options struct to ProfileData (cgd)
	    * Correct PC-handling of --base in pprof (csilvers)
	    * Handle 1 function occurring twice in an image (sanjay)
	    * Improve stack-data cleaning (maxim)
	    * Use 'struct Foo' to make header C compatible (csilvers)
	    * Add 'total' line to pprof --text (csilvers)
	    * Pre-allocate buffer for heap-profiler to avoid OOM errors
	    (csilvers)
	    * Allow a few more env-settings to control tcmalloc (csilvers)
	    * Document some of the issues involving thread-local storage
	    (csilvers)
	    * BUG FIX: Define strtoll and friends for windows (csilvers)


    git-svn-id: http://gperftools.googlecode.com/svn/trunk@54
    6b5cf1ce-ec42-a296-1ba9-69fdba395a50

M	ChangeLog
M	INSTALL
M	Makefile.am
M	Makefile.in
M	README
M	README.windows
M	configure
M	configure.ac
M	doc/heapprofile.html
M	doc/tcmalloc.html
M	packages/deb/changelog
M	src/addressmap-inl.h
M	src/base/basictypes.h
M	src/base/commandlineflags.h
M	src/base/cycleclock.h
M	src/base/dynamic_annotations.cc
M	src/base/dynamic_annotations.h
R099	src/base/linuxthreads.c src/base/linuxthreads.cc
M	src/base/spinlock.h
M	src/base/stl_allocator.h
M	src/base/sysinfo.cc
M	src/google/heap-checker.h
M	src/google/heap-profiler.h
M	src/google/malloc_extension.h
A	src/google/malloc_extension_c.h
M	src/google/malloc_hook.h
A	src/google/malloc_hook_c.h
M	src/google/profiler.h
M	src/google/stacktrace.h
M	src/heap-checker.cc
M	src/heap-profile-table.cc
M	src/heap-profile-table.h
M	src/heap-profiler.cc
M	src/internal_logging.cc
M	src/internal_logging.h
M	src/malloc_extension.cc
M	src/malloc_hook.cc
M	src/memory_region_map.cc
M	src/pprof
M	src/profiledata.cc
M	src/profiledata.h
M	src/profiler.cc
M	src/system-alloc.cc
M	src/tcmalloc.cc
M	src/tests/heap-checker-death_unittest.sh
M	src/tests/heap-checker_unittest.cc
M	src/tests/heap-profiler_unittest.cc
M	src/tests/markidle_unittest.cc
M	src/tests/profiledata_unittest.cc
M	src/windows/port.h

commit 100e657c5092bc274424286a728db5116a4bbc54
Author: csilvers <csilvers@6b5cf1ce-ec42-a296-1ba9-69fdba395a50>
Date:	Sat Jun 14 02:30:53 2008 +0000

    Mon Jun  9 16:47:03 2008  Google Inc. <<EMAIL>>

	    * google-perftools: version 0.98 release
	    * Add ProfilerStartWithOptions() (cgd)
	    * Change tcmalloc_minimal to not do any stack-tracing at all
	    (csilvers)
	    * Prefer mmap to sbrk for 64-buit debug mode (sanjay)
	    * Fix accounting for some tcmalloc stats (sanjay)
	    * Use setrlimit() to keep unittests from killing the machine
	    (odo)
	    * Fix a bug when sbrk-ing near address 4G (csilvers)
	    * Make MallocHook thread-safe (jyasskin)
	    * Fix windows build for MemoryBarrier (jyasskin)
	    * Fix CPU-profiler docs to mention correct libs (csilvers)
	    * Fix for GetHeapProfile() when heap-profiling is off (maxim)
	    * Avoid realloc resizing ping-pongs using hysteresis
	    (csilvers)
	    * Add --callgrind output support to pprof (klimek)
	    * Fix profiler.h and heap-profiler.h to be C-compatible
	    (csilvers)
	    * Break malloc_hook.h into two parts to reduce dependencies
	    (csilvers)
	    * Better handle systems that don't implement mmap (csilvers)
	    * PORTING: disable system_alloc_unittest for msvc (csilvers)
	    * PORTING: Makefile tweaks to build better on cygwin
	    (csilvers)


    git-svn-id: http://gperftools.googlecode.com/svn/trunk@52
    6b5cf1ce-ec42-a296-1ba9-69fdba395a50

M	ChangeLog
M	Makefile.am
M	Makefile.in
M	README.windows
M	configure
M	configure.ac
M	doc/cpuprofile.html
M	google-perftools.sln
M	m4/acx_pthread.m4
M	packages/deb/changelog
M	src/addressmap-inl.h
M	src/base/atomicops-internals-linuxppc.h
M	src/base/atomicops-internals-macosx.h
M	src/base/atomicops-internals-x86-msvc.h
M	src/base/atomicops-internals-x86.cc
M	src/base/atomicops-internals-x86.h
M	src/base/atomicops.h
M	src/base/commandlineflags.h
M	src/base/cycleclock.h
M	src/base/dynamic_annotations.cc
M	src/base/dynamic_annotations.h
M	src/base/linux_syscall_support.h
M	src/base/low_level_alloc.cc
M	src/base/low_level_alloc.h
M	src/base/simple_mutex.h
M	src/base/spinlock.h
M	src/base/stl_allocator.h
M	src/base/sysinfo.h
M	src/config.h.in
M	src/getpc.h
M	src/google/heap-checker.h
M	src/google/heap-profiler.h
M	src/google/malloc_extension.h
M	src/google/malloc_hook.h
M	src/google/profiler.h
M	src/google/stacktrace.h
M	src/heap-checker.cc
M	src/heap-profile-table.h
M	src/heap-profiler.cc
M	src/internal_logging.h
A	src/malloc_hook-inl.h
M	src/malloc_hook.cc
M	src/maybe_threads.cc
M	src/maybe_threads.h
M	src/memory_region_map.cc
M	src/memory_region_map.h
M	src/packed-cache-inl.h
M	src/pagemap.h
M	src/pprof
M	src/profiledata.h
M	src/profiler.cc
M	src/stacktrace_generic-inl.h
M	src/system-alloc.cc
M	src/system-alloc.h
M	src/tcmalloc.cc
M	src/tests/maybe_threads_unittest.sh
M	src/tests/memalign_unittest.cc
M	src/tests/tcmalloc_unittest.cc
M	src/tests/testutil.cc
M	src/tests/testutil.h
M	src/windows/config.h
M	src/windows/mingw.h
M	src/windows/mini_disassembler.h
M	src/windows/mini_disassembler_types.h
M	src/windows/patch_functions.cc
M	src/windows/port.h
M	src/windows/preamble_patcher.h
M	vsprojects/libtcmalloc_minimal/libtcmalloc_minimal.vcproj
M
vsprojects/low_level_alloc_unittest/low_level_alloc_unittest.vcproj
M	vsprojects/memalign_unittest/memalign_unittest.vcproj
D	vsprojects/system_alloc_unittest/system_alloc_unittest.vcproj
M
vsprojects/tcmalloc_minimal_unittest-static/tcmalloc_minimal_unittest-static.vcproj

commit 7ec719093b1c9fda979ba0d07eed288e2a7c3c9b
Author: csilvers <csilvers@6b5cf1ce-ec42-a296-1ba9-69fdba395a50>
Date:	Tue Apr 22 01:47:16 2008 +0000

    Mon Apr 21 15:20:52 2008  Google Inc. <<EMAIL>>

	    * google-perftools: version 0.97 release
	    * Refactor GetHeapProfile to avoid using malloc (maxim)
	    * Fix heap-checker and heap-profiler hook interactions (maxim)
	    * Fix a data race in MemoryRegionMap::Lock (jyasskin)
	    * Improve thread-safety of leak checker (maxim)
	    * Fix mmap profile to no longer deadlock (maxim)
	    * Fix rpm to have devel package depend on non-devel (csilvers)
	    * PORTING: Fix clock-speed detection for Mac OS X (csilvers)


    git-svn-id: http://gperftools.googlecode.com/svn/trunk@50
    6b5cf1ce-ec42-a296-1ba9-69fdba395a50

M	ChangeLog
M	configure
M	configure.ac
M	doc/heapprofile.html
M	packages/deb/changelog
M	packages/rpm/rpm.spec
M	src/addressmap-inl.h
M	src/base/cycleclock.h
M	src/base/elfcore.h
M	src/base/low_level_alloc.cc
M	src/base/spinlock.h
M	src/base/sysinfo.cc
M	src/base/sysinfo.h
M	src/google/heap-checker.h
M	src/google/heap-profiler.h
M	src/google/profiler.h
M	src/heap-checker.cc
M	src/heap-profile-table.cc
M	src/heap-profile-table.h
M	src/heap-profiler.cc
M	src/internal_logging.cc
M	src/internal_logging.h
M	src/memory_region_map.cc
M	src/memory_region_map.h
M	src/pprof
M	src/stacktrace_libunwind-inl.h
M	src/stacktrace_x86-inl.h
M	src/tcmalloc.cc
M	src/tests/heap-checker-death_unittest.sh
M	src/tests/heap-checker_unittest.cc
M	src/tests/heap-profiler_unittest.cc
M	src/tests/low_level_alloc_unittest.cc
M	src/tests/maybe_threads_unittest.sh
M	src/windows/port.cc

commit 97fdd4a4f97dd15e8803ed51ac153903c2cdffc2
Author: csilvers <csilvers@6b5cf1ce-ec42-a296-1ba9-69fdba395a50>
Date:	Wed Mar 19 23:35:27 2008 +0000

    Tue Mar 18 14:30:44 2008  Google Inc. <<EMAIL>>

	    * google-perftools: version 0.96 release
	    * major atomicops rewrite; fixed atomic ops code for linux/ppc
	    (vchen)
	    * nix the stacktrace library; now build structure is simpler
	    (csilvers)
	    * Speed up heap-checker, and reduce extraneous logging (maxim)
	    * Improve itimer code for NPTL case (cgd)
	    * Add source code annotations for use by valgrind, etc (kcc)
	    * PORTING: Fix high resolution timers for Mac OS X (adlr)


    git-svn-id: http://gperftools.googlecode.com/svn/trunk@48
    6b5cf1ce-ec42-a296-1ba9-69fdba395a50

M	ChangeLog
M	INSTALL
M	Makefile.am
M	Makefile.in
M	TODO
M	configure
M	configure.ac
M	doc/cpuprofile.html
M	doc/heapprofile.html
M	packages/deb/changelog
M	packages/rpm/rpm.spec
M	src/addressmap-inl.h
M	src/base/atomicops-internals-linuxppc.h
M	src/base/atomicops-internals-macosx.h
M	src/base/atomicops-internals-x86-msvc.h
M	src/base/atomicops-internals-x86.cc
M	src/base/atomicops-internals-x86.h
M	src/base/atomicops.h
M	src/base/basictypes.h
M	src/base/cycleclock.h
A	src/base/dynamic_annotations.cc
A	src/base/dynamic_annotations.h
M	src/base/linux_syscall_support.h
M	src/base/low_level_alloc.cc
M	src/base/spinlock.cc
M	src/base/spinlock.h
M	src/base/sysinfo.cc
M	src/heap-checker.cc
M	src/heap-profile-table.cc
M	src/memfs_malloc.cc
M	src/pprof
M	src/profiler.cc
M	src/tests/atomicops_unittest.cc
M	src/tests/heap-checker-death_unittest.sh
M	src/tests/maybe_threads_unittest.sh
M	vsprojects/libtcmalloc_minimal/libtcmalloc_minimal.vcproj
M
vsprojects/low_level_alloc_unittest/low_level_alloc_unittest.vcproj
M
vsprojects/tcmalloc_minimal_unittest-static/tcmalloc_minimal_unittest-static.vcproj

commit a644b4f2d61b9610ec6eeb1f09ebce7054aa0762
Author: csilvers <csilvers@6b5cf1ce-ec42-a296-1ba9-69fdba395a50>
Date:	Tue Feb 19 22:19:22 2008 +0000

    Tue Feb 19 12:01:31 2008  Google Inc. <<EMAIL>>

	    * google-perftools: version 0.95.1 release	(bugfix release)
	    * x86_64 compile-fix: nix pread64 and pwrite64 (csilvers)
	    * more heap-checker debug logging (maxim)
	    * minor improvement to x86_64 CycleClock (gpike)


    git-svn-id: http://gperftools.googlecode.com/svn/trunk@46
    6b5cf1ce-ec42-a296-1ba9-69fdba395a50

M	ChangeLog
M	configure
M	configure.ac
M	src/base/cycleclock.h
M	src/base/linux_syscall_support.h
M	src/heap-checker.cc

commit 8a0a3101bc6a7d56ac04b278f28bdf3f95b00a3c
Author: csilvers <csilvers@6b5cf1ce-ec42-a296-1ba9-69fdba395a50>
Date:	Wed Feb 13 00:55:09 2008 +0000

    Tue Feb 12 12:28:32 2008  Google Inc. <<EMAIL>>

	    * google-perftools: version 0.95 release
	    * Better -- not perfect -- support for linux-ppc (csilvers)
	    * Fix race condition in libunwind stacktrace (aruns)
	    * Speed up x86 spinlock locking (m3b)
	    * Improve heap-checker performance (maxim)
	    * Heap checker traverses more ptrs inside heap-alloced objects
	    (maxim)
	    * Remove deprecated ProfilerThreadState function (cgd)
	    * Update libunwind documentation for statically linked
	    binaries (aruns)


    git-svn-id: http://gperftools.googlecode.com/svn/trunk@44
    6b5cf1ce-ec42-a296-1ba9-69fdba395a50

M	ChangeLog
M	INSTALL
M	Makefile.am
M	Makefile.in
M	configure
M	configure.ac
M	doc/heap_checker.html
M	doc/tcmalloc.html
M	packages/deb/changelog
M	packages/deb/docs
M	packages/rpm/rpm.spec
M	src/addressmap-inl.h
A	src/base/atomicops-internals-linuxppc.h
M	src/base/atomicops-internals-x86-msvc.h
M	src/base/atomicops-internals-x86.h
M	src/base/atomicops.h
M	src/base/commandlineflags.h
A	src/base/cycleclock.h
M	src/base/elfcore.h
M	src/base/linux_syscall_support.h
M	src/base/linuxthreads.c
M	src/base/linuxthreads.h
M	src/base/logging.h
R090	src/base/mutex.h	src/base/simple_mutex.h
M	src/base/spinlock.cc
M	src/base/spinlock.h
M	src/base/sysinfo.cc
M	src/base/sysinfo.h
M	src/google/heap-checker.h
M	src/google/profiler.h
M	src/heap-checker.cc
M	src/heap-profile-table.cc
M	src/heap-profile-table.h
M	src/heap-profiler.cc
M	src/malloc_extension.cc
M	src/malloc_hook.cc
M	src/maybe_threads.cc
M	src/packed-cache-inl.h
M	src/profiledata.h
M	src/profiler.cc
M	src/stacktrace.cc
M	src/stacktrace_libunwind-inl.h
M	src/stacktrace_powerpc-inl.h
M	src/system-alloc.cc
M	src/tcmalloc.cc
M	src/tests/addressmap_unittest.cc
M	src/tests/heap-checker_unittest.cc
M	src/tests/profiler_unittest.cc
M	src/tests/stacktrace_unittest.cc
M	src/tests/tcmalloc_unittest.cc
M	src/windows/preamble_patcher.h

commit b43ba444fcd74fa7c3260f6b2494dcbaa3fdb296
Author: csilvers <csilvers@6b5cf1ce-ec42-a296-1ba9-69fdba395a50>
Date:	Wed Dec 5 00:08:28 2007 +0000

    Mon Dec  3 23:51:54 2007  Google Inc. <<EMAIL>>

	    * google-perftools: version 0.94.1 release	(bugfix release)
	    * Fix missing #includes for x86_64 compile using libunwind
	    (csilvers)


    git-svn-id: http://gperftools.googlecode.com/svn/trunk@42
    6b5cf1ce-ec42-a296-1ba9-69fdba395a50

M	ChangeLog
M	configure
M	configure.ac
M	src/stacktrace_libunwind-inl.h

commit 11b02f7aebd05cf39f6f93bdd48786909f99f34e
Author: csilvers <csilvers@6b5cf1ce-ec42-a296-1ba9-69fdba395a50>
Date:	Thu Nov 29 23:39:24 2007 +0000

    Thu Nov 29 07:59:43 2007  Google Inc. <<EMAIL>>

	    * google-perftools: version 0.94 release
	    * PORTING: MinGW/Msys support -- runs same code as MSVC does
	    (csilvers)
	    * PORTING: Add NumCPUs support for Mac OS X (csilvers)
	    * Work around a sscanf bug in glibc(?) (waldemar)
	    * Fix Windows MSVC bug triggered by thread deletion (csilvers)
	    * Fix bug that triggers in MSVC /O2: missing volatile (gpike)
	    * March-of-time support: quiet warnings/errors for gcc 4.2,
	    OS X 10.5
	    * Modify pprof so it works without nm: useful for windows
	    (csilvers)
	    * pprof: Support filtering for CPU profiles (cgd)
	    * Bugfix: have realloc report to hooks in all situations
	    (maxim)
	    * Speed improvement: replace slow memcpy with std::copy
	    (soren)
	    * Speed: better iterator efficiency in RecordRegionRemoval
	    (soren)
	    * Speed: minor speed improvements via better bitfield
	    alignment (gpike)
	    * Documentation: add documentation of binary profile output
	    (cgd)


    git-svn-id: http://gperftools.googlecode.com/svn/trunk@40
    6b5cf1ce-ec42-a296-1ba9-69fdba395a50

M	ChangeLog
M	INSTALL
M	Makefile.am
M	Makefile.in
M	configure
M	configure.ac
A	doc/cpuprofile-fileformat.html
M	doc/cpuprofile.html
M	packages/deb/changelog
M	src/base/linux_syscall_support.h
M	src/base/spinlock.cc
M	src/base/sysinfo.cc
M	src/config.h.in
M	src/getpc.h
M	src/google/heap-checker.h
M	src/google/profiler.h
M	src/google/stacktrace.h
M	src/heap-checker.cc
M	src/heap-profile-table.cc
M	src/heap-profiler.cc
M	src/malloc_hook.cc
M	src/memfs_malloc.cc
M	src/memory_region_map.cc
M	src/packed-cache-inl.h
M	src/pprof
A	src/profiledata.cc
A	src/profiledata.h
M	src/profiler.cc
M	src/stacktrace_generic-inl.h
M	src/stacktrace_libunwind-inl.h
M	src/stacktrace_powerpc-inl.h
M	src/stacktrace_x86-inl.h
M	src/stacktrace_x86_64-inl.h
M	src/system-alloc.cc
M	src/system-alloc.h
M	src/tcmalloc.cc
M	src/tests/addressmap_unittest.cc
M	src/tests/heap-checker_unittest.cc
M	src/tests/low_level_alloc_unittest.cc
M	src/tests/maybe_threads_unittest.sh
A	src/tests/profiledata_unittest.cc
M	src/tests/system-alloc_unittest.cc
M	src/tests/tcmalloc_unittest.cc
M	src/tests/testutil.cc
M	src/windows/config.h
A	src/windows/mingw.h
M	src/windows/mini_disassembler.h
M	src/windows/patch_functions.cc
M	src/windows/port.cc
M	src/windows/port.h
M	src/windows/preamble_patcher.h
M	src/windows/preamble_patcher_with_stub.cc
M	vsprojects/libtcmalloc_minimal/libtcmalloc_minimal.vcproj

commit 49b74b9508797f8aafe6b86e62e7efc4ec200e48
Author: csilvers <csilvers@6b5cf1ce-ec42-a296-1ba9-69fdba395a50>
Date:	Fri Aug 17 20:56:15 2007 +0000

	    * google-perftools: version 0.93 release
	    * PORTING: everything compiles on Solaris, OS X, FreeBSD
	    (see INSTALL)
	    * PORTING: cpu-profiler works on most platforms (much
	    better GetPC())
	    * PORTING: heap-profiler works on most platforms
	    * PORTING: improved windows support, including release builds
	    * No longer build or run ptmalloc tests by default
	    * Add support for using memfs filesystem to allocate memory
	    in linux
	    * WINDOWS: give debug library and release library different
	    names

    Tue Jul 17 22:26:27 2007  Google Inc. <<EMAIL>>


    git-svn-id: http://gperftools.googlecode.com/svn/trunk@38
    6b5cf1ce-ec42-a296-1ba9-69fdba395a50

M	ChangeLog
M	INSTALL
M	Makefile.am
M	Makefile.in
M	README
M	README.windows
M	autogen.sh
M	configure
M	configure.ac
M	google-perftools.sln
M	m4/program_invocation_name.m4
M	packages/deb/changelog
M	src/addressmap-inl.h
M	src/base/basictypes.h
M	src/base/logging.h
M	src/base/sysinfo.cc
M	src/base/sysinfo.h
M	src/base/thread_lister.c
M	src/config.h.in
M	src/config_for_unittests.h
A	src/getpc.h
M	src/google/heap-checker.h
M	src/google/malloc_hook.h
M	src/heap-checker.cc
M	src/heap-profile-table.cc
M	src/heap-profile-table.h
M	src/heap-profiler.cc
M	src/malloc_extension.cc
M	src/malloc_hook.cc
A	src/memfs_malloc.cc
M	src/memory_region_map.cc
M	src/memory_region_map.h
M	src/pprof
M	src/profiler.cc
M	src/stacktrace_powerpc-inl.h
M	src/system-alloc.cc
M	src/system-alloc.h
M	src/tcmalloc.cc
M	src/tests/addressmap_unittest.cc
A	src/tests/getpc_test.cc
M	src/tests/heap-checker-death_unittest.sh
M	src/tests/heap-checker_unittest.cc
M	src/tests/heap-profiler_unittest.sh
M	src/tests/low_level_alloc_unittest.cc
M	src/tests/maybe_threads_unittest.sh
M	src/tests/memalign_unittest.cc
M	src/tests/system-alloc_unittest.cc
M	src/tests/tcmalloc_unittest.cc
M	src/windows/TODO
M	src/windows/config.h
A	src/windows/ia32_modrm_map.cc
A	src/windows/ia32_opcode_map.cc
A	src/windows/mini_disassembler.cc
A	src/windows/mini_disassembler.h
A	src/windows/mini_disassembler_types.h
A	src/windows/patch_functions.cc
M	src/windows/port.cc
M	src/windows/port.h
A	src/windows/preamble_patcher.cc
A	src/windows/preamble_patcher.h
A	src/windows/preamble_patcher_with_stub.cc
M	src/windows/vc7and8.def
M	vsprojects/addressmap_unittest/addressmap_unittest.vcproj
M	vsprojects/libtcmalloc_minimal/libtcmalloc_minimal.vcproj
M
vsprojects/low_level_alloc_unittest/low_level_alloc_unittest.vcproj
C054	vsprojects/libtcmalloc_minimal/libtcmalloc_minimal.vcproj
vsprojects/tcmalloc_minimal_unittest-static/tcmalloc_minimal_unittest-static.vcproj

commit c437e1fcdd1e6ff3f032928d460cbfc115e2324f
Author: csilvers <csilvers@6b5cf1ce-ec42-a296-1ba9-69fdba395a50>
Date:	Wed Jul 18 18:30:50 2007 +0000

    Tue Jul 17 22:26:27 2007  Google Inc. <<EMAIL>>

	    * google-perftools: version 0.92 release
	    * PERFORMANCE: use a packed cache to speed up tcmalloc
	    * PORTING: preliminary windows support! (see README.windows)
	    * PORTING: better support for solaris, OS X, FreeBSD
	    (see INSTALL)
	    * Envvar support for running the heap-checker under gdb
	    * Add weak declarations to maybe_threads to fix no-pthreads
	    compile bugs
	    * Some 64bit fixes, especially with pprof
	    * Better heap-checker support for some low-level allocations
	    * Fix bug where heap-profiles would sometimes get truncated
	    * New documentation about how to handle common heap leak
	    situations
	    * Use computed includes for hash_map/set: easier config
	    * Added all used .m4 templates to the distribution


    git-svn-id: http://gperftools.googlecode.com/svn/trunk@36
    6b5cf1ce-ec42-a296-1ba9-69fdba395a50

M	ChangeLog
M	INSTALL
M	Makefile.am
M	Makefile.in
M	README
A	README.windows
M	aclocal.m4
A	autogen.sh
M	compile
M	config.guess
M	config.sub
M	configure
M	configure.ac
M	depcomp
M	doc/cpuprofile.html
M	doc/heap_checker.html
M	doc/heapprofile.html
A	google-perftools.sln
M	install-sh
A	m4/ac_have_attribute.m4
A	m4/acx_pthread.m4
A	m4/compiler_characteristics.m4
A	m4/install_prefix.m4
A	m4/namespaces.m4
A	m4/program_invocation_name.m4
A	m4/stl_hash.m4
A	m4/stl_namespace.m4
M	missing
M	mkinstalldirs
M	packages/deb/changelog
M	packages/rpm/rpm.spec
M	src/base/atomicops-internals-x86-msvc.h
M	src/base/atomicops-internals-x86.h
M	src/base/atomicops.h
M	src/base/basictypes.h
M	src/base/commandlineflags.h
M	src/base/logging.h
M	src/base/low_level_alloc.cc
M	src/base/low_level_alloc.h
D	src/base/mutex.cc
M	src/base/mutex.h
M	src/base/spinlock.cc
M	src/base/spinlock.h
M	src/base/stl_allocator.h
M	src/base/sysinfo.cc
M	src/base/sysinfo.h
M	src/config.h.in
C068	src/tests/testutil.h	src/config_for_unittests.h
M	src/google/heap-checker.h
M	src/google/heap-profiler.h
M	src/google/malloc_extension.h
M	src/google/malloc_hook.h
M	src/google/profiler.h
M	src/google/stacktrace.h
M	src/heap-checker.cc
M	src/heap-profile-table.cc
M	src/heap-profiler.cc
M	src/internal_logging.cc
M	src/internal_logging.h
M	src/malloc_extension.cc
M	src/malloc_hook.cc
M	src/maybe_threads.cc
M	src/maybe_threads.h
M	src/memory_region_map.cc
M	src/memory_region_map.h
A	src/packed-cache-inl.h
M	src/pprof
M	src/profiler.cc
M	src/solaris/libstdc++.la
M	src/stacktrace.cc
A	src/stacktrace_powerpc-inl.h
M	src/stacktrace_x86-inl.h
M	src/system-alloc.cc
M	src/system-alloc.h
M	src/tcmalloc.cc
C053	src/tests/testutil.cc	src/tcmalloc.h
M	src/tests/frag_unittest.cc
M	src/tests/heap-checker_unittest.cc
M	src/tests/heap-profiler_unittest.cc
M	src/tests/heap-profiler_unittest.sh
M	src/tests/low_level_alloc_unittest.cc
M	src/tests/markidle_unittest.cc
A	src/tests/maybe_threads_unittest.sh
M	src/tests/memalign_unittest.cc
C065	src/tests/testutil.h	src/tests/packed-cache_test.cc
M	src/tests/profiler_unittest.cc
M	src/tests/stacktrace_unittest.cc
C051	src/tests/thread_dealloc_unittest.cc
src/tests/system-alloc_unittest.cc
M	src/tests/tcmalloc_unittest.cc
M	src/tests/testutil.cc
M	src/tests/testutil.h
M	src/tests/thread_dealloc_unittest.cc
A	src/windows/TODO
C060	src/config.h.in src/windows/config.h
A	src/windows/port.cc
A	src/windows/port.h
A	src/windows/vc7and8.def
A	vsprojects/addressmap_unittest/addressmap_unittest.vcproj
A	vsprojects/frag_unittest/frag_unittest.vcproj
A	vsprojects/libtcmalloc_minimal/libtcmalloc_minimal.vcproj
A
vsprojects/low_level_alloc_unittest/low_level_alloc_unittest.vcproj
A	vsprojects/markidle_unittest/markidle_unittest.vcproj
A	vsprojects/memalign_unittest/memalign_unittest.vcproj
A	vsprojects/packed-cache_test/packed-cache_test.vcproj
A	vsprojects/system_alloc_unittest/system_alloc_unittest.vcproj
A
vsprojects/tcmalloc_minimal_large/tcmalloc_minimal_large_unittest.vcproj
A
vsprojects/tcmalloc_minimal_unittest/tcmalloc_minimal_unittest.vcproj
A	vsprojects/thread_dealloc_unittest/thread_dealloc_unittest.vcproj

commit 6878379d5bab87c787cdd3487b5620a9c8adf376
Author: csilvers <csilvers@6b5cf1ce-ec42-a296-1ba9-69fdba395a50>
Date:	Thu Apr 19 00:53:22 2007 +0000

    Wed Apr 18 16:43:55 2007  Google Inc. <<EMAIL>>

	    * google-perftools: version 0.91 release
	    * Brown-paper-bag bugfix: compilation error on some x86-64
	    machines


    git-svn-id: http://gperftools.googlecode.com/svn/trunk@30
    6b5cf1ce-ec42-a296-1ba9-69fdba395a50

M	ChangeLog
M	README
M	config.guess
M	config.sub
M	configure
M	configure.ac
M	src/base/sysinfo.cc
M	src/base/sysinfo.h
M	src/stacktrace_libunwind-inl.h
M	src/tests/ptmalloc/malloc-machine.h

commit 74ad5d57ec08abace386befc6c3c695d85f44d1a
Author: csilvers <csilvers@6b5cf1ce-ec42-a296-1ba9-69fdba395a50>
Date:	Mon Apr 16 20:49:32 2007 +0000

    Fri Apr 13 14:50:51 2007  Google Inc. <<EMAIL>>

	    * google-perftools: version 0.90 release
	    * (As the version-number jump hints, this is a major new
	    release:
	      almost every piece of functionality was rewritten.
	      I can't do
	      justice to all the changes, but will concentrate on
	      highlights.)
	    *** USER-VISIBLE CHANGES:
	    * Ability to "release" unused memory added to tcmalloc
	    * Exposed more tweaking knobs via environment variables
	    (see docs)
	    * pprof tries harder to map addresses to functions
	    * tcmalloc_minimal compiles and runs on FreeBSD 6.0 and
	    Solaris 10
	    *** INTERNAL CHANGES:
	    * Much better 64-bit support
	    * Better multiple-processor support (e.g. multicore contention
	    tweaks)
	    * Support for recent kernel ABI changes (e.g. new arg
	    to mremap)
	    * Addition of spinlocks to tcmalloc to reduce contention cost
	    * Speed up tcmalloc by using __thread on systems that
	    support TLS
	    * Total redesign of heap-checker to improve liveness checking
	    * More portable stack-frame analysis -- no more hard-coded
	    constants!
	    * Disentangled heap-profiler code and heap-checker code
	    * Several new unittests to test, e.g., thread-contention costs
	    * Lots of small (but important!) bug fixes: e.g., fixing
	    GetPC on amd64
	    *** KNOWN PROBLEMS:
	    * CPU-profiling may crash on x86_64 (64-bit) systems.
	    See the README
	    * Profiling/heap-checking may deadlock on x86_64 systems.
	    See README


    git-svn-id: http://gperftools.googlecode.com/svn/trunk@28
    6b5cf1ce-ec42-a296-1ba9-69fdba395a50

M	ChangeLog
M	INSTALL
M	Makefile.am
M	Makefile.in
M	README
M	TODO
M	aclocal.m4
M	config.guess
M	config.sub
M	configure
M	configure.ac
R050	doc/cpu_profiler.html	doc/cpuprofile.html
A	doc/designstyle.css
M	doc/heap_checker.html
D	doc/heap_profiler.html
A	doc/heapprofile.html
M	doc/index.html
M	doc/tcmalloc.html
M	ltmain.sh
M	packages/deb/changelog
A	packages/deb/docs
M	packages/rpm/rpm.spec
M	src/addressmap-inl.h
A	src/base/atomicops-internals-macosx.h
A	src/base/atomicops-internals-x86-msvc.h
A	src/base/atomicops-internals-x86.cc
A	src/base/atomicops-internals-x86.h
A	src/base/atomicops.h
M	src/base/basictypes.h
M	src/base/commandlineflags.h
M	src/base/elfcore.h
M	src/base/googleinit.h
M	src/base/linux_syscall_support.h
M	src/base/linuxthreads.c
M	src/base/linuxthreads.h
C074	src/base/googleinit.h	src/base/logging.cc
M	src/base/logging.h
A	src/base/low_level_alloc.cc
A	src/base/low_level_alloc.h
A	src/base/mutex.cc
A	src/base/mutex.h
A	src/base/spinlock.cc
A	src/base/spinlock.h
A	src/base/stl_allocator.h
A	src/base/sysinfo.cc
A	src/base/sysinfo.h
M	src/base/thread_lister.c
M	src/base/thread_lister.h
M	src/config.h.in
M	src/google/heap-checker.h
M	src/google/heap-profiler.h
M	src/google/malloc_extension.h
M	src/google/malloc_hook.h
M	src/google/profiler.h
M	src/google/stacktrace.h
M	src/heap-checker-bcad.cc
M	src/heap-checker.cc
A	src/heap-profile-table.cc
A	src/heap-profile-table.h
D	src/heap-profiler-inl.h
M	src/heap-profiler.cc
M	src/internal_logging.cc
M	src/internal_logging.h
D	src/internal_spinlock.h
M	src/malloc_extension.cc
M	src/malloc_hook.cc
M	src/maybe_threads.cc
A	src/memory_region_map.cc
A	src/memory_region_map.h
M	src/pprof
M	src/profiler.cc
A	src/solaris/libstdc++.la
M	src/stacktrace.cc
M	src/stacktrace_generic-inl.h
M	src/stacktrace_libunwind-inl.h
M	src/stacktrace_x86-inl.h
M	src/stacktrace_x86_64-inl.h
M	src/system-alloc.cc
M	src/system-alloc.h
M	src/tcmalloc.cc
M	src/tests/addressmap_unittest.cc
A	src/tests/atomicops_unittest.cc
A	src/tests/frag_unittest.cc
M	src/tests/heap-checker-death_unittest.sh
M	src/tests/heap-checker_unittest.cc
M	src/tests/heap-checker_unittest.sh
M	src/tests/heap-profiler_unittest.cc
M	src/tests/heap-profiler_unittest.sh
A	src/tests/low_level_alloc_unittest.cc
A	src/tests/markidle_unittest.cc
A	src/tests/memalign_unittest.cc
M	src/tests/profiler_unittest.sh
M	src/tests/ptmalloc/malloc-machine.h
M	src/tests/stacktrace_unittest.cc
M	src/tests/tcmalloc_large_unittest.cc
M	src/tests/tcmalloc_unittest.cc
C055	src/stacktrace.cc	src/tests/testutil.cc
C074	src/base/googleinit.h	src/tests/testutil.h
C055	src/stacktrace.cc	src/tests/thread_dealloc_unittest.cc

commit ddbf2f027fb4ca8781fd50820ceb870570f414bc
Author: csilvers <csilvers@6b5cf1ce-ec42-a296-1ba9-69fdba395a50>
Date:	Thu Mar 22 05:03:28 2007 +0000

    Now that we've uploaded the full source, including the doc/ directory,
    we can get rid of docs/

    git-svn-id: http://gperftools.googlecode.com/svn/trunk@27
    6b5cf1ce-ec42-a296-1ba9-69fdba395a50

D	docs/html/cpu_profiler.html
D	docs/html/heap_checker.html
D	docs/html/heap_profiler.html
D	docs/html/tcmalloc.html
D	docs/images/heap-example1.png
D	docs/images/overview.gif
D	docs/images/pageheap.gif
D	docs/images/pprof-test.gif
D	docs/images/pprof-vsnprintf.gif
D	docs/images/spanmap.gif
D	docs/images/tcmalloc-opspercpusec.png
D	docs/images/tcmalloc-opspercpusec_002.png
D	docs/images/tcmalloc-opspercpusec_003.png
D	docs/images/tcmalloc-opspercpusec_004.png
D	docs/images/tcmalloc-opspercpusec_005.png
D	docs/images/tcmalloc-opspercpusec_006.png
D	docs/images/tcmalloc-opspercpusec_007.png
D	docs/images/tcmalloc-opspercpusec_008.png
D	docs/images/tcmalloc-opspercpusec_009.png
D	docs/images/tcmalloc-opspersec.png
D	docs/images/tcmalloc-opspersec_002.png
D	docs/images/tcmalloc-opspersec_003.png
D	docs/images/tcmalloc-opspersec_004.png
D	docs/images/tcmalloc-opspersec_005.png
D	docs/images/tcmalloc-opspersec_006.png
D	docs/images/tcmalloc-opspersec_007.png
D	docs/images/tcmalloc-opspersec_008.png
D	docs/images/tcmalloc-opspersec_009.png
D	docs/images/threadheap.gif

commit 7ede7d6a9fe772b0bfa05acb2a2a6867405b474a
Author: csilvers <csilvers@6b5cf1ce-ec42-a296-1ba9-69fdba395a50>
Date:	Thu Mar 22 05:01:39 2007 +0000

    set mime-type for png and gifs

    git-svn-id: http://gperftools.googlecode.com/svn/trunk@26
    6b5cf1ce-ec42-a296-1ba9-69fdba395a50

commit 8e188310f7d8732d81b7b04f193f89964b7af6c5
Author: csilvers <csilvers@6b5cf1ce-ec42-a296-1ba9-69fdba395a50>
Date:	Thu Mar 22 04:55:49 2007 +0000

    Wed Jun 14 15:11:14 2006  Google Inc. <<EMAIL>>

	    * google-perftools: version 0.8 release
	    * Experimental support for remote profiling added to pprof
	    (many)
	    * Fixed race condition in ProfileData::FlushTable (etune)
	    * Better support for weird /proc maps (maxim, mec)
	    * Fix heap-checker interaction with gdb (markus)
	    * Better 64-bit support in pprof (aruns)
	    * Reduce scavenging cost in tcmalloc by capping NumMoveSize
	    (sanjay)
	    * Cast syscall(SYS_mmap); works on more 64-bit systems now
	    (menage)
	    * Document the text output of pprof! (csilvers)
	    * Better compiler support for no-THREADS and for old compilers
	    (csilvers)
	    * Make libunwind the default stack unwinder for x86-64 (aruns)
	    * Somehow the COPYING file got erased.  Regenerate it
	    (csilvers)


    git-svn-id: http://gperftools.googlecode.com/svn/trunk@23
    6b5cf1ce-ec42-a296-1ba9-69fdba395a50

M	COPYING
M	ChangeLog
M	Makefile.am
M	aclocal.m4
M	configure
M	configure.ac
M	doc/cpu_profiler.html
A	doc/pprof_remote_servers.html
M	src/base/linux_syscall_support.h
M	src/base/linuxthreads.c
M	src/base/thread_lister.c
M	src/google/heap-checker.h
M	src/heap-checker.cc
M	src/malloc_extension.cc
M	src/malloc_hook.cc
M	src/pprof
M	src/profiler.cc
M	src/stacktrace.cc
M	src/stacktrace_libunwind-inl.h
M	src/tcmalloc.cc
M	src/tests/heap-checker_unittest.cc
M	src/tests/tcmalloc_unittest.cc

commit c3b96b3ac552160abde541bba8ac7b4f8338efa0
Author: csilvers <csilvers@6b5cf1ce-ec42-a296-1ba9-69fdba395a50>
Date:	Thu Mar 22 04:48:00 2007 +0000

    Thu Apr 13 20:59:09 2006  Google Inc. <<EMAIL>>

	    * google-perftools: version 0.7 release
	    * Major rewrite of thread introspection for new kernels
	    (markus)
	    * Major rewrite of heap-checker to use new thread tools
	    (maxim)
	    * Add proper support for following data in thread registers
	    (maxim)
	    * Syscall support for older kernels, including _syscall6
	    (markus)
	    * Support PIC mode (markus, mbland, iant)
	    * Better support for running in non-threaded contexts
	    (csilvers)


    git-svn-id: http://gperftools.googlecode.com/svn/trunk@21
    6b5cf1ce-ec42-a296-1ba9-69fdba395a50

M	ChangeLog
M	configure
M	configure.ac
M	src/base/elfcore.h
M	src/base/linux_syscall_support.h
M	src/base/linuxthreads.c
M	src/base/thread_lister.h
M	src/google/heap-checker.h
M	src/heap-checker.cc
M	src/heap-profiler.cc
M	src/malloc_hook.cc
M	src/pprof
M	src/profiler.cc
M	src/tests/heap-checker-death_unittest.sh
M	src/tests/heap-checker_unittest.cc
M	src/tests/tcmalloc_unittest.cc

commit 60a3a2ce77ed2713b2eedd20952d9cfc56ff7ccf
Author: csilvers <csilvers@6b5cf1ce-ec42-a296-1ba9-69fdba395a50>
Date:	Thu Mar 22 04:46:29 2007 +0000

    Fri Jan 27 14:04:27 2006  Google Inc. <<EMAIL>>

	    * google-perftools: version 0.6 release
	    * More sophisticated stacktrace usage, possibly using
	    libunwind (aruns)
	    * Update pprof to handle 64-bit profiles (dehnert)
	    * Fix GetStackTrace to correctly return top stackframe
	    (sanjay)
	    * Add ANSI compliance for new and new[], including new_handler
	    (jkearney)
	    * More accuracy by reading ELF files directly rather than
	    objdump (mec)
	    * Add readline support for pprof (addi)
	    * Add #includes for PPC (csilvers)
	    * New PC-detection routine for ibook powerpc (asbestoshead)
	    * Vastly improved tcmalloc unittest (csilvers)
	    * Move documentation from /usr/doc to /usr/share/doc


    git-svn-id: http://gperftools.googlecode.com/svn/trunk@19
    6b5cf1ce-ec42-a296-1ba9-69fdba395a50

M	ChangeLog
M	Makefile.am
M	configure
M	configure.ac
M	packages/rpm/rpm.spec
M	src/base/thread_lister.c
M	src/config.h.in
M	src/heap-checker.cc
M	src/heap-profiler.cc
M	src/malloc_extension.cc
M	src/pprof
M	src/profiler.cc
M	src/stacktrace.cc
C065	src/tests/tcmalloc_unittest.cc	src/stacktrace_generic-inl.h
C064	src/tests/tcmalloc_unittest.cc	src/stacktrace_libunwind-inl.h
C068	src/stacktrace.cc	src/stacktrace_x86-inl.h
A	src/stacktrace_x86_64-inl.h
M	src/tcmalloc.cc
M	src/tests/stacktrace_unittest.cc
M	src/tests/tcmalloc_unittest.cc

commit 298274f8d4f474d2b16a35c8babc58817088c59e
Author: csilvers <csilvers@6b5cf1ce-ec42-a296-1ba9-69fdba395a50>
Date:	Thu Mar 22 04:45:23 2007 +0000

    Mon Nov 14 17:28:59 2005  Google Inc. <<EMAIL>>

	    * google-perftools: version 0.5 release
	    * Add va_start/va_end calls around vsnprintf() (csilvers)
	    * Write our own __syscall_return(), since it's not defined
	      consistently on all 64-bit linux distros (markus)


    git-svn-id: http://gperftools.googlecode.com/svn/trunk@17
    6b5cf1ce-ec42-a296-1ba9-69fdba395a50

M	ChangeLog
M	configure
M	configure.ac
M	src/base/linux_syscall_support.h
M	src/base/logging.h
M	src/config.h.in

commit ee5805f1296f8546c16f90d5427efa347a5f7338
Author: csilvers <csilvers@6b5cf1ce-ec42-a296-1ba9-69fdba395a50>
Date:	Thu Mar 22 04:44:18 2007 +0000

    Wed Oct 26 15:19:16 2005  Google Inc. <<EMAIL>>

	    * Decrease fragmentation in tcmalloc (lefevere)
	    * Support for ARM in some of the thread-specific code (markus)
	    * Turn off heap-checker for statically-linked binaries, which
	      cause error leak reports now (etune)
	    * Many pprof improvements, including a command-line interface
	    (jeff)
	    * CPU profiling now automatically affects all threads in
	    linux 2.6.
	      (Kernel bugs break CPU profiling and threads in linux 2.4
	      a bit.)
	      ProfilerEnable() and ProfilerDisable() are deprecated.
	      (sanjay)
	    * tcmalloc now correctly intercepts memalign (m3b, maxim)
	    * Syntax fix: added missing va_end()s.  Helps non-gcc
	    compiling (etune)
	    * Fixed a few coredumper bugs: race condition after
	    PTRACE_DETACH,
	      ignore non-aligned stackframe pointers (markus, menage)
	    * 64-bit cleanup, especially for spinlock code (etune)
	    and mmap (sanjay)
	    * Better support for finding threads in linux (markus)
	    * tcmalloc now tracks those stack traces that allocate memory
	    (sanjay)
	    * Work around a weird setspecific problem (sanjay)
	    * Fix tcmalloc overflow problems when an alloc is close to
	    2G/4G (sanjay)


    git-svn-id: http://gperftools.googlecode.com/svn/trunk@15
    6b5cf1ce-ec42-a296-1ba9-69fdba395a50

M	ChangeLog
M	Makefile.am
M	aclocal.m4
M	configure
M	configure.ac
M	doc/cpu_profiler.html
D	src/base/elfcore.c
M	src/base/elfcore.h
A	src/base/linux_syscall_support.h
M	src/base/linuxthreads.c
M	src/base/linuxthreads.h
M	src/base/thread_lister.c
M	src/base/thread_lister.h
M	src/google/heap-checker.h
M	src/google/malloc_extension.h
M	src/google/profiler.h
M	src/heap-checker.cc
M	src/heap-profiler.cc
M	src/internal_logging.cc
M	src/internal_spinlock.h
M	src/malloc_extension.cc
M	src/malloc_hook.cc
M	src/pagemap.h
M	src/pprof
M	src/profiler.cc
M	src/stacktrace.cc
M	src/system-alloc.cc
M	src/tcmalloc.cc
A	src/tests/tcmalloc_large_unittest.cc
M	src/tests/tcmalloc_unittest.cc

commit bc455d7b63949fab94ed9518d277866e95f08768
Author: csilvers <csilvers@6b5cf1ce-ec42-a296-1ba9-69fdba395a50>
Date:	Thu Mar 22 04:42:30 2007 +0000

    Fri Jun 24 18:02:26 2005  Google Inc. <<EMAIL>>

	    * Add missing errno include for one of the unittests
	    (csilvers)
	    * Reduce tcmalloc startup memory from 5M to 256K (sanjay)
	    * Add support for mallopt() and mallinfo (sanjay)
	    * Improve stacktrace's performance on some 64-bit systems
	    (etune)
	    * Improve the stacktrace unittest (etune)


    git-svn-id: http://gperftools.googlecode.com/svn/trunk@13
    6b5cf1ce-ec42-a296-1ba9-69fdba395a50

M	ChangeLog
M	configure
M	configure.ac
M	src/pagemap.h
M	src/stacktrace.cc
M	src/tcmalloc.cc
M	src/tests/heap-checker_unittest.cc
M	src/tests/stacktrace_unittest.cc

commit 91fad389784766782263133c5510976a8f76d89e
Author: csilvers <csilvers@6b5cf1ce-ec42-a296-1ba9-69fdba395a50>
Date:	Thu Mar 22 03:28:56 2007 +0000

    Tue May 31 08:14:38 2005  Google Inc. <<EMAIL>>

	    * google-perftools: version 0.2 release
	    * Use mmap2() instead of mmap(), to map more memory (menage)
	    * Do correct pthread-local checking in heap-checker! (maxim)
	    * Avoid overflow on 64-bit machines in pprof (sanjay)
	    * Add a few more GetPC() functions, including for AMD
	    (csilvers)
	    * Better method for overriding pthread functions (menage)
	    * (Hacky) fix to avoid overwriting profile files after fork()
	    (csilvers)
	    * Crashing bugfix involving dumping heaps on small-stack
	    threads (tudor)
	    * Allow library versions with letters at the end (csilvers)
	    * Config fixes for systems that don't define PATH_MAX
	    (csilvers)
	    * Confix fixes so we no longer need config.h after install
	    (csilvers)
	    * Fix to pprof to correctly read very big cpu profiles
	    (csilvers)
	    * Fix to pprof to deal with new commandline flags in
	    modern gv's
	    * Better error reporting when we can't access /proc/maps
	    (etune)
	    * Get rid of the libc-preallocate code (which could crash
	    on some
	      systems); no longer needed with local-threads fix (csilvers)


    git-svn-id: http://gperftools.googlecode.com/svn/trunk@11
    6b5cf1ce-ec42-a296-1ba9-69fdba395a50

M	ChangeLog
M	Makefile.am
M	README
M	TODO
M	configure
M	configure.ac
M	doc/cpu_profiler.html
A	doc/heap_checker.html
M	doc/heap_profiler.html
A	doc/index.html
A	doc/t-test1.times.txt
A	doc/tcmalloc-opspercpusec.vs.threads.1024.bytes.png
A	doc/tcmalloc-opspercpusec.vs.threads.128.bytes.png
A	doc/tcmalloc-opspercpusec.vs.threads.131072.bytes.png
A	doc/tcmalloc-opspercpusec.vs.threads.16384.bytes.png
A	doc/tcmalloc-opspercpusec.vs.threads.2048.bytes.png
A	doc/tcmalloc-opspercpusec.vs.threads.256.bytes.png
A	doc/tcmalloc-opspercpusec.vs.threads.32768.bytes.png
A	doc/tcmalloc-opspercpusec.vs.threads.4096.bytes.png
A	doc/tcmalloc-opspercpusec.vs.threads.512.bytes.png
A	doc/tcmalloc-opspercpusec.vs.threads.64.bytes.png
A	doc/tcmalloc-opspercpusec.vs.threads.65536.bytes.png
A	doc/tcmalloc-opspercpusec.vs.threads.8192.bytes.png
A	doc/tcmalloc-opspersec.vs.size.1.threads.png
A	doc/tcmalloc-opspersec.vs.size.12.threads.png
A	doc/tcmalloc-opspersec.vs.size.16.threads.png
A	doc/tcmalloc-opspersec.vs.size.2.threads.png
A	doc/tcmalloc-opspersec.vs.size.20.threads.png
A	doc/tcmalloc-opspersec.vs.size.3.threads.png
A	doc/tcmalloc-opspersec.vs.size.4.threads.png
A	doc/tcmalloc-opspersec.vs.size.5.threads.png
A	doc/tcmalloc-opspersec.vs.size.8.threads.png
M	doc/tcmalloc.html
M	packages/deb/changelog
M	packages/deb/control
M	packages/deb/copyright
D	packages/deb/files
M	packages/deb/libgoogle-perftools-dev.install
M	packages/deb/libgoogle-perftools0.install
M	packages/rpm.sh
M	packages/rpm/rpm.spec
M	src/addressmap-inl.h
R099	src/google/perftools/basictypes.h	src/base/basictypes.h
M	src/base/commandlineflags.h
A	src/base/elfcore.c
A	src/base/elfcore.h
A	src/base/linuxthreads.c
A	src/base/linuxthreads.h
A	src/base/thread_lister.c
A	src/base/thread_lister.h
M	src/google/heap-checker.h
M	src/google/heap-profiler.h
R088	src/google/malloc_interface.h	src/google/malloc_extension.h
M	src/google/malloc_hook.h
D	src/google/perftools/config.h.in
M	src/google/stacktrace.h
M	src/heap-checker-bcad.cc
M	src/heap-checker.cc
M	src/heap-profiler-inl.h
M	src/heap-profiler.cc
M	src/internal_logging.h
M	src/internal_spinlock.h
R068	src/malloc_interface.cc src/malloc_extension.cc
M	src/malloc_hook.cc
A	src/maybe_threads.cc
C070	src/tests/tcmalloc_unittest.cc	src/maybe_threads.h
M	src/pagemap.h
M	src/pprof
M	src/profiler.cc
M	src/stacktrace.cc
M	src/system-alloc.cc
M	src/tcmalloc.cc
M	src/tests/addressmap_unittest.cc
M	src/tests/heap-checker-death_unittest.sh
M	src/tests/heap-checker_unittest.cc
C051	src/tests/heap-checker-death_unittest.sh
src/tests/heap-checker_unittest.sh
A	src/tests/heap-profiler_unittest.cc
A	src/tests/heap-profiler_unittest.sh
M	src/tests/profiler_unittest.cc
M	src/tests/profiler_unittest.sh
M	src/tests/stacktrace_unittest.cc
M	src/tests/tcmalloc_unittest.cc

commit 51b4875f8ade3e0930eed2dc2a842ec607a94a2c
Author: csilvers <csilvers@6b5cf1ce-ec42-a296-1ba9-69fdba395a50>
Date:	Thu Mar 22 03:00:33 2007 +0000

    Tue Feb 8 09:57:17 2005  El Goog <<EMAIL>>

	    * google-perftools: initial release:
	      The google-perftools package contains some utilities
	      to improve
	      and analyze the performance of C++ programs.  This includes
	      an
	      optimized thread-caching malloc() and cpu and heap profiling
	      utilities.


    git-svn-id: http://gperftools.googlecode.com/svn/trunk@9
    6b5cf1ce-ec42-a296-1ba9-69fdba395a50

A	AUTHORS
A	COPYING
A	ChangeLog
A	INSTALL
A	Makefile.am
A	Makefile.in
A	NEWS
A	README
A	TODO
A	aclocal.m4
A	compile
A	config.guess
A	config.sub
A	configure
A	configure.ac
A	depcomp
A	doc/cpu_profiler.html
A	doc/heap-example1.png
A	doc/heap_profiler.html
A	doc/overview.dot
A	doc/overview.gif
A	doc/pageheap.dot
A	doc/pageheap.gif
A	doc/pprof-test-big.gif
A	doc/pprof-test.gif
A	doc/pprof-vsnprintf-big.gif
A	doc/pprof-vsnprintf.gif
A	doc/pprof.1
A	doc/pprof.see_also
A	doc/spanmap.dot
A	doc/spanmap.gif
A	doc/tcmalloc.html
A	doc/threadheap.dot
A	doc/threadheap.gif
A	install-sh
A	ltmain.sh
A	missing
A	mkinstalldirs
A	packages/deb.sh
A	packages/deb/README
A	packages/deb/changelog
A	packages/deb/compat
A	packages/deb/control
A	packages/deb/copyright
A	packages/deb/files
A	packages/deb/libgoogle-perftools-dev.dirs
A	packages/deb/libgoogle-perftools-dev.install
A	packages/deb/libgoogle-perftools0.dirs
A	packages/deb/libgoogle-perftools0.install
A	packages/deb/libgoogle-perftools0.manpages
A	packages/deb/rules
A	packages/rpm.sh
A	packages/rpm/rpm.spec
A	src/addressmap-inl.h
A	src/base/commandlineflags.h
A	src/base/googleinit.h
A	src/base/logging.h
A	src/config.h.in
A	src/google/heap-checker.h
A	src/google/heap-profiler.h
A	src/google/malloc_hook.h
A	src/google/malloc_interface.h
A	src/google/perftools/basictypes.h
A	src/google/perftools/config.h.in
A	src/google/profiler.h
A	src/google/stacktrace.h
A	src/heap-checker-bcad.cc
A	src/heap-checker.cc
A	src/heap-profiler-inl.h
A	src/heap-profiler.cc
A	src/internal_logging.cc
A	src/internal_logging.h
A	src/internal_spinlock.h
A	src/malloc_hook.cc
A	src/malloc_interface.cc
A	src/pagemap.h
A	src/pprof
A	src/profiler.cc
A	src/stacktrace.cc
A	src/system-alloc.cc
A	src/system-alloc.h
A	src/tcmalloc.cc
A	src/tests/addressmap_unittest.cc
A	src/tests/heap-checker-death_unittest.sh
A	src/tests/heap-checker_unittest.cc
A	src/tests/profiler_unittest.cc
A	src/tests/profiler_unittest.sh
A	src/tests/ptmalloc/COPYRIGHT
A	src/tests/ptmalloc/lran2.h
A	src/tests/ptmalloc/malloc-machine.h
A	src/tests/ptmalloc/t-test.h
A	src/tests/ptmalloc/t-test1.c
A	src/tests/ptmalloc/t-test2.c
A	src/tests/ptmalloc/thread-m.h
A	src/tests/ptmalloc/thread-st.h
A	src/tests/stacktrace_unittest.cc
A	src/tests/tcmalloc_unittest.cc

commit e3a8513447d1141f083d4aaced0b240a6e161f47
Author: trowbridge.jon
<trowbridge.jon@6b5cf1ce-ec42-a296-1ba9-69fdba395a50>
Date:	Thu Dec 28 22:53:59 2006 +0000

    Set page mime-type to text/html.


    git-svn-id: http://gperftools.googlecode.com/svn/trunk@5
    6b5cf1ce-ec42-a296-1ba9-69fdba395a50

commit 66737d1c2519e4a1622f61139bfe2f683ea3696c
Author: trowbridge.jon
<trowbridge.jon@6b5cf1ce-ec42-a296-1ba9-69fdba395a50>
Date:	Thu Dec 28 22:39:33 2006 +0000

    Import of HTML documentation from SourceForge.



    git-svn-id: http://gperftools.googlecode.com/svn/trunk@3
    6b5cf1ce-ec42-a296-1ba9-69fdba395a50

A	docs/html/cpu_profiler.html
A	docs/html/heap_checker.html
A	docs/html/heap_profiler.html
A	docs/html/tcmalloc.html
A	docs/images/heap-example1.png
A	docs/images/overview.gif
A	docs/images/pageheap.gif
A	docs/images/pprof-test.gif
A	docs/images/pprof-vsnprintf.gif
A	docs/images/spanmap.gif
A	docs/images/tcmalloc-opspercpusec.png
A	docs/images/tcmalloc-opspercpusec_002.png
A	docs/images/tcmalloc-opspercpusec_003.png
A	docs/images/tcmalloc-opspercpusec_004.png
A	docs/images/tcmalloc-opspercpusec_005.png
A	docs/images/tcmalloc-opspercpusec_006.png
A	docs/images/tcmalloc-opspercpusec_007.png
A	docs/images/tcmalloc-opspercpusec_008.png
A	docs/images/tcmalloc-opspercpusec_009.png
A	docs/images/tcmalloc-opspersec.png
A	docs/images/tcmalloc-opspersec_002.png
A	docs/images/tcmalloc-opspersec_003.png
A	docs/images/tcmalloc-opspersec_004.png
A	docs/images/tcmalloc-opspersec_005.png
A	docs/images/tcmalloc-opspersec_006.png
A	docs/images/tcmalloc-opspersec_007.png
A	docs/images/tcmalloc-opspersec_008.png
A	docs/images/tcmalloc-opspersec_009.png
A	docs/images/threadheap.gif

commit 55d679a05f0518ea73a4bca6e8b71b54fcecf68f
Author: (no author) <(no author)@6b5cf1ce-ec42-a296-1ba9-69fdba395a50>
Date:	Thu Jul 27 00:57:14 2006 +0000

    Initial directory structure.

    git-svn-id: http://gperftools.googlecode.com/svn/trunk@1
    6b5cf1ce-ec42-a296-1ba9-69fdba395a50
