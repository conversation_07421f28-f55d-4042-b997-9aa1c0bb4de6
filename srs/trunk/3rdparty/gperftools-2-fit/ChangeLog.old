Fri Feb 03 15:40:45 2012  Google Inc. <<EMAIL>>

	* gperftools: version 2.0
	* Renamed the project from google-perftools to gperftools (csilvers)
	* Renamed the .deb/.rpm packagse from google-perftools to gperftools too
	* Renamed include directory from google/ to gperftools/ (csilvers)
	* Changed the 'official' perftools email in setup.py/etc
	* Renamed google-perftools.sln to gperftools.sln
	* PORTING: Removed bash-isms & grep -q in heap-checker-death_unittest.sh
	* Changed copyright text to reflect Google's relinquished ownership

Tue Jan 31 10:43:50 2012    Google Inc. <<EMAIL>>

	* google-perftools: version 1.10 release
	* PORTING: Support for patching assembly on win x86_64! (scott.fr...)
	* PORTING: Work around atexit-execution-order bug on freebsd (csilvers)
	* PORTING: Patch _calloc_crt for windows (roger orr)
	* PORTING: Add C++11 compatibility method for stl allocator (j<PERSON>nett)
	* PORTING: use MADV_FREE, not MADV_DONTNEED, on freebsd (csilvers)
	* PORTING: Don't use SYS_open when not supported on solaris (csilvers)
	* PORTING: Do not assume uname() returns 0 on success (csilvers)
	* LSS: Improved ARM support in linux-syscall-support (dougkwan)
	* LSS: Get rid of unused syscalls in linux-syscall-support (csilvers)
	* LSS: Fix broken mmap wrapping for ppc (markus)
	* LSS: Emit .cfi_adjust_cfa_offset when appropriate (ppluzhnikov)
	* LSS: Be more accurate in register use in __asm__ (markus)
	* LSS: Fix __asm__ calls to compile under clang (chandlerc)
	* LSS: Fix ARM inline assembly bug around r7 and swi (lcwu)
	* No longer log when an allocator fails (csilvers)
	* void* -> const void* for MallocExtension methods (llib)
	* Improve HEAP_PROFILE_MMAP and fix bugs with it (dmikurube)
	* Replace int-based abs with more correct fabs in a test (pmurin)

Thu Dec 22 16:22:45 2011    Google Inc. <<EMAIL>>

	* google-perftools: version 1.9 release
	* Lightweight check for double-frees (blount)
	* BUGFIX: Fix pprof to exit properly if run with no args (dagitses)
	* Suggest ASan as a way to diagnose buggy code (ppluzhnikov)
	* Get rid of unused CACHELINE_SIZE (csilvers)
	* Replace atexit() calls with global dtors; helps freebsd (csilvers)
	* Disable heap-checker under AddressSanitizer (kcc)
	* Fix bug in powerpc stacktracing (ppluzhnikov)
	* PERF: Use exponential backoff waiting for spinlocks (m3b)
	* Fix 64-bit nm on 32-bit binaries in pprof (csilvers)
	* Add ProfileHandlerDisallowForever (rsc)
	* BUGFIX: Shell escape when forking in pprof (csilvers)
	* No longer combine overloaded functions in pprof (csilvers)
	* Fix address-normalizing bug in pprof (csilvers)
	* More consistently call abort() instead of exit() on failure (csilvers)
	* Allow NoGlobalLeaks to be safely called more than once (csilvers)
	* PORTING/BUGFIX: Fix ARM cycleclock to use volatile asm (dougkwan)
	* PORTING: 64-bit atomic ops for ARMv7 (dougkwan)
	* PORTING: Implement stacktrace for ARM (dougkwan)
	* PORTING: Fix malloc_hook_mmap_linux for ARM (dougkwan)
	* PORTING: Update linux_syscall_support.h for ARM/etc (evannier, sanek)
	* PORTING: Fix freebsd to work on x86_64 (<EMAIL>)
	* PORTING: Added additional SYS_mmap fixes for FreeBSD (chappedm)
	* PORTING: Allow us to compile on OS X 10.6 and run on 10.5 (raltherr)
	* PORTING: Check for mingw compilers that *do* define timespec
	* PORTING: Add "support" for MIPS cycletimer
	* PORTING: Fix fallback cycle-timer to work with Now (dougkwan)
	* PERF: Move stack trace collecting out of the mutex (taylorc)
	* PERF: Get the deallocation stack trace outside the mutex (sean)
	* Make PageHeap dynamically allocated for leak checks (maxim)
	* BUGFIX: Fix probing of nm -f behavior in pprof (dpeng)
	* BUGFIX: Fix a race with the CentralFreeList lock before main (sanjay)
	* Support /pprof/censusprofile url arguments (rajatjain)
	* Change IgnoreObject to return its argument (nlewycky)
	* Update malloc-hook files to support more CPUs
	* BUGFIX: write our own strstr to avoid libc problems (csilvers)
	* Use simple callgrind compression facility in pprof
	* Print an error message when we can't run pprof to symbolize (csilvers)
	* Die in configure when g++ is't installed (csilvers)
	* DOC: Beef up the documentation a bit about using libunwind (csilvers)

Fri Aug 26 13:29:25 2011    Google Inc. <<EMAIL>>

	* google-perftools: version 1.8.3 release
	* Added back the 'pthreads unsafe early' #define, needed for FreeBSD

Thu Aug 11 15:01:47 2011    Google Inc. <<EMAIL>>

	* google-perftools: version 1.8.2 release
	* Fixed calculation of patchlevel, 'make check' should all pass again

Tue Jul 26 20:57:51 2011    Google Inc. <<EMAIL>>

	* google-perftools: version 1.8.1 release
	* Added an #include to fix compile breakage on latest gcc's
	* Removed an extra , in the configure.ac script

Fri Jul 15 16:10:51 2011    Google Inc. <<EMAIL>>

	* google-perftools: version 1.8 release
	* PORTING: (Disabled) support for patching mmap on freebsd (chapp...)
	* PORTING: Support volatile __malloc_hook for glibc 2.14 (csilvers)
	* PORTING: Use _asm rdtsc and __rdtsc to get cycleclock in windows (koda)
	* PORTING: Fix fd vs. HANDLE compiler error on cygwin (csilvers)
	* PORTING: Do not test memalign or double-linking on OS X (csilvers)
	* PORTING: Actually enable TLS on windows (jontra)
	* PORTING: Some work to compile under Native Client (krasin)
	* PORTING: deal with pthread_once w/o -pthread on freebsd (csilvers)
	* Rearrange libc-overriding to make it easier to port (csilvers)
	* Display source locations in pprof disassembly (sanjay)
	* BUGFIX: Actually initialize allocator name (mec)
	* BUGFIX: Keep track of 'overhead' bytes in malloc reporting (csilvers)
	* Allow ignoring one object twice in the leak checker (glider)
	* BUGFIX: top10 in pprof should print 10 lines, not 11 (rsc)
	* Refactor vdso source files (tipp)
	* Some documentation cleanups
	* Document MAX_TOTAL_THREAD_CACHE_SIZE <= 1Gb (nsethi)
	* Add MallocExtension::GetOwnership(ptr) (csilvers)
	* BUGFIX: We were leaving out a needed $(top_srcdir) in the Makefile
	* PORTING: Support getting argv0 on OS X
	* Add 'weblist' command to pprof: like 'list' but html (sanjay)
	* Improve source listing in pprof (sanjay)
	* Cap cache sizes to reduce fragmentation (ruemmler)
	* Improve performance by capping or increasing sizes (ruemmler)
	* Add M{,un}mapReplacmenet hooks into MallocHook (ribrdb)
	* Refactored system allocator logic (gangren)
	* Include cleanups (csilvers)
	* Add TCMALLOC_SMALL_BUT_SLOW support (ruemmler)
	* Clarify that tcmalloc stats are MiB (robinson)
	* Remove support for non-tcmalloc debugallocation (blount)
	* Add a new test: malloc_hook_test (csilvers)
	* Change the configure script to be more crosstool-friendly (mcgrathr)
	* PORTING: leading-underscore changes to support win64 (csilvers)
	* Improve debugallocation tc_malloc_size (csilvers)
	* Extend atomicops.h and cyceclock to use ARM V6+ optimized code (sanek)
	* Change malloc-hook to use a list-like structure (llib)
	* Add flag to use MAP_PRIVATE in memfs_malloc (gangren)
	* Windows support for pprof: nul and /usr/bin/file (csilvers)
	* TESTING: add test on strdup to tcmalloc_test (csilvers)
	* Augment heap-checker to deal with no-inode maps (csilvers)
	* Count .dll/.dylib as shared libs in heap-checker (csilvers)
	* Disable sys_futex for arm; it's not always reliable (sanek)
	* PORTING: change lots of windows/port.h macros to functions
	* BUGFIX: Generate correct version# in tcmalloc.h on windows (csilvers)
	* PORTING: Some casting to make solaris happier about types (csilvers)
	* TESTING: Disable debugallocation_test in 'minimal' mode (csilvers)
	* Rewrite debugallocation to be more modular (csilvers)
	* Don't try to run the heap-checker under valgrind (ppluzhnikov)
	* BUGFIX: Make focused stat %'s relative, not absolute (sanjay)
	* BUGFIX: Don't use '//' comments in a C file (csilvers)
	* Quiet new-gcc compiler warnings via -Wno-unused-result, etc (csilvers)

Fri Feb 04 15:54:31 2011    Google Inc. <<EMAIL>>

	* google-perftools: version 1.7 release
	* Reduce page map key size under x86_64 by 4.4MB (rus)
	* Remove a flaky malloc-extension test (fdabek)
	* Improve the performance of PageHeap::New (ond..., csilvers)
	* Improve sampling_test with no-inline additions/etc (fdabek)
	* 16-byte align debug allocs (jyasskin)
	* Change FillProcSelfMaps to detect out-of-buffer-space (csilvers)
	* Document the need for sampling to use GetHeapSample (csilvers)
	* Try to read TSC frequency from tsc_freq_khs (adurbin)
	* Do better at figuring out if tests are running under gdb (ppluzhnikov)
	* Improve spinlock contention performance (ruemmler)
	* Better internal-function list for pprof's /contention (ruemmler)
	* Speed up GoogleOnce (m3b)
	* Limit number of incoming/outgoing edges in pprof (sanjay)
	* Add pprof --evince to go along with --gv (csilvers)
	* Document the various ways to get heap-profiling information (csilvers)
	* Separate out synchronization profiling routines (ruemmler)
	* Improve malloc-stats output to be more understandable (csilvers)
	* Add support for census profiler in pporf (nabeelmian)
	* Document how pprof's /symbol must support GET requests (csilvers)
	* Improve acx_pthread.m4 (ssuomi, liujisi)
	* Speed up pprof's ExtractSymbols (csilvers)
	* Ignore some known-leaky (java) libraries in the heap checker (davidyu)
	* Make kHideMask use all 64 bits in tests (ppluzhnikov)
	* Clean up pprof input-file handling (csilvers)
	* BUGFIX: Don't crash if __environ is NULL (csilvers)
	* BUGFIX: Fix totally broken debugallocation tests (csilvers)
	* BUGFIX: Fix up fake_VDSO handling for unittest (ppluzhnikov)
	* BUGFIX: Suppress all large allocs when report threshold is 0 (lexie)
	* BUGFIX: mmap2 on i386 takes an off_t, not off64_t (csilvers)
	* PORTING: Add missing PERFTOOLS_DLL_DECL (csilvers)
	* PORTING: Add stddef.h to make newer gcc's happy (csilvers)
	* PORTING: Document some tricks for working under OS X (csilvers)
	* PORTING: Don't try to check valgrind for windows (csilvers)
	* PORTING: Make array-size a var to compile under clang (chandlerc)
	* PORTING: No longer hook _aligned_malloc and _aligned_free (csilvers)
	* PORTING: Quiet some gcc warnings (csilvers)
	* PORTING: Replace %PRIxPTR with %p to be more portable (csilvers)
	* PORTING: Support systems that capitalize /proc weirdly (sanek)
	* PORTING: Treat arm3 the same as arm5t in cycletimer (csilvers)
	* PORTING: Update windows logging to not allocate memory (csilvers)
	* PORTING: avoid double-patching newer windows DLLs (roger.orr)
	* PORTING: get dynamic_annotations.c to work on windows (csilvers)
	* Add pkg-config .pc files for the 5 libraries we produce (csilvers)
	* Added proper libtool versioning, so this lib will be 0.1.0 (csilvers)
	* Moved from autoconf 2.64 to 2.65

Thu Aug  5 12:48:03 PDT 2010  Google Inc. <<EMAIL>>

	* google-perftools: version 1.6 release
	* Add tc_malloc_usable_size for compatibility with glibc (csilvers)
	* Override malloc_usable_size with tc_malloc_usable_size (csilvers)
	* Default to no automatic heap sampling in tcmalloc (csilvers)
	* Add -DTCMALLOC_LARGE_PAGES, a possibly faster tcmalloc (rus)
	* Make some functions extern "C" to avoid false ODR warnings (jyasskin)
	* pprof: Add SVG-based output (rsc)
	* pprof: Extend pprof --tools to allow per-tool configs (csilvers)
	* pprof: Improve support of 64-bit and big-endian profiles (csilvers)
	* pprof: Add interactive callgrind suport (weidenri...)
	* pprof: Improve address->function mapping a bit (dpeng)
	* Better detection of when we're running under valgrind (csilvers)
	* Better CPU-speed detection under valgrind (saito)
	* Use, and recommend, -fno-builtin-malloc when compiling (csilvers)
	* Avoid false-sharing of memory between caches (bmaurer)
	* BUGFIX: Fix heap sampling to use correct alloc size (bmauer)
	* BUGFIX: Avoid gcc 4.0.x bug by making hook-clearing atomic (csilvers)
	* BUGFIX: Avoid gcc 4.5.x optimization bug (csilvers)
	* BUGFIX: Work around deps-determining bug in libtool 1.5.26 (csilvers)
	* BUGFIX: Fixed test to use HAVE_PTHREAD, not HAVE_PTHREADS (csilvers)
	* BUGFIX: Fix tls callback behavior on windows when using wpo (wtc)
	* BUGFIX: properly align allocation sizes on Windows (antonm)
	* BUGFIX: Fix prototypes for tcmalloc/debugalloc wrt throw() (csilvers)
	* DOC: Updated heap-checker doc to match reality better (fischman)
	* DOC: Document ProfilerFlush, ProfilerStartWithOptions (csilvers)
	* DOC: Update docs for heap-profiler functions (csilvers)
	* DOC: Clean up documentation around tcmalloc.slack_bytes (fikes)
	* DOC: Renamed README.windows to README_windows.txt (csilvers)
	* DOC: Update the NEWS file to be non-empty (csilvers)
	* PORTING: Fix windows addr2line and nm with proper rc code (csilvers)
	* PORTING: Add CycleClock and atomicops support for arm 5 (sanek)
	* PORTING: Improve PC finding on cygwin and redhat 7 (csilvers)
	* PORTING: speed up function-patching under windows (csilvers)

Tue Jan 19 14:46:12 2010  Google Inc. <<EMAIL>>

	* google-perftools: version 1.5 release
	* Add tc_set_new_mode (willchan)
	* Make memalign functions + realloc respect tc_set_new_mode (willchan)
	* Add ReleaseToSystem(num_bytes) (kash)
	* Handle zero-length symbols a bit better in pprof (csilvers)
	* Prefer __environ to /proc/self/environ in cpu profiler (csilvers)
	* Add HEAP_CHECK_MAX_LEAKS flag to control #leaks to report (glider)
	* Add two new numeric pageheap properties to MallocExtension (fikes)
	* Print alloc size when mmap fails (hakon)
	* Add ITIMER_REAL support to cpu profiler (csilvers, nabeelmian)
	* Speed up symbolizer in heap-checker reporting (glider)
	* Speed up futexes with FUTEX_PRIVATE_FLAG (m3b)
	* Speed up tcmalloc but doing better span coalescing (sanjay)
	* Better support for different wget's and addr2maps in pprof (csilvres)
	* Implement a nothrow version of delete and delete[] (csilvers)
	* BUGFIX: fix a race on module_libcs[i] in windows patching (csilvers)
	* BUGFIX: Fix debugallocation to call cpp_alloc for new (willchan)
	* BUGFIX: A simple bugfix for --raw mode (mrabkin)
	* BUGFIX: Fix C shims to actually be valid C (csilvers)
	* BUGFIX: Fix recursively-unmapped-region accounting (ppluzhnikov)
	* BUGFIX: better distinguish real and fake vdso (ppluzhnikov)
	* WINDOWS: replace debugmodule with more reliable psai (andrey)
	* PORTING: Add .bundle as another shared library extension (csilvers)
	* PORTING: Fixed a typo bug in the ocnfigure PRIxx m4 macro (csilvers)
	* PORTING: Augment sysinfo to work on 64-bit OS X (csilvers)
	* PORTING: Use sys/ucontext.h to fix compiing on OS X 10.6 (csilvers)
	* PORTING: Fix sysinfo libname reporting for solaris x86 (jeffrey)
	* PORTING: Use libunwind for i386 when using --omitfp (ppluzhnikov)
	
Thu Sep 10 13:51:15 2009  Google Inc. <<EMAIL>>

	* google-perftools: version 1.4 release
	* Add debugallocation library, to catch memory leaks, stomping, etc
	* Add --raw mode to allow for delayed processing of pprof files
	* Use less memory when reading CPU profiles
	* New environment variables to control kernel-allocs (sbrk, memfs, etc)
	* Add MarkThreadBusy(): performance improvement
	* Remove static thread-cache-size code; all is dynamic now
	* Add new HiddenPointer class to heap checker
	* BUGFIX: pvalloc(0) allocates now (found by new debugalloc library)
	* BUGFIX: valloc test (not implementation) no longer overruns memory
	* BUGFIX: GetHeapProfile no longer deadlocks
	* BUGFIX: Support unmapping memory regions before main
	* BUGFIX: Fix some malloc-stats formatting
	* BUGFIX: Don't crash as often when freeing libc-allocated memory
	* BUGFIX: Deal better with incorrect PPROF_PATH when symbolizing
	* BUGFIX: weaken new/delete/etc in addition to malloc/free/etc
	* BUGFIX: Fix return value of GetAllocatedSize
	* PORTING: Fix mmap-#define problem on some 64-bit systems
	* PORTING: Call ranlib again (some OS X versions need it)
	* PORTING: Fix a leak when building with LLVM
	* PORTING: Remove some unneeded bash-ishs from testing scripts
	* WINDOWS: Support library unloading as well as loading
	* WINDOWS/BUGFIX: Set page to 'xrw' instead of 'rw' when patching
	
Tue Jun  9 18:19:06 2009  Google Inc. <<EMAIL>>

	* google-perftools: version 1.3 release
	* Provide our own name for memory functions: tc_malloc, etc (csilvers)
	* Weaken memory-alloc functions so user can override them (csilvers)
	* Remove meaningless delete(nothrow) and delete[](nothrow) (csilvers)
	* BUILD: replace clever libtcmalloc/profiler.a with a new .a (csilvers)
	* PORTING: improve windows port  by using google spinlocks (csilvers)
	* PORTING: Fix RedHat 9 memory allocation in heapchecker (csilvers)
	* PORTING: Rename OS_WINDOWS macro to PLATFORM_WINDOWS (mbelshe)
	* PORTING/BUGFIX: Make sure we don't clobber GetLastError (mbelshe)
	* BUGFIX: get rid of useless data for callgrind (weidenrinde)
	* BUGFIX: Modify windows patching to deadlock sometimes (csilvers)
	* BUGFIX: an improved fix for hook handling during fork (csilvers)
	* BUGFIX: revamp profiler_unittest.sh, which was very broken (csilvers)
	
Fri Apr 17 16:40:48 2009  Google Inc. <<EMAIL>>

	* google-perftools: version 1.2 release
	* Allow large_alloc_threshold=0 to turn it off entirely (csilvers)
	* Die more helpfully when out of memory for internal data (csilvers)
	* Refactor profile-data gathering, add a new unittest (cgd, nabeelmian)
	* BUGFIX: fix rounding errors with static thread-size caches (addi)
	* BUGFIX: disable hooks better when forking in leak-checker (csilvers)
	* BUGFIX: fix realloc of crt pointers on windows (csilvers)
	* BUGFIX: do a better job of finding binaries in .sh tests (csilvers)
	* WINDOWS: allow overriding malloc/etc instead of patching (mbelshe)
	* PORTING: fix compilation error in a ppc-specific file (csilvers)
	* PORTING: deal with quirks in cygwin's /proc/self/maps (csilvers)
	* PORTING: use 'A' version of functions for ascii input (mbelshe)
	* PORTING: generate .so's on cygwin and mingw (ajenjo)
	* PORTING: disable profiler methods on cygwin (jperkins)
	* Updated autoconf version to 2.61 and libtool version to 1.5.26

Wed Mar 11 11:25:34 2009  Google Inc. <<EMAIL>>

	* google-perftools: version 1.1 release
	* Dynamically resize thread caches -- nice perf. improvement (kash)
	* Add VDSO support to give better stacktraces in linux (ppluzhnikov)
	* Improve heap-profiling sampling algorithm (ford)
	* Rewrite leak-checking code: should be faster and more robust (sanjay)
	* Use ps2 instead of ps for dot: better page cropping for gv (csilvers)
	* Disable malloc-failure warning messages by default (csilvers)
	* Update config/Makefile to disable tests on a per-OS basis (csilvers)
	* PORTING: Get perftools compiling under MSVC 7.1 again (csilvers)
	* PORTING: Get perftools compiling under cygwin again (csilvers)
	* PORTING: automatically set library flags for solaris x86 (csilvers)
	* Add TCMALLOC_SKIP_SBRK to mirror TCMALLOC_SKIP_MMAP (csilvers)
	* Add --enable flags to allow selective building (csilvers)
	* Put addr2line-pdb and nm-pdb in proper output directory (csilvers)
	* Remove deprecated DisableChecksIn (sanjay)
	* DOCUMENTATION: Document most MallocExtension routines (csilvers)
	
Tue Jan  6 13:58:56 2009  Google Inc. <<EMAIL>>

	* google-perftools: version 1.0 release
	* Exactly the same as 1.0rc2

Sun Dec 14 17:10:35 2008  Google Inc. <<EMAIL>>

	* google-perftools: version 1.0rc2 release
	* Fix compile error on 64-bit systems (casting ptr to int) (csilvers)

Thu Dec 11 16:01:32 2008  Google Inc. <<EMAIL>>

	* google-perftools: version 1.0rc1 release
	* Replace API for selectively disabling heap-checker in code (sanjay)
	* Add a pre-mmap hook (daven, adlr)
	* Add MallocExtension interface to set memory-releasing rate (fikes)
	* Augment pprof to allow any string ending in /pprof/profile (csilvers)
	* PORTING: Rewrite -- and fix --  malloc patching for windows (dvitek)
	* PORTING: Add nm-pdb and addr2line-pdb for use by pprof (dvitek)
	* PORTING: Improve cygwin and mingw support (jperkins, csilvers)
	* PORTING: Fix pprof for mac os x, other pprof improvements (csilvers)
	* PORTING: Fix some PPC bugs in our locking code (anton.blanchard)
	* A new unittest, smapling_test, to verify tcmalloc-profiles (csilvers)
	* Turn off TLS for gcc < 4.1.2, due to a TLS + -fPIC bug (csilvers)
	* Prefer __builtin_frame_address to assembly for stacktraces (nlewycky)
	* Separate tcmalloc.cc out into multiple files -- finally! (kash)
	* Make our locking code work with -fPIC on 32-bit x86 (aruns)
	* Fix an initialization-ordering bug for tcmalloc/profiling (csilvers)
	* Use "initial exec" model of TLS to speed up tcmalloc (csilvers)
	* Enforce 16-byte alignment for tcmalloc, for SSE (sanjay)
	
Tue Sep 23 08:56:31 2008  Google Inc. <<EMAIL>>

	* google-perftools: version 0.99.2 release
	* COMPILE FIX: add #include needed for FreeBSD and OS X (csilvers)

Sat Sep 20 09:37:18 2008  Google Inc. <<EMAIL>>

	* google-perftools: version 0.99.1 release
	* BUG FIX: look for nm, etc in /usr/bin, not /usr/crosstool (csilvers)

Thu Sep 18 16:00:27 2008  Google Inc. <<EMAIL>>

	* google-perftools: version 0.99 release
	* Add IsHeapProfileRunning (csilvers)
	* Add C shims for some of the C++ header files (csilvers)
	* Fix heap profile file clean-up logic (maxim)
	* Rename linuxthreads.c to .cc for better compiler support (csilvers)
	* Add source info to disassembly in pprof (sanjay)
	* Use open instead of fopen to avoid memory alloc (csilvers)
	* Disable malloc extensions when running under valgrind (kcc)
	* BUG FIX: Fix out-of-bound error by reordering a check (larryz)
	* Add Options struct to ProfileData (cgd)
	* Correct PC-handling of --base in pprof (csilvers)
	* Handle 1 function occurring twice in an image (sanjay)
	* Improve stack-data cleaning (maxim)
	* Use 'struct Foo' to make header C compatible (csilvers)
	* Add 'total' line to pprof --text (csilvers)
	* Pre-allocate buffer for heap-profiler to avoid OOM errors (csilvers)
	* Allow a few more env-settings to control tcmalloc (csilvers)
	* Document some of the issues involving thread-local storage (csilvers)
	* BUG FIX: Define strtoll and friends for windows (csilvers)

Mon Jun  9 16:47:03 2008  Google Inc. <<EMAIL>>

	* google-perftools: version 0.98 release
	* Add ProfilerStartWithOptions() (cgd)
	* Change tcmalloc_minimal to not do any stack-tracing at all (csilvers)
	* Prefer mmap to sbrk for 64-buit debug mode (sanjay)
	* Fix accounting for some tcmalloc stats (sanjay)
	* Use setrlimit() to keep unittests from killing the machine (odo)
	* Fix a bug when sbrk-ing near address 4G (csilvers)
	* Make MallocHook thread-safe (jyasskin)
	* Fix windows build for MemoryBarrier (jyasskin)
	* Fix CPU-profiler docs to mention correct libs (csilvers)
	* Fix for GetHeapProfile() when heap-profiling is off (maxim)
	* Avoid realloc resizing ping-pongs using hysteresis (csilvers)
	* Add --callgrind output support to pprof (klimek)
	* Fix profiler.h and heap-profiler.h to be C-compatible (csilvers)
	* Break malloc_hook.h into two parts to reduce dependencies (csilvers)
	* Better handle systems that don't implement mmap (csilvers)
	* PORTING: disable system_alloc_unittest for msvc (csilvers)
	* PORTING: Makefile tweaks to build better on cygwin (csilvers)
	
Mon Apr 21 15:20:52 2008  Google Inc. <<EMAIL>>

	* google-perftools: version 0.97 release
	* Refactor GetHeapProfile to avoid using malloc (maxim)
	* Fix heap-checker and heap-profiler hook interactions (maxim)
	* Fix a data race in MemoryRegionMap::Lock (jyasskin)
	* Improve thread-safety of leak checker (maxim)
	* Fix mmap profile to no longer deadlock (maxim)
	* Fix rpm to have devel package depend on non-devel (csilvers)
	* PORTING: Fix clock-speed detection for Mac OS X (csilvers)

Tue Mar 18 14:30:44 2008  Google Inc. <<EMAIL>>

	* google-perftools: version 0.96 release
	* major atomicops rewrite; fixed atomic ops code for linux/ppc (vchen)
	* nix the stacktrace library; now build structure is simpler (csilvers)
	* Speed up heap-checker, and reduce extraneous logging (maxim)
	* Improve itimer code for NPTL case (cgd)
	* Add source code annotations for use by valgrind, etc (kcc)
	* PORTING: Fix high resolution timers for Mac OS X (adlr)

Tue Feb 19 12:01:31 2008  Google Inc. <<EMAIL>>

	* google-perftools: version 0.95.1 release  (bugfix release)
	* x86_64 compile-fix: nix pread64 and pwrite64 (csilvers)
	* more heap-checker debug logging (maxim)
	* minor improvement to x86_64 CycleClock (gpike)

Tue Feb 12 12:28:32 2008  Google Inc. <<EMAIL>>

	* google-perftools: version 0.95 release
	* Better -- not perfect -- support for linux-ppc (csilvers)
	* Fix race condition in libunwind stacktrace (aruns)
	* Speed up x86 spinlock locking (m3b)
	* Improve heap-checker performance (maxim)
	* Heap checker traverses more ptrs inside heap-alloced objects (maxim)
	* Remove deprecated ProfilerThreadState function (cgd)
	* Update libunwind documentation for statically linked binaries (aruns)

Mon Dec  3 23:51:54 2007  Google Inc. <<EMAIL>>

	* google-perftools: version 0.94.1 release  (bugfix release)
	* Fix missing #includes for x86_64 compile using libunwind (csilvers)

Thu Nov 29 07:59:43 2007  Google Inc. <<EMAIL>>

	* google-perftools: version 0.94 release
	* PORTING: MinGW/Msys support -- runs same code as MSVC does (csilvers)
	* PORTING: Add NumCPUs support for Mac OS X (csilvers)
	* Work around a sscanf bug in glibc(?) (waldemar)
	* Fix Windows MSVC bug triggered by thread deletion (csilvers)
	* Fix bug that triggers in MSVC /O2: missing volatile (gpike)
	* March-of-time support: quiet warnings/errors for gcc 4.2, OS X 10.5
	* Modify pprof so it works without nm: useful for windows (csilvers)
	* pprof: Support filtering for CPU profiles (cgd)
	* Bugfix: have realloc report to hooks in all situations (maxim)
	* Speed improvement: replace slow memcpy with std::copy (soren)
	* Speed: better iterator efficiency in RecordRegionRemoval (soren)
	* Speed: minor speed improvements via better bitfield alignment (gpike)
	* Documentation: add documentation of binary profile output (cgd)
	
Fri Aug 17 12:32:56 2007  Google Inc. <<EMAIL>>

	* google-perftools: version 0.93 release
	* PORTING: everything compiles on Solaris, OS X, FreeBSD (see INSTALL)
	* PORTING: cpu-profiler works on most platforms (much better GetPC())
	* PORTING: heap-profiler works on most platforms
	* PORTING: improved windows support, including release builds
	* No longer build or run ptmalloc tests by default
	* Add support for using memfs filesystem to allocate memory in linux
	* WINDOWS: give debug library and release library different names
	
Tue Jul 17 22:26:27 2007  Google Inc. <<EMAIL>>

	* google-perftools: version 0.92 release
	* PERFORMANCE: use a packed cache to speed up tcmalloc
	* PORTING: preliminary windows support! (see README.windows)
	* PORTING: better support for solaris, OS X, FreeBSD (see INSTALL)
	* Envvar support for running the heap-checker under gdb
	* Add weak declarations to maybe_threads to fix no-pthreads compile bugs
	* Some 64bit fixes, especially with pprof
	* Better heap-checker support for some low-level allocations
	* Fix bug where heap-profiles would sometimes get truncated
	* New documentation about how to handle common heap leak situations
	* Use computed includes for hash_map/set: easier config
	* Added all used .m4 templates to the distribution

Wed Apr 18 16:43:55 2007  Google Inc. <<EMAIL>>

	* google-perftools: version 0.91 release
	* Brown-paper-bag bugfix: compilation error on some x86-64 machines

Fri Apr 13 14:50:51 2007  Google Inc. <<EMAIL>>

	* google-perftools: version 0.90 release
	* (As the version-number jump hints, this is a major new release:
	  almost every piece of functionality was rewritten.  I can't do
	  justice to all the changes, but will concentrate on highlights.)
	*** USER-VISIBLE CHANGES:
	* Ability to "release" unused memory added to tcmalloc
	* Exposed more tweaking knobs via environment variables (see docs)
	* pprof tries harder to map addresses to functions
	* tcmalloc_minimal compiles and runs on FreeBSD 6.0 and Solaris 10
	*** INTERNAL CHANGES:
	* Much better 64-bit support
	* Better multiple-processor support (e.g. multicore contention tweaks)
	* Support for recent kernel ABI changes (e.g. new arg to mremap)
	* Addition of spinlocks to tcmalloc to reduce contention cost
	* Speed up tcmalloc by using __thread on systems that support TLS
	* Total redesign of heap-checker to improve liveness checking
	* More portable stack-frame analysis -- no more hard-coded constants!
	* Disentangled heap-profiler code and heap-checker code
	* Several new unittests to test, e.g., thread-contention costs
	* Lots of small (but important!) bug fixes: e.g., fixing GetPC on amd64
	*** KNOWN PROBLEMS:
	* CPU-profiling may crash on x86_64 (64-bit) systems.  See the README
	* Profiling/heap-checking may deadlock on x86_64 systems.  See README

Wed Jun 14 15:11:14 2006  Google Inc. <<EMAIL>>

	* google-perftools: version 0.8 release
	* Experimental support for remote profiling added to pprof (many)
	* Fixed race condition in ProfileData::FlushTable (etune)
	* Better support for weird /proc maps (maxim, mec)
	* Fix heap-checker interaction with gdb (markus)
	* Better 64-bit support in pprof (aruns)
	* Reduce scavenging cost in tcmalloc by capping NumMoveSize (sanjay)
	* Cast syscall(SYS_mmap); works on more 64-bit systems now (menage)
	* Document the text output of pprof! (csilvers)
	* Better compiler support for no-THREADS and for old compilers (csilvers)
	* Make libunwind the default stack unwinder for x86-64 (aruns)
	* Somehow the COPYING file got erased.  Regenerate it (csilvers)

Thu Apr 13 20:59:09 2006  Google Inc. <<EMAIL>>

	* google-perftools: version 0.7 release
	* Major rewrite of thread introspection for new kernels (markus)
	* Major rewrite of heap-checker to use new thread tools (maxim)
	* Add proper support for following data in thread registers (maxim)
	* Syscall support for older kernels, including _syscall6 (markus)
	* Support PIC mode (markus, mbland, iant)
	* Better support for running in non-threaded contexts (csilvers)

Fri Jan 27 14:04:27 2006  Google Inc. <<EMAIL>>

	* google-perftools: version 0.6 release
	* More sophisticated stacktrace usage, possibly using libunwind (aruns)
	* Update pprof to handle 64-bit profiles (dehnert)
	* Fix GetStackTrace to correctly return top stackframe (sanjay)
	* Add ANSI compliance for new and new[], including new_handler (jkearney)
	* More accuracy by reading ELF files directly rather than objdump (mec)
	* Add readline support for pprof (addi)
	* Add #includes for PPC (csilvers)
	* New PC-detection routine for ibook powerpc (asbestoshead)
	* Vastly improved tcmalloc unittest (csilvers)
	* Move documentation from /usr/doc to /usr/share/doc

Mon Nov 14 17:28:59 2005  Google Inc. <<EMAIL>>

	* google-perftools: version 0.5 release
	* Add va_start/va_end calls around vsnprintf() (csilvers)
	* Write our own __syscall_return(), since it's not defined
	  consistently on all 64-bit linux distros (markus)

Wed Oct 26 15:19:16 2005  Google Inc. <<EMAIL>>

	* google-perftools: version 0.4 release
	* Decrease fragmentation in tcmalloc (lefevere)
	* Support for ARM in some of the thread-specific code (markus)
	* Turn off heap-checker for statically-linked binaries, which
	  cause error leak reports now (etune)
	* Many pprof improvements, including a command-line interface (jeff)
	* CPU profiling now automatically affects all threads in linux 2.6.
	  (Kernel bugs break CPU profiling and threads in linux 2.4 a bit.)
	  ProfilerEnable() and ProfilerDisable() are deprecated.  (sanjay)
	* tcmalloc now correctly intercepts memalign (m3b, maxim)
	* Syntax fix: added missing va_end()s.  Helps non-gcc compiling (etune)
	* Fixed a few coredumper bugs: race condition after PTRACE_DETACH,
	  ignore non-aligned stackframe pointers (markus, menage)
	* 64-bit cleanup, especially for spinlock code (etune) and mmap (sanjay)
	* Better support for finding threads in linux (markus)
	* tcmalloc now tracks those stack traces that allocate memory (sanjay)
	* Work around a weird setspecific problem (sanjay)
	* Fix tcmalloc overflow problems when an alloc is close to 2G/4G (sanjay)

Fri Jun 24 18:02:26 2005  Google Inc. <<EMAIL>>

	* google-perftools: version 0.3 release
	* Add missing errno include for one of the unittests (csilvers)
	* Reduce tcmalloc startup memory from 5M to 256K (sanjay)
	* Add support for mallopt() and mallinfo (sanjay)
	* Improve stacktrace's performance on some 64-bit systems (etune)
	* Improve the stacktrace unittest (etune)

Tue May 31 08:14:38 2005  Google Inc. <<EMAIL>>

	* google-perftools: version 0.2 release
	* Use mmap2() instead of mmap(), to map more memory (menage)
	* Do correct pthread-local checking in heap-checker! (maxim)
	* Avoid overflow on 64-bit machines in pprof (sanjay)
	* Add a few more GetPC() functions, including for AMD (csilvers)
	* Better method for overriding pthread functions (menage)
	* (Hacky) fix to avoid overwriting profile files after fork() (csilvers)
	* Crashing bugfix involving dumping heaps on small-stack threads (tudor)
	* Allow library versions with letters at the end (csilvers)
	* Config fixes for systems that don't define PATH_MAX (csilvers)
	* Confix fixes so we no longer need config.h after install (csilvers)
	* Fix to pprof to correctly read very big cpu profiles (csilvers)
	* Fix to pprof to deal with new commandline flags in modern gv's
	* Better error reporting when we can't access /proc/maps (etune)
	* Get rid of the libc-preallocate code (which could crash on some
	  systems); no longer needed with local-threads fix (csilvers)

Tue Feb 8 09:57:17 2005  Google Inc. <<EMAIL>>

	* google-perftools: initial release:
	  The google-perftools package contains some utilities to improve
	  and analyze the performance of C++ programs.  This includes an
	  optimized thread-caching malloc() and cpu and heap profiling
	  utilities.
