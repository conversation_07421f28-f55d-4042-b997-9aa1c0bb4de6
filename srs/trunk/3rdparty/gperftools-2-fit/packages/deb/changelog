gperftools (2.1-1) unstable; urgency=low

  * New upstream release.

 -- gperftools Contributors <<EMAIL>>  <PERSON><PERSON>, 30 Jul 2013 11:51:13 +0300

gperftools (2.0.99-1) unstable; urgency=low

  * New upstream release.

 -- gperftools Contributors <<EMAIL>>  Sat, 20 Jul 2013 14:21:10 -0700

gperftools (2.0-1) unstable; urgency=low

  * New upstream release.
  * Package renamed from google-perftools to gperftools.

 -- Google Inc. and others <<EMAIL>>  Fri, 03 Feb 2012 15:40:45 -0800

google-perftools (1.10-1) unstable; urgency=low

  * New upstream release.

 -- Google Inc. <<EMAIL>>  Tue, 31 Jan 2012 10:43:50 -0800

google-perftools (1.9-1) unstable; urgency=low

  * New upstream release.

 -- Google Inc. <<EMAIL>>  Thu, 22 Dec 2011 16:22:45 -0800

google-perftools (1.8-1) unstable; urgency=low

  * New upstream release.

 -- Google Inc. <<EMAIL>>  Fri, 15 Jul 2011 16:10:51 -0700

google-perftools (1.7-1) unstable; urgency=low

  * New upstream release.

 -- Google Inc. <<EMAIL>>  Fri, 04 Feb 2011 15:54:31 -0800

google-perftools (1.6-1) unstable; urgency=low

  * New upstream release.

 -- Google Inc. <<EMAIL>>  Thu, 05 Aug 2010 12:48:03 -0700

google-perftools (1.5-1) unstable; urgency=low

  * New upstream release.

 -- Google Inc. <<EMAIL>>  Tue, 19 Jan 2010 14:46:12 -0800

google-perftools (1.4-1) unstable; urgency=low

  * New upstream release.

 -- Google Inc. <<EMAIL>>  Thu, 10 Sep 2009 13:51:15 -0700

google-perftools (1.3-1) unstable; urgency=low

  * New upstream release.

 -- Google Inc. <<EMAIL>>  Tue, 09 Jun 2009 18:19:06 -0700

google-perftools (1.2-1) unstable; urgency=low

  * New upstream release.

 -- Google Inc. <<EMAIL>>  Fri, 17 Apr 2009 16:40:48 -0700

google-perftools (1.1-1) unstable; urgency=low

  * New upstream release.

 -- Google Inc. <<EMAIL>>  Wed, 11 Mar 2009 11:25:34 -0700

google-perftools (1.0-1) unstable; urgency=low

  * New upstream release.

 -- Google Inc. <<EMAIL>>  Tue, 06 Jan 2009 13:58:56 -0800
	
google-perftools (1.0rc1-1) unstable; urgency=low

  * New upstream release.

 -- Google Inc. <<EMAIL>>  Thu, 11 Dec 2008 16:01:32 -0800
	
google-perftools (0.99.1-1) unstable; urgency=low

  * New upstream release.

 -- Google Inc. <<EMAIL>>  Sat, 20 Sep 2008 09:37:18 -0700
	
google-perftools (0.99-1) unstable; urgency=low

  * New upstream release.

 -- Google Inc. <<EMAIL>>  Thu, 18 Sep 2008 16:00:27 -0700
	
google-perftools (0.98-1) unstable; urgency=low

  * New upstream release.

 -- Google Inc. <<EMAIL>>  Mon, 09 Jun 2008 16:47:03 -0700
	
google-perftools (0.97-1) unstable; urgency=low

  * New upstream release.

 -- Google Inc. <<EMAIL>>  Mon, 21 Apr 2008 15:20:52 -0700
	
google-perftools (0.96-1) unstable; urgency=low

  * New upstream release.

 -- Google Inc. <<EMAIL>>  Tue, 18 Mar 2008 14:30:44 -0700
	
google-perftools (0.95-1) unstable; urgency=low

  * New upstream release.

 -- Google Inc. <<EMAIL>>  Tue, 12 Feb 2008 12:28:32 -0800

google-perftools (0.94-1) unstable; urgency=low

  * New upstream release.

 -- Google Inc. <<EMAIL>>  Thu, 29 Nov 2007 07:59:43 -0800

google-perftools (0.93-1) unstable; urgency=low

  * New upstream release.

 -- Google Inc. <<EMAIL>>  Fri, 17 Aug 2007 12:32:56 -0700

google-perftools (0.92-1) unstable; urgency=low

  * New upstream release.

 -- Google Inc. <<EMAIL>>  Tue, 17 Jul 2007 22:26:27 -0700

google-perftools (0.91-1) unstable; urgency=low

  * New upstream release.

 -- Google Inc. <<EMAIL>>  Wed, 18 Apr 2007 16:43:55 -0700

google-perftools (0.90-1) unstable; urgency=low

  * New upstream release.

 -- Google Inc. <<EMAIL>>  Fri, 13 Apr 2007 14:50:51 -0700

google-perftools (0.8-1) unstable; urgency=low

  * New upstream release.

 -- Google Inc. <<EMAIL>>  Wed, 14 Jun 2006 15:11:14 -0700

google-perftools (0.7-1) unstable; urgency=low

  * New upstream release.

 -- Google Inc. <<EMAIL>>  Thu, 13 Apr 2006 20:59:09 -0700

google-perftools (0.6-1) unstable; urgency=low

  * New upstream release.

 -- Google Inc. <<EMAIL>>  Fri, 27 Jan 2006 14:04:27 -0800

google-perftools (0.5-1) unstable; urgency=low

  * New upstream release.

 -- Google Inc. <<EMAIL>>  Mon, Nov 14 17:28:59 2005 -0800

google-perftools (0.4-1) unstable; urgency=low

  * New upstream release.

 -- Google Inc. <<EMAIL>>  Wed, 26 Oct 2005 15:19:16 -0700

google-perftools (0.3-1) unstable; urgency=low

  * New upstream release.
  
 -- Google Inc. <<EMAIL>>  Fri, 24 Jun 2005 18:02:26 -0700

google-perftools (0.2-1) unstable; urgency=low

  * New upstream release.

 -- Google Inc. <<EMAIL>>  Tue, 31 May 2005 08:14:38 -0700

google-perftools (0.1-1) unstable; urgency=low

  * Initial release.
    The google-perftools package contains some utilities to improve
    and analyze the performance of C++ programs.  This includes an
    optimized thread-caching malloc() and cpu and heap profiling
    utilities.

 -- Google Inc. <<EMAIL>>  Fri, 11 Mar 2005 08:07:33 -0800
