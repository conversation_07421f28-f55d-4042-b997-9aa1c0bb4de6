/* src/config.h.in.  Generated from configure.ac by autoheader.  */


#ifndef GPERFTOOLS_CONFIG_H_
#define GPERFTOOLS_CONFIG_H_


/* enable aggressive decommit by default */
#undef ENABLE_AGGRESSIVE_DECOMMIT_BY_DEFAULT

/* Build new/delete operators for overaligned types */
#undef ENABLE_ALIGNED_NEW_DELETE

/* Build runtime detection for sized delete */
#undef ENABLE_DYNAMIC_SIZED_DELETE

/* report large allocation */
#undef ENABLE_LARGE_ALLOC_REPORT

/* Build sized deletion operators */
#undef ENABLE_SIZED_DELETE

/* Define to 1 if you have the <asm/ptrace.h> header file. */
#undef HAVE_ASM_PTRACE_H

/* Define to 1 if you have the <conflict-signal.h> header file. */
#undef HAVE_CONFLICT_SIGNAL_H

/* define if the compiler supports basic C++11 syntax */
#undef HAVE_CXX11

/* Define to 1 if you have the <cygwin/signal.h> header file. */
#undef HAVE_CYGWIN_SIGNAL_H

/* Define to 1 if you have the declaration of `backtrace', and to 0 if you
   don't. */
#undef HAVE_DECL_BACKTRACE

/* Define to 1 if you have the declaration of `cfree', and to 0 if you don't.
   */
#undef HAVE_DECL_CFREE

/* Define to 1 if you have the declaration of `memalign', and to 0 if you
   don't. */
#undef HAVE_DECL_MEMALIGN

/* Define to 1 if you have the declaration of `nanosleep', and to 0 if you
   don't. */
#undef HAVE_DECL_NANOSLEEP

/* Define to 1 if you have the declaration of `posix_memalign', and to 0 if
   you don't. */
#undef HAVE_DECL_POSIX_MEMALIGN

/* Define to 1 if you have the declaration of `pvalloc', and to 0 if you
   don't. */
#undef HAVE_DECL_PVALLOC

/* Define to 1 if you have the declaration of `sleep', and to 0 if you don't.
   */
#undef HAVE_DECL_SLEEP

/* Define to 1 if you have the declaration of `valloc', and to 0 if you don't.
   */
#undef HAVE_DECL_VALLOC

/* Define to 1 if you have the <dlfcn.h> header file. */
#undef HAVE_DLFCN_H

/* Define to 1 if the system has the type `Elf32_Versym'. */
#undef HAVE_ELF32_VERSYM

/* Define to 1 if you have the <execinfo.h> header file. */
#undef HAVE_EXECINFO_H

/* Define to 1 if you have the <fcntl.h> header file. */
#undef HAVE_FCNTL_H

/* Define to 1 if you have the <features.h> header file. */
#undef HAVE_FEATURES_H

/* Define to 1 if you have the `fork' function. */
#undef HAVE_FORK

/* Define to 1 if you have the `geteuid' function. */
#undef HAVE_GETEUID

/* Define to 1 if you have the <glob.h> header file. */
#undef HAVE_GLOB_H

/* Define to 1 if you have the <grp.h> header file. */
#undef HAVE_GRP_H

/* Define to 1 if you have the <inttypes.h> header file. */
#undef HAVE_INTTYPES_H

/* Define to 1 if you have the <libunwind.h> header file. */
#undef HAVE_LIBUNWIND_H

/* Define to 1 if you have the <linux/ptrace.h> header file. */
#undef HAVE_LINUX_PTRACE_H

/* Define if this is Linux that has SIGEV_THREAD_ID */
#undef HAVE_LINUX_SIGEV_THREAD_ID

/* Define to 1 if you have the <malloc.h> header file. */
#undef HAVE_MALLOC_H

/* Define to 1 if you have the <memory.h> header file. */
#undef HAVE_MEMORY_H

/* Define to 1 if you have a working `mmap' system call. */
#undef HAVE_MMAP

/* Define to 1 if you have the <poll.h> header file. */
#undef HAVE_POLL_H

/* define if libc has program_invocation_name */
#undef HAVE_PROGRAM_INVOCATION_NAME

/* Define if you have POSIX threads libraries and header files. */
#undef HAVE_PTHREAD

/* defined to 1 if pthread symbols are exposed even without include pthread.h
   */
#undef HAVE_PTHREAD_DESPITE_ASKING_FOR

/* Define to 1 if you have the <pwd.h> header file. */
#undef HAVE_PWD_H

/* Define to 1 if you have the `sbrk' function. */
#undef HAVE_SBRK

/* Define to 1 if you have the <sched.h> header file. */
#undef HAVE_SCHED_H

/* Define to 1 if you have the <stdint.h> header file. */
#undef HAVE_STDINT_H

/* Define to 1 if you have the <stdlib.h> header file. */
#undef HAVE_STDLIB_H

/* Define to 1 if you have the <strings.h> header file. */
#undef HAVE_STRINGS_H

/* Define to 1 if you have the <string.h> header file. */
#undef HAVE_STRING_H

/* Define to 1 if the system has the type `struct mallinfo'. */
#undef HAVE_STRUCT_MALLINFO

/* Define to 1 if you have the <sys/cdefs.h> header file. */
#undef HAVE_SYS_CDEFS_H

/* Define to 1 if you have the <sys/prctl.h> header file. */
#undef HAVE_SYS_PRCTL_H

/* Define to 1 if you have the <sys/resource.h> header file. */
#undef HAVE_SYS_RESOURCE_H

/* Define to 1 if you have the <sys/socket.h> header file. */
#undef HAVE_SYS_SOCKET_H

/* Define to 1 if you have the <sys/stat.h> header file. */
#undef HAVE_SYS_STAT_H

/* Define to 1 if you have the <sys/syscall.h> header file. */
#undef HAVE_SYS_SYSCALL_H

/* Define to 1 if you have the <sys/types.h> header file. */
#undef HAVE_SYS_TYPES_H

/* Define to 1 if you have the <sys/ucontext.h> header file. */
#undef HAVE_SYS_UCONTEXT_H

/* Define to 1 if you have the <sys/wait.h> header file. */
#undef HAVE_SYS_WAIT_H

/* Define to 1 if compiler supports __thread */
#undef HAVE_TLS

/* Define to 1 if you have the <ucontext.h> header file. */
#undef HAVE_UCONTEXT_H

/* Define to 1 if you have the <unistd.h> header file. */
#undef HAVE_UNISTD_H

/* Whether <unwind.h> contains _Unwind_Backtrace */
#undef HAVE_UNWIND_BACKTRACE

/* Define to 1 if you have the <unwind.h> header file. */
#undef HAVE_UNWIND_H

/* define if your compiler has __attribute__ */
#undef HAVE___ATTRIBUTE__

/* define if your compiler supports alignment of functions */
#undef HAVE___ATTRIBUTE__ALIGNED_FN

/* Define to 1 if compiler supports __environ */
#undef HAVE___ENVIRON

/* Define to 1 if you have the `__sbrk' function. */
#undef HAVE___SBRK

/* prefix where we look for installed files */
#undef INSTALL_PREFIX

/* Define to 1 if int32_t is equivalent to intptr_t */
#undef INT32_EQUALS_INTPTR

/* Define to the sub-directory where libtool stores uninstalled libraries. */
#undef LT_OBJDIR

/* Name of package */
#undef PACKAGE

/* Define to the address where bug reports for this package should be sent. */
#undef PACKAGE_BUGREPORT

/* Define to the full name of this package. */
#undef PACKAGE_NAME

/* Define to the full name and version of this package. */
#undef PACKAGE_STRING

/* Define to the one symbol short name of this package. */
#undef PACKAGE_TARNAME

/* Define to the home page for this package. */
#undef PACKAGE_URL

/* Define to the version of this package. */
#undef PACKAGE_VERSION

/* How to access the PC from a struct ucontext */
#undef PC_FROM_UCONTEXT

/* Always the empty-string on non-windows systems. On windows, should be
   "__declspec(dllexport)". This way, when we compile the dll, we export our
   functions/classes. It's safe to define this here because config.h is only
   used internally, to compile the DLL, and every DLL source file #includes
   "config.h" before anything else. */
#undef PERFTOOLS_DLL_DECL

/* Mark the systems where we know it's bad if pthreads runs too
   early before main (before threads are initialized, presumably).  */
#ifdef __FreeBSD__
#define PTHREADS_CRASHES_IF_RUN_TOO_EARLY 1
#endif

/* Define to necessary symbol if this constant uses a non-standard name on
   your system. */
#undef PTHREAD_CREATE_JOINABLE

/* Define to 1 if you have the ANSI C header files. */
#undef STDC_HEADERS

/* Define 8 bytes of allocation alignment for tcmalloc */
#undef TCMALLOC_ALIGN_8BYTES

/* Define internal page size for tcmalloc as number of left bitshift */
#undef TCMALLOC_PAGE_SIZE_SHIFT

/* Version number of package */
#undef VERSION

/* C99 says: define this to get the PRI... macros from stdint.h */
#ifndef __STDC_FORMAT_MACROS
# define __STDC_FORMAT_MACROS 1
#endif


#ifdef __MINGW32__
#include "windows/mingw.h"
#endif

#endif  /* #ifndef GPERFTOOLS_CONFIG_H_ */

