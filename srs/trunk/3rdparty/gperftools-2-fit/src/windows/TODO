* Get heap-profile-table.cc using DeleteMatchingFiles
* Get heap-profile-table.cc using FillProcSelfMaps, DumpProcSelfMaps
* Play around with ExperimentalGetStackTrace
* Support the windows-level memory-allocation functions?  See
    /home/<USER>/googleclient/earth/client/tools/memorytracking/client/memorytrace/src/memorytrace.cpp
    /home/<USER>/googleclient/total_recall/common/sitestep/*
    http://www.internals.com/articles/apispy/apispy.htm
    http://www.wheaty.net/APISPY32.zip
* Verify /proc/xxx/maps:
    http://www.geocities.com/wah_java_dotnet/procmap/index.html
* Figure out how to edit the executable IAT so tcmalloc.dll is loaded first
* Use QueryPerformanceCounter instead of GetTickCount() (also for sparsehash)

----
More info on windows-level memory-allocation functions:
   C runtime malloc
   LocalAlloc
   GlobalAlloc
   HeapAlloc
   VirtualAlloc
   mmap stuff

malloc, LocalAlloc and GlobalAlloc call HeapAlloc, which calls
VirtualAlloc when needed, which calls VirtualAllocEx (the __sbrk equiv?)

siggi sez: If you want to do a generic job, you probably need to
preserve the semantics of all of these Win32 calls:
   Heap32First
   Heap32ListFirst
   Heap32ListNext
   Heap32Next
   HeapAlloc
   HeapCompact
   HeapCreate
   HeapCreateTagsW
   HeapDestroy
   HeapExtend
   HeapFree
   HeapLock
   HeapQueryInformation
   HeapQueryTagW
   HeapReAlloc
   HeapSetInformation
   HeapSize
   HeapSummary
   HeapUnlock
   HeapUsage
   HeapValidate
   HeapWalk

kernel32.dll export functions and nt.dll export functions:
   http://www.shorthike.com/svn/trunk/tools_win32/dm/lib/kernel32.def
   http://undocumented.ntinternals.net/

You can edit the executable IAT to have the patching DLL be the
first one loaded.

Most complete way to intercept system calls is patch the functions
(not the IAT).

Microsoft has somee built-in routines for heap-checking:
   http://support.microsoft.com/kb/268343

----
Itimer replacement:
   http://msdn2.microsoft.com/en-us/library/ms712713.aspx

----
Changes I've had to make to the project file:

0) When creating the project file, click on "no autogenerated files"

--- For each project:
1) Alt-F7 -> General -> [pulldown "all configurations" ] -> Output Directory -> $(SolutionDir)$(ConfigurationName)
2) Alt-F7 -> General -> [pulldown "all configurations" ] -> Intermediate Directory -> $(ConfigurationName)

--- For each .cc file:
1) Alt-F7 -> C/C++ -> General -> [pulldown "all configurations"] -> Additional Include Directives --> src/windows + src/
2) Alt-F7 -> C/C++ -> Code Generation -> Runtime Library -> Multi-threaded, debug/release, DLL or not

--- For DLL:
3) Alt-F7 -> Linker -> Input -> [pulldown "all configurations" ] -> Module Definition File -> src\windows\vc7and8.def
--- For binaries depending on a DLL:
3) Right-click on project -> Project Dependencies -> [add dll]
--- For static binaries (not depending on a DLL)
3) Alt-F7 -> C/C++ -> Command Line -> [pulldown "all configurations"] -> /D PERFTOOLS_DLL_DECL=
