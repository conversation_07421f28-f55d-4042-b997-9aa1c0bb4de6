<configuration>
    <config>
        <add key="dependencyVersion" value="Highest" />
        <add key="globalPackagesFolder" value="c:\packages" />
        <add key="repositoryPath" value="packages" />
    </config>
    <packageSources>
        <add key="nuget.org" value="https://api.nuget.org/v3/index.json" protocolVersion="3" />
        <!--<add key="Local Source" value="D:\Data\Dev\LocalNuget" />-->
    </packageSources>
</configuration>