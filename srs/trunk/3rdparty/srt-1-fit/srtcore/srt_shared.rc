// Microsoft Visual C++ generated resource script.
//
#include "version.h"
#include "winres.h"

/////////////////////////////////////////////////////////////////////////////
//
// Version
//

VS_VERSION_INFO VERSIONINFO
#ifdef SRT_VERSION_BUILD
 FILEVERSION SRT_VERSION_MAJOR, SRT_VERSION_MINOR, SRT_VERSION_PATCH, SRT_VERSION_BUILD
#else
 FILEVERSION SRT_VERSION_MAJOR, SRT_VERSION_MINOR, SRT_VERSION_PATCH
#endif
 FILEFLAGSMASK 0x3fL
#ifdef _DEBUG
 FILEFLAGS 0x1L
#else
 FILEFLAGS 0x0L
#endif
 FILEOS 0x40004L
 FILETYPE 0x2L
 FILESUBTYPE 0x0L
BEGIN
    BLOCK "StringFileInfo"
    BEGIN
        BLOCK "080904b0"
        BEGIN
            VALUE "CompanyName", "SRT Alliance"
            VALUE "FileDescription", "SRT Local Build"
            VALUE "InternalName", "srt.dll"
            VALUE "LegalCopyright", ""
            VALUE "OriginalFilename", "srt.dll"
            VALUE "ProductName", "SRT"
            VALUE "ProductVersion", SRT_VERSION_STRING
        END
    END
    BLOCK "VarFileInfo"
    BEGIN
        VALUE "Translation", 0x809, 1200
    END
END

