

SOURCES
api.cpp
buffer_snd.cpp
buffer_rcv.cpp
buffer_tools.cpp
cache.cpp
channel.cpp
common.cpp
core.cpp
crypto.cpp
epoll.cpp
fec.cpp
handshake.cpp
list.cpp
logger_default.cpp
logger_defs.cpp
md5.cpp
packet.cpp
packetfilter.cpp
queue.cpp
congctl.cpp
socketconfig.cpp
srt_c_api.cpp
srt_compat.c
strerror_defs.cpp
sync.cpp
tsbpd_time.cpp
window.cpp

SOURCES - ENABLE_BONDING
group.cpp
group_backup.cpp
group_common.cpp

SOURCES - !ENABLE_STDCXX_SYNC
sync_posix.cpp

SOURCES - ENABLE_STDCXX_SYNC
sync_cxx11.cpp

SOURCES - EXTRA_WIN32_SHARED
srt_shared.rc

PUBLIC HEADERS
srt.h
logging_api.h
access_control.h

PROTECTED HEADERS
platform_sys.h
udt.h

PRIVATE HEADERS
api.h
buffer_snd.h
buffer_rcv.h
buffer_tools.h
cache.h
channel.h
common.h
core.h
crypto.h
epoll.h
handshake.h
list.h
logging.h
md5.h
netinet_any.h
packet.h
sync.h
queue.h
congctl.h
socketconfig.h
srt_compat.h
stats.h
threadname.h
tsbpd_time.h
utilities.h
window.h

PRIVATE HEADERS - ENABLE_BONDING
group.h
group_backup.h
group_common.h
