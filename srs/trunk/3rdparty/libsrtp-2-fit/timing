# timing.plt
# 
# gnuplot script file for plotting the output generated by srtp_driver -t
# 
# <PERSON>. McGrew
# Cisco Systems, Inc.
#
set xrange [0:2500]
set term pict "Times-Roman" 9
#
# plot authentication-only data
#
set title "Authentication Only"
set ylabel "Megabits per second"
set xlabel "Octets in packet"
set yrange [0:2000]
set output "plot-auth.pict"
plot "timing.dat" index 0 title "HMAC SHA1" with lines, "timing.dat" index 1 title "TMMH/AES" with lines, "timing.dat" index 2 title "TMMH/SEAL" with lines
#
# plot encryption-only data
#
set title "Encryption Only"
set ylabel "Megabits per second"
set xlabel "Octets in packet"
set output "plot-enc.pict"
set yrange [0:1200]
plot "timing.dat" index 3 title "SEAL" with lines, "timing.dat" index 4 title "AES ICM" with lines
#
# plot encryption and authentication data
#
set title "Encryption and Authentication"
set ylabel "Megabits per second"
set xlabel "Octets in packet"
set yrange [0:1000]
set output "plot-enc-auth.pict"
plot "timing.dat" index 5 title "TMMH/SEAL" with lines, "timing.dat" index 6 title "TMMH/AES" with lines, "timing.dat" index 7 title "HMAC/AES" with lines