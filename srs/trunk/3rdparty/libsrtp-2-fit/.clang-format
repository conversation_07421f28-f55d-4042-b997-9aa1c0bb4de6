AccessModifierOffset: -4

AlignAfterOpenBracket: Align
AlignConsecutiveAssignments: false
AlignConsecutiveDeclarations: false
AlignEscapedNewlinesLeft: false
AlignOperands:   true
AlignTrailingComments: true

AllowAllParametersOfDeclarationOnNextLine: false

AllowShortBlocksOnASingleLine: false
AllowShortCaseLabelsOnASingleLine: false
AllowShortFunctionsOnASingleLine: Inline
AllowShortIfStatementsOnASingleLine: false
AllowShortLoopsOnASingleLine: false

AlwaysBreakAfterDefinitionReturnType: None
AlwaysBreakBeforeMultilineStrings: false
AlwaysBreakTemplateDeclarations: false

BinPackArguments: true
BinPackParameters: false
BraceWrapping:
  AfterControlStatement: false
  AfterEnum:       false
  AfterFunction:   true
  AfterStruct:     false
  AfterUnion:      false
  BeforeCatch:     false
  BeforeElse:      false
  IndentBraces:    false

BreakBeforeBinaryOperators: None
BreakBeforeBraces: Custom
BreakBeforeTernaryOperators: true

ColumnLimit:     80
CommentPragmas:  '^ IWYU pragma:'
ContinuationIndentWidth: 4
DerivePointerAlignment: false
DisableFormat:   false
ExperimentalAutoDetectBinPacking: false

IndentCaseLabels: false
IndentWidth:     4
IndentWrappedFunctionNames: false

KeepEmptyLinesAtTheStartOfBlocks: false

MaxEmptyLinesToKeep: 1

PenaltyBreakBeforeFirstCallParameter: 16
PenaltyBreakComment: 300
PenaltyBreakFirstLessLess: 120
PenaltyBreakString: 1000
PenaltyExcessCharacter: 1000
PenaltyReturnTypeOnItsOwnLine: 9000

Cpp11BracedListStyle: false

PointerAlignment: Right

ReflowComments:  true

SortIncludes: false

SpaceAfterCStyleCast: false
SpaceBeforeAssignmentOperators: true
SpaceBeforeParens: ControlStatements
SpaceInEmptyParentheses: false
SpacesBeforeTrailingComments: 1
SpacesInAngles:  false
SpacesInContainerLiterals: true
SpacesInCStyleCastParentheses: false
SpacesInParentheses: false
SpacesInSquareBrackets: false
TabWidth:        4
UseTab:          Never
