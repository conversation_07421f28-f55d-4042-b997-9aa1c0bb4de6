
<html>
<head>
    <title>SRS</title>
    <meta charset="utf-8">
</head>
<body>
<h3><a href="https://github.com/ossrs/signaling">Signaling</a> works!</h3>
<p>
    Run demo for <a class="srs_demo" href="one2one.html?autostart=true">WebRTC: One to One over SFU(SRS)</a><br/>
    点击进入<a class="srs_demo" href="one2one.html?autostart=true">SRS一对一通话演示</a>
</p>
<p>
    Run demo for <a class="srs_demo" href="room.html?autostart=true">WebRTC: Video Room over SFU(SRS)</a><br/>
    点击进入<a class="srs_demo" href="room.html?autostart=true">SRS多人通话演示</a>
</p>
<script>
    let roomName = Number(parseInt(new Date().getTime()*Math.random()*100)).toString(16).slice(0, 7);
    let elems = document.getElementsByClassName('srs_demo');
    for (var i = 0; i < elems.length; i++) {
        let elem = elems.item(i);

        // Use random room.
        let href = elem.getAttribute('href') + '&room=' + roomName;

        // For run demos on SRS http server.
        if (window.location.port === '8080') {
            href += '&wsp=1989';
        }

        elem.setAttribute('href', href);
    }
</script>
</body>
