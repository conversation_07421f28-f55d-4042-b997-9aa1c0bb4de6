<HTML>
<HEAD>
<TITLE>State Threads Library Reference</TITLE>
</HEAD>
<BODY BGCOLOR=#FFFFFF>

<H2>State Threads Library Reference</H2>

<DL>
<DD><A HREF=#types><B>Types</B></A></DD>
<DL><DD><A HREF=#thread_t>st_thread_t</A></DD></DL>
<DL><DD><A HREF=#cond_t>st_cond_t</A></DD></DL>
<DL><DD><A HREF=#mutex_t>st_mutex_t</A></DD></DL>
<DL><DD><A HREF=#utime_t>st_utime_t</A></DD></DL>
<DL><DD><A HREF=#netfd_t>st_netfd_t</A></DD></DL>
<DL><DD><A HREF=#switch_cb_t>st_switch_cb_t</A></DD></DL>
<P>
<DD><A HREF=#errors><B>Error Handling</B></A></DD>
<P>
<DD><A HREF=#init><B>Library Initialization</B></A></DD>
<P>
<DL><DD><A HREF=#st_init>st_init()</A></DD></DL>
<DL><DD><A HREF=#getfdlimit>st_getfdlimit()</A></DD></DL>
<DL><DD><A HREF=#set_eventsys>st_set_eventsys()</A></DD></DL>
<DL><DD><A HREF=#get_eventsys>st_get_eventsys()</A></DD></DL>
<DL><DD><A HREF=#get_eventsys_name>st_get_eventsys_name()</A></DD></DL>
<DL><DD><A HREF=#set_utime_function>st_set_utime_function()</A></DD></DL>
<DL><DD><A HREF=#timecache_set>st_timecache_set()</A></DD></DL>
<DL><DD><A HREF=#randomize_stacks>st_randomize_stacks()</A></DD></DL>
<P>
<DL><DD><A HREF=#switch_cb_t><B>st_switch_cb_t</B> type</A></DD></DL>
<DL><DD><A HREF=#set_switch_in_cb>st_set_switch_in_cb()</A></DD></DL>
<DL><DD><A HREF=#set_switch_out_cb>st_set_switch_out_cb()</A></DD></DL>
<P>
<DD><A HREF=#threads><B>Thread Control and Identification</B></A></DD>
<P>
<DL><DD><A HREF=#thread_t><B>st_thread_t</B> type</A></DD></DL>
<DL><DD><A HREF=#thread_create>st_thread_create()</A></DD></DL>
<DL><DD><A HREF=#thread_exit>st_thread_exit()</A></DD></DL>
<DL><DD><A HREF=#thread_join>st_thread_join()</A></DD></DL>
<DL><DD><A HREF=#thread_self>st_thread_self()</A></DD></DL>
<DL><DD><A HREF=#thread_interrupt>st_thread_interrupt()</A></DD></DL>
<DL><DD><A HREF=#sleep>st_sleep()</A></DD></DL>
<DL><DD><A HREF=#sleep>st_usleep()</A></DD></DL>
<DL><DD><A HREF=#randomize_stacks>st_randomize_stacks()</A></DD></DL>
<P>
<DD><A HREF=#priv><B>Per-Thread Private Data</B></A></DD>
<P>
<DL><DD><A HREF=#key_create>st_key_create()</A></DD></DL>
<DL><DD><A HREF=#key_getlimit>st_key_getlimit()</A></DD></DL>
<DL><DD><A HREF=#thread_setspecific>st_thread_setspecific()</A></DD></DL>
<DL><DD><A HREF=#thread_getspecific>st_thread_getspecific()</A></DD></DL>
<P>
<DD><A HREF=#sync><B>Synchronization</B></A></DD>
<P>
<DL><DD><A HREF=#cond_t><B>st_cond_t</B> type</A></DD></DL>
<DL><DD><A HREF=#cond_new>st_cond_new()</A></DD></DL>
<DL><DD><A HREF=#cond_destroy>st_cond_destroy()</A></DD></DL>
<DL><DD><A HREF=#cond_wait>st_cond_wait()</A></DD></DL>
<DL><DD><A HREF=#cond_timedwait>st_cond_timedwait()</A></DD></DL>
<DL><DD><A HREF=#cond_signal>st_cond_signal()</A></DD></DL>
<DL><DD><A HREF=#cond_broadcast>st_cond_broadcast()</A></DD></DL>
<P>
<DL><DD><A HREF=#mutex_t><B>st_mutex_t</B> type</A></DD></DL>
<DL><DD><A HREF=#mutex_new>st_mutex_new()</A></DD></DL>
<DL><DD><A HREF=#mutex_destroy>st_mutex_destroy()</A></DD></DL>
<DL><DD><A HREF=#mutex_lock>st_mutex_lock()</A></DD></DL>
<DL><DD><A HREF=#mutex_trylock>st_mutex_trylock()</A></DD></DL>
<DL><DD><A HREF=#mutex_unlock>st_mutex_unlock()</A></DD></DL>
<P>
<DD><A HREF=#timing><B>Timing</B></A></DD>
<P>
<DL><DD><A HREF=#utime_t><B>st_utime_t</B> type</A></DD></DL>
<DL><DD><A HREF=#utime>st_utime()</A></DD></DL>
<DL><DD><A HREF=#set_utime_function>st_set_utime_function()</A></DD></DL>
<DL><DD><A HREF=#timecache_set>st_timecache_set()</A></DD></DL>
<DL><DD><A HREF=#time>st_time()</A></DD></DL>
<P>
<DD><A HREF=#io><B>I/O Functions</B></A></DD>
<P>
<DL><DD><A HREF=#netfd_t><B>st_netfd_t</B> type</A></DD></DL>
<DL><DD><A HREF=#netfd_open>st_netfd_open()</A></DD></DL>
<DL><DD><A HREF=#netfd_open_socket>st_netfd_open_socket()</A></DD></DL>
<DL><DD><A HREF=#netfd_free>st_netfd_free()</A></DD></DL>
<DL><DD><A HREF=#netfd_close>st_netfd_close()</A></DD></DL>
<DL><DD><A HREF=#netfd_fileno>st_netfd_fileno()</A></DD></DL>
<DL><DD><A HREF=#netfd_setspecific>st_netfd_setspecific()</A></DD></DL>
<DL><DD><A HREF=#netfd_getspecific>st_netfd_getspecific()</A></DD></DL>
<DL><DD><A HREF=#netfd_serialize_accept>st_netfd_serialize_accept()</A></DD>
</DL>
<DL><DD><A HREF=#netfd_poll>st_netfd_poll()</A></DD></DL>
<P>
<DL><DD><A HREF=#accept>st_accept()</A></DD></DL>
<DL><DD><A HREF=#connect>st_connect()</A></DD></DL>
<DL><DD><A HREF=#read>st_read()</A></DD></DL>
<DL><DD><A HREF=#read_fully>st_read_fully()</A></DD></DL>
<DL><DD><A HREF=#read_resid>st_read_resid()</A></DD></DL>
<DL><DD><A HREF=#readv>st_readv()</A></DD></DL>
<DL><DD><A HREF=#readv_resid>st_readv_resid()</A></DD></DL>
<DL><DD><A HREF=#write>st_write()</A></DD></DL>
<DL><DD><A HREF=#write_resid>st_write_resid()</A></DD></DL>
<DL><DD><A HREF=#writev>st_writev()</A></DD></DL>
<DL><DD><A HREF=#writev_resid>st_writev_resid()</A></DD></DL>
<DL><DD><A HREF=#recvfrom>st_recvfrom()</A></DD></DL>
<DL><DD><A HREF=#sendto>st_sendto()</A></DD></DL>
<DL><DD><A HREF=#recvmsg>st_recvmsg()</A></DD></DL>
<DL><DD><A HREF=#sendmsg>st_sendmsg()</A></DD></DL>
<P>
<DL><DD><A HREF=#open>st_open()</A></DD></DL>
<DL><DD><A HREF=#poll>st_poll()</A></DD></DL>
<P>
<DD><A HREF=#progr><B>Program Structure</B></A></DD>
<P>
<DD><A HREF=#block><B>List of Blocking Functions</B></A></DD>
<P>
</DL>
<P>
<HR>
<P>


<A NAME="types">
<H2>Types</H2>
</A>
The State Thread library defines the following types in the <TT>st.h</TT>
header file:
<P>
<DL>
<DD><A HREF=#thread_t>st_thread_t</A></DD>
<DD><A HREF=#cond_t>st_cond_t</A></DD>
<DD><A HREF=#mutex_t>st_mutex_t</A></DD>
<DD><A HREF=#utime_t>st_utime_t</A></DD>
<DD><A HREF=#netfd_t>st_netfd_t</A></DD>
</DL>
<P>
<HR>
<P>

<A NAME="thread_t">
<H4>st_thread_t</H4>
</A>
Thread type.
<P>
<H5>Syntax</H5>

<PRE>
#include &lt;st.h&gt;

typedef void *  st_thread_t;
</PRE>
<P>
<H5>Description</H5>

A thread is represented and identified by a pointer to an opaque data
structure. This pointer is a required parameter for most of the functions
that operate on threads.
<P>
The thread identifier remains valid until the thread returns from its root
function and, if the thread was created joinable, is joined.
<P>
<HR>
<P>

<A NAME="cond_t">
<H4>st_cond_t</H4>
</A>
Condition variable type.
<P>
<H5>Syntax</H5>

<PRE>
#include &lt;st.h&gt;

typedef void *  st_cond_t;
</PRE>
<P>
<H5>Description</H5>

A condition variable is an opaque object identified by a pointer.
Condition variables provide synchronization primitives to wait for or wake
up threads waiting for certain conditions to be satisfied.
<P>
In the State Threads library there is no need to lock a mutex before
waiting on a condition variable.
<P>
<HR>
<P>

<A NAME="mutex_t">
<H4>st_mutex_t</H4>
</A>
Mutex type.
<P>
<H5>Syntax</H5>

<PRE>
#include &lt;st.h&gt;

typedef void *  st_mutex_t;
</PRE>
<P>
<H5>Description</H5>

A mutex is an opaque object identified by a pointer.
Mutual exclusion locks (mutexes) are used to serialize the execution of
threads through critical sections of code.
<P>
If application using the State Threads library is written with no
I/O or control yielding in critical sections (that is no
<A HREF=#block>blocking functions</A> in critical sections), then there is
no need for mutexes.<P>
These mutexes can only be used for intra-process thread synchronization.
They cannot be used for inter-process synchronization.
<P>
<HR>
<P>

<A NAME="utime_t">
<H4>st_utime_t</H4>
</A>
High resolution time type ("u" stands for "micro").
<P>
<H5>Syntax</H5>

<PRE>
#include &lt;st.h&gt;

typedef unsigned long long  st_utime_t;
</PRE>
<P>
<H5>Description</H5>

This datatype (unsigned 64-bit integer) represents high-resolution real time
expressed in microseconds since some arbitrary time in the past. It is not
correlated in any way to the time of day.
<P>
<HR>
<P>

<A NAME="netfd_t">
<H4>st_netfd_t</H4>
</A>
File descriptor type.
<P>
<H5>Syntax</H5>

<PRE>
#include &lt;st.h&gt;

typedef void *  st_netfd_t;
</PRE>
<P>
<H5>Description</H5>

This datatype typically represents any open end point of network
communication (socket, end point of a pipe, FIFO, etc.) but can
encapsulate any open file descriptor.  Objects of this type are
identified by a pointer to an opaque data structure.

<P>
<HR>
<P>

<A NAME="switch_cb_t">
<H4>st_switch_cb_t</H4>
</A>
Context switch callback function type.
<P>
<H5>Syntax</H5>

<PRE>
#include &lt;st.h&gt;

typedef void (*st_switch_cb_t)(void);
</PRE>
<P>
<H5>Description</H5>

This datatype is a convenience type for describing a pointer
to a function that will be called when a thread is set to stop
or set to run.
This feature is available only when <TT>ST_SWITCH_CB</TT> is defined
in <TT>&lt;st.h&gt;</TT>.

<P>
<HR>
<P>

<A NAME="errors">
<H2>Error Handling</H2>
</A>

All State Threads library non-void functions return on success either a
non-negative integer or a pointer to a newly created object (constructor-type
functions). On failure they return either <TT>-1</TT> or a <TT>NULL</TT>
pointer respectively and set global <TT>errno</TT> to indicate the error.
It is safe to use <TT>errno</TT> because it is set right before the function
return and only one thread at a time can modify its value.<P>
The <TT>perror(3)</TT> function can be used to produce an error message on the
standard error output.
<P>
<HR>
<P>

<A NAME="init">
<H2>Library Initialization</H2>
</A>
<P>
<DL>
<DD><A HREF=#st_init>st_init()</A></DD>
<DD><A HREF=#getfdlimit>st_getfdlimit()</A></DD>
<DD><A HREF=#set_eventsys>st_set_eventsys()</A></DD>
<DD><A HREF=#get_eventsys>st_get_eventsys()</A></DD>
<DD><A HREF=#get_eventsys_name>st_get_eventsys_name()</A></DD>
<P>
These functions operate on a callback function of type
<A HREF=#switch_cb_t><B>st_switch_cb_t</B></A>:
<DD><A HREF=#set_switch_in_cb>st_set_switch_in_cb()</A></DD>
<DD><A HREF=#set_switch_out_cb>st_set_switch_out_cb()</A></DD>
</DL>
<P>
<HR>
<P>

<A NAME="st_init">
<H4>st_init()</H4>
</A>
Initializes the runtime.
<P>
<H5>Syntax</H5>

<PRE>
#include &lt;st.h&gt;

int st_init(void);
</PRE>
<P>
<H5>Parameters</H5>
None.
<P>
<H5>Returns</H5>
Upon successful completion, a value of <TT>0</TT> is returned.
Otherwise, a value of <TT>-1</TT> is returned and <TT>errno</TT> is set
to indicate the error.
<P>
<H5>Description</H5>
This function initializes the library runtime. It should be called near
the beginning of the application's <TT>main()</TT> function before any other
State Threads library function is called.<P>
Among other things, this function limits the number of open file descriptors
to the OS imposed per-process maximum number or, if <TT>select(2)</TT> is
used, to <TT>FD_SETSIZE</TT>, whichever is less (<TT>getrlimit(2)</TT>).
This limit can be
retrieved by <A HREF=#getfdlimit>st_getfdlimit()</A>. It also sets the
disposition of the <TT>SIGPIPE</TT> signal to <TT>SIG_IGN</TT> (to be ignored)
(<TT>signal(5)</TT>).
<P>
Unlike POSIX threads, a new process created by the <TT>fork(2)</TT> system
call is an <I>exact</I> copy of the calling process and all state threads
which are running in the parent do exist in the child. That means that
<TT>st_init()</TT> may be called either before or after multiple processes
are created by <TT>fork(2)</TT>.
<P>
If the library runtime is not properly initialized (e.g., <TT>st_init()</TT>
is accidentally omitted), then the process will receive either an arithmetic
exception (SIGFPE or SIGTRAP) or segmentation fault (SIGSEGV) signal upon
new thread creation or the first context switch, respectively.
<P>
<HR>
<P>
<A NAME="getfdlimit">
<H4>st_getfdlimit()</H4>
</A>
Returns the maximum number of file descriptors that the calling process
can open.
<P>
<H5>Syntax</H5>

<PRE>
#include &lt;st.h&gt;

int st_getfdlimit(void);
</PRE>
<P>
<H5>Parameters</H5>
None.
<P>
<H5>Returns</H5>
The maximum number of file descriptors that the calling process can open.
If this function is called before the library is successfully initialized by
<A HREF=#st_init>st_init()</A>, a value of <TT>-1</TT> is returned.
<P>
<H5>Description</H5>
This function returns the limit on the number of open file descriptors which
is set by the <A HREF=#st_init>st_init()</A> function.
<P>
<HR>
<P>

<A NAME="set_eventsys">
<H4>st_set_eventsys()</H4>
</A>
Sets event notification mechanism.
<P>
<H5>Syntax</H5>

<PRE>
#include &lt;st.h&gt;

int st_set_eventsys(int eventsys);
</PRE>
<P>
<H5>Parameters</H5>
<TT>st_set_eventsys()</TT> has the following parameter:<P>
<TT>eventsys</TT><P>
An integer value identifying selected event notification mechanism. The
following values are defined in the <TT>st.h</TT> header file:
<P>
<TABLE BORDER=0 CELLPADDING=5 CELLSPACING=15>
<TR>
<TD VALIGN=TOP><TT>ST_EVENTSYS_DEFAULT</TT></TD>
<TD>Use default event notification mechanism. Usually it's <TT>select(2)</TT>
but if the library was compiled with the <TT>USE_POLL</TT> macro defined
then the default is <TT>poll(2)</TT>.</TD>
</TR>
<TR>
<TD VALIGN=TOP><TT>ST_EVENTSYS_SELECT</TT></TD>
<TD>Use <TT>select(2)</TT> as an event notification mechanism.</TD>
</TR>
<TD VALIGN=TOP><TT>ST_EVENTSYS_POLL</TT></TD>
<TD>Use <TT>poll(2)</TT> as an event notification mechanism.</TD>
</TR>
<TD VALIGN=TOP><TT>ST_EVENTSYS_ALT</TT></TD>
<TD>Use an alternative event notification mechanism. The actual
mechanism selected depends on OS support. For example, <TT>epoll(4)</TT>
will be used on Linux if supported and <TT>kqueue(2)</TT> will be used
on FreeBSD/OpenBSD. If the OS supports no alternative event
notification mechanism, setting <TT>ST_EVENTSYS_ALT</TT> has no effect
and the <TT>ST_EVENTSYS_DEFAULT</TT> mechanism will be used.</TD>
</TR>
</TABLE>
<P>
<H5>Returns</H5>
Upon successful completion, a value of <TT>0</TT> is returned.
Otherwise, a value of <TT>-1</TT> is returned and <TT>errno</TT> is set
to indicate the error:<P>
<TABLE BORDER=0 CELLSPACING=15>
<TR><TD><TT>EINVAL</TT></TD>
<TD>
The supplied <TT>eventsys</TT> parameter has an invalid value.
</TD></TR>
<TR><TD><TT>EBUSY</TT></TD>
<TD>
The event notification mechanism has already been set.
</TD></TR>
</TABLE>
<P>
<H5>Description</H5>
This function sets the event notification mechanism that will be used by
the State Threads library. To have any effect, it must be called
<i>before</i> the <A HREF=#st_init>st_init()</A> function which performs
the actual initialization. If <TT>st_set_eventsys()</TT> is not called,
<A HREF=#st_init>st_init()</A> will set the <TT>ST_EVENTSYS_DEFAULT</TT>
mechanism. The mechanism cannot be changed once set.
<P>
There are no strict rules for selecting an event notification
mechanism.  The "best" one depends on how your application behaves.
Try a few to see which one works best for you.  As a rule of
thumb, you should use the <TT>ST_EVENTSYS_ALT</TT> mechanism if your
application deals with a very large number of network connections of
which only a few are active at once.
<P>
<HR>
<P>
<A NAME="get_eventsys">
<H4>st_get_eventsys()</H4>
</A>
Returns the integer value identifying the event notification mechanism
being used by the State Threads library.
<P>
<H5>Syntax</H5>

<PRE>
#include &lt;st.h&gt;

int st_get_eventsys(void);
</PRE>
<P>
<H5>Parameters</H5>
None.
<P>
<H5>Returns</H5>
The integer value identifying the current event notification mechanism.
This value can be one of the following (see <A
HREF=#set_eventsys>st_set_eventsys()</A>):
<TT>ST_EVENTSYS_SELECT</TT>, <TT>ST_EVENTSYS_POLL</TT>, or
<TT>ST_EVENTSYS_ALT</TT>. Future versions of the library may return other
values. If a mechanism hasn't been set yet, a value of <TT>-1</TT> is returned.
<P>
<H5>Description</H5>
This function returns the integer value identifying the event notification
mechanism which is actually being used by the State Threads library.
<P>
<HR>
<P>
<A NAME="get_eventsys_name">
<H4>st_get_eventsys_name()</H4>
</A>
Returns the name of the event notification mechanism being used by the
State Threads library.
<P>
<H5>Syntax</H5>

<PRE>
#include &lt;st.h&gt;

const char *st_get_eventsys_name(void);
</PRE>
<P>
<H5>Parameters</H5>
None.
<P>
<H5>Returns</H5>
The string identifying the current event notification mechanism. If a
mechanism hasn't been set yet (see <A
HREF=#set_eventsys>st_set_eventsys()</A>), an empty string is
returned. Possible return values are <TT>"select"</TT>,
<TT>"poll"</TT>, <TT>"kqueue"</TT>, or <TT>"epoll"</TT>.  Future versions
of the library may return other values.
<P>
<H5>Description</H5>
This function returns the string identifying the event notification
mechanism which is actually being used by the State Threads library.
<P>
<HR>
<P>

<A NAME="set_switch_in_cb">
<H4>st_set_switch_in_cb()</H4>
</A>
<A NAME="set_switch_out_cb">
<H4>st_set_switch_out_cb()</H4>
</A>
Set the optional callback function for thread switches.
<P>
<H5>Syntax</H5>

<PRE>
#include &lt;st.h&gt;

st_switch_cb_t st_set_switch_in_cb(st_switch_cb_t cb);
st_switch_cb_t st_set_switch_out_cb(st_switch_cb_t cb);
</PRE>
<P>
<H5>Parameters</H5>
<TT>st_set_switch_in_cb()</TT> and <TT>st_set_switch_out_cb()</TT> have the
following parameter:<P>
<TT>cb</TT><P>
A function to be called when a thread is resumed and stopped respectively.<P>
<H5>Returns</H5>
The previous callback function pointer.
<P>
<H5>Description</H5>
These functions set the callback for when a thread is resumed and stopped
respectively.  After being called any thread switch will call the callback.
Use a <TT>NULL</TT> pointer to disable the callback (this is the default).
Use <A HREF=#thread_self>st_thread_self()</A> or <A HREF=#priv>thread
specific data</A> to differentiate between threads.<P>
These functions can be called at any time.<P>
This feature is available only when <TT>ST_SWITCH_CB</TT> is defined
in <TT>&lt;st.h&gt;</TT>.
<P>
<HR>
<P>

<A NAME="threads">
<H2>Thread Control and Identification</H2>
</A>
<P>
These functions operate on a thread object of type
<A HREF=#thread_t><B>st_thread_t</B></A>.
<P>
<DL>
<DD><A HREF=#thread_create>st_thread_create()</A></DD>
<DD><A HREF=#thread_exit>st_thread_exit()</A></DD>
<DD><A HREF=#thread_join>st_thread_join()</A></DD>
<DD><A HREF=#thread_self>st_thread_self()</A></DD>
<DD><A HREF=#thread_interrupt>st_thread_interrupt()</A></DD>
<DD><A HREF=#sleep>st_sleep()</A></DD>
<DD><A HREF=#sleep>st_usleep()</A></DD>
<DD><A HREF=#randomize_stacks>st_randomize_stacks()</A></DD>
</DL>
<P>
<HR>
<P>
<A NAME="thread_create">
<H4>st_thread_create()</H4>
</A>
Creates a new thread.
<P>
<H5>Syntax</H5>

<PRE>
#include &lt;st.h&gt;

st_thread_t st_thread_create(void *(*start)(void *arg), void *arg,
                             int joinable, int stack_size);

</PRE>
<P>
<H5>Parameters</H5>
<TT>st_thread_create()</TT> has the following parameters:<P>
<TT>start</TT><P>
A pointer to the thread's start function, which is called as the root of the
new thread. Return from this function terminates a thread.<P>
<TT>arg</TT><P>
A pointer to the root function's only parameter.<P>
<TT>joinable</TT><P>
Specifies whether the thread is joinable or unjoinable. If this parameter
is zero, the thread is unjoinable. Otherwise, it is joinable.
See also <A HREF=#thread_join>st_thread_join()</A>.<P>
<TT>stack_size</TT><P>
Specifies your preference for the size of the stack, in bytes, associated
with the newly created thread. If you pass zero in this parameter, the
default stack size will be used. The default stack size is 128 KB on IA-64
and 64 KB on all other platforms. On IA-64 only a half of <TT>stack_size</TT>
bytes is used for the memory stack. The other half is used for the register
stack backing store.
<P>
<H5>Returns</H5>
Upon successful completion, a new thread identifier is returned (this
identifier remains valid until the thread returns from its start function).
Otherwise, <TT>NULL</TT> is returned and <TT>errno</TT> is set
to indicate the error.
<P>
<H5>Description</H5>
This function creates a new thread. Note that the total number of threads
created by the application is limited by the amount of swap space available.
Upon thread creation, <TT>stack_size</TT> bytes are reserved on the swap
space. The stack pages are not actually used (valid) until touched by the
application.
<P>
<HR>
<P>
<A NAME="thread_exit">
<H4>st_thread_exit()</H4>
</A>
Terminates the calling thread.
<P>
<H5>Syntax</H5>

<PRE>
#include &lt;st.h&gt;

void st_thread_exit(void *retval);
</PRE>
<P>
<H5>Parameters</H5>
<TT>st_thread_exit()</TT> has the following parameters:<P>
<TT>retval</TT><P>
If the thread is joinable, then the value <TT>retval</TT> may be retrieved
by <A HREF=#thread_join>st_thread_join()</A>. If a thread returns from its
start function, it acts as if it had called <TT>st_thread_exit()</TT> with 
<TT>retval</TT> as the value returned.
<P>
<H5>Returns</H5>
Nothing.
<P>
<H5>Description</H5>
This function terminates the calling thread. When a thread exits, per-thread
private data is destroyed by invoking the destructor function for any
non-<TT>NULL</TT> thread specific values associated with active keys (see
<A HREF=#key_create>st_key_create()</A>). This function is implicitly called
when a thread returns from its start function.<P>
When the last thread terminates the process exits with a zero status value.
<P>
<HR>
<P>
<A NAME="thread_join">
<H4>st_thread_join()</H4>
</A>
Blocks the calling thread until a specified thread terminates.
<P>
<H5>Syntax</H5>

<PRE>
#include &lt;st.h&gt;

int st_thread_join(st_thread_t thread, void **retvalp);
</PRE>
<P>
<H5>Parameters</H5>
<TT>st_thread_join()</TT> has the following parameters:<P>
<TT>thread</TT><P>
A valid identifier for the thread that is to be joined.<P>
<TT>retvalp</TT><P>
If this parameter is not <TT>NULL</TT>, then the exit value of the
<TT>thread</TT> will be placed in the location referenced by this parameter
(see <A HREF=#thread_exit>st_thread_exit()</A>).
<P>
<H5>Returns</H5>
Upon successful completion, a value of <TT>0</TT> is returned.
Otherwise, a value of <TT>-1</TT> is returned and <TT>errno</TT> is set
to indicate the error:<P>
<TABLE BORDER=0>
<TR><TD><TT>EINVAL</TT></TD><TD>Target thread is unjoinable.</TD></TR>
<TR><TD><TT>EINVAL</TT></TD><TD>Other thread already waits on the same
joinable thread.</TD></TR>
<TR><TD><TT>EDEADLK</TT></TD><TD>Target thread is the same as the
calling thread.</TD></TR>
<TR><TD><TT>EINTR</TT></TD><TD>Current thread was interrupted by
<A HREF=#thread_interrupt>st_thread_interrupt()</A>.</TD></TR>
</TABLE>
<P>
<H5>Description</H5>
This function is used to synchronize the termination of a thread and possibly
retrieve its exit value. Several threads cannot wait for the same thread
to complete - one of the calling threads operates successfully, and the others
terminate with the error. The calling thread is not blocked if the target
thread has already terminated.
<P>
<HR>
<P>
<A NAME="thread_self">
<H4>st_thread_self()</H4>
</A>
Identifies the calling thread.
<P>
<H5>Syntax</H5>

<PRE>
#include &lt;st.h&gt;

st_thread_t st_thread_self(void);
</PRE>
<P>
<H5>Parameters</H5>
None.
<P>
<H5>Returns</H5>
Always returns a valid reference to the calling thread - a self-identity.
<P>
<H5>Description</H5>
This function identifies the calling thread. This is the same identifier
that the creating thread obtains from
<A HREF=#thread_create>st_thread_create()</A>.
<P>
<HR>
<P>
<A NAME="thread_interrupt">
<H4>st_thread_interrupt()</H4>
</A>
Interrupts a target thread.
<P>
<H5>Syntax</H5>

<PRE>
#include &lt;st.h&gt;

void st_thread_interrupt(st_thread_t thread);
</PRE>
<P>
<H5>Parameters</H5>
<TT>st_thread_interrupt()</TT> has the following parameters:<P>
<TT>thread</TT><P>
A valid identifier for the thread being interrupted.
<P>
<H5>Returns</H5>
Nothing.
<P>
<H5>Description</H5>
This function interrupts (unblocks) a target thread that is blocked in one
of the <A HREF=#block>blocking functions</A>. A function that was interrupted
returns an error and sets <TT>errno</TT> to <TT>EINTR</TT>. It is up to
the target thread to act upon an interrupt (e.g., it may exit or just
abort the current transaction).<P>
<B>Note: </B> State Threads library functions are never interrupted by a
caught signal. A blocking library function returns an error and sets
<TT>errno</TT> to <TT>EINTR</TT> <I>only</I> if the current thread was
interrupted via <TT>st_thread_interrupt()</TT>.
<P>
If a target thread is already runnable or running (e.g., it is a newly
created thread or calling thread itself), this function will prevent it
from subsequent blocking. In other words, the interrupt will be "delivered"
only when a target thread is about to block.
<P>
<HR>
<P>
<A NAME="sleep">
<H4>st_sleep(), st_usleep()</H4>
</A>
Suspends current thread for a specified amount of time.
<P>
<H5>Syntax</H5>

<PRE>
#include &lt;st.h&gt;

int st_sleep(int secs);

int st_usleep(st_utime_t usecs);
</PRE>
<P>
<H5>Parameters</H5>
<TT>st_sleep()</TT> has the following parameters:<P>
<TT>secs</TT><P>
The number of seconds you want the thread to sleep for.
<P>
<TT>st_usleep()</TT> has the following parameters:<P>
<TT>usecs</TT><P>
The number of microseconds you want the thread to sleep for. This parameter
is a variable of type <A HREF=#utime_t><B>st_utime_t</B></A>.
<P>
<H5>Returns</H5>
Upon successful completion, a value of <TT>0</TT> is returned.
Otherwise, a value of <TT>-1</TT> is returned and <TT>errno</TT> is set
to indicate the error:<P>
<TABLE BORDER=0>
<TR><TD><TT>EINTR</TT></TD><TD>The current thread was interrupted by
<A HREF=#thread_interrupt>st_thread_interrupt()</A>.</TD></TR>
</TABLE>
<P>
<H5>Description</H5>
These functions suspend the calling thread from execution for a specified
number of seconds (<TT>st_sleep()</TT>) or microseconds (<TT>st_usleep()</TT>).
<P>

If zero is passed as a parameter to <tt>st_sleep()</tt>, or
<tt>ST_UTIME_NO_WAIT</tt> (<tt>0</tt>) is passed to
<tt>st_usleep()</tt>, the calling thread yields, thus potentially
allowing another thread to run.

<P>

If <TT>-1</TT> is passed as a parameter to <tt>st_sleep()</tt>, or
<tt>ST_UTIME_NO_TIMEOUT</tt> (<tt>-1</tt>) is passed to
<tt>st_usleep()</tt>, the calling thread will be suspended permanently.
It can be resumed again by interrupting it via <A
HREF=#thread_interrupt>st_thread_interrupt()</A>.

<P>
<HR>
<P>
<A NAME="randomize_stacks">
<H4>st_randomize_stacks()</H4>
</A>
Turns stack base address randomization on or off.
<P>
<H5>Syntax</H5>

<PRE>
#include &lt;st.h&gt;

int st_randomize_stacks(int on);
</PRE>
<P>
<H5>Parameters</H5>
<TT>st_randomize_stacks()</TT> has the following parameters:<P>
<TT>on</TT><P>
If this parameter has a non-zero value, the State Threads library
randomizes the base addresses of stacks allocated for threads created
after this call.  Otherwise new threads' stacks are typically page
aligned.
<P>
<H5>Returns</H5>
The previous state of stack randomization (a value of <TT>0</TT> if it
was off and a non-zero value otherwise).
<P>
<H5>Description</H5>
Randomizing state threads' stack bases may improve cache performance on
some systems when large numbers of state threads all perform roughly the
same work, as when they all start from the same root function.  On many
modern systems the performance increase is negligible.  You should
compare your application's performance with this feature on and off to
see if you really need it.
<P>
When randomization is enabled, new stacks are allocated one page larger
to accomodate the randomization.
<P>
This call affects only threads created afterward.  It has no effect on
existing threads.
<P>
<HR>
<P>

<A NAME="priv">
<H2>Per-Thread Private Data</H2>
</A>
These functions allow to associate private data with each of the threads in
a process.
<P>
<DL>
<DD><A HREF=#key_create>st_key_create()</A></DD>
<DD><A HREF=#key_getlimit>st_key_getlimit()</A></DD>
<DD><A HREF=#thread_setspecific>st_thread_setspecific()</A></DD>
<DD><A HREF=#thread_getspecific>st_thread_getspecific()</A></DD>
</DL>
<P>
<HR>
<P>
<A NAME="key_create">
<H4>st_key_create()</H4>
</A>
Creates a key (non-negative integer) that can be used by all
threads in the process to get and set thread-specific data.
<P>
<H5>Syntax</H5>

<PRE>
#include &lt;st.h&gt;

int st_key_create(int *keyp, void (*destructor)(void *));
</PRE>
<P>
<H5>Parameters</H5>
<TT>st_key_create()</TT> has the following parameters:<P>
<TT>keyp</TT><P>
The newly created key is returned in the memory pointed to by this parameter.
The new key can be used with
<A HREF=#thread_setspecific>st_thread_setspecific()</A> and
<A HREF=#thread_getspecific>st_thread_getspecific()</A>.<P>
<TT>destructor</TT><P>
Specifies an optional destructor function for the private data associated
with the key. This function can be specified as <TT>NULL</TT>.
Upon thread exit (see <A HREF=#thread_exit>st_thread_exit()</A>), if a key
has a non-<TT>NULL</TT> <TT>destructor</TT> and has a non-<TT>NULL</TT> value
associated with that key, then the <TT>destructor</TT> function will be
called with the associated value.
<P>
<H5>Returns</H5>
Upon successful completion, a value of <TT>0</TT> is returned.
Otherwise, a value of <TT>-1</TT> is returned and <TT>errno</TT> is set
to indicate the error:<P>
<TABLE BORDER=0>
<TR><TD><TT>EAGAIN</TT></TD><TD>The limit on the total number of keys per
process has been exceeded (see <A HREF=#key_getlimit>st_key_getlimit()</A>).
</TD></TR>
</TABLE>
<P>
<H5>Description</H5>
If this function is successful, every thread in the same process is capable
of associating private data with the new key. After a new key is created, all
active threads have the value <TT>NULL</TT> associated with that key.
After a new thread is created, the value <TT>NULL</TT> is associated with
all keys for that thread.  If a non-<TT>NULL</TT> destructor function is
registered with a new key, it will be called at one of two times, as long as
the private data is not <TT>NULL</TT>:
<UL>
<LI>when replacement private data is set with
<A HREF=#thread_setspecific>st_thread_setspecific()</A></LI>
<LI>when a thread exits (see <A HREF=#thread_exit>st_thread_exit()</A>)</LI>
</UL>
<P>
The key maintains independent data values for each binding thread. A thread
can get access only to its own thread-specific data. There is no way to
deallocate a private data key once it is allocated.
<P>
<HR>
<P>
<A NAME="key_getlimit">
<H4>st_key_getlimit()</H4>
</A>
Returns the key limit.
<P>
<H5>Syntax</H5>

<PRE>
#include &lt;st.h&gt;

int st_key_getlimit(void);
</PRE>
<P>
<H5>Parameters</H5>
None.
<P>
<H5>Returns</H5>
The limit on the total number of keys per process.
<P>
<H5>Description</H5>
This function can be used to obtain the limit on the total number of keys
per process (see <A HREF=#key_create>st_key_create()</A>).
<P>
<HR>
<P>
<A NAME="thread_setspecific">
<H4>st_thread_setspecific()</H4>
</A>
Sets per-thread private data.
<P>
<H5>Syntax</H5>

<PRE>
#include &lt;st.h&gt;

int st_thread_setspecific(int key, void *value);
</PRE>
<P>
<H5>Parameters</H5>
<TT>st_thread_setspecific()</TT> has the following parameters:<P>
<TT>key</TT><P>
This parameter represents a key with which thread-specific data is associated.
<P>
<TT>value</TT><P>
The per-thread private data, or more likely, a pointer to the data which is
associated with <TT>key</TT>.
<P>
<H5>Returns</H5>
Upon successful completion, a value of <TT>0</TT> is returned.
Otherwise, a value of <TT>-1</TT> is returned and <TT>errno</TT> is set
to indicate the error:<P>
<TABLE BORDER=0>
<TR><TD><TT>EINVAL</TT></TD><TD>The specified <TT>key</TT> is invalid.</TD>
</TR>
</TABLE>
<P>
<H5>Description</H5>
This function associates a thread-specific <TT>value</TT> with <TT>key</TT>.
Different threads may bind different values to the same key.<P>
If the thread already has non-<TT>NULL</TT> private data associated with
<TT>key</TT>, and if the destructor function for that key is not
<TT>NULL</TT>, this destructor function will be called before setting the
new data value.
<P>
<HR>
<P>
<A NAME="thread_getspecific">
<H4>st_thread_getspecific()</H4>
</A>
Retrieves the per-thread private data for the current thread.
<P>
<H5>Syntax</H5>

<PRE>
#include &lt;st.h&gt;

void *st_thread_getspecific(int key);
</PRE>
<P>
<H5>Parameters</H5>
<TT>st_thread_getspecific()</TT> has the following parameters:<P>
<TT>key</TT><P>
This parameter represents a key with which thread-specific data is associated.
<P>
<H5>Returns</H5>
The thread-specific data associated with <TT>key</TT>. If no data is
associated with <TT>key</TT>, then <TT>NULL</TT> is returned.
<P>
<H5>Description</H5>
This function returns the calling thread's value that is bound to the
specified <TT>key</TT> (see
<A HREF=#thread_setspecific>st_thread_setspecific()</A>).
<P>
<HR>
<P>

<A NAME="sync">
<H2>Synchronization</H2>
</A>
<P>
These functions operate on <A HREF=#cond_t>condition variables</A>
and <A HREF=#mutex_t>mutual exclusion locks</A> (mutexes).<P>
Functions are provided to wait on a condition variable and to wake up
(signal) threads that are waiting on the condition variable.
<P>
<DL>
<DD><A HREF=#cond_new>st_cond_new()</A></DD>
<DD><A HREF=#cond_destroy>st_cond_destroy()</A></DD>
<DD><A HREF=#cond_wait>st_cond_wait()</A></DD>
<DD><A HREF=#cond_timedwait>st_cond_timedwait()</A></DD>
<DD><A HREF=#cond_signal>st_cond_signal()</A></DD>
<DD><A HREF=#cond_broadcast>st_cond_broadcast()</A></DD>
<P>
<DD><A HREF=#mutex_new>st_mutex_new()</A></DD>
<DD><A HREF=#mutex_destroy>st_mutex_destroy()</A></DD>
<DD><A HREF=#mutex_lock>st_mutex_lock()</A></DD>
<DD><A HREF=#mutex_trylock>st_mutex_trylock()</A></DD>
<DD><A HREF=#mutex_unlock>st_mutex_unlock()</A></DD>
</DL>
<P>
<HR>
<P>
<A NAME="cond_new">
<H4>st_cond_new()</H4>
</A>
Creates a new condition variable.
<P>
<H5>Syntax</H5>

<PRE>
#include &lt;st.h&gt;

st_cond_t st_cond_new(void);
</PRE>
<P>
<H5>Parameters</H5>
None.
<P>
<H5>Returns</H5>
Upon successful completion, a new condition variable identifier is returned.
Otherwise, <TT>NULL</TT> is returned and <TT>errno</TT> is set
to indicate the error.
<P>
<H5>Description</H5>
This function creates a new condition variable.
<P>
<HR>
<P>
<A NAME="cond_destroy">
<H4>st_cond_destroy()</H4>
</A>
Destroys a condition variable.
<P>
<H5>Syntax</H5>

<PRE>
#include &lt;st.h&gt;

int st_cond_destroy(st_cond_t cvar);
</PRE>
<P>
<H5>Parameters</H5>
<TT>st_cond_destroy()</TT> has the following parameters:<P>
<TT>cvar</TT><P>
An identifier of the condition variable object to be destroyed.
<P>
<H5>Returns</H5>
Upon successful completion, a value of <TT>0</TT> is returned.
Otherwise, a value of <TT>-1</TT> is returned and <TT>errno</TT> is set
to indicate the error:<P>
<TABLE BORDER=0>
<TR><TD><TT>EBUSY</TT></TD><TD>The condition variable is currently being
used by one or more threads.</TD>
</TR>
</TABLE>
<P>
<H5>Description</H5>
This function destroys a condition variable. The caller is responsible for
ensuring that the condition variable is no longer in use.
<P>
<HR>
<P>
<A NAME="cond_wait">
<H4>st_cond_wait()</H4>
</A>
Waits on a condition.
<P>
<H5>Syntax</H5>

<PRE>
#include &lt;st.h&gt;

int st_cond_wait(st_cond_t cvar);
</PRE>
<P>
<H5>Parameters</H5>
<TT>st_cond_wait()</TT> has the following parameters:<P>
<TT>cvar</TT><P>
The condition variable on which to wait.
<P>
<H5>Returns</H5>
Upon successful completion, a value of <TT>0</TT> is returned.
Otherwise, a value of <TT>-1</TT> is returned and <TT>errno</TT> is set
to indicate the error:<P>
<TABLE BORDER=0>
<TR><TD><TT>EINTR</TT></TD><TD>The current thread was interrupted by
<A HREF=#thread_interrupt>st_thread_interrupt()</A>.</TD></TR>
</TABLE>
<P>
<H5>Description</H5>
This function is used to block on a condition variable. A return from this
function does not guarantee that the condition or event for which the caller
was waiting actually occurred. It is the responsibility of the caller
to recheck the condition wait predicate before proceeding.<P>
<B>Note:</B> The State Threads library scheduling guarantees that the
condition cannot change between the checking and blocking, therefore there
is no need for mutex protection. You must not call any
<A HREF=#block>blocking functions</A> between the condition checking and
the <TT>st_cond_wait()</TT> call.
<P>
<HR>
<P>
<A NAME="cond_timedwait">
<H4>st_cond_timedwait()</H4>
</A>
Waits on a condition.
<P>
<H5>Syntax</H5>

<PRE>
#include &lt;st.h&gt;

int st_cond_timedwait(st_cond_t cvar, st_utime_t timeout);
</PRE>
<P>
<H5>Parameters</H5>
<TT>st_cond_timedwait()</TT> has the following parameters:<P>
<TT>cvar</TT><P>
The condition variable on which to wait.<P>
<TT>timeout</TT><P>
If the number of microseconds specified by this parameter passes before the
waiting thread is signalled, an error is returned. This parameter is a
variable of type <A HREF=#utime_t><B>st_utime_t</B></A>. Note that this
time value is a <I>time delta</I>; it is not an <I>absolute time</I>.
Also note that timeouts are measured <A HREF="notes.html#timeouts">since
the last context switch</A>.
<P>
<H5>Returns</H5>
Upon successful completion, a value of <TT>0</TT> is returned.
Otherwise, a value of <TT>-1</TT> is returned and <TT>errno</TT> is set
to indicate the error:<P>
<TABLE BORDER=0>
<TR><TD><TT>EINTR</TT></TD><TD>The current thread was interrupted by
<A HREF=#thread_interrupt>st_thread_interrupt()</A>.</TD></TR>
<TR><TD><TT>ETIME</TT></TD><TD>The timeout occurred before the thread was
awakened by <A HREF=#cond_signal>st_cond_signal()</A> or
<A HREF=#cond_broadcast>st_cond_broadcast()</A>.</TD></TR>
</TABLE>
<P>
<H5>Description</H5>
This function works the same way as <A HREF=#cond_wait>st_cond_wait()</A>,
except that an error is returned if the number of microseconds specified by
<TT>timeout</TT> passes before the waiting thread is signalled.
<P>
<HR>
<P>
<A NAME="cond_signal">
<H4>st_cond_signal()</H4>
</A>
Unblocks a thread waiting on a condition variable.
<P>
<H5>Syntax</H5>

<PRE>
#include &lt;st.h&gt;

int st_cond_signal(st_cond_t cvar);
</PRE>
<P>
<H5>Parameters</H5>
<TT>st_cond_signal()</TT> has the following parameters:<P>
<TT>cvar</TT><P>
The condition variable to signal.
<P>
<H5>Returns</H5>
Always zero.
<P>
<H5>Description</H5>
This function unblocks (signals) one of the threads that are blocked on
<TT>cvar</TT> at the time of the call.  If no thread is waiting on the
condition variable, the signal operation is a no-op.
<P>
<HR>
<P>
<A NAME="cond_broadcast">
<H4>st_cond_broadcast()</H4>
</A>
Unblocks all threads waiting on a condition variable.
<P>
<H5>Syntax</H5>

<PRE>
#include &lt;st.h&gt;

int st_cond_broadcast(st_cond_t cvar);
</PRE>
<P>
<H5>Parameters</H5>
<TT>st_cond_broadcast()</TT> has the following parameters:<P>
<TT>cvar</TT><P>
The condition variable to broadcast.
<P>
<H5>Returns</H5>
Always zero.
<P>
<H5>Description</H5>
This function unblocks all threads blocked on the specified condition
variable at the time of the call. If no threads are waiting, this operation
is a no-op.
<P>
<HR>
<P>

<A NAME="mutex_new">
<H4>st_mutex_new()</H4>
</A>
Creates a new mutual exclusion lock (mutex).
<P>
<H5>Syntax</H5>

<PRE>
#include &lt;st.h&gt;

st_mutex_t st_mutex_new(void);
</PRE>
<P>
<H5>Parameters</H5>
None.
<P>
<H5>Returns</H5>
Upon successful completion, a new mutex identifier is returned.
Otherwise, <TT>NULL</TT> is returned and <TT>errno</TT> is set to
indicate the error.
<P>
<H5>Description</H5>
This function creates a new opaque mutual exclusion lock (see
<A HREF=#mutex_t><B>st_mutex_t</B></A>).
<P>
<HR>
<P>
<A NAME="mutex_destroy">
<H4>st_mutex_destroy()</H4>
</A>
Destroys a specified mutex object.
<P>
<H5>Syntax</H5>

<PRE>
#include &lt;st.h&gt;

int st_mutex_destroy(st_mutex_t lock);
</PRE>
<P>
<H5>Parameters</H5>
<TT>st_mutex_destroy()</TT> has the following parameters:<P>
<TT>lock</TT><P>
An identifier of the mutex object to be destroyed.
<P>
<H5>Returns</H5>
Upon successful completion, a value of <TT>0</TT> is returned.
Otherwise, a value of <TT>-1</TT> is returned and <TT>errno</TT> is set
to indicate the error:<P>
<TABLE BORDER=0>
<TR><TD><TT>EBUSY</TT></TD><TD>The mutex is currently being used by
other threads.</TD>
</TR>
</TABLE>
<P>
<H5>Description</H5>
This function destroys a mutex. The caller is responsible for ensuring
that the mutex is no longer in use.
<P>
<HR>
<P>
<A NAME="mutex_lock">
<H4>st_mutex_lock()</H4>
</A>
Locks a specified mutex object.
<P>
<H5>Syntax</H5>

<PRE>
#include &lt;st.h&gt;

int st_mutex_lock(st_mutex_t lock);
</PRE>
<P>
<H5>Parameters</H5>
<TT>st_mutex_lock()</TT> has the following parameters:<P>
<TT>lock</TT><P>
An identifier of the mutex object to be locked.
<P>
<H5>Returns</H5>
Upon successful completion, a value of <TT>0</TT> is returned.
Otherwise, a value of <TT>-1</TT> is returned and <TT>errno</TT> is set
to indicate the error:<P>
<TABLE BORDER=0>
<TR><TD><TT>EDEADLK</TT></TD><TD>The current thread already owns the mutex.
</TD></TR>
<TR><TD><TT>EINTR</TT></TD><TD>The current thread was interrupted by
<A HREF=#thread_interrupt>st_thread_interrupt()</A>.</TD></TR>
</TABLE>
<P>
<H5>Description</H5>
A thread that calls this function will block until it can gain exclusive
ownership of a mutex, and retains ownership until it calls
<A HREF=#mutex_unlock>st_mutex_unlock()</A>.
<P>
<HR>
<P>
<A NAME="mutex_trylock">
<H4>st_mutex_trylock()</H4>
</A>
Attempts to acquire a mutex.
<P>
<H5>Syntax</H5>

<PRE>
#include &lt;st.h&gt;

int st_mutex_trylock(st_mutex_t lock);
</PRE>
<P>
<H5>Parameters</H5>
<TT>st_mutex_trylock()</TT> has the following parameters:<P>
<TT>lock</TT><P>
An identifier of the mutex object to be locked.
<P>
<H5>Returns</H5>
Upon successful completion, a value of <TT>0</TT> is returned.
Otherwise, a value of <TT>-1</TT> is returned and <TT>errno</TT> is set
to indicate the error:<P>
<TABLE BORDER=0>
<TR><TD><TT>EBUSY</TT></TD><TD>The mutex is currently held by another
thread.</TD></TR>
</TABLE>
<P>
<H5>Description</H5>
This function attempts to acquire a mutex. If the mutex object is locked
(by any thread, including the current thread), the call returns immediately
with an error.
<P>
<HR>
<P>
<A NAME="mutex_unlock">
<H4>st_mutex_unlock()</H4>
</A>
Releases a specified mutex object.
<P>
<H5>Syntax</H5>

<PRE>
#include &lt;st.h&gt;

int st_mutex_unlock(st_mutex_t lock);
</PRE>
<P>
<H5>Parameters</H5>
<TT>st_mutex_unlock()</TT> has the following parameters:<P>
<TT>lock</TT><P>
An identifier of the mutex object to be unlocked.
<P>
<H5>Returns</H5>
Upon successful completion, a value of <TT>0</TT> is returned.
Otherwise, a value of <TT>-1</TT> is returned and <TT>errno</TT> is set
to indicate the error:<P>
<TABLE BORDER=0>
<TR><TD><TT>EPERM</TT></TD><TD>The current thread does not own the mutex.
</TD></TR>
</TABLE>
<P>
<H5>Description</H5>
This function releases a specified mutex object previously acquired by
<A HREF=#mutex_lock>st_mutex_lock()</A> or
<A HREF=#mutex_trylock>st_mutex_trylock()</A>. Only the thread that locked
a mutex should unlock it.
<P>
<HR>
<P>

<A NAME="timing">
<H2>Timing</H2>
</A>
<P>
<DL>
<DD><A HREF=#utime>st_utime()</A></DD>
<DD><A HREF=#set_utime_function>st_set_utime_function()</A></DD>
<DD><A HREF=#timecache_set>st_timecache_set()</A></DD>
<DD><A HREF=#time>st_time()</A></DD>
</DL>
<P>
<HR>
<P>
<A NAME="utime">
<H4>st_utime()</H4>
</A>
Returns current high-resolution time.
<P>
<H5>Syntax</H5>

<PRE>
#include &lt;st.h&gt;

st_utime_t st_utime(void);
</PRE>
<P>
<H5>Parameters</H5>
None.
<P>
<H5>Returns</H5>
Current high-resolution time value of type
<A HREF=#utime_t><B>st_utime_t</B></A>.
<P>
<H5>Description</H5>
This function returns the current high-resolution time. Time is
expressed as microseconds since some arbitrary time in the past. It is
not correlated in any way to the time of day.  See also <A
HREF=#utime_t><B>st_utime_t</B></A> and <A
HREF="#time"><B>st_time()</B></A>.
<P>
<HR>
<P>
<A NAME="set_utime_function">
<H4>st_set_utime_function()</H4>
</A>
Set high-resolution time function.
<P>
<H5>Syntax</H5>

<PRE>
#include &lt;st.h&gt;

int st_set_utime_function(st_utime_t (*func)(void));
</PRE>
<P>
<H5>Parameters</H5>
<TT>st_set_utime_function()</TT> has the following parameters:<P>
<TT>func</TT><P>
This function will be called to get high-resolution time instead of the
default <A HREF=#utime>st_utime()</A> function. It must return
number of microseconds since some arbitrary time in the past.
<P>
<H5>Returns</H5>
Upon successful completion, a value of <TT>0</TT> is returned.
Otherwise, a value of <TT>-1</TT> is returned and <TT>errno</TT> is set
to <TT>EINVAL</TT> to indicate the error.
<P>
<H5>Description</H5>
This function may be called to replace the default implementation of the
<A HREF=#utime>st_utime()</A> function.  It must be called before the ST
library has been initialized (see <A HREF=#st_init>st_init()</A>).
The user-provided function <TT>func</TT> will be invoked whenever
<A HREF=#utime>st_utime()</A> is called to obtain current high-resolution time.
Replacing default implementation may be useful, for example, for taking
advantage of high performance CPU cycle counters.
<P>
<HR>
<P>
<A NAME="timecache_set">
<H4>st_timecache_set()</H4>
</A>
Turns the time caching on or off.
<P>
<H5>Syntax</H5>

<PRE>
#include &lt;st.h&gt;

int st_timecache_set(int on);
</PRE>
<P>
<H5>Parameters</H5>
<TT>st_timecache_set()</TT> has the following parameters:<P>
<TT>on</TT><P>
If this parameter has a non-zero value, the time caching is turned on
(enabled). Otherwise, the time caching is turned off (disabled).
By default time caching is disabled.
<P>
<H5>Returns</H5>
The previous state of time caching (a value of <TT>0</TT> if it was off and
a value of <TT>1</TT> otherwise).
<P>
<H5>Description</H5>
The State Threads library has the ability to "cache" the time value that is
reported by the <TT>time(2)</TT> system call. If the time caching is enabled
by calling this function with a non-zero argument, then the result value
of <TT>time(2)</TT> will be stored and updated at most once per second. The
cached time can be retrieved by <A HREF=#time>st_time()</A>.
By default time caching is disabled.
You may enable or disable time caching at any time but generally
you enable it once (if desired) during program initialization.<P>
<B>Note:</B> There are some pathological cases (e.g., very heavy loads during
application benchmarking) when a single thread runs for a long time without
giving up control and the cached time value is not updated properly. If you
<I>always</I> need "real-time" time values, don't enable the time caching.
<P>
<HR>
<P>
<A NAME="time">
<H4>st_time()</H4>
</A>
Returns the value of time in seconds since 00:00:00 UTC, January 1, 1970.
<P>
<H5>Syntax</H5>

<PRE>
#include &lt;st.h&gt;

time_t st_time(void);
</PRE>
<P>
<H5>Parameters</H5>
None.
<P>
<H5>Returns</H5>
The value of time in seconds since 00:00:00 UTC, January 1, 1970 as reported
by the <TT>time(2)</TT> system call.
<P>
<H5>Description</H5>
If the time caching was enabled by
<A HREF=#timecache_set>st_timecache_set()</A>, then this function returns
the cached result. Otherwise, it just calls <TT>time(2)</TT>.
<P>
<HR>
<P>

<A NAME="io">
<H2>I/O Functions</H2>
</A>
<P>
Most State Threads library I/O functions look like corresponding C library
functions with two exceptions:
<UL>
<LI>They operate on file descriptor objects of type
<A HREF=#netfd_t><B>st_netfd_t</B></A>.</LI>
<LI>They take an additional argument of type
<A HREF=#utime_t><B>st_utime_t</B></A> which represents an <I>inactivity
timeout</I>: if no I/O is possible during this amount of time, I/O functions
return an error code and set <TT>errno</TT> to <TT>ETIME</TT>.

The boundary values <tt>ST_UTIME_NO_WAIT</tt> (<TT>0</TT>) and
<tt>ST_UTIME_NO_TIMEOUT</tt> (<TT>-1</TT>) for this argument indicate
that the thread should wait no time (function returns immediately) or
wait forever (never time out), respectively.

Note that timeouts are measured <A HREF="notes.html#timeouts">since the
last context switch</A>.
</LI>
</UL>
<P>
<DL>
<DD><A HREF=#netfd_open>st_netfd_open()</A></DD>
<DD><A HREF=#netfd_open_socket>st_netfd_open_socket()</A></DD>
<DD><A HREF=#netfd_free>st_netfd_free()</A></DD>
<DD><A HREF=#netfd_close>st_netfd_close()</A></DD>
<DD><A HREF=#netfd_fileno>st_netfd_fileno()</A></DD>
<DD><A HREF=#netfd_setspecific>st_netfd_setspecific()</A></DD>
<DD><A HREF=#netfd_getspecific>st_netfd_getspecific()</A></DD>
<DD><A HREF=#netfd_serialize_accept>st_netfd_serialize_accept()</A></DD>
<DD><A HREF=#netfd_poll>st_netfd_poll()</A></DD>
<P>
<DD><A HREF=#accept>st_accept()</A></DD>
<DD><A HREF=#connect>st_connect()</A></DD>
<DD><A HREF=#read>st_read()</A></DD>
<DD><A HREF=#read_fully>st_read_fully()</A></DD>
<DD><A HREF=#read_resid>st_read_resid()</A></DD>
<DD><A HREF=#readv>st_readv()</A></DD>
<DD><A HREF=#readv_resid>st_read_resid()</A></DD>
<DD><A HREF=#write>st_write()</A></DD>
<DD><A HREF=#write_resid>st_write_resid()</A></DD>
<DD><A HREF=#writev>st_writev()</A></DD>
<DD><A HREF=#writev_resid>st_writev_resid()</A></DD>
<DD><A HREF=#recvfrom>st_recvfrom()</A></DD>
<DD><A HREF=#sendto>st_sendto()</A></DD>
<DD><A HREF=#recvmsg>st_recvmsg()</A></DD>
<DD><A HREF=#sendmsg>st_sendmsg()</A></DD>
<DD><A HREF=#open>st_open()</A></DD>
<DD><A HREF=#poll>st_poll()</A></DD>
</DL>
<P>
<HR>
<P>
<A NAME="netfd_open">
<H4>st_netfd_open()</H4>
</A>
Creates a new file descriptor object.
<P>
<H5>Syntax</H5>

<PRE>
#include &lt;st.h&gt;

st_netfd_t st_netfd_open(int osfd);
</PRE>
<P>
<H5>Parameters</H5>
<TT>st_netfd_open()</TT> has the following parameters:<P>
<TT>osfd</TT><P>

Any open OS file descriptor; can be obtained from calls to
functions including, but not restricted to, <TT>pipe(2), socket(3),
socketpair(3), fcntl(2), dup(2),</TT> etc.


<P>
<H5>Returns</H5>
Upon successful completion, a new file descriptor object identifier is
returned. Otherwise, <TT>NULL</TT> is returned and <TT>errno</TT> is set
to indicate the error.
<P>
<H5>Description</H5>
This function creates a new file descriptor object of type
<A HREF=#netfd_t><B>st_netfd_t</B></A>.<P>

<B>Note:</B> Among other things, this function sets a non-blocking
flag on the underlying OS file descriptor. You should not modify this
flag directly. Also, once an <A HREF=#netfd_t><B>st_netfd_t</B></A>
has been created with a given file descriptor, you should avoid
passing that descriptor to normal I/O or stdio functions. Since the
O_NONBLOCK flag is shared across <TT>dup(2)</TT>, this applies to
<TT>dup()</TT>'ed file descriptors as well - for instance, if you pass
standard output or standard input to <TT>st_netfd_open()</TT>, then
you should use <A HREF=#write>st_write()</A> instead of <TT>write</TT>
or <TT>fprintf</TT> when writing to standard error as well - since all
three descriptors could point to the same terminal. If necessary, you
can still use <TT>write</TT> directly if you remember to check
<TT>errno</TT> for <TT>EAGAIN</TT>, but <TT>fprintf</TT> and other
stdio functions should be avoided completely because, at least on
Linux, the stdio library cannot be made to work reliably with
non-blocking files. (This only applies to file descriptors which are
passed to <TT>st_netfd_open()</TT> or <A
HREF=#netfd_open_socket>st_netfd_open_socket()</A>, or which are
related to such descriptors through <TT>dup()</TT>; other file
descriptors are untouched by State Threads.)
<P>
<HR>
<P>
<A NAME="netfd_open_socket">
<H4>st_netfd_open_socket()</H4>
</A>
Creates a new file descriptor object from a socket.
<P>
<H5>Syntax</H5>

<PRE>
#include &lt;st.h&gt;

st_netfd_t st_netfd_open_socket(int osfd);
</PRE>
<P>
<H5>Parameters</H5>
<TT>st_netfd_open_socket()</TT> has the following parameters:<P>
<TT>osfd</TT><P>
An open OS file descriptor which is a socket initially obtained from a<TT>
socket(3) or socketpair(3)</TT> call.
<P>
<H5>Returns</H5>
Upon successful completion, a new file descriptor object identifier is
returned. Otherwise, <TT>NULL</TT> is returned and <TT>errno</TT> is set
to indicate the error.
<P>
<H5>Description</H5>
This function creates a new file descriptor object of type
<A HREF=#netfd_t><B>st_netfd_t</B></A> which represents an open end
point of network communication.<P>
Unlike the <A HREF=#netfd_open>st_netfd_open()</A> function which may be used
on OS file descriptors of any origin, <TT>st_netfd_open_socket()</TT> must
be used only on sockets. It is slightly more efficient than
<A HREF=#netfd_open>st_netfd_open()</A>.<P>
<B>Note:</B> Among other things, this function sets a non-blocking flag
on the underlying OS socket. You should not modify this flag directly.
See <A HREF=#netfd_open>st_netfd_open()</A>.
<P>
<HR>
<P>
<A NAME="netfd_free">
<H4>st_netfd_free()</H4>
</A>
Frees a file descriptor object without closing the underlying OS file
descriptor.
<P>
<H5>Syntax</H5>

<PRE>
#include &lt;st.h&gt;

void st_netfd_free(st_netfd_t fd);
</PRE>
<P>
<H5>Parameters</H5>
<TT>st_netfd_free()</TT> has the following parameters:<P>
<TT>fd</TT><P>
A file descriptor object identifier (see
<A HREF=#netfd_t><B>st_netfd_t</B></A>).
<P>
<H5>Returns</H5>
Nothing.
<P>
<H5>Description</H5>
This function frees the memory and other resources identified by the
<TT>fd</TT> parameter without closing the underlying OS file descriptor.
Any non-<TT>NULL</TT> descriptor-specific data is destroyed by invoking
the specified destructor function (see <A
HREF=#netfd_setspecific>st_netfd_setspecific()</A>).<P> A thread should
not free file descriptor objects that are in use by other threads
because it may lead to unpredictable results (e.g., a freed file
descriptor may be reused without other threads knowing that).
<P>
<HR>
<P>
<A NAME="netfd_close">
<H4>st_netfd_close()</H4>
</A>
Closes a file descriptor.
<P>
<H5>Syntax</H5>

<PRE>
#include &lt;st.h&gt;

int st_netfd_close(st_netfd_t fd);
</PRE>
<P>
<H5>Parameters</H5>
<TT>st_netfd_close()</TT> has the following parameters:<P>
<TT>fd</TT><P>
A file descriptor object identifier (see
<A HREF=#netfd_t><B>st_netfd_t</B></A>).
<P>
<H5>Returns</H5>
Upon successful completion, a value of <TT>0</TT> is returned.
Otherwise, a value of <TT>-1</TT> is returned and <TT>errno</TT> is set
to indicate the error.
<P>
<H5>Description</H5>
This function closes the underlying OS file descriptor, frees the memory and
other resources identified by the <TT>fd</TT> parameter. Any non-<TT>NULL</TT>
descriptor-specific data is destroyed by invoking the specified destructor
function (see <A HREF=#netfd_setspecific>st_netfd_setspecific()</A>).<P>
A thread should not close file descriptor objects that are in use by other
threads because it may lead to unpredictable results (e.g., a closed
file descriptor may be reused without other threads knowing that).
<P>
<HR>
<P>
<A NAME="netfd_fileno">
<H4>st_netfd_fileno()</H4>
</A>
Returns an underlying OS file descriptor.
<P>
<H5>Syntax</H5>

<PRE>
#include &lt;st.h&gt;

int st_netfd_fileno(st_netfd_t fd);
</PRE>
<P>
<H5>Parameters</H5>
<TT>st_netfd_fileno()</TT> has the following parameters:<P>
<TT>fd</TT><P>
A file descriptor object identifier (see
<A HREF=#netfd_t><B>st_netfd_t</B></A>).
<P>
<H5>Returns</H5>
An underlying OS file descriptor.
<P>
<H5>Description</H5>
This function returns the integer OS file descriptor associated with the named
file descriptor object.
<P>
<HR>
<P>
<A NAME="netfd_setspecific">
<H4>st_netfd_setspecific()</H4>
</A>
Sets per-descriptor private data.
<P>
<H5>Syntax</H5>

<PRE>
#include &lt;st.h&gt;

void st_netfd_setspecific(st_netfd_t fd, void *value,
                          void (*destructor)(void *));
</PRE>
<P>
<H5>Parameters</H5>
<TT>st_netfd_setspecific()</TT> has the following parameters:<P>
<TT>fd</TT><P>
A valid file descriptor object identifier (see
<A HREF=#netfd_t><B>st_netfd_t</B></A>).
<P>
<TT>value</TT><P>
The per-descriptor private data, or more likely, a pointer to the data which
is being associated with the named file descriptor object.
<P>
<TT>destructor</TT><P>
Specifies an optional destructor function for the private data associated
with <TT>fd</TT>. This function can be specified as <TT>NULL</TT>.
If <TT>value</TT> is not <TT>NULL</TT>, then this destructor function will
be called with <TT>value</TT> as an argument upon freeing the file descriptor
object (see <A HREF=#netfd_free>st_netfd_free()</A> and
<A HREF=#netfd_close>st_netfd_close()</A>).
<P>
<H5>Returns</H5>
Nothing.
<P>
<H5>Description</H5>
This function allows to associate any data with the specified file
descriptor object (network connection). If a non-<TT>NULL</TT> destructor
function is registered, it will be called at one of two times, as long as
the associated data is not <TT>NULL</TT>:
<UL>
<LI>when private data is replaced by calling
<TT>st_netfd_setspecific()</TT> again
<LI>upon freeing the file descriptor object (see
<A HREF=#netfd_free>st_netfd_free()</A> and
<A HREF=#netfd_close>st_netfd_close()</A>)
</UL>
<P>
<HR>
<P>
<A NAME="netfd_getspecific">
<H4>st_netfd_getspecific()</H4>
</A>
Retrieves the per-descriptor private data.
<P>
<H5>Syntax</H5>

<PRE>
#include &lt;st.h&gt;

void *st_netfd_getspecific(st_netfd_t fd);
</PRE>
<P>
<H5>Parameters</H5>
<TT>st_netfd_getspecific()</TT> has the following parameters:<P>
<TT>fd</TT><P>
A valid file descriptor object identifier (see
<A HREF=#netfd_t><B>st_netfd_t</B></A>).
<P>
<H5>Returns</H5>
The data associated with the named file descriptor object. If no data is
associated with <TT>fd</TT>, then <TT>NULL</TT> is returned. 
<P>
<H5>Description</H5>
This function allows to retrieve the data that was associated with the
specified file descriptor object (see
<A HREF=#netfd_setspecific>st_netfd_setspecific()</A>).
<P>
<HR>
<P>
<A NAME="netfd_serialize_accept">
<H4>st_netfd_serialize_accept()</H4>
</A>
Serializes all subsequent <TT>accept(3)</TT> calls on a specified file
descriptor object.
<P>
<H5>Syntax</H5>

<PRE>
#include &lt;st.h&gt;

int st_netfd_serialize_accept(st_netfd_t fd);
</PRE>
<P>
<H5>Parameters</H5>
<TT>st_netfd_serialize_accept()</TT> has the following parameters:<P>
<TT>fd</TT><P>
A file descriptor object identifier (see
<A HREF=#netfd_t><B>st_netfd_t</B></A>) which has been successfully created
from a valid listening socket by <A HREF=#netfd_open>st_netfd_open()</A> or
<A HREF=#netfd_open_socket>st_netfd_open_socket()</A>.
<P>
<H5>Returns</H5>
Upon successful completion, a value of <TT>0</TT> is returned.
Otherwise, a value of <TT>-1</TT> is returned and <TT>errno</TT> is set
to indicate the error.
<P>
<H5>Description</H5>
On some platforms (e.g., Solaris 2.5 and possibly other SVR4 implementations)
<TT>accept(3)</TT> calls from different processes on
the same listening socket (see <TT>bind(3)</TT>, <TT>listen(3)</TT>) must be
serialized. This function causes all subsequent <TT>accept(3)</TT> calls
made by <A HREF=#accept>st_accept()</A> on the specified file descriptor
object to be serialized.
<P>
<TT>st_netfd_serialize_accept()</TT> must be called <I>before</I>
creating multiple server processes via <TT>fork(2)</TT>. If the application
does not create multiple processes to accept network connections on
the same listening socket, there is no need to call this function.
<P>
Deciding whether or not to serialize accepts is tricky.  On some
platforms (IRIX, Linux) it's not needed at all and
<TT>st_netfd_serialize_accept()</TT> is a no-op.  On other platforms
it depends on the version of the OS (Solaris 2.6 doesn't need it but
earlier versions do).  Serializing accepts does incur a slight
performance penalty so you want to enable it only if necessary.  Read
your system's manual pages for <tt>accept(2)</tt> and <tt>select(2)</tt>
to see if accept serialization is necessary on your system.
<P>
<TT>st_netfd_serialize_accept()</TT> allocates resources that are
freed upon freeing of the specified file descriptor object (see
<A HREF=#netfd_free>st_netfd_free()</A> and
<A HREF=#netfd_close>st_netfd_close()</A>).
<P>
<HR>
<P>
<A NAME="netfd_poll">
<H4>st_netfd_poll()</H4>
</A>
Waits for I/O on a single file descriptor object.
<P>
<H5>Syntax</H5>

<PRE>
#include &lt;st.h&gt;

int st_netfd_poll(st_netfd_t fd, int how, st_utime_t timeout);
</PRE>
<P>
<H5>Parameters</H5>
<TT>st_netfd_poll()</TT> has the following parameters:<P>
<TT>fd</TT><P>
A file descriptor object identifier (see
<A HREF=#netfd_t><B>st_netfd_t</B></A>).
<P>
<TT>how</TT><P>
Specifies I/O events of interest. This parameter can be constructed by
OR-ing any combination of the following event flags which are defined
in the <TT>poll.h</TT> header file:<P>
<TABLE BORDER=0>
<TR><TD><TT>POLLIN</TT></TD><TD><TT>fd</TT> is readable.</TD></TR>
<TR><TD><TT>POLLOUT</TT></TD><TD><TT>fd</TT> is is writable.</TD></TR>
<TR><TD><TT>POLLPRI</TT></TD><TD><TT>fd</TT> has an exception condition.</TD>
</TR>
</TABLE>
<P>
<TT>timeout</TT><P>
Amount of time in microseconds the call will block waiting for I/O
to become ready. This parameter is a variable of type
<A HREF=#utime_t><B>st_utime_t</B></A>. If this time expires without any
I/O becoming ready, <TT>st_netfd_poll()</TT> returns an error and sets
<TT>errno</TT> to <TT>ETIME</TT>.
Note that timeouts are measured <A HREF="notes.html#timeouts">since the
last context switch</A>.
<P>
<H5>Returns</H5>
If the named file descriptor object is ready for I/O within the specified
amount of time, a value of <TT>0</TT> is returned. Otherwise, a value
of <TT>-1</TT> is returned and <TT>errno</TT> is set to indicate the error:
<P>
<TABLE BORDER=0>
<TR><TD><TT>EBADF</TT></TD><TD>The underlying OS file descriptor is invalid.
</TD></TR>
<TR><TD><TT>EINTR</TT></TD><TD>The current thread was interrupted by
<A HREF=#thread_interrupt>st_thread_interrupt()</A>.</TD></TR>
<TR><TD><TT>ETIME</TT></TD><TD>The timeout occurred without any I/O
becoming ready.</TD></TR>
</TABLE>
<P>
<H5>Description</H5>
This function returns as soon as I/O is ready on the named file
descriptor object or the specified amount of time expires. The
<TT>how</TT> parameter should be set to the I/O events (readable,
writable, exception, or some combination) that the caller is interested
in. If the value of <TT>timeout</TT> is <tt>ST_UTIME_NO_TIMEOUT</tt>
(<TT>-1</TT>), this function blocks until a requested I/O event occurs
or until the call is interrupted by <A
HREF=#thread_interrupt>st_thread_interrupt()</A>.<p>
Despite having an interface like <tt>poll(2)</tt>, this function uses
the same event notification mechanism as the rest of the library.  For
instance if an alternative event nofication mechanism was set using <a
href=#set_eventsys>st_set_eventsys()</a>, this function uses that
mechanism to check for events.<p>
<b>Note: </b> if <TT>kqueue(2)</TT> is used as an alternative event
notification mechanism (see <A
HREF=#set_eventsys>st_set_eventsys()</A>), the <TT>POLLPRI</TT>
event flag is not supported and <TT>st_netfd_poll()</TT> will return an error
if it's set (<TT>errno</TT> will be set to <TT>EINVAL</TT>).
<P>
<HR>
<P>
<A NAME="accept">
<H4>st_accept()</H4>
</A>
Accepts a connection on a specified file descriptor object.
<P>
<H5>Syntax</H5>

<PRE>
#include &lt;st.h&gt;

st_netfd_t st_accept(st_netfd_t fd, struct sockaddr *addr, int *addrlen,
                     st_utime_t timeout);
</PRE>
<P>
<H5>Parameters</H5>
<TT>st_accept()</TT> has the following parameters:<P>
<TT>fd</TT><P>
A file descriptor object identifier (see
<A HREF=#netfd_t><B>st_netfd_t</B></A>) representing the rendezvous socket
on which the caller is willing to accept new connections. This object has been
created from a valid listening socket by
<A HREF=#netfd_open>st_netfd_open()</A> or
<A HREF=#netfd_open_socket>st_netfd_open_socket()</A>.<P>
<TT>addr</TT><P>
If this value is non-zero, it is a result parameter that is filled
in with the address of the connecting entity, as known to the communications
layer (see <TT>accept(3)</TT>).<P>
<TT>addrlen</TT><P>
This parameter should initially contain the amount of space pointed to by
<TT>addr</TT>; on return it will contain the actual length (in bytes) of the
address returned (see <TT>accept(3)</TT>).<P>
<TT>timeout</TT><P>
A value of type <A HREF=#utime_t><B>st_utime_t</B></A> specifying the time
limit in microseconds for completion of the accept operation.
Note that timeouts are measured <A HREF="notes.html#timeouts">since the
last context switch</A>.
<P>
<H5>Returns</H5>
Upon successful completion, a new file descriptor object identifier
representing the newly accepted connection is returned. Otherwise,
<TT>NULL</TT> is returned and <TT>errno</TT> is set to indicate the error.
Possible <TT>errno</TT> values are the same as set by the <TT>accept(3)</TT>
call with two exceptions:<P>
<TABLE BORDER=0>
<TR><TD><TT>EINTR</TT></TD><TD>The current thread was interrupted by
<A HREF=#thread_interrupt>st_thread_interrupt()</A>.</TD></TR>
<TR><TD><TT>ETIME</TT></TD><TD>The timeout occurred and no pending
connection was accepted.</TD></TR>
</TABLE>
<P>
<H5>Description</H5>
This function accepts the first connection from the queue of pending
connections and creates a new file descriptor object for the newly
accepted connection. The rendezvous socket can still be used to accept
more connections.<P>
<TT>st_accept()</TT> blocks the calling thread until either a new connection
is successfully accepted or an error occurs. If no pending connection can
be accepted before the time limit, this function returns <TT>NULL</TT>
and sets <TT>errno</TT> to <TT>ETIME</TT>.
<P>
<HR>
<P>
<A NAME="connect">
<H4>st_connect()</H4>
</A>
Initiates a connection on a specified file descriptor object.
<P>
<H5>Syntax</H5>

<PRE>
#include &lt;st.h&gt;

int st_connect(st_netfd_t fd, struct sockaddr *addr, int addrlen,
               st_utime_t timeout);
</PRE>
<P>
<H5>Parameters</H5>
<TT>st_connect()</TT> has the following parameters:<P>
<TT>fd</TT><P>
A file descriptor object identifier (see
<A HREF=#netfd_t><B>st_netfd_t</B></A>) representing a socket.<P>
<TT>addr</TT><P>
A pointer to the address of the peer to which the socket is to be connected.
<P>
<TT>addrlen</TT><P>
This parameter specifies the amount of space pointed to by <TT>addr</TT>.
<P>
<TT>timeout</TT><P>
A value of type <A HREF=#utime_t><B>st_utime_t</B></A> specifying the time
limit in microseconds for completion of the connect operation.
Note that timeouts are measured <A HREF="notes.html#timeouts">since the
last context switch</A>.
<P>
<H5>Returns</H5>
Upon successful completion, a value of <TT>0</TT> is returned.
Otherwise, a value of <TT>-1</TT> is returned and <TT>errno</TT> is set
to indicate the error. Possible <TT>errno</TT> values are the same as set
by the <TT>connect(3)</TT> call with two exceptions:<P>
<TABLE BORDER=0>
<TR><TD><TT>EINTR</TT></TD><TD>The current thread was interrupted by
<A HREF=#thread_interrupt>st_thread_interrupt()</A>.</TD></TR>
<TR><TD><TT>ETIME</TT></TD><TD>The timeout occurred and connection setup
was not completed.</TD></TR>
</TABLE> 
<P>
<H5>Description</H5>
This function is usually invoked on a file descriptor object representing
a TCP socket. Upon completion it establishes a TCP connection to the peer.
If the underlying OS socket is not bound, it will be bound to an arbitrary
local address (see <TT>connect(3)</TT>).<P>
<TT>st_connect()</TT> blocks the calling thread until either the connection
is successfully established or an error occurs. If the connection setup
cannot complete before the specified time limit, this function fails with
<TT>errno</TT> set to <TT>ETIME</TT>.
<P>
<HR>
<P>
<A NAME="read">
<H4>st_read()</H4>
</A>
Reads data from a specified file descriptor object.
<P>
<H5>Syntax</H5>

<PRE>
#include &lt;st.h&gt;

ssize_t st_read(st_netfd_t fd, void *buf, size_t nbyte, st_utime_t timeout);
</PRE>
<P>
<H5>Parameters</H5>
<TT>st_read()</TT> has the following parameters:<P>
<TT>fd</TT><P>
A file descriptor object identifier (see
<A HREF=#netfd_t><B>st_netfd_t</B></A>).<P>
<TT>buf</TT><P>
A pointer to a buffer to hold the data read in. On output the buffer
contains the data.<P>
<TT>nbyte</TT><P>
The size of <TT>buf</TT> in bytes.<P>
<TT>timeout</TT><P>
A value of type <A HREF=#utime_t><B>st_utime_t</B></A> specifying the time
limit in microseconds for completion of the read operation.
Note that timeouts are measured <A HREF="notes.html#timeouts">since the
last context switch</A>.
<P>
<H5>Returns</H5>
On success a non-negative integer indicating the number of bytes actually
read is returned (a value of <TT>0</TT> means the network connection is
closed or end of file is reached). Otherwise, a value of <TT>-1</TT> is
returned and <TT>errno</TT> is set to indicate the error.
Possible <TT>errno</TT> values are the same as set by the <TT>read(2)</TT>
call with two exceptions:<P>
<TABLE BORDER=0>
<TR><TD><TT>EINTR</TT></TD><TD>The current thread was interrupted by
<A HREF=#thread_interrupt>st_thread_interrupt()</A>.</TD></TR>
<TR><TD><TT>ETIME</TT></TD><TD>The timeout occurred and no data was read.
</TD></TR>
</TABLE>
<P>
<H5>Description</H5>
This function blocks the calling thread until it encounters an end-of-stream
indication, some positive number of bytes (but no more than <TT>nbyte</TT>
bytes) are read in, a timeout occurs, or an error occurs.
<P>
<HR>
<P>
<A NAME="read_fully">
<H4>st_read_fully()</H4>
</A>
Reads the specified amount of data in full from a file descriptor object.
<P>
<H5>Syntax</H5>

<PRE>
#include &lt;st.h&gt;

ssize_t st_read_fully(st_netfd_t fd, void *buf, size_t nbyte,
                      st_utime_t timeout);
</PRE>
<P>
<H5>Parameters</H5>
<TT>st_read_fully()</TT> has the following parameters:<P>
<TT>fd</TT><P>
A file descriptor object identifier (see
<A HREF=#netfd_t><B>st_netfd_t</B></A>).<P>
<TT>buf</TT><P>
A pointer to a buffer to hold the data read in. On output the buffer
contains the data.<P>
<TT>nbyte</TT><P>
The amount of data to be read in full (in bytes). It must not exceed the
size of <TT>buf</TT>.<P>
<TT>timeout</TT><P>
A value of type <A HREF=#utime_t><B>st_utime_t</B></A> specifying the
inactivity timeout (in microseconds).
Note that timeouts are measured <A HREF="notes.html#timeouts">since the
last context switch</A>.
<P>
<H5>Returns</H5>
On success a non-negative integer indicating the number of bytes actually
read is returned (a value less than <TT>nbyte</TT> means the network
connection is closed or end of file is reached). Otherwise, a value of
<TT>-1</TT> is returned and <TT>errno</TT> is set to indicate the error.
Possible <TT>errno</TT> values are the same as set by the <TT>read(2)</TT>
call with two exceptions:<P>
<TABLE BORDER=0>
<TR><TD><TT>EINTR</TT></TD><TD>The current thread was interrupted by
<A HREF=#thread_interrupt>st_thread_interrupt()</A>.</TD></TR>
<TR><TD><TT>ETIME</TT></TD><TD>The timeout occurred.
</TD></TR>
</TABLE>
<P>
<H5>Description</H5>
This function blocks the calling thread until the specified amount of data
is read in full, it encounters an end-of-stream indication, a timeout occurs,
or an error occurs.
<P>
<HR>
<P>
<A NAME="read_resid">
<H4>st_read_resid()</H4>
</A>
Reads the specified amount of data in full from a file descriptor object.
<P>
<H5>Syntax</H5>

<PRE>
#include &lt;st.h&gt;

int st_read_resid(st_netfd_t fd, void *buf, size_t *resid,
		  st_utime_t timeout);
</PRE>
<P>
<H5>Parameters</H5>
<TT>st_read_resid()</TT> has the following parameters:<P>
<TT>fd</TT><P>
A file descriptor object identifier (see
<A HREF=#netfd_t><B>st_netfd_t</B></A>).<P>
<TT>buf</TT><P>
A pointer to a buffer to hold the data read in. On output the buffer
contains the data.<P>
<TT>resid</TT><P>
A pointer to a number of bytes.
On entry, the amount of data to be read in full.
It must not exceed the size of <TT>buf</TT>.
On return, the amount of data remaining to be read.
(A non-zero returned value means some but not all of the data was read.)<P>
<TT>timeout</TT><P>
A value of type <A HREF=#utime_t><B>st_utime_t</B></A> specifying the
inactivity timeout (in microseconds).
Note that timeouts are measured <A HREF="notes.html#timeouts">since the
last context switch</A>.
<P>
<H5>Returns</H5>
On success, zero is returned.  <TT>*resid</TT> may be zero, indicating
a complete read, or non-zero, indicating the network
connection is closed or end of file is reached.
<P>
Otherwise, a value of <TT>-1</TT> is returned, <TT>*resid</TT> is non-zero,
and <TT>errno</TT> is set to indicate the error.
Possible <TT>errno</TT> values are the same as set by the <TT>read(2)</TT>
call with two exceptions:<P>
<TABLE BORDER=0>
<TR><TD><TT>EINTR</TT></TD><TD>The current thread was interrupted by
<A HREF=#thread_interrupt>st_thread_interrupt()</A>.</TD></TR>
<TR><TD><TT>ETIME</TT></TD><TD>The timeout occurred.
</TD></TR>
</TABLE>
<P>
<H5>Description</H5>
This function blocks the calling thread until the specified amount of data
is read in full, it encounters an end-of-stream indication, a timeout occurs,
or an error occurs.  It differs from <TT>st_read_fully()</TT> only in that
it allows the caller to know how many bytes were transferred before an error
occurred.
<P>
<HR>
<P>
<A NAME="readv">
<H4>st_readv()</H4>
</A>
Reads data from a specified file descriptor object into multiple buffers.
<P>
<H5>Syntax</H5>

<PRE>
#include &lt;st.h&gt;

ssize_t st_readv(st_netfd_t fd, const struct iovec *iov, int iov_size,
		 st_utime_t timeout);
</PRE>
<P>
<H5>Parameters</H5>
<TT>st_readv()</TT> has the following parameters:<P>
<TT>fd</TT><P>
A file descriptor object identifier (see
<A HREF=#netfd_t><B>st_netfd_t</B></A>).<P>
<TT>iov</TT><P>
An array of <tt>iovec</tt> structures that identify the buffers for holding
the data read in.
On return the buffers contain the data.<P>
<TT>iov_size</TT><P>
The number of <tt>iovec</tt> structures in the <tt>iov</tt> array.<P>
<TT>timeout</TT><P>
A value of type <A HREF=#utime_t><B>st_utime_t</B></A> specifying the time
limit in microseconds for completion of the read operation.
Note that timeouts are measured <A HREF="notes.html#timeouts">since the
last context switch</A>.
<P>
<H5>Returns</H5>
On success a non-negative integer indicating the number of bytes actually
read is returned (a value of <TT>0</TT> means the network connection is
closed or end of file is reached). Otherwise, a value of <TT>-1</TT> is
returned and <TT>errno</TT> is set to indicate the error.
Possible <TT>errno</TT> values are the same as set by the <TT>readv(2)</TT>
call with two exceptions:<P>
<TABLE BORDER=0>
<TR><TD><TT>EINTR</TT></TD><TD>The current thread was interrupted by
<A HREF=#thread_interrupt>st_thread_interrupt()</A>.</TD></TR>
<TR><TD><TT>ETIME</TT></TD><TD>The timeout occurred and no data was read.
</TD></TR>
</TABLE>
<P>
<H5>Description</H5>
This function blocks the calling thread until it encounters an end-of-stream
indication, some positive number of bytes (but no more than fit in the buffers)
are read in, a timeout occurs, or an error occurs.
<P>
<HR>
<P>
<A NAME="readv_resid">
<H4>st_readv_resid()</H4>
</A>
Reads the specified amount of data in full from a file descriptor object
into multiple buffers.
<P>
<H5>Syntax</H5>

<PRE>
#include &lt;st.h&gt;

int st_readv_resid(st_netfd_t fd, struct iovec **iov, int *iov_size,
		   st_utime_t timeout);
</PRE>
<P>
<H5>Parameters</H5>
<TT>st_readv_resid()</TT> has the following parameters:<P>
<TT>fd</TT><P>
A file descriptor object identifier (see
<A HREF=#netfd_t><B>st_netfd_t</B></A>).<P>
<TT>iov</TT><P>
A pointer to an array of <tt>iovec</tt> structures.
On entry, the iovecs identify the buffers for holding the data read in.
On return, the incomplete iovecs.
This function modifies both the pointer and the array to which it points.<P>
<TT>iov_size</TT><P>
A pointer to a number of iovec structures.
On entry, the number of iovec structures pointed to by <tt>*iov</tt>.
On return, the number of incomplete or unused iovec structures.
(A non-zero returned value means some but not all of the data was read.)<P>
<TT>timeout</TT><P>
A value of type <A HREF=#utime_t><B>st_utime_t</B></A> specifying the
inactivity timeout (in microseconds).
Note that timeouts are measured <A HREF="notes.html#timeouts">since the
last context switch</A>.
<P>
<H5>Returns</H5>
On success, zero is returned. <TT>*iov_size</TT> may be zero, indicating
a complete read, or non-zero, indicating the network connection is
closed or end of file is reached.  <tt>*iov</tt> points to the first
iovec after the end of the original array on a complete read, or to the
first incomplete iovec on an incomplete read.
<P>
Otherwise, a value of <TT>-1</TT> is returned, <TT>*iov_size</TT> is non-zero,
and <TT>errno</TT> is set to indicate the error.  <tt>*iov</tt> points to the
first unused iovec.
Possible <TT>errno</TT> values are the same as set by the <TT>readv(2)</TT>
call with two exceptions:<P>
<TABLE BORDER=0>
<TR><TD><TT>EINTR</TT></TD><TD>The current thread was interrupted by
<A HREF=#thread_interrupt>st_thread_interrupt()</A>.</TD></TR>
<TR><TD><TT>ETIME</TT></TD><TD>The timeout occurred.
</TD></TR>
</TABLE>
<P>All of the iovecs before <tt>*iov</tt> are modified such that
<tt>iov_base</tt> points to the end of the original buffer and
<tt>iov_len</tt> is zero.
<P>
<H5>Description</H5>
This function blocks the calling thread until the specified amount of data
is read in full, it encounters an end-of-stream indication, a timeout occurs,
or an error occurs.  Like <tt>st_read_resid()</tt> it blocks the thread until
<i>all</i> of the requested data is read or an error occurs.  Use
<tt>st_readv()</tt> to read <i>up to</i> the requested amount of data.
<P>
<HR>
<P>
<A NAME="write">
<H4>st_write()</H4>
</A>
Writes a buffer of data to a specified file descriptor object.
<P>
<H5>Syntax</H5>

<PRE>
#include &lt;st.h&gt;

ssize_t st_write(st_netfd_t fd, const void *buf, size_t nbyte,
                 st_utime_t timeout);
</PRE>
<P>
<H5>Parameters</H5>
<TT>st_write()</TT> has the following parameters:<P>
<TT>fd</TT><P>
A file descriptor object identifier (see
<A HREF=#netfd_t><B>st_netfd_t</B></A>).<P>
<TT>buf</TT><P>
A pointer to the buffer holding the data to be written.<P>
<TT>nbyte</TT><P>
The amount of data in bytes to be written from the buffer.<P>
<TT>timeout</TT><P>
A value of type <A HREF=#utime_t><B>st_utime_t</B></A> specifying the
inactivity timeout (in microseconds).
Note that timeouts are measured <A HREF="notes.html#timeouts">since the
last context switch</A>.
<P>
<H5>Returns</H5>
On success a non-negative integer equal to <TT>nbyte</TT> is returned.
Otherwise, a value of <TT>-1</TT> is returned and <TT>errno</TT> is set
to indicate the error. Possible <TT>errno</TT> values are the same as set
by the <TT>write(2)</TT> call with two exceptions:<P>
<TABLE BORDER=0>
<TR><TD><TT>EINTR</TT></TD><TD>The current thread was interrupted by
<A HREF=#thread_interrupt>st_thread_interrupt()</A>.</TD></TR>
<TR><TD><TT>ETIME</TT></TD><TD>The timeout occurred.
</TD></TR>
</TABLE> 
<P>
<H5>Description</H5>
This function blocks the calling thread until <I>all</I> the data is written,
a timeout occurs, or the write operation fails. The return value is equal to
either <TT>nbyte</TT> (on success) or <TT>-1</TT> (on failure). Note that if
<TT>st_write()</TT> returns <TT>-1</TT>, some data (less than <TT>nbyte</TT>
bytes) may have been written before an error occurred.
<P>
<HR>
<P>
<A NAME="write_resid">
<H4>st_write_resid()</H4>
</A>
Writes a buffer of data to a specified file descriptor object.
<P>
<H5>Syntax</H5>

<PRE>
#include &lt;st.h&gt;

int st_write_resid(st_netfd_t fd, const void *buf, size_t *resid,
                   st_utime_t timeout);
</PRE>
<P>
<H5>Parameters</H5>
<TT>st_write_resid()</TT> has the following parameters:<P>
<TT>fd</TT><P>
A file descriptor object identifier (see
<A HREF=#netfd_t><B>st_netfd_t</B></A>).<P>
<TT>buf</TT><P>
A pointer to the buffer holding the data to be written.<P>
<TT>resid</TT><P>
A pointer to a number of bytes.
On entry, the amount of data to be written from the buffer.
On return, the amount of data remaining to be written.
(A non-zero returned value means some but not all of the data was written.)<P>
<TT>timeout</TT><P>
A value of type <A HREF=#utime_t><B>st_utime_t</B></A> specifying the
inactivity timeout (in microseconds).
Note that timeouts are measured <A HREF="notes.html#timeouts">since the
last context switch</A>.
<P>
<H5>Returns</H5>
On success, zero is returned and <TT>*resid</TT> is zero.
Otherwise, a value of <TT>-1</TT> is returned, <TT>*resid</TT> is non-zero,
and <TT>errno</TT> is set
to indicate the error. Possible <TT>errno</TT> values are the same as set
by the <TT>write(2)</TT> call with two exceptions:<P>
<TABLE BORDER=0>
<TR><TD><TT>EINTR</TT></TD><TD>The current thread was interrupted by
<A HREF=#thread_interrupt>st_thread_interrupt()</A>.</TD></TR>
<TR><TD><TT>ETIME</TT></TD><TD>The timeout occurred.
</TD></TR>
</TABLE> 
<P>
<H5>Description</H5>
This function blocks the calling thread until <I>all</I> the data is written,
a timeout occurs, or the write operation fails. It differs from
<TT>st_write()</TT> only in that it allows the caller to know how many bytes
were transferred before an error occurred.
<P>
<HR>
<P>
<A NAME="writev">
<H4>st_writev()</H4>
</A>
Writes data to a specified file descriptor object from multiple buffers.
<P>
<H5>Syntax</H5>

<PRE>
#include &lt;st.h&gt;

ssize_t st_writev(st_netfd_t fd, const struct iovec *iov, int iov_size,
                  st_utime_t timeout);
</PRE>
<P>
<H5>Parameters</H5>
<TT>st_writev()</TT> has the following parameters:<P>
<TT>fd</TT><P>
A file descriptor object identifier (see
<A HREF=#netfd_t><B>st_netfd_t</B></A>).<P>
<TT>iov</TT><P>
An array of <TT>iovec</TT> structures that describe the buffers to write
from (see <TT>writev(2)</TT>).<P>
<TT>iov_size</TT><P>
Number of <TT>iovec</TT> structures in the <TT>iov</TT> array.<P>
<TT>timeout</TT><P>
A value of type <A HREF=#utime_t><B>st_utime_t</B></A> specifying the
inactivity timeout (in microseconds).
Note that timeouts are measured <A HREF="notes.html#timeouts">since the
last context switch</A>.
<P>
<H5>Returns</H5>
On success a non-negative integer equal to the sum of all the buffer lengths
is returned. Otherwise, a value of <TT>-1</TT> is returned and <TT>errno</TT>
is set to indicate the error. Possible <TT>errno</TT> values are the same as
set by the <TT>writev(2)</TT> call with two exceptions:<P>
<TABLE BORDER=0>
<TR><TD><TT>EINTR</TT></TD><TD>The current thread was interrupted by
<A HREF=#thread_interrupt>st_thread_interrupt()</A>.</TD></TR>
<TR><TD><TT>ETIME</TT></TD><TD>The timeout occurred.
</TD></TR>
</TABLE>
<P>
<H5>Description</H5>
This function blocks the calling thread until <I>all</I> the data is written,
a timeout occurs, or the write operation fails. The return value is equal to
either the sum of all the buffer lengths (on success) or <TT>-1</TT> (on
failure). Note that if <TT>st_writev()</TT> returns <TT>-1</TT>, part of the
data may have been written before an error occurred.
<P>
<HR>
<P>
<A NAME="writev_resid">
<H4>st_writev_resid()</H4>
</A>
Writes multiple buffers of data to a specified file descriptor object.
<P>
<H5>Syntax</H5>

<PRE>
#include &lt;st.h&gt;

int st_writev_resid(st_netfd_t fd, struct iovec **iov, int *iov_size,
		    st_utime_t timeout);
</PRE>
<P>
<H5>Parameters</H5>
<TT>st_writev_resid()</TT> has the following parameters:<P>
<TT>fd</TT><P>
A file descriptor object identifier (see
<A HREF=#netfd_t><B>st_netfd_t</B></A>).<P>
<TT>iov</TT><P>
A pointer to an array of <tt>iovec</tt> structures.
On entry, the iovecs identify the buffers holding the data to write.
On return, the incomplete iovecs.
This function modifies both the pointer and the array to which it points.<P>
<TT>iov_size</TT><P>
A pointer to a number of iovec structures.
On entry, the number of iovec structures pointed to by <tt>*iov</tt>.
On return, the number of incomplete or unused iovec structures.
(A non-zero returned value means some but not all of the data was written.)<P>
<TT>timeout</TT><P>
A value of type <A HREF=#utime_t><B>st_utime_t</B></A> specifying the
inactivity timeout (in microseconds).
Note that timeouts are measured <A HREF="notes.html#timeouts">since the
last context switch</A>.
<P>
<H5>Returns</H5>
On success, zero is returned, <TT>*iov_size</TT> is zero, and <tt>*iov</tt>
points to the first iovec after the end of the original array.
Otherwise, a value of <TT>-1</TT> is returned, <TT>*iov_size</TT> is non-zero,
<tt>*iov</tt> points to the first incomplete iovec, and <TT>errno</TT> is set
to indicate the error. Possible <TT>errno</TT> values are the same as set
by the <TT>writev(2)</TT> call with two exceptions:<P>
<TABLE BORDER=0>
<TR><TD><TT>EINTR</TT></TD><TD>The current thread was interrupted by
<A HREF=#thread_interrupt>st_thread_interrupt()</A>.</TD></TR>
<TR><TD><TT>ETIME</TT></TD><TD>The timeout occurred.
</TD></TR>
</TABLE> 
<P>
All of the iovecs before <tt>*iov</tt> are modified such that
<tt>iov_base</tt> points to the end of the original buffer and
<tt>iov_len</tt> is zero.
<P>
<H5>Description</H5>
This function blocks the calling thread until <I>all</I> the data is written,
a timeout occurs, or the write operation fails. It differs from
<TT>st_writev()</TT> only in that it allows the caller to know how many bytes
were transferred before an error occurred.
<P>
<HR>
<P>
<A NAME="recvfrom">
<H4>st_recvfrom()</H4>
</A>
Receives bytes from a file descriptor object and stores the sending peer's
address.
<P>
<H5>Syntax</H5>

<PRE>
#include &lt;st.h&gt;

int st_recvfrom(st_netfd_t fd, void *buf, int len, struct sockaddr *from,
                int *fromlen, st_utime_t timeout);
</PRE>
<P>
<H5>Parameters</H5>
<TT>st_recvfrom()</TT> has the following parameters:<P>
<TT>fd</TT><P>
A file descriptor object identifier (see
<A HREF=#netfd_t><B>st_netfd_t</B></A>) representing a UDP socket.<P>
<TT>buf</TT><P>
A pointer to a buffer to hold the data received.<P>
<TT>len</TT><P>
The size of <TT>buf</TT> in bytes.<P>
<TT>from</TT><P>
If this parameter is not a <TT>NULL</TT> pointer, the source address of the
message is filled in (see <TT>recvfrom(3)</TT>).<P>
<TT>fromlen</TT><P>
This is a value-result parameter, initialized to the size of the buffer
associated with <TT>from</TT>, and modified on return to indicate the actual
size of the address stored there.<P>
<TT>timeout</TT><P>
A value of type <A HREF=#utime_t><B>st_utime_t</B></A> specifying the time
limit in microseconds for completion of the receive operation.
Note that timeouts are measured <A HREF="notes.html#timeouts">since the
last context switch</A>.
<P>
<H5>Returns</H5>
On success a non-negative integer indicating the length of the received
message in bytes is returned. Otherwise, a value of <TT>-1</TT> is returned
and <TT>errno</TT> is set to indicate the error. Possible <TT>errno</TT>
values are the same as set by the <TT>recvfrom(3)</TT> call with two
exceptions:<P>
<TABLE BORDER=0>
<TR><TD><TT>EINTR</TT></TD><TD>The current thread was interrupted by
<A HREF=#thread_interrupt>st_thread_interrupt()</A>.</TD></TR>
<TR><TD><TT>ETIME</TT></TD><TD>The timeout occurred and no data was received.
</TD></TR>
</TABLE>
<P>
<H5>Description</H5>
This function receives up to a specified number of bytes from the specified
file descriptor object representing a UDP socket.<P>
<TT>st_recvfrom()</TT> blocks the calling thread until one or more bytes are
transferred, a timeout has occurred, or there is an error. No more than
<TT>len</TT> bytes will be transferred.
<P>
<HR>
<P>
<A NAME="sendto">
<H4>st_sendto()</H4>
</A>
Sends bytes to a specified destination.
<P>
<H5>Syntax</H5>

<PRE>
#include &lt;st.h&gt;

int st_sendto(st_netfd_t fd, const void *msg, int len, struct sockaddr *to,
              int tolen, st_utime_t timeout);
</PRE>
<P>
<H5>Parameters</H5>
<TT>st_sendto()</TT> has the following parameters:<P>
<TT>fd</TT><P>
A file descriptor object identifier (see
<A HREF=#netfd_t><B>st_netfd_t</B></A>) representing a UDP socket.<P>
<TT>msg</TT><P>
A pointer to a buffer containing the message to be sent.<P>
<TT>len</TT><P>
The length of the message to be sent (in bytes).<P>
<TT>to</TT><P>
A pointer to the address of the destination (see <TT>sendto(3)</TT>).<P>
<TT>tolen</TT><P>
This parameter specifies the size of the destination address.<P>
<TT>timeout</TT><P>
A value of type <A HREF=#utime_t><B>st_utime_t</B></A> specifying the time
limit in microseconds for completion of the send operation.
Note that timeouts are measured <A HREF="notes.html#timeouts">since the
last context switch</A>.
<P>
<H5>Returns</H5>
On success a non-negative integer indicating the number of bytes sent is
returned. Otherwise, a value of <TT>-1</TT> is returned and <TT>errno</TT> is
set to indicate the error. Possible <TT>errno</TT> values are the same as
set by the <TT>sendto(3)</TT> call with two exceptions:<P>
<TABLE BORDER=0>
<TR><TD><TT>EINTR</TT></TD><TD>The current thread was interrupted by
<A HREF=#thread_interrupt>st_thread_interrupt()</A>.</TD></TR>
<TR><TD><TT>ETIME</TT></TD><TD>The timeout occurred and no data was sent.
</TD></TR>
</TABLE>
<P>
<H5>Description</H5>
This function sends a specified number of bytes from a file descriptor
object representing a UDP socket to the specified destination address.
If no buffer space is available at the underlying OS socket to hold the
message to be transmitted, then <TT>st_sendto()</TT> blocks the calling
thread until the space becomes available, a timeout occurs, or an error
occurs.
<P>
<HR>
<P>
<A NAME="recvmsg">
<H4>st_recvmsg()</H4>
</A>
Receives a message from a file descriptor object.
<P>
<H5>Syntax</H5>

<PRE>
#include &lt;st.h&gt;

int st_recvmsg(st_netfd_t fd, struct msghdr *msg, int flags,
               st_utime_t timeout);
</PRE>
<P>
<H5>Parameters</H5>
<TT>st_recvmsg()</TT> has the following parameters:<P>
<TT>fd</TT><P>
A file descriptor object identifier (see
<A HREF=#netfd_t><B>st_netfd_t</B></A>) representing a UDP socket.<P>
<TT>msg</TT><P>
A pointer to a <TT>msghdr</TT> structure to describe the data received.<P>
<TT>flags</TT><P>
Control flags for <TT>recvmsg(3)</TT>.<P>
<TT>timeout</TT><P>
A value of type <A HREF=#utime_t><B>st_utime_t</B></A> specifying the time
limit in microseconds for completion of the receive operation.
Note that timeouts are measured <A HREF="notes.html#timeouts">since the
last context switch</A>.
<P>
<H5>Returns</H5>
On success a non-negative integer indicating the number of bytes received
is returned. Otherwise, a value of <TT>-1</TT> is returned
and <TT>errno</TT> is set to indicate the error. Possible <TT>errno</TT>
values are the same as set by the <TT>recvmsg(3)</TT> call with two
exceptions:<P>
<TABLE BORDER=0>
<TR><TD><TT>EINTR</TT></TD><TD>The current thread was interrupted by
<A HREF=#thread_interrupt>st_thread_interrupt()</A>.</TD></TR>
<TR><TD><TT>ETIME</TT></TD><TD>The timeout occurred and no data was received.
</TD></TR>
</TABLE>
<P>
<H5>Description</H5>
This function receives bytes from the specified file descriptor object
representing a UDP socket.  The operation is controlled by the in/out
<TT>msg</TT> parameter.<P>
<TT>st_recvmsg()</TT> blocks the calling thread until one or more bytes are
transferred, a timeout has occurred, or there is an error.
<P>
<HR>
<P>
<A NAME="sendmsg">
<H4>st_sendmsg()</H4>
</A>
Sends a message to a file descriptor object.
<P>
<H5>Syntax</H5>

<PRE>
#include &lt;st.h&gt;

int st_sendmsg(st_netfd_t fd, const struct msghdr *msg, int flags,
               st_utime_t timeout);
</PRE>
<P>
<H5>Parameters</H5>
<TT>st_sendmsg()</TT> has the following parameters:<P>
<TT>fd</TT><P>
A file descriptor object identifier (see
<A HREF=#netfd_t><B>st_netfd_t</B></A>) representing a UDP socket.<P>
<TT>msg</TT><P>
A pointer to a <TT>msghdr</TT> structure describing the message to be sent.<P>
<TT>flags</TT><P>
Control flags for <TT>sendmsg(3)</TT>.<P>
<TT>timeout</TT><P>
A value of type <A HREF=#utime_t><B>st_utime_t</B></A> specifying the time
limit in microseconds for completion of the send operation.
Note that timeouts are measured <A HREF="notes.html#timeouts">since the
last context switch</A>.
<P>
<H5>Returns</H5>
On success a non-negative integer indicating the number of bytes sent is
returned. Otherwise, a value of <TT>-1</TT> is returned and <TT>errno</TT> is
set to indicate the error. Possible <TT>errno</TT> values are the same as
set by the <TT>sendmsg(3)</TT> call with two exceptions:<P>
<TABLE BORDER=0>
<TR><TD><TT>EINTR</TT></TD><TD>The current thread was interrupted by
<A HREF=#thread_interrupt>st_thread_interrupt()</A>.</TD></TR>
<TR><TD><TT>ETIME</TT></TD><TD>The timeout occurred and no data was sent.
</TD></TR>
</TABLE>
<P>
<H5>Description</H5>
This function sends bytes to a file descriptor object representing a UDP
socket.  The operation is controlled by the <TT>msg</TT> parameter.
If no buffer space is available at the underlying OS socket to hold the
message to be transmitted, then <TT>st_sendmsg()</TT> blocks the calling
thread until the space becomes available, a timeout occurs, or an error
occurs.
<P>
<HR>
<P>
<A NAME="open">
<H4>st_open()</H4>
</A>
Opens a file for reading, writing, or both.
<P>
<H5>Syntax</H5>

<PRE>
#include &lt;st.h&gt;

st_netfd_t st_open(const char *path, int oflags, mode_t mode);
</PRE>
<P>
<H5>Parameters</H5>
<TT>st_open()</TT> has the following parameters:<P>
<TT>path</TT><P>
The pathname of the file to be opened.<P>
<TT>oflags</TT><P>
File status flags. These are the same flags that are used by the
<TT>open(2)</TT> system call.<P>
<TT>mode</TT><P>
Access permission bits of the file mode, if the file is created when
<TT>O_CREAT</TT> is set in <TT>oflags</TT> (see <TT>open(2)</TT>).
<P>
<H5>Returns</H5>
Upon successful completion, a new file descriptor object identifier is
returned. Otherwise, <TT>NULL</TT> is returned and <TT>errno</TT> is set
to indicate the error.
<P>
<H5>Description</H5>
This function creates a new file descriptor object of type
<A HREF=#netfd_t><B>st_netfd_t</B></A> for the file with the pathname
<TT>path</TT>. This object can be freed by
<A HREF=#netfd_free>st_netfd_free()</A> or
<A HREF=#netfd_close>st_netfd_close()</A>.<P>
The primary purpose of this function is to open FIFOs (named pipes) or
other special files in order to create an end point of communication.
However, it can be used on regular files as well.<P>
Among other things, this function always sets a non-blocking flag on the
underlying OS file descriptor, so there is no need to include that flag in
<TT>oflags</TT>.
<P>
<HR>
<P>
<A NAME="poll">
<H4>st_poll()</H4>
</A>
Detects when I/O is ready for a set of OS file descriptors.
<P>
<H5>Syntax</H5>

<PRE>
#include &lt;st.h&gt;

int st_poll(struct pollfd *pds, int npds, st_utime_t timeout);
</PRE>
<P>
<H5>Parameters</H5>
<TT>st_poll()</TT> has the following parameters:<P>
<TT>pds</TT><P>
A pointer to an array of <TT>pollfd</TT> structures (see <TT>poll(2)</TT>).
<P>
<TT>npds</TT><P>
The number of elements in the <TT>pds</TT> array.<P>
<TT>timeout</TT><P>
A value of type <A HREF=#utime_t><B>st_utime_t</B></A> specifying the
amount of time in <I>microseconds</I> the call will block waiting for I/O
to become ready. If this time expires without any I/O becoming ready,
<TT>st_poll()</TT> returns zero.
Note that timeouts are measured <A HREF="notes.html#timeouts">since the
last context switch</A>.
<P>
<H5>Returns</H5>
Upon successful completion, a non-negative value is returned. A positive
value indicates the total number of OS file descriptors in <TT>pds</TT>
that have events. A value of <TT>0</TT> indicates that the call timed out.
Upon failure, a value of <TT>-1</TT> is returned and <TT>errno</TT> is set
to indicate the error:<P>
<TABLE BORDER=0>
<TR><TD><TT>EINTR</TT></TD><TD>The current thread was interrupted by
<A HREF=#thread_interrupt>st_thread_interrupt()</A>.</TD></TR>
</TABLE>
<P>
If an alternative event notification mechanism has been set by
<A HREF=#set_eventsys>st_set_eventsys()</A>, other values of
<TT>errno</TT> could be set upon failure as well.  The values
depend on the specific mechanism in use.
<P>
<H5>Description</H5>
This function returns as soon as I/O is ready on one or more of the specified
OS file descriptors. A count of the number of ready descriptors is returned
unless a timeout occurs, in which case zero is returned.<P>
The <TT>pollfd</TT> structure is defined in the <TT>poll.h</TT> header file
and contains the following members:<P>
<PRE>
    int fd;             /* OS file descriptor */
    short events;       /* requested events   */
    short revents;      /* returned events    */
</PRE>
The <TT>events</TT> field should be set to the I/O events (readable,
writable, exception, or some combination) that the caller is interested in.
On return, the <TT>revents</TT> field is set to indicate what kind of I/O
is ready on the respective descriptor.<P>
The <TT>events</TT> and <TT>revents</TT> fields are constructed by OR-ing
any combination of the following event flags (defined in <TT>poll.h</TT>):
<P>
<TABLE BORDER=0>
<TR><TD><TT>POLLIN</TT></TD><TD><TT>fd</TT> is readable.</TD></TR>
<TR><TD><TT>POLLOUT</TT></TD><TD><TT>fd</TT> is is writable.</TD></TR>
<TR><TD><TT>POLLPRI</TT></TD><TD><TT>fd</TT> has an exception condition.</TD>
</TR>
<TR><TD><TT>POLLNVAL</TT></TD><TD><TT>fd</TT> is bad.</TD></TR>
</TABLE>
<P>
The <TT>POLLNVAL</TT> flag is only valid in the <TT>revents</TT> field;
it is not used in the <TT>events</TT> field.<P>
Despite having an interface like <tt>poll(2)</tt>, this function uses
the same event notification mechanism as the rest of the library.  For
instance if an alternative event nofication mechanism was set using <a
href=#set_eventsys>st_set_eventsys()</a>, this function uses that
mechanism to check for events.<p>
Note that unlike the <TT>poll(2)</TT> call, this function has the
<TT>timeout</TT> parameter expressed in microseconds. If the value of
<TT>timeout</TT> is <tt>ST_UTIME_NO_TIMEOUT</tt>
(<TT>-1</TT>), this function blocks until a requested I/O
event occurs or until the call is interrupted by
<A HREF=#thread_interrupt>st_thread_interrupt()</A>.
<P>
<b>Note: </b> if <TT>kqueue(2)</TT> is used as an alternative event
notification mechanism (see <A
HREF=#set_eventsys>st_set_eventsys()</A>), the <TT>POLLPRI</TT>
event flag is not supported and <TT>st_poll()</TT> will return an error
if it's set (<TT>errno</TT> will be set to <TT>EINVAL</TT>).
<P>
<HR>
<P>

<A NAME="progr">
<H2>Program Structure</H2>
</A>
<P>
Generally, the following steps should be followed when writing an application
using the State Threads library:
<P>
<OL>
<LI>Configure the library by calling these pre-init functions, if desired.
  <ul>
  <li><A HREF="#set_utime_function">st_set_utime_function()</A></li>
  <li><A HREF="#set_eventsys">st_set_eventsys()</A></li>
  </ul>
  </LI>
<P>
<LI>Initialize the library by calling <A HREF=#st_init>st_init()</A>.</LI>
<P>
<LI>Configure the library by calling these post-init functions, if desired.
  <ul>
  <li><A HREF="#timecache_set">st_timecache_set()</A></li>
  <li><A HREF="#randomize_stacks">st_randomize_stacks()</A></li>
  <li><A HREF="#set_switch_in_cb">st_set_switch_in_cb()</A></li>
  <li><A HREF="#set_switch_out_cb">st_set_switch_out_cb()</A></li>
  </ul>
  </LI>
<P>
<LI>Create resources that will be shared among different processes:
    create and bind listening sockets (see <TT>socket(3)</TT>,
    <TT>bind(3)</TT>, <TT>listen(3)</TT>,
    <A HREF=#netfd_open_socket>st_netfd_open_socket()</A>, and possibly
    <A HREF=#netfd_serialize_accept>st_netfd_serialize_accept()</A>),
    create shared memory segments, inter-process communication (IPC)
    channels and synchronization primitives (if any).</LI>
<P>
<LI>Create several processes via <TT>fork(2)</TT>. The parent process should
    either exit or become a "watchdog" (e.g., it starts a new process when
    an existing one crashes, does a cleanup upon application termination,
    etc.).</LI>
<P>
<LI>In each child process create a pool of threads (see
    <A HREF=#thread_create>st_thread_create()</A>) to handle user
    connections. Each thread in the pool may accept client connections
    (<A HREF=#accept>st_accept()</A>), connect to other servers
    (<A HREF=#connect>st_connect()</A>), perform various network I/O
    (<A HREF=#read>st_read()</A>, <A HREF=#write>st_write()</A>, etc.).</LI>
</OL>
<P>
Note that only State Threads library <A HREF=#io>I/O functions</A> should
be used for a network I/O: any other I/O calls may block the calling process
indefinitely. For example, standard I/O functions (<TT>fgets(3)</TT>,
<TT>fread(3)</TT>, <TT>fwrite(3)</TT>, <TT>fprintf(3)</TT>, etc.) call
<TT>read(2)</TT> and <TT>write(2)</TT> directly and therefore should not be
used on sockets or pipes.
<P>
Also note that for short <A
HREF="notes.html#timeouts">timeouts</A> to work the program
should do context switches (for example by calling
<TT>st_usleep()</TT>) on a regular basis.
<P>
<HR>
<P>

<A NAME="block">
<H2>List of Blocking Functions</H2>
</A>
<P>
The thread context switch (process state change) can <I>only</I> happen
in a well-known set of blocking functions.
Only the following functions can block the calling thread:
<P>
<DL>
<DD><A HREF=#thread_join>st_thread_join()</A></DD>
<DD><A HREF=#sleep>st_sleep()</A></DD>
<DD><A HREF=#sleep>st_usleep()</A></DD>
<DD><A HREF=#cond_wait>st_cond_wait()</A></DD>
<DD><A HREF=#cond_timedwait>st_cond_timedwait()</A></DD>
<DD><A HREF=#mutex_lock>st_mutex_lock()</A></DD>
<DD><A HREF=#netfd_poll>st_netfd_poll()</A></DD>
<DD><A HREF=#accept>st_accept()</A></DD>
<DD><A HREF=#connect>st_connect()</A></DD>
<DD><A HREF=#read>st_read()</A></DD>
<DD><A HREF=#read_fully>st_read_fully()</A></DD>
<DD><A HREF=#read_resid>st_read_resid()</A></DD>
<DD><A HREF=#readv>st_readv()</A></DD>
<DD><A HREF=#readv_resid>st_readv_resid()</A></DD>
<DD><A HREF=#write>st_write()</A></DD>
<DD><A HREF=#write_resid>st_write_resid()</A></DD>
<DD><A HREF=#writev>st_writev()</A></DD>
<DD><A HREF=#writev_resid>st_writev_resid()</A></DD>
<DD><A HREF=#recvfrom>st_recvfrom()</A></DD>
<DD><A HREF=#sendto>st_sendto()</A></DD>
<DD><A HREF=#recvmsg>st_recvmsg()</A></DD>
<DD><A HREF=#sendmsg>st_sendmsg()</A></DD>
<DD><A HREF=#poll>st_poll()</A></DD>
</DL>
<P>
<HR>
<P>

</BODY>
</HTML>

