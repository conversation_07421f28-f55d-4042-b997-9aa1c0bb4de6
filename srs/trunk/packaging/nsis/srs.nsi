; Script generated by the HM NIS Edit Script Wizard.
; See https://nsis.sourceforge.io/Download
;       "C:\Program Files (x86)\NSIS\makensis.exe" /DSRS_VERSION=5.0.89 /DCYGWIN_DIR="C:\tools\cygwin" srs.nsi
;       "/cygdrive/c/Program Files (x86)/NSIS/makensis.exe" /DSRS_VERSION=5.0.89 /DCYGWIN_DIR="C:\cygwin64" srs.nsi
;       "/cygdrive/c/Program Files (x86)/NSIS/makensis.exe" /DSRS_VERSION=$(./objs/srs.exe -v 2>&1) /DCYGWIN_DIR="C:\cygwin64" packaging/nsis/srs.nsi

; HM NIS Edit Wizard helper defines
!define PRODUCT_NAME "SRS"
!define PRODUCT_VERSION "${SRS_VERSION}"
!define PRODUCT_PUBLISHER "ossrs"
!define PRODUCT_WEB_SITE "https://ossrs.io"
!define PRODUCT_DIR_REGKEY "Software\Microsoft\Windows\CurrentVersion\App Paths\srs.exe"
!define PRODUCT_INSTALL_REGKEY "Software\Microsoft\Windows\CurrentVersion\App Paths\srs\ins_dir"
!define PRODUCT_UNINST_KEY "Software\Microsoft\Windows\CurrentVersion\Uninstall\${PRODUCT_NAME}"
!define PRODUCT_UNINST_ROOT_KEY "HKLM"

; MUI 1.67 compatible ------
!include "MUI.nsh"

; MUI Settings
!define MUI_ABORTWARNING
!define MUI_ICON "..\..\doc\srs-logo.ico"
!define MUI_UNICON "${NSISDIR}\Contrib\Graphics\Icons\modern-uninstall.ico"

; Welcome page
!insertmacro MUI_PAGE_WELCOME
; License page
!insertmacro MUI_PAGE_LICENSE "..\..\..\LICENSE"
; Directory page
!insertmacro MUI_PAGE_DIRECTORY
; Instfiles page
!insertmacro MUI_PAGE_INSTFILES
; Finish page
;!define MUI_FINISHPAGE_RUN "$INSTDIR\srs-cli.bat"
!insertmacro MUI_PAGE_FINISH

; Uninstaller pages
!insertmacro MUI_UNPAGE_INSTFILES

; Language files
!insertmacro MUI_LANGUAGE "English"

; MUI end ------

Name "${PRODUCT_NAME} ${PRODUCT_VERSION}"
OutFile "SRS-Windows-x86_64-${PRODUCT_VERSION}-setup.exe"
InstallDir "$PROGRAMFILES\SRS"
InstallDirRegKey HKLM "${PRODUCT_DIR_REGKEY}" ""
ShowInstDetails show
ShowUnInstDetails show

Section "MainSection" SEC01
  SetOutPath "$INSTDIR"
  SetOverwrite ifnewer
  File "..\..\..\LICENSE"
  File ".\*.bat"
  SetOutPath "$INSTDIR\conf"
  SetOverwrite try
  File "..\..\conf\*.conf"
  SetOutPath "$INSTDIR\logo"
  File "..\..\doc\srs-logo.ico"
  SetOutPath "$INSTDIR\objs"
  File "${CYGWIN_DIR}\bin\cyggcc_s-seh-1.dll"
  File "${CYGWIN_DIR}\bin\cygstdc++-6.dll"
  File "${CYGWIN_DIR}\bin\cygwin1.dll"
  SetOutPath "$INSTDIR\objs\nginx\html"
  File "..\..\objs\nginx\html\nginx.html"
  File "..\..\objs\nginx\html\favicon.ico"
  File "..\..\objs\nginx\html\index.html"
  SetOutPath "$INSTDIR\objs\nginx\html\live"
  File "..\..\objs\nginx\html\live\livestream.html"
  SetOutPath "$INSTDIR\objs\nginx\html\console"
  File /r "..\..\objs\nginx\html\console\"
  SetOutPath "$INSTDIR\objs\nginx\html\demos"
  File /r "..\..\objs\nginx\html\demos\"
  SetOutPath "$INSTDIR\objs\nginx\html\players"
  File /r "..\..\objs\nginx\html\players\"
  SetOutPath "$INSTDIR\objs"
  File "..\..\objs\srs.exe"
  File ".\srs.pid"
  SetOutPath "$INSTDIR"
  CreateDirectory "$SMPROGRAMS\SRS"
  CreateShortCut "$SMPROGRAMS\SRS\SRS.lnk" "$INSTDIR\srs-cli.bat" "" "$INSTDIR\logo\srs-logo.ico"
  CreateShortCut "$DESKTOP\SRS.lnk" "$INSTDIR\srs-cli.bat" "" "$INSTDIR\logo\srs-logo.ico"
SectionEnd

Section -AdditionalIcons
  SetOutPath $INSTDIR
  CreateShortCut "$SMPROGRAMS\SRS\Uninstall.lnk" "$INSTDIR\uninst.exe"
SectionEnd

Section -Post
  WriteUninstaller "$INSTDIR\uninst.exe"
  WriteRegStr HKLM "${PRODUCT_DIR_REGKEY}" "" "$INSTDIR\objs\srs.exe"
  WriteRegStr HKLM "${PRODUCT_INSTALL_REGKEY}" "" "$INSTDIR"
  WriteRegStr ${PRODUCT_UNINST_ROOT_KEY} "${PRODUCT_UNINST_KEY}" "DisplayName" "$(^Name)"
  WriteRegStr ${PRODUCT_UNINST_ROOT_KEY} "${PRODUCT_UNINST_KEY}" "UninstallString" "$INSTDIR\uninst.exe"
  WriteRegStr ${PRODUCT_UNINST_ROOT_KEY} "${PRODUCT_UNINST_KEY}" "DisplayIcon" "$INSTDIR\objs\srs.exe"
  WriteRegStr ${PRODUCT_UNINST_ROOT_KEY} "${PRODUCT_UNINST_KEY}" "DisplayVersion" "${PRODUCT_VERSION}"
  WriteRegStr ${PRODUCT_UNINST_ROOT_KEY} "${PRODUCT_UNINST_KEY}" "URLInfoAbout" "${PRODUCT_WEB_SITE}"
  WriteRegStr ${PRODUCT_UNINST_ROOT_KEY} "${PRODUCT_UNINST_KEY}" "Publisher" "${PRODUCT_PUBLISHER}"
SectionEnd


Function un.onUninstSuccess
  HideWindow
  MessageBox MB_ICONINFORMATION|MB_OK "$(^Name) is removed from your computer."
FunctionEnd

Function un.onInit
  MessageBox MB_ICONQUESTION|MB_YESNO|MB_DEFBUTTON2 "Uninstall $(^Name) from your computer?" IDYES +2
  Abort
FunctionEnd

Section Uninstall
  Delete "$INSTDIR\uninst.exe"
  Delete "$INSTDIR\objs\srs.pid"
  Delete "$INSTDIR\objs\srs.exe"
  Delete "$INSTDIR\objs\nginx\html\nginx.html"
  Delete "$INSTDIR\objs\nginx\html\live\livestream.html"
  Delete "$INSTDIR\objs\nginx\html\index.html"
  Delete "$INSTDIR\objs\nginx\html\favicon.ico"
  Delete "$INSTDIR\objs\cygwin1.dll"
  Delete "$INSTDIR\objs\cygstdc++-6.dll"
  Delete "$INSTDIR\objs\cyggcc_s-seh-1.dll"
  Delete "$INSTDIR\logo\srs-logo.ico"
  Delete "$INSTDIR\conf\*.conf"
  Delete "$INSTDIR\LICENSE"
  Delete "$INSTDIR\srs-cli.bat"
  Delete "$INSTDIR\srs-live.bat"
  Delete "$INSTDIR\srs-rtc.bat"

  Delete "$SMPROGRAMS\SRS\Uninstall.lnk"
  Delete "$DESKTOP\SRS.lnk"
  Delete "$SMPROGRAMS\SRS\SRS.lnk"

  RMDir "$SMPROGRAMS\SRS"
  RMDir /r "$INSTDIR\objs\nginx\html\players"
  RMDir /r "$INSTDIR\objs\nginx\html\demos"
  RMDir /r "$INSTDIR\objs\nginx\html\console"
  RMDir "$INSTDIR\objs\nginx\html\live"
  RMDir "$INSTDIR\objs\nginx\html"
  RMDir "$INSTDIR\objs\nginx"
  RMDir "$INSTDIR\objs"
  RMDir "$INSTDIR\logo"
  RMDir "$INSTDIR\conf"
  RMDir "$INSTDIR"

  DeleteRegKey ${PRODUCT_UNINST_ROOT_KEY} "${PRODUCT_UNINST_KEY}"
  DeleteRegKey HKLM "${PRODUCT_DIR_REGKEY}"
  DeleteRegKey HKLM "${PRODUCT_INSTALL_REGKEY}"
  SetAutoClose true
SectionEnd