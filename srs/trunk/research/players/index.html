<!DOCTYPE html>
<html>
<head>
    <title>SRS</title>   
    <meta charset="utf-8">
    <link rel="stylesheet" type="text/css" href="css/bootstrap.min.css"/>
    <style>
        body{
            padding-top: 30px;
        }
    </style>
    <script type="text/javascript" src="js/jquery-1.12.2.min.js"></script>
    <script type="text/javascript" src="js/bootstrap.min.js"></script>
    <script type="text/javascript" src="js/swfobject.js"></script>
    <script type="text/javascript" src="js/srs.page.js"></script>
    <script type="text/javascript" src="js/srs.utility.js"></script>
    <script type="text/javascript" src="js/winlin.utility.js"></script>
    <script type="text/javascript">
        $(function(){
            update_nav();

            var query = parse_query_string();
            var params = [];
            for (var key in query.user_query) {
                params.push(key + "=" + query[key]);
            }

            var url = "srs_player.html";
            if (params.length) {
                url += '?' + params.join('&');
            }

            setTimeout(function () {
                window.location.href = url;
            }, 0);
        });
    </script>
</head>
<body>
</body>

