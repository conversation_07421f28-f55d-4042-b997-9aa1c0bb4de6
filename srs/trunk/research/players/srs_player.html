<!DOCTYPE html>
<html>
<head>
    <title>SRS</title>   
    <meta charset="utf-8">
    <link rel="stylesheet" type="text/css" href="css/bootstrap.min.css"/>
    <style>
        body{
            padding-top: 30px;
        }
    </style>
</head>
<body>
<img src='//ossrs.net/gif/v1/sls.gif?site=ossrs.net&path=/player/srsplayer'/>
<div class="navbar navbar-fixed-top">
    <div class="navbar-inner">
        <div class="container">
            <a class="brand" href="https://github.com/ossrs/srs" target="_blank">SRS</a>
            <div class="nav-collapse collapse">
                <ul class="nav">
                    <li class="active"><a id="nav_srs_player" href="srs_player.html">LivePlayer</a></li>
                    <!--<li><a id="nav_rtc_player" href="rtc_player.html">RTC播放器</a></li>-->
                    <!--<li><a id="nav_rtc_publisher" href="rtc_publisher.html">RTC推流</a></li>-->
                    <li><a id="nav_whip" href="whip.html">WHIP</a></li>
                    <li><a id="nav_whep" href="whip.html">WHEP</a></li>
                    <li><a  href="http://ossrs.net/srs.release/releases/app.html">iOS/Andriod</a></li>
                    <!--<li><a id="nav_srs_publisher" href="srs_publisher.html">SRS编码器</a></li>-->
                    <!--<li><a id="nav_srs_chat" href="srs_chat.html">SRS会议</a></li>-->
                    <!--<li><a id="nav_srs_bwt" href="srs_bwt.html">SRS测网速</a></li>-->
                    <!--li><a id="nav_vlc" href="vlc.html">VLC播放器</a></li>-->
                    <!--<li><a id="nav_gb28181" href="srs_gb28181.html">GB28181</a></li>-->
                    <!--<li>
                        <a href="https://github.com/ossrs/srs">
                            <img alt="GitHub Repo stars" src="https://img.shields.io/github/stars/ossrs/srs?style=social">
                        </a>
                    </li>-->
                </ul>
            </div>
        </div>
    </div>
</div>
<div class="container">

    <div id="main_content">
        <div id="main_info" class="alert alert-info fade in">
            <button type="button" class="close" data-dismiss="alert">×</button>
            <strong><span>Usage:</span></strong> <span>Enter the HTTP-FLV/HLS URL and click "Play" to start playing the video.</span>
        </div>
        <div class="form-inline">
            <div>
                URL:
                <input type="text" id="txt_url" class="input-xxlarge" value="">
                <button class="btn btn-primary" id="btn_play">Play</button>
            </div>
            <p></p>
            <div>
                <video id="video_player" width="100%" autoplay controls></video>
            </div>
            <p>
                Share: <a href="#" id="link_url" target="_blank">Please right-click and copy this link.</a>
            </p>
            <div id="main_tips">
                <p></p>
                <p>
                    Recommended other players:
                    <ul>
                        <li><a href="https://github.com/xqq/mpegts.js">flv.js</a>, HTML5/MSE for playing HTTP-FLV</li>
                        <li><a href="https://github.com/xqq/mpegts.js">mpegts.js</a>, HTML5/MSE for playing HTTP-TS</li>
                        <li><a href="https://github.com/video-dev/hls.js/">hls.js</a>, HTML5/MSE for playing HLS</li>
                        <li><a href="https://github.com/Dash-Industry-Forum/dash.js">dash.js</a>, HTML5/MSE for playing MPEG-DASH</li>
                    </ul>
                </p>
            </div>
        </div>
    </div>

    <div name="detect_flash">
        <div id="main_flash_alert" class="alert alert-danger fade in hide">
            <button type="button" class="close" data-dismiss="alert">×</button>
            <p>
                <a href="https://www.adobe.com/products/flashplayer/end-of-life.html" target="_blank">Flash is dead</a>，
                Unable to play RTMP streams, you can use ffplay or VLC player to play.
            </p>
            <ul>
                <li>If you want low-latency live streaming (3-5 seconds), you can use HTTP-FLV with the <a href="https://github.com/xqq/mpegts.js">flv.js</a> player for H5/MSE playback of HTTP-FLV.</li>
                <li>If you want low-latency live streaming (3-5 seconds), you can use HTTP-TS with the <a href="https://github.com/xqq/mpegts.js">mpegts.js</a> player for H5/MSE playback of HTTP-TS.</li>
                <li>If you are not sensitive to latency (5-10 seconds) and want better cross-platform compatibility, you can use HLS with the <a href="https://github.com/video-dev/hls.js/">hls.js</a> player for H5/MSE playback of HLS.</li>
                <li>If you want ultra-low latency (within 1 second) and only need to support mainstream browsers, you can use WebRTC with the <a href="rtc_player.html">RTC player</a>.</li>
            </ul>
            <p>
                更多信息，参考<a href="https://mp.weixin.qq.com/s/oYn5q4fF9afaged23Ueudg" target="_blank">没有Flash如何做直播？</a>
            </p>
        </div>
    </div>

    <footer>
        <p></p>
        <p><a href="https://github.com/ossrs/srs">SRS Team &copy; 2013</a></p>
    </footer>
</div>
</body>
<script type="text/javascript" src="js/jquery-1.12.2.min.js"></script>
<script type="text/javascript" src="js/bootstrap.min.js"></script>
<script type="text/javascript" src="js/mpegts-1.7.2.min.js"></script>
<script type="text/javascript" src="js/hls-0.14.17.min.js"></script>
<script type="text/javascript" src="js/dash-v4.5.1.all.min.js"></script>
<script type="text/javascript" src="js/json2.js"></script>
<script type="text/javascript" src="js/srs.page.js"></script>
<script type="text/javascript" src="js/srs.log.js"></script>
<script type="text/javascript" src="js/srs.utility.js"></script>
<script type="text/javascript" src="js/winlin.utility.js"></script>
<script type="text/javascript">
    var flvPlayer = null;
    var tsPlayer = null;
    var hlsPlayer = null;
    var dashPlayer = null;

    var stopPlayers = function () {
        if (flvPlayer) {
            flvPlayer.destroy();
            flvPlayer = null;
        }
        if (tsPlayer) {
            tsPlayer.destroy();
            tsPlayer = null;
        }
        if (hlsPlayer) {
            hlsPlayer.destroy();
            hlsPlayer = null;
        }
        if (dashPlayer) {
            dashPlayer.destroy();
            dashPlayer = null;
        }
    };

    var hide_for_error = function () {
        $('#main_flash_alert').show();
        $('#main_info').hide();
        $('#main_tips').hide();
        $('#video_player').hide();
        //$('#btn_play').hide();

        stopPlayers();
    };

    var show_for_ok = function () {
        $('#main_flash_alert').hide();
        $('#main_info').show();
        $('#main_tips').show();
        $('#video_player').show();
        //$('#btn_play').show();
    };

    var apply_url_change = function() {
        var r = parse_rtmp_url($("#txt_url").val());
        var url = window.location.protocol + "//" + query.host + query.pathname + "?autostart=true"
            + "&app=" + r.app + "&stream=" + r.stream + "&server=" + r.server + "&port=" + r.port;
        url += (query.shp_identify) ? "&shp_identify=" + query.shp_identify : '';
        url += (r.vhost === "__defaultVhost__") ? "&vhost=" + r.server : "&vhost=" + r.vhost;
        url += (r.schema !== "rtmp") ? "&schema=" + r.schema : '';
        url += (query.buffer) ? "&buffer=" + query.buffer : '';
        url += (query.api_port) ? "&api_port=" + query.api_port : '';

        var queries = user_extra_params(query);
        queries = user_extra_params(r, queries);

        if (queries && queries.length) {
            url += '&' + queries.join('&');
        }
        $("#player_url").text($("#txt_url").val()).attr("href", url);
        $("#link_url").attr("href", url);

        // For RTMP, not support.
        if (r.schema === 'rtmp') {
            hide_for_error();
            return;
        }

        return r;
    };

    var start_play = function (r) {
        stopPlayers();
        if (!r) return;

        // Use H5 native to play mp4.
        if (r.stream.indexOf('.mp4') > 0) {
            $('#video_player').attr('src', r.url).show();
            return;
        }

        // Start play HTTP-TS.
        if (r.stream.indexOf('.ts') > 0) {
            if (!mpegts.getFeatureList().mseLivePlayback) {
                hide_for_error();
                return;
            }

            show_for_ok();

            tsPlayer = mpegts.createPlayer({type: 'mpegts', url: r.url, isLive: true, enableStashBuffer: false});
            tsPlayer.attachMediaElement(document.getElementById('video_player'));
            tsPlayer.load();
            tsPlayer.play();
            return;
        }

        // Start play HLS.
        if (r.stream.indexOf('.m3u8') > 0) {
            if (!Hls.isSupported()) {
                hide_for_error();
                return;
            }

            show_for_ok();

            hlsPlayer = new Hls();
            hlsPlayer.loadSource(r.url);
            hlsPlayer.attachMedia(document.getElementById('video_player'));
            return;
        }

        // Start play MPEG-DASH.
        if (r.stream.indexOf('.mpd') > 0) {
            show_for_ok();

            dashPlayer = dashjs.MediaPlayer().create();
            dashPlayer.initialize(document.querySelector("#video_player"), r.url, true);
            return;
        }

        // Start play HTTP-FLV.
        let isFlv = r.stream.indexOf('.flv') > 0;
        // Compatible with NGINX-HTTP-FLV module, see https://github.com/winshining/nginx-http-flv-module and the stream
        // url without .flv, such as:
        //          http://localhost:8080/live?app=live&stream=livestream
        isFlv = isFlv || r.stream && r.url.indexOf('http') === 0;
        if (isFlv) {
            if (!mpegts.getFeatureList().mseLivePlayback) {
                hide_for_error();
                return;
            }

            show_for_ok();

            flvPlayer = mpegts.createPlayer({type: 'flv', url: r.url, isLive: true, enableStashBuffer: false});
            flvPlayer.attachMediaElement(document.getElementById('video_player'));
            flvPlayer.load();
            flvPlayer.play();
            return;
        }

        console.error('不支持的URL', r.url, r);
        $('#video_player').hide();
    };

    $("#txt_url").change(function(){
        apply_url_change();
    });

    $("#btn_play").click(function(){
        $('#video_player').prop('muted', false);
        var r = apply_url_change();
        start_play(r);
    });

    /****
     * The parameters for this page:
     *       schema, the protocol schema, rtmp or http.
     *       server, the ip of the url.
     *       port, the rtmp port of url.
     *       vhost, the vhost of url, can equals to server.
     *       app, the app of url.
     *       stream, the stream of url, can endwith .flv or .mp4 or nothing for RTMP.
     *       autostart, whether auto play the stream.
     *       buffer, the buffer time in seconds.
     * extra params:
     *       shp_identify, hls+ param.
     * for example:
     *       http://localhost:8088/players/srs_player.html?vhost=ossrs.net&app=live&stream=livestream&server=ossrs.net&port=1935&autostart=true&schema=rtmp
     *       http://localhost:8088/players/srs_player.html?vhost=ossrs.net&app=live&stream=livestream.flv&server=ossrs.net&port=8080&autostart=true&schema=http
     */
    var query = parse_query_string();

    // get the vhost and port to set the default url.
    // url set to: http://localhost:8080/live/livestream.flv
    srs_init_flv("#txt_url");

    if (query.autostart === "true") {
        $('#video_player').prop('muted', true);
        console.warn('For autostart, we should mute it, see https://www.jianshu.com/p/c3c6944eed5a ' +
            'or https://developers.google.com/web/updates/2017/09/autoplay-policy-changes#audiovideo_elements');

        var r = apply_url_change();
        start_play(r);
    } else {
        $('#video_player').hide();
    }
</script>
</html>
