<!DOCTYPE html>
<html>
<head>
    <title>SRS</title>
    <meta charset="utf-8">
    <style>
        body{
            padding-top: 30px;
        }
    </style>
    <link rel="stylesheet" type="text/css" href="css/bootstrap.min.css"/>
    <script type="text/javascript" src="js/jquery-1.12.2.min.js"></script>
    <script type="text/javascript" src="js/adapter-7.4.0.min.js"></script>
    <script type="text/javascript" src="js/srs.sdk.js"></script>
    <script type="text/javascript" src="js/winlin.utility.js"></script>
    <script type="text/javascript" src="js/srs.page.js"></script>
</head>
<body>
<img src='//ossrs.net/gif/v1/sls.gif?site=ossrs.net&path=/player/rtcpublisher'/>
<div class="navbar navbar-fixed-top">
    <div class="navbar-inner">
        <div class="container">
            <a class="brand" href="https://github.com/ossrs/srs" target="_blank">SRS</a>
            <div class="nav-collapse collapse">
                <ul class="nav">
                    <li><a id="nav_srs_player" href="srs_player.html">LivePlayer</a></li>
                    <!--<li><a id="nav_rtc_player" href="rtc_player.html">RTC播放器</a></li>-->
                    <!--<li><a id="nav_rtc_publisher" href="rtc_publisher.html">RTC推流</a></li>-->
                    <li class="active"><a id="nav_whip" href="whip.html">WHIP</a></li>
                    <li><a id="nav_whep" href="whip.html">WHEP</a></li>
                    <li><a  href="http://ossrs.net/srs.release/releases/app.html">iOS/Andriod</a></li>
                    <!--<li><a id="nav_srs_publisher" href="srs_publisher.html">SRS编码器</a></li>-->
                    <!--<li><a id="nav_srs_chat" href="srs_chat.html">SRS会议</a></li>-->
                    <!--<li><a id="nav_srs_bwt" href="srs_bwt.html">SRS测网速</a></li>-->
                    <!--<li><a id="nav_vlc" href="vlc.html">VLC播放器</a></li>-->
                    <!--<li><a id="nav_gb28181" href="srs_gb28181.html">GB28181</a></li>-->
                    <!--<li>
                        <a href="https://github.com/ossrs/srs">
                            <img alt="GitHub Repo stars" src="https://img.shields.io/github/stars/ossrs/srs?style=social">
                        </a>
                    </li>-->
                </ul>
            </div>
        </div>
    </div>
</div>
<div class="container">
    <div id="main_info" class="alert alert-info fade in">
        <button type="button" class="close" data-dismiss="alert">×</button>
        <strong><span>Usage:</span></strong> <span>Enter the WebRTC WHIP URL and click "Publish" to start publishing.</span>
    </div>
    <div class="form-inline">
        URL:
        <input type="text" id="txt_url" class="input-xxlarge" value="">
        <button class="btn btn-primary" id="btn_publish">Publish</button>
    </div>

    <label></label>
    <video id="rtc_media_player" width="320" autoplay muted></video>

    <label></label>
    SessionID: <span id='sessionid'></span>

    <label></label>
    Audio: <span id='acodecs'></span><br/>
    Video: <span id='vcodecs'></span>

    <label></label>
    Simulator: <a href='#' id='simulator-drop'>Drop</a>

    <footer>
        <p></p>
        <p><a href="https://github.com/ossrs/srs">SRS Team &copy; 2020</a></p>
    </footer>
</div>
<script type="text/javascript">
$(function(){
    var sdk = null; // Global handler to do cleanup when republishing.
    var startPublish = function() {
        $('#rtc_media_player').show();

        // Close PC when user replay.
        if (sdk) {
            sdk.close();
        }
        sdk = new SrsRtcWhipWhepAsync();

        // User should set the stream when publish is done, @see https://webrtc.org/getting-started/media-devices
        // However SRS SDK provides a consist API like https://webrtc.org/getting-started/remote-streams
        $('#rtc_media_player').prop('srcObject', sdk.stream);
        // Optional callback, SDK will add track to stream.
        // sdk.ontrack = function (event) { console.log('Got track', event); sdk.stream.addTrack(event.track); };

        // https://developer.mozilla.org/en-US/docs/Web/Media/Formats/WebRTC_codecs#getting_the_supported_codecs
        sdk.pc.onicegatheringstatechange = function (event) {
            if (sdk.pc.iceGatheringState === "complete") {
                $('#acodecs').html(SrsRtcFormatSenders(sdk.pc.getSenders(), "audio"));
                $('#vcodecs').html(SrsRtcFormatSenders(sdk.pc.getSenders(), "video"));
            }
        };

        // For example: webrtc://r.ossrs.net/live/livestream
        var url = $("#txt_url").val();
        sdk.publish(url).then(function(session){
            $('#sessionid').html(session.sessionid);
            $('#simulator-drop').attr('href', session.simulator + '?drop=1&username=' + session.sessionid);
        }).catch(function (reason) {
            // Throw by sdk.
            if (reason instanceof SrsError) {
                if (reason.name === 'HttpsRequiredError') {
                    alert(`WebRTC推流必须是HTTPS或者localhost：${reason.name} ${reason.message}`);
                } else {
                    alert(`${reason.name} ${reason.message}`);
                }
            }
            // See https://developer.mozilla.org/en-US/docs/Web/API/MediaDevices/getUserMedia#exceptions
            if (reason instanceof DOMException) {
                if (reason.name === 'NotFoundError') {
                    alert(`找不到麦克风和摄像头设备：getUserMedia ${reason.name} ${reason.message}`);
                } else if (reason.name === 'NotAllowedError') {
                    alert(`你禁止了网页访问摄像头和麦克风：getUserMedia ${reason.name} ${reason.message}`);
                } else if (['AbortError', 'NotAllowedError', 'NotFoundError', 'NotReadableError', 'OverconstrainedError', 'SecurityError', 'TypeError'].includes(reason.name)) {
                    alert(`getUserMedia ${reason.name} ${reason.message}`);
                }
            }

            sdk.close();
            $('#rtc_media_player').hide();
            console.error(reason);
        });
    };

    $('#rtc_media_player').hide();
    var query = parse_query_string();
    srs_init_whip("#txt_url", query);

    $("#btn_publish").click(startPublish);
    // Never play util windows loaded @see https://github.com/ossrs/srs/issues/2732
    if (query.autostart === 'true') {
        window.addEventListener("load", function(){ startPublish(); });
    }
});
</script>
</body>
</html>

