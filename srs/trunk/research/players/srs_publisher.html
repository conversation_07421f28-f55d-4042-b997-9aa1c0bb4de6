<!DOCTYPE html>
<html>
<head>
    <title>SRS</title>
    <meta charset="utf-8">
    <link rel="stylesheet" type="text/css" href="css/bootstrap.min.css"/>
    <style>
        body{
            padding-top: 30px;
        }
        .accordion-group {
            width: 310px;
        }
    </style>
</head>
<body>
<img src='//ossrs.net/gif/v1/sls.gif?site=ossrs.net&path=/player/obs'/>
<div class="navbar navbar-fixed-top">
    <div class="navbar-inner">
        <div class="container">
            <a id="srs_index" class="brand" href="#">SRS</a>
            <div class="nav-collapse collapse">
                <ul class="nav">
                    <li><a id="nav_srs_player" href="srs_player.html">LivePlayer</a></li>
                    <li><a id="nav_rtc_player" href="rtc_player.html">RTC播放器</a></li>
                    <li><a id="nav_rtc_publisher" href="rtc_publisher.html">RTC推流</a></li>
                </ul>
            </div>
        </div>
    </div>
</div>
<div class="container">
    <!-- for the log -->
    <div class="alert alert-danger fade in" id="txt_log">
        <button type="button" class="close" data-dismiss="alert">×</button>
        <strong><span id="txt_log_title">Warning:</span></strong>
        <span id="txt_log_msg">
            Flash推流已经很少用，建议用<a href="rtc_publisher.html">RTC推流</a>，<a href="https://obsproject.com/" target="_blank">OBS</a>或<a href="http://ffmpeg.org/" target="_blank">FFMPEG</a>推流，
            如果一定要使用Flash推流请点<a id="https_publisher" href="srs_publisher_flash.html">这里</a>。
        </span>
    </div>
    <hr/>
    <footer>
        <p><a href="https://github.com/ossrs/srs">SRS Team &copy; 2013</a></p>
    </footer>
</div>
</body>
<script type="text/javascript" src="js/jquery-1.12.2.min.js"></script>
<script type="text/javascript" src="js/bootstrap.min.js"></script>
<script type="text/javascript">
    $(function(){
        var l = window.location;
        var url = window.location.href;
        if (l.hostname !== 'localhost' && l.hostname !== '127.0.0.1' && l.protocol === 'http:') {
            // For flash publisher, must use HTTPS.
            url = window.location.href.replace('http:', 'https:');
        }

        url = url.substring(0, url.lastIndexOf('/')) + '/srs_publisher_flash.html';
        $('#https_publisher').attr('href', url);
    });
</script>
</html>

