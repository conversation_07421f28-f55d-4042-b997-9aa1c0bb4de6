<!DOCTYPE html>
<html>
<head>
    <title>SRS</title>   
    <meta charset="utf-8">
    <link rel="stylesheet" type="text/css" href="css/bootstrap.min.css"/>
    <style>
        body{
            padding-top: 30px;
        }
    </style>
</head>
<body>
<img src='//ossrs.net/gif/v1/sls.gif?site=ossrs.net&path=/player/srspublisher'/>
<div class="navbar navbar-fixed-top">
    <div class="navbar-inner">
        <div class="container">
            <a id="srs_index" class="brand" href="#">SRS</a>
            <div class="nav-collapse collapse">
                <ul class="nav">
                    <li><a id="nav_srs_player" href="srs_player.html">LivePlayer</a></li>
                </ul>
            </div>
        </div>
    </div>
</div>
<div class="container">
    <div name="detect_flash">
        <div id="main_flash_alert" class="alert alert-danger fade in hide">
            <button type="button" class="close" data-dismiss="alert">×</button>
            <strong><p>Usage:</p></strong>
            <p>
                请点击下面的图标，启用Flash
            </p>
            <p>
                若没有见到这个图标，Chrome浏览器请打开
                <span class="text-info">chrome://settings/content/flash</span> 并修改为"Ask first"。
            </p>
        </div>
        <div id="main_flash_hdr" class="hide">
        </div>
    </div>

    <div id="main_content" class="hide">
        <div class="alert alert-danger fade in">
            <button type="button" class="close" data-dismiss="alert">×</button>
            <strong><span>Remark:</span></strong>
            <span>Flash推流只支持H.264+Speex，音频不支持AAC，所以无法输出HLS</span>
        </div>
        <div class="alert alert-info fade in" id="txt_log">
            <button type="button" class="close" data-dismiss="alert">×</button>
            <strong><span id="txt_log_title">Usage:</span></strong>
            <span id="txt_log_msg">设置编码参数，点“发布视频”，允许Flash访问摄像头即可推流</span>
        </div>
        <div class="control-group">
            <div class="form-inline">
                <button class="btn" id="btn_video_settings">视频编码配置</button>
                <button class="btn" id="btn_audio_settings">音频编码配置</button>
            </div>
        </div>
        <div class="control-group">
            <div class="form-inline">
                发布地址:
                <input type="text" id="txt_url" class="input-xxlarge" value=""></input>
                <button class="btn btn-primary" id="btn_publish">发布视频</button>
                <label class="checkbox">
                    <input type="checkbox" id="cb_preview" checked>预览
                </label>
                <a id="txt_play_realtime" class="input-xxlarge" href="#">RTMP播放地址(请发布视频)</a>
            </div>
        </div>
        <div id="video_modal" class="modal hide fade">
            <div class="modal-header">
                <button type="button" class="close" data-dismiss="modal" aria-hidden="true">×</button>
                <h3>视频编码</h3>
            </div>
            <div class="modal-body">
                <div class="form-horizontal">
                    <div class="control-group">
                        <label class="control-label" for="sl_cameras">
                            摄像头
                            <a id="sl_cameras_tips" href="#" data-toggle="tooltip" data-placement="right" title="">
                                <img src="img/tooltip.png"/>
                            </a>
                        </label>
                        <div class="controls">
                            <select class="span4" id="sl_cameras"></select>
                        </div>
                    </div>
                    <div class="control-group">
                        <label class="control-label" for="sl_vcodec">
                            Codec
                            <a id="sl_cameras_tips" href="#" data-toggle="tooltip" data-placement="right" title="">
                                <img src="img/tooltip.png"/>
                            </a>
                        </label>
                        <div class="controls">
                            <select class="span2" id="sl_vcodec"></select>
                        </div>
                    </div>
                    <div class="control-group">
                        <label class="control-label" for="sl_profile">
                            Profile
                            <a id="sl_profile_tips" href="#" data-toggle="tooltip" data-placement="right" title="">
                                <img src="img/tooltip.png"/>
                            </a>
                        </label>
                        <div class="controls">
                            <select class="span2" id="sl_profile"></select>
                        </div>
                    </div>
                    <div class="control-group">
                        <label class="control-label" for="sl_level">
                            Level
                            <a id="sl_level_tips" href="#" data-toggle="tooltip" data-placement="right" title="">
                                <img src="img/tooltip.png"/>
                            </a>
                        </label>
                        <div class="controls">
                            <select class="span2" id="sl_level"></select>
                        </div>
                    </div>
                    <div class="control-group">
                        <label class="control-label" for="sl_gop">
                            GOP
                            <a id="sl_gop_tips" href="#" data-toggle="tooltip" data-placement="right" title="">
                                <img src="img/tooltip.png"/>
                            </a>
                        </label>
                        <div class="controls">
                            <select class="span2" id="sl_gop"></select>
                        </div>
                    </div>
                    <div class="control-group">
                        <label class="control-label" for="sl_size">
                            尺寸
                            <a id="sl_size_tips" href="#" data-toggle="tooltip" data-placement="right" title="">
                                <img src="img/tooltip.png"/>
                            </a>
                        </label>
                        <div class="controls">
                            <select class="span2" id="sl_size"></select>
                        </div>
                    </div>
                    <div class="control-group">
                        <label class="control-label" for="sl_fps">
                            帧率
                            <a id="sl_fps_tips" href="#" data-toggle="tooltip" data-placement="right" title="">
                                <img src="img/tooltip.png"/>
                            </a>
                        </label>
                        <div class="controls">
                            <select class="span2" id="sl_fps"></select>
                        </div>
                    </div>
                    <div class="control-group">
                        <label class="control-label" for="sl_bitrate">
                            码率
                            <a id="sl_bitrate_tips" href="#" data-toggle="tooltip" data-placement="right" title="">
                                <img src="img/tooltip.png"/>
                            </a>
                        </label>
                        <div class="controls">
                            <select class="span2" id="sl_bitrate"></select>
                        </div>
                    </div>
                </div>
            </div>
            <div class="modal-footer">
                <button class="btn btn-primary" data-dismiss="modal" aria-hidden="true">设置</button>
            </div>
        </div>
        <div id="audio_modal" class="modal hide fade">
            <div class="modal-header">
                <button type="button" class="close" data-dismiss="modal" aria-hidden="true">×</button>
                <h3>音频编码</h3>
            </div>
            <div class="modal-body">
                <div class="form-horizontal">
                    <div class="control-group">
                        <label class="control-label" for="sl_microphones">
                            麦克风
                            <a id="worker_id_tips" href="#" data-toggle="tooltip" data-placement="right" title="">
                                <img src="img/tooltip.png"/>
                            </a>
                        </label>
                        <div class="controls">
                            <select class="span4" id="sl_microphones"></select>
                        </div>
                    </div>
                    <div class="control-group">
                        <label class="control-label" for="sl_acodec">
                            编码
                            <a id="sl_acodec_tips" href="#" data-toggle="tooltip" data-placement="right" title="">
                                <img src="img/tooltip.png"/>
                            </a>
                        </label>
                        <div class="controls">
                            <select class="span2" id="sl_acodec"></select>
                        </div>
                    </div>
                </div>
            </div>
            <div class="modal-footer">
                <button class="btn btn-primary" data-dismiss="modal" aria-hidden="true">设置</button>
            </div>
        </div>
        <div class="container">
            <div class="row-fluid">
                <div class="span6">
                    <div class="accordion-group">
                        <div class="accordion-heading">
                            <span class="accordion-toggle">
                                <strong>本地摄像头</strong>
                            </span>
                        </div>
                        <div class="accordion-body collapse in">
                            <div class="accordion-inner">
                                <div id="local_publisher"></div>
                            </div>
                        </div>
                    </div>
                </div>
                <div class="span6">
                    <div class="accordion-group">
                        <div class="accordion-heading">
                            <span class="accordion-toggle">
                                <strong>远程服务器</strong>
                                <a id="low_latecy_tips" href="#" data-toggle="tooltip" data-placement="top" title="">
                                    低延时<img src="img/tooltip.png"/>
                                </a>
                                <a id="realtime_player_url" href="#" data-toggle="tooltip" data-placement="top" title="">
                                    播放地址<img src="img/tooltip.png"/>
                                </a>
                            </span>
                        </div>
                        <div class="accordion-body collapse in">
                            <div class="accordion-inner">
                                <div id="realtime_player"></div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <footer>
        <p></p>
        <p><a href="https://github.com/ossrs/srs">SRS Team &copy; 2013</a></p>
    </footer>
</div>
</body>
<script type="text/javascript" src="js/jquery-1.12.2.min.js"></script>
<script type="text/javascript" src="js/bootstrap.min.js"></script>
<script type="text/javascript" src="js/swfobject.js"></script>
<script type="text/javascript" src="js/json2.js"></script>
<script type="text/javascript" src="js/srs.page.js"></script>
<script type="text/javascript" src="js/srs.log.js"></script>
<script type="text/javascript" src="js/srs.player.js"></script>
<script type="text/javascript" src="js/srs.publisher.js"></script>
<script type="text/javascript" src="js/srs.utility.js"></script>
<script type="text/javascript" src="js/winlin.utility.js"></script>
<script type="text/javascript">
    var __on_flash_ready = null;

    $(function(){
        // 探测Flash是否正常启用。
        $('#main_flash_hdr').html(
            '\
            <object classid="clsid:d27cdb6e-ae6d-11cf-96b8-444553540000" width="100%" height="100%"> \
                <param name="movie" value="srs_player/release/srs_player.swf"> \
                <param name="quality" value="autohigh"> \
                <param name="swliveconnect" value="true"> \
                <param name="allowScriptAccess" value="always"> \
                <param name="bgcolor" value="#0"> \
                <param name="allowFullScreen" value="true"> \
                <param name="wmode" value="opaque"> \
                <param name="FlashVars" value="log=1"> \
                <param name="flashvars" value="id=1&on_player_ready=__on_flash_ready"> \
                <embed src="srs_player/release/srs_player.swf" width="100%" height="100%" \
                    quality="autohigh" bgcolor="#0" align="middle" allowfullscreen="true" allowscriptaccess="always" \
                    type="application/x-shockwave-flash" swliveconnect="true" wmode="opaque" \
                    flashvars="id=1&on_player_ready=__on_flash_ready" \
                    pluginspage="http://www.macromedia.com/go/getflashplayer"> \
            </object> \
            '
        );
        $('#main_flash_hdr').show();

        var showFlashHdr = setTimeout(function(){
            $('#main_flash_alert').show();
        }, 300);

        __on_flash_ready = function (id) {
            clearTimeout(showFlashHdr);

            $('#main_flash_alert').hide();
            $('#main_flash_hdr').hide();
            $('#main_content').show();

            autoLoadPage();
        };
    });
</script>
<script type="text/javascript">
    var srs_publisher = null;
    var realtime_player = null;

    var query = parse_query_string();
    var autoLoadPage = function() {
        // get the vhost and port to set the default url.
        // url set to: http://localhost:8080/live/livestream.flv
        srs_init_flv("#txt_url", null);

        if (query.agent == "true") {
            document.write(navigator.userAgent);
            return;
        }

        $("#btn_video_settings").click(function(){
            $("#video_modal").modal({show:true});
        });
        $("#btn_audio_settings").click(function(){
            $("#audio_modal").modal({show:true});
        });

        $("#low_latecy_tips").tooltip({
            title: "服务器不转码直接转发FLASH编码器的流，所以延迟比支持HLS的流要低很多"
        });
        $("#realtime_player_url").tooltip({
            title: "右键复制RTMP地址"
        });

        $("#btn_publish").click(on_user_publish);

        // for publish, we use randome stream name.
        $("#txt_url").val($("#txt_url").val() + "." + Number(parseInt(new Date().getTime()*Math.random()*100)).toString(16).slice(0, 7));

        // start the publisher.
        srs_publisher = new SrsPublisher("local_publisher", 430, 185);
        srs_publisher.on_publisher_ready = function(cameras, microphones) {
            srs_publisher_initialize_page(
                    cameras, microphones,
                    "#sl_cameras", "#sl_microphones",
                    "#sl_vcodec", "#sl_profile", "#sl_level", "#sl_gop", "#sl_size",
                    "#sl_fps", "#sl_bitrate",
                    "#sl_acodec"
            );
        };
        srs_publisher.on_publisher_error = function(code, desc) {
            if (!on_publish_stop()) {
                return;
            }
            error(code, desc + "请重试。");
        };
        srs_publisher.on_publisher_warn = function(code, desc) {
            warn(code, desc);
        };
        srs_publisher.start();

        // if no play specified, donot show the player, for debug the publisher.
        if (query.no_play != "true") {
            // start the realtime player.
            realtime_player = new SrsPlayer("realtime_player", 430, 185);
            realtime_player.on_player_ready = function() {
                this.set_bt(0.8);
            };
            realtime_player.on_player_metadata = function(metadata) {
                this.set_dar(0, 0);
                this.set_fs("screen", 100);
            }
            realtime_player.start();
        }
    };

    function on_publish_stop() {
        if (!srs_can_republish()) {
            $("#btn_join").attr("disabled", true);
            error(0, "您使用的浏览器很弱，请关闭页面后重新打开页面（刷新也不管用）。<br/>推荐使用Chrome浏览器，支持重试");

            srs_log_disabled = true;
            return false;
        }

        return true;
    }

    function update_play_url() {
        var url = $("#txt_url").val();
        var ret = srs_parse_rtmp_url(url);

        var remote_url = "rtmp://" + ret.server + ":" + ret.port + "/" + ret.app +  "/" + ret.stream + '?vhost=' + ret.vhost;
        $("#realtime_player_url").attr("href", url).attr("target", "_blank");

        var srs_player_rt_url = "http://" + query.host + query.dir + "/srs_player.html?";
        srs_player_rt_url += "vhost=" + ret.vhost + "&port=" + ret.port + "&app=" + ret.app + "&stream=" + ret.stream;
        srs_player_rt_url += "&autostart=true";

        $("#txt_play_realtime").text("RTMP播放地址").attr("href", srs_player_rt_url).attr("target", "_blank");
    }
    function on_user_publish() {
        if ($("#btn_publish").text() == "停止发布") {
            srs_publisher.stop();
            $("#btn_publish").text("发布视频");

            if (!on_publish_stop()) {
                return;
            }
            return;
        }

        $("#btn_publish").text("停止发布");

        update_play_url();

        var url = $("#txt_url").val();
        var vcodec = {};
        var acodec = {};
        srs_publiser_get_codec(
                vcodec, acodec,
                "#sl_cameras", "#sl_microphones",
                "#sl_vcodec", "#sl_profile", "#sl_level", "#sl_gop", "#sl_size",
                "#sl_fps", "#sl_bitrate",
                "#sl_acodec"
        );

        info("开始推流到服务器");
        srs_publisher.publish(url, vcodec, acodec);

        if (!$("#cb_preview").is(":checked")) {
            return;
        }

        if (realtime_player) {
            // directly play the url for the realtime player.
            realtime_player.stop();
            realtime_player.play(url);
        }
    }
    /**
     * get the vcodec and acodec.
     */
    function srs_publiser_get_codec(
        vcodec, acodec,
        sl_cameras, sl_microphones, sl_vcodec, sl_profile, sl_level, sl_gop, sl_size, sl_fps, sl_bitrate,
        sl_acodec
    ) {
        acodec.codec       = $(sl_acodec).val();
        acodec.device_code = $(sl_microphones).val();
        acodec.device_name = $(sl_microphones).text();

        vcodec.device_code = $(sl_cameras).find("option:selected").val();
        vcodec.device_name = $(sl_cameras).find("option:selected").text();

        vcodec.codec    = $(sl_vcodec).find("option:selected").val();
        vcodec.profile  = $(sl_profile).find("option:selected").val();
        vcodec.level    = $(sl_level).find("option:selected").val();
        vcodec.fps      = $(sl_fps).find("option:selected").val();
        vcodec.gop      = $(sl_gop).find("option:selected").val();
        vcodec.size     = $(sl_size).find("option:selected").val();
        vcodec.bitrate  = $(sl_bitrate).find("option:selected").val();
    }
    /**
     * when publisher ready, init the page elements.
     */
    function srs_publisher_initialize_page(
        cameras, microphones,
        sl_cameras, sl_microphones, sl_vcodec, sl_profile, sl_level, sl_gop, sl_size, sl_fps, sl_bitrate,
        sl_acodec
    ) {
        srs_initialize_codec_page(
            cameras, microphones,
            sl_cameras, sl_microphones, sl_vcodec, sl_profile, sl_level, sl_gop, sl_size, sl_fps, sl_bitrate,
            sl_acodec
        );

        //var profiles = ["baseline", "main"];
        $(sl_profile + " option[value='main']").attr("selected", true);

        //var levels = ["1", "1b", "1.1", "1.2", "1.3",
        //    "2", "2.1", "2.2", "3", "3.1", "3.2", "4", "4.1", "4.2", "5", "5.1"];
        $(sl_level + " option[value='4.1']").attr("selected", true);

        //var gops = ["0.3", "0.5", "1", "2", "3", "4",
        //    "5", "6", "7", "8", "9", "10", "15", "20"];
        $(sl_gop + " option[value='10']").attr("selected", true);

        //var sizes = ["176x144", "320x240", "352x240",
        //    "352x288", "480x360", "640x480", "720x480", "720x576", "800x600",
        //    "1024x768", "1280x720", "1360x768", "1920x1080"];
        $(sl_size + " option[value='640x480']").attr("selected", true);

        //var fpses = ["5", "10", "15", "20", "24", "25", "29.97", "30"];
        $(sl_fps + " option[value='20']").attr("selected", true);

        //var bitrates = ["50", "200", "350", "500", "650", "800",
        //    "950", "1000", "1200", "1500", "1800", "2000", "3000", "5000"];
        $(sl_bitrate + " option[value='500']").attr("selected", true);

        // speex
        $(sl_acodec + " option[value='speex']").attr("selected", true);
    }

    // without default values set.
    function srs_initialize_codec_page(
        cameras, microphones,
        sl_cameras, sl_microphones, sl_vcodec, sl_profile, sl_level, sl_gop, sl_size, sl_fps, sl_bitrate,
        sl_acodec
    ) {
        $(sl_cameras).empty();
        for (var i = 0; i < cameras.length; i++) {
            $(sl_cameras).append("<option value='" + i + "'>" + cameras[i] + "</option");
        }
        // optional: select the except matches
        matchs = ["virtual"];
        for (var i = 0; i < cameras.length; i++) {
            for (var j = 0; j < matchs.length; j++) {
                if (cameras[i].toLowerCase().indexOf(matchs[j]) == -1) {
                    $(sl_cameras + " option[value='" + i + "']").attr("selected", true);
                    break;
                }
            }
            if (j < matchs.length) {
                break;
            }
        }
        // optional: select the first matched.
        matchs = ["truevision", "integrated"];
        for (var i = 0; i < cameras.length; i++) {
            for (var j = 0; j < matchs.length; j++) {
                if (cameras[i].toLowerCase().indexOf(matchs[j]) >= 0) {
                    $(sl_cameras + " option[value='" + i + "']").attr("selected", true);
                    break;
                }
            }
            if (j < matchs.length) {
                break;
            }
        }

        $(sl_microphones).empty();
        for (var i = 0; i < microphones.length; i++) {
            $(sl_microphones).append("<option value='" + i + "'>" + microphones[i] + "</option");
        }
        // optional: select the except matches
        matchs = ["default"];
        for (var i = 0; i < microphones.length; i++) {
            for (var j = 0; j < matchs.length; j++) {
                if (microphones[i].toLowerCase().indexOf(matchs[j]) == -1) {
                    $(sl_microphones + " option[value='" + i + "']").attr("selected", true);
                    break;
                }
            }
            if (j < matchs.length) {
                break;
            }
        }
        // optional: select the first matched.
        matchs = ["realtek", "内置式麦克风"];
        for (var i = 0; i < microphones.length; i++) {
            for (var j = 0; j < matchs.length; j++) {
                if (microphones[i].toLowerCase().indexOf(matchs[j]) >= 0) {
                    $(sl_microphones + " option[value='" + i + "']").attr("selected", true);
                    break;
                }
            }
            if (j < matchs.length) {
                break;
            }
        }

        $(sl_vcodec).empty();
        var vcodecs = ["h264", "vp6"];
        vcodecs = ["h264"]; // h264 only.
        for (var i = 0; i < vcodecs.length; i++) {
            $(sl_vcodec).append("<option value='" + vcodecs[i] + "'>" + vcodecs[i] + "</option");
        }

        $(sl_profile).empty();
        var profiles = ["baseline", "main"];
        for (var i = 0; i < profiles.length; i++) {
            $(sl_profile).append("<option value='" + profiles[i] + "'>" + profiles[i] + "</option");
        }

        $(sl_level).empty();
        var levels = ["1", "1b", "1.1", "1.2", "1.3",
            "2", "2.1", "2.2", "3", "3.1", "3.2", "4", "4.1", "4.2", "5", "5.1"];
        for (var i = 0; i < levels.length; i++) {
            $(sl_level).append("<option value='" + levels[i] + "'>" + levels[i] + "</option");
        }

        $(sl_gop).empty();
        var gops = ["0.3", "0.5", "1", "2", "3", "4",
            "5", "6", "7", "8", "9", "10", "15", "20"];
        for (var i = 0; i < gops.length; i++) {
            $(sl_gop).append("<option value='" + gops[i] + "'>" + gops[i] + "秒</option");
        }

        $(sl_size).empty();
        var sizes = ["176x144", "320x240", "352x240",
            "352x288", "480x360", "640x480", "720x480", "720x576", "800x600",
            "1024x768", "1280x720", "1360x768", "1920x1080"];
        for (i = 0; i < sizes.length; i++) {
            $(sl_size).append("<option value='" + sizes[i] + "'>" + sizes[i] + "</option");
        }

        $(sl_fps).empty();
        var fpses = ["5", "10", "15", "20", "24", "25", "29.97", "30"];
        for (i = 0; i < fpses.length; i++) {
            $(sl_fps).append("<option value='" + fpses[i] + "'>" + Number(fpses[i]).toFixed(2) + " 帧/秒</option");
        }

        $(sl_bitrate).empty();
        var bitrates = ["50", "200", "350", "500", "650", "800",
            "950", "1000", "1200", "1500", "1800", "2000", "3000", "5000"];
        for (i = 0; i < bitrates.length; i++) {
            $(sl_bitrate).append("<option value='" + bitrates[i] + "'>" + bitrates[i] + " kbps</option");
        }

        $(sl_acodec).empty();
        var bitrates = ["speex", "nellymoser", "pcma", "pcmu"];
        for (i = 0; i < bitrates.length; i++) {
            $(sl_acodec).append("<option value='" + bitrates[i] + "'>" + bitrates[i] + "</option");
        }
    }

    // check whether can republish
    function srs_can_republish() {
        var browser = get_browser_agents();

        if (browser.Chrome || browser.Firefox) {
            return true;
        }

        if (browser.MSIE || browser.QQBrowser) {
            return false;
        }

        return false;
    }
</script>
</html>

