<!DOCTYPE html>
<html>
<head>
    <title>SRS</title>   
    <meta charset="utf-8">
    <link rel="stylesheet" type="text/css" href="css/bootstrap.min.css"/>
    <style>
        body{
            padding-top: 30px;
        }
        #main_modal {
            width: 700px;
            margin-left: -350px;
        }
    </style>
</head>
<body>
<img src='//ossrs.net/gif/v1/sls.gif?site=ossrs.net&path=/player/bwt'/>
<div class="navbar navbar-fixed-top">
    <div class="navbar-inner">
        <div class="container">
            <a id="srs_index" class="brand" href="#">SRS</a>
            <div class="nav-collapse collapse">
                <ul class="nav">
                    <li><a id="nav_srs_player" href="srs_player.html">LivePlayer</a></li>
                    <li><a id="nav_rtc_player" href="rtc_player.html">RTC播放器</a></li>
                    <li><a id="nav_rtc_publisher" href="rtc_publisher.html">RTC推流</a></li>
                    <li><a id="nav_srs_publisher" href="srs_publisher.html">SRS编码器</a></li>
                    <li><a id="nav_srs_chat" href="srs_chat.html">SRS会议</a></li>
                    <li class="active"><a id="nav_srs_bwt" href="srs_bwt.html">SRS测网速</a></li>
                    <li><a id="nav_vlc" href="vlc.html">VLC播放器</a></li>
                </ul>
            </div>
        </div>
    </div>
</div>
<div class="container">
    <div name="detect_flash">
        <div id="main_flash_alert" class="alert alert-danger fade in hide">
            <button type="button" class="close" data-dismiss="alert">×</button>
            <strong><p>Usage:</p></strong>
            <p>
                请点击下面的图标，启用Flash
            </p>
            <p>
                若没有见到这个图标，Chrome浏览器请打开
                <span class="text-info">chrome://settings/content/flash</span> 并修改为"Ask first"。
            </p>
        </div>
        <div id="main_flash_hdr" class="hide">
        </div>
    </div>

    <div id="main_content" class="hide">
        <div class="alert alert-info fade in">
            <button type="button" class="close" data-dismiss="alert">×</button>
            <strong><span>Usage:</span></strong> <span>点击“开始测速”即可测带宽，最大可测试带宽由服务器限制</span>
        </div>
         <div class="form-inline">
             URL:
             <input type="text" id="txt_url" class="input-xxlarge" value=""></input>
             <button class="btn btn-primary" id="btn_play">开始测速</button>
        </div>
         <div id="main_modal" class="modal hide fade">
             <div class="modal-header">
                 <button type="button" class="close" data-dismiss="modal" aria-hidden="true">×</button>
                 <h3>SRS Bandwidth Check</h3>
             </div>
             <div class="modal-body">
                <div class="row-fluid">
                    <div class="span1"></div>
                    <div class="span10">
                        <div class="progress progress-striped active" id="pb_buffer_bg">
                            <div class="bar" style="width: 0%;" id="progress_bar"></div>
                        </div>
                    </div>
                    <div class="span1"></div>
                </div>
                <div id="check_status">status</div>
                <div id="check_info">info</div>
             </div>
             <div class="modal-footer">
                 <button class="btn btn-primary" data-dismiss="modal" aria-hidden="true"> 关闭 </button>
             </div>
         </div>
    </div>

    <footer>
        <p></p>
        <p><a href="https://github.com/ossrs/srs">SRS Team &copy; 2013</a></p>
    </footer>
    <div class="container">
        <div id="player"></div>
    </div>
</div>
</body>
<script type="text/javascript" src="js/jquery-1.12.2.min.js"></script>
<script type="text/javascript" src="js/bootstrap.min.js"></script>
<script type="text/javascript" src="js/swfobject.js"></script>
<script type="text/javascript" src="js/srs.page.js"></script>
<script type="text/javascript" src="js/srs.log.js"></script>
<script type="text/javascript" src="js/srs.player.js"></script>
<script type="text/javascript" src="js/srs.publisher.js"></script>
<script type="text/javascript" src="js/srs.utility.js"></script>
<script type="text/javascript" src="js/winlin.utility.js"></script>
<script type="text/javascript" src="srs_bwt/src/srs.bandwidth.js"></script>
<script type="text/javascript">
    var __on_flash_ready = null;

    $(function(){
        // 探测Flash是否正常启用。
        $('#main_flash_hdr').html(
            '\
            <object classid="clsid:d27cdb6e-ae6d-11cf-96b8-444553540000" width="100%" height="100%"> \
                <param name="movie" value="srs_player/release/srs_player.swf"> \
                <param name="quality" value="autohigh"> \
                <param name="swliveconnect" value="true"> \
                <param name="allowScriptAccess" value="always"> \
                <param name="bgcolor" value="#0"> \
                <param name="allowFullScreen" value="true"> \
                <param name="wmode" value="opaque"> \
                <param name="FlashVars" value="log=1"> \
                <param name="flashvars" value="id=1&on_player_ready=__on_flash_ready"> \
                <embed src="srs_player/release/srs_player.swf" width="100%" height="100%" \
                    quality="autohigh" bgcolor="#0" align="middle" allowfullscreen="true" allowscriptaccess="always" \
                    type="application/x-shockwave-flash" swliveconnect="true" wmode="opaque" \
                    flashvars="id=1&on_player_ready=__on_flash_ready" \
                    pluginspage="http://www.macromedia.com/go/getflashplayer"> \
            </object> \
            '
        );
        $('#main_flash_hdr').show();

        var showFlashHdr = setTimeout(function(){
            $('#main_flash_alert').show();
        }, 300);

        __on_flash_ready = function (id) {
            clearTimeout(showFlashHdr);

            $('#main_flash_alert').hide();
            $('#main_flash_hdr').hide();
            $('#main_content').show();

            autoLoadPage();
        };
    });
</script>
<script type="text/javascript">
    var bandwidth = null;

    // for bw to init url
    // url: scheme://host:port/path?query#fragment
    function srs_init_bwt(rtmp_url, hls_url) {
        update_nav();

        if (rtmp_url) {
            $(rtmp_url).val(build_default_bandwidth_rtmp_url());
        }
    }
    // for the bandwidth tool to init page
    function build_default_bandwidth_rtmp_url() {
        var query = parse_query_string();

        var schema = 'rtmp';
        var server = (!query.server)? window.location.hostname:query.server;
        var port = (!query.port)? 1935:query.port;
        var vhost = "bandcheck.srs.com";
        var app = (!query.app)? "app":query.app;
        var key = (!query.key)? "35c9b402c12a7246868752e2878f7e0e":query.key;

        var uri = schema + "://" + server;
        if (!is_default_port(schema, port)) {
            uri += ":" + port;
        }
        uri += "/" + app + "?key=" + key + "&vhost=" + vhost;

        return uri;
    }

    var autoLoadPage = function() {
        srs_init_bwt("#txt_url");

        $("#btn_play").click(on_click_play);
        $("#main_modal").on("show", on_start_bandwidth_test);
        $("#main_modal").on("hide", on_stop_bandwidth_test);
    };

    function on_click_play() {
        $("#check_status").text("");
        $("#check_info").text("");
        $("#progress_bar").width("0%");
        $("#main_modal").modal({show:true, keyboard:false});
    }
    function on_start_bandwidth_test() {
        $("#div_container").remove();
        $("#progress_bar").removeClass("bar-danger");

        var div_container = $("<div/>");
        $(div_container).attr("id", "div_container");
        $("#player").append(div_container);

        var player = $("<div/>");
        $(player).attr("id", "player_id");
        $(div_container).append(player);

        var url = $("#txt_url").val();

        bandwidth = new SrsBandwidth("player_id", 100, 1);
        bandwidth.on_bandwidth_ready = function() {
            this.check_bandwidth(url);
        }
        bandwidth.on_update_progress = function(percent) {
            $("#progress_bar").width(percent + "%");
        }
        bandwidth.on_update_status = function(status) {
            $("#check_status").text(status);
        }
        bandwidth.on_srs_info = function(srs_server, srs_primary, srs_authors, srs_id, srs_pid, srs_server_ip) {
            $("#check_info").text(
                    "server:" + srs_server + ", srs_id:" + srs_id + ", srs_pid:" + srs_pid + ", ip:" + srs_server_ip
            );
        }
        bandwidth.on_error = function(code) {
            $("#progress_bar").addClass("bar-danger");
        }
        bandwidth.render(url);
    }
    function on_stop_bandwidth_test() {
        bandwidth.stop();
    }
</script>
</html>
