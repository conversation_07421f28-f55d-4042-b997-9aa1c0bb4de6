
<html>
<head>
    <title>SRS</title>
    <meta charset="utf-8">
    <style>
        .span6 {
            width: 480px;
        }
        .code {
            background-color: rgb(246 248 250);
            padding: 8px;
            overflow: auto;
            font-size: 85%;
            line-height: 1.45;
            border-radius: 6px;
            word-break: normal;
            word-wrap: normal;
            box-sizing: border-box;
            display: block;
            white-space: pre;
        }
    </style>
</head>
<body>
    <div>
        <h3><a href="https://github.com/ossrs/srs">SRS</a> works!</h3>
        <p>
            Click <a id="enConsole" href="#">here</a> to enter SRS console.<br/>
            点击进入<a id="cnConsole" href="#">SRS控制台</a>
        </p>
        <p>
            Publish stream by <a href="https://ffmpeg.org/download.html">FFmpeg</a> or <a href="https://obsproject.com/download">OBS</a>:<br/>
            请使用工具<a href="https://ffmpeg.org/download.html">FFmpeg</a>或者<a href="https://obsproject.com/download">OBS</a>推流到下面地址:
            <pre id="url" class="code span6"></pre>
        </p>
        <p>
            Click <a id="enPlayer" href="#">here</a> to start SRS player.<br/>
            点击进入<a id="cnPlayer" href="#">SRS播放器</a>
        </p>
        <p><a href="https://github.com/ossrs/srs">SRS Team &copy; 2022</a></p>
    </div>
    <script type="text/javascript">
        // Build RTMP url.
        if (true) {
            const rtmpUrl = `rtmp://${window.location.hostname}/live/livestream`;
            document.getElementById('url').innerText = rtmpUrl;
        }

        // Build console url.
        if (true) {
            // The prefix for default website.
            const prefix = `${window.location.protocol}//${window.location.host}`;
            // If not 8080, user should proxy to the default port.
            const query = parseInt(window.location.port) === 8080 ? `?port=1985` : '';
            const enUrl = `${prefix}/console/en_index.html#/summaries${query}`;
            const cnUrl = `${prefix}/console/ng_index.html#/summaries${query}`;
            document.getElementById("enConsole").setAttribute('href', enUrl);
            document.getElementById("cnConsole").setAttribute('href', cnUrl);
        }

        // The player url.
        if (true) {
            const prefix = `players/?schema=${window.location.protocol.replace(':', '')}`;
            const httpPort = window.location.port || (window.location.protocol === 'http:' ? 80 : 443);
            // If not 8080, user should proxy both stream and API to the default port.
            const query = parseInt(window.location.port) === 8080 ? '' : `&port=${httpPort}&api=${httpPort}`;
            document.getElementById("enPlayer").setAttribute('href', `${prefix}${query}`);
            document.getElementById("cnPlayer").setAttribute('href', `${prefix}${query}`);
        }
    </script>
</body>
