<div>
    <div class="accordion">
        <div class="accordion-group" ng-show="!support_raw_api">
            <div class="accordion-heading" sc-collapse="in">
                <a class="accordion-toggle" href="javascript:void(0)">
                    HTTP RAW API
                </a>
            </div>
            <div class="accordion-body collapse">
                <div class="accordion-inner">
                    <div class="alert alert-block alert-danger">
                        SRS 4.0不再支持HTTP RAW API。参考<a href='https://github.com/ossrs/srs/issues/2653'>#2653</a>。
                    </div>
                    <table class="table table-striped table-hover table-bordered">
                        <tr>
                            <th>Key</th>
                            <th>Value</th>
                            <th>Description</th>
                            <th>Opt</th>
                        </tr>
                        <tr sc-pretty scp-key="http_api.enabled" scp-value="http_api.enabled" scp-bool="true"
                            scp-desc="是否开启HTTP API，开启后就可以访问SRS提供的API管理服务器。默认: {{false| sc_filter_enabled}}">
                        </tr>
                        <tr sc-pretty scp-key="http_api.listen" scp-value="http_api.listen"
                            scp-desc="HTTP API的侦听地址，格式是&lt;[address:]port&gt;。默认: 1985">
                        </tr>
                        <tr sc-pretty scp-key="http_api.crossdomain" scp-value="http_api.crossdomain" scp-bool="true"
                            scp-desc="是否允许JS跨域，开启后JS可以直接跨域(还可以通过JSONP访问)。默认: {{true| sc_filter_enabled}}">
                        </tr>
                        <tr sc-pretty scp-key="http_api.raw_api.enabled" scp-value="http_api.raw_api.enabled" scp-bool="true"
                            scp-desc="是否开启HTTP RAW API，允许API修改服务器配置。默认: {{false| sc_filter_enabled}}">
                        </tr>
                        <tr sc-pretty scp-key="http_api.raw_api.allow_reload" scp-value="http_api.raw_api.allow_reload" scp-bool="true"
                            scp-desc="是否允许API进行Reload操作。默认: {{false| sc_filter_enabled}}">
                        </tr>
                        <tr sc-pretty scp-key="http_api.raw_api.allow_query" scp-value="http_api.raw_api.allow_query" scp-bool="true"
                            scp-desc="是否允许API进行Query查询操作。默认: {{false| sc_filter_enabled}}">
                        </tr>
                        <tr sc-pretty scp-key="http_api.raw_api.allow_update" scp-value="http_api.raw_api.allow_update" scp-bool="true"
                            scp-desc="是否允许API进行Update更新操作。默认: {{false| sc_filter_enabled}}">
                        </tr>
                    </table>
                </div>
            </div>
        </div>
    </div>
</div>