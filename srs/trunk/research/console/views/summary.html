<div>
    <div class="accordion">
        <div class="accordion-group">
            <div class="accordion-heading" sc-collapse="in">
                <a class="accordion-toggle" href="javascript:void(0)">
                    服务器基本信息
                </a>
            </div>
            <div id="collapseOne" class="accordion-body collapse">
                <div class="accordion-inner">
                    <div class="row">
                        <div class="span3">
                            <table class="table table-bordered" ng-if="server">
                                <tr>
                                    <th colspan="2">SRS/{{server.version}}</th>
                                </tr>
                                <tr>
                                    <td>运行</td>
                                    <td>{{server.srs_uptime| sc_filter_time}}</td>
                                </tr>
                                <tr>
                                    <td>CPU</td>
                                    <td>{{server.cpu_percent| sc_filter_percentf}} / {{system.cpus_online| sc_filter_percentf}}</td>
                                </tr>
                                <tr>
                                    <td>内存</td>
                                    <td>
                                        {{server.mem_percent| sc_filter_percentf2}}
                                        {{server.mem_kbyte| sc_filter_filesize_k2}} / {{system.mem_ram_kbyte| sc_filter_filesize_k2}}
                                    </td>
                                </tr>
                                <tr>
                                    <td>网络</td>
                                    <td>
                                        <span title="SRS的连接数">{{system.conn_srs}}</span> /
                                        <span title="SRS的入网带宽，即客户端上传带宽">{{kbps.in.srs| sc_filter_bitrate_k2}}</span> /
                                        <span title="SRS的出网带宽，即客户端下载带宽">{{kbps.out.srs| sc_filter_bitrate_k2}}</span>
                                    </td>
                                </tr>
                            </table>
                        </div>
                        <div class="span3">
                            <table class="table table-bordered" ng-if="system">
                                <tr>
                                    <th colspan="2">OS信息</th>
                                </tr>
                                <tr>
                                    <td>运行</td>
                                    <td>{{system.uptime| sc_filter_time}}</td>
                                </tr>
                                <tr>
                                    <td>CPU</td>
                                    <td>{{system.cpu_percent * system.cpus_online| sc_filter_percentf}} / {{system.cpus_online| sc_filter_percentf}}</td>
                                </tr>
                                <tr>
                                    <td>内存</td>
                                    <td>
                                        {{system.mem_ram_percent| sc_filter_percentf2}}
                                        {{system.mem_ram_kbyte * system.mem_ram_percent| sc_filter_filesize_k2}} / {{system.mem_ram_kbyte| sc_filter_filesize_k2}}
                                    </td>
                                </tr>
                                <tr>
                                    <td>负载</td>
                                    <td>{{system.load_1m| sc_filter_number}} / {{system.load_5m| sc_filter_number}} / {{system.load_15m| sc_filter_number}}</td>
                                </tr>
                            </table>
                        </div>
                        <div class="span3">
                            <table class="table table-bordered" ng-if="system">
                                <tr>
                                    <th colspan="2">负载信息</th>
                                </tr>
                                <tr>
                                    <td>外网</td>
                                    <td>
                                        <span title="系统外网的入网带宽，即客户端上传带宽">{{kbps.in.sys| sc_filter_bitrate_k2}}</span> /
                                        <span title="系统外网的出网带宽，即客户端下载带宽">{{kbps.out.sys| sc_filter_bitrate_k2}}</span>
                                    </td>
                                </tr>
                                <tr>
                                    <td>内网</td>
                                    <td>
                                        <span title="系统内网的入网带宽，即客户端上传带宽">{{kbps.in.inner| sc_filter_bitrate_k2}}</span> /
                                        <span title="系统内网的出网带宽，即客户端下载带宽">{{kbps.out.inner| sc_filter_bitrate_k2}}</span>
                                    </td>
                                </tr>
                                <tr>
                                    <td>连接</td>
                                    <td>
                                        <span title="系统总连接数">{{system.conn_sys}}</span>
                                        <span title="系统ESTABLISHED状态的连接数">{{system.conn_sys_et}}</span>
                                        <span title="系统TIME_WAIT状态的连接数">{{system.conn_sys_tw}}</span>
                                        <span title="系统UDP绑定端口">{{system.conn_sys_udp}}</span>
                                    </td>
                                </tr>
                                <tr>
                                    <td>磁盘</td>
                                    <td>
                                        <span title="IO繁忙度">{{system.disk_busy_percent| sc_filter_percentf2}}</span>
                                        <span title="磁盘读取速度Bps">{{system.disk_read_KBps| sc_filter_filerate_k2}}</span>
                                        <span title="磁盘写入速度Bps">{{system.disk_write_KBps| sc_filter_filerate_k2}}</span>
                                    </td>
                                </tr>
                            </table>
                        </div>
                        <div class="span2">
                            <table class="table table-bordered" ng-if="system">
                                <tr>
                                    <th colspan="2">其他信息</th>
                                </tr>
                                <tr>
                                    <td title="系统CPU信息">CPU</td>
                                    <td>
                                        <span title="CPU核心数">{{system.cpus}}</span> /
                                        <span title="在线CPU核心数">{{system.cpus_online}}</span>
                                    </td>
                                </tr>
                                <tr>
                                    <td title="SRS的PID">PID</td>
                                    <td>{{server.pid}}</td>
                                </tr>
                                <tr>
                                    <td title="SRS的父PID">PPID</td>
                                    <td>{{server.ppid}}</td>
                                </tr>
                                <tr>
                                    <td title="SRS API是否有效">Ready</td>
                                    <td>{{global.ok| sc_filter_yesno}}</td>
                                </tr>
                            </table>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>