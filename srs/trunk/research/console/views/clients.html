<div>
    <div class="accordion">
        <div class="accordion-group">
            <div class="accordion-heading" sc-collapse="in">
                <a class="accordion-toggle" href="javascript:void(0)">
                    客户端(Clients)列表
                </a>
            </div>
            <div id="collapseOne" class="accordion-body collapse">
                <div class="accordion-inner">
                    <table class="table table-striped table-bordered">
                        <tr>
                            <th>ID</th>
                            <th>IP</th>
                            <th>Vhost</th>
                            <th>Stream</th>
                            <th>类型</th>
                            <th>时长</th>
                            <th>URL</th>
                            <th>分类</th>
                            <th>管理</th>
                        </tr>
                        <tr ng-repeat="client in clients">
                            <td><a href="javascript:void(0)" ng-click="gogogo('/clients/' + client.id)">{{client.id}}</a></td>
                            <td>{{client.ip}}</td>
                            <td><a href="javascript:void(0)" ng-click="gogogo('/vhosts/' + client.vhost)">{{client.vhost}}</a></td>
                            <td><a href="javascript:void(0)" ng-click="gogogo('/streams/' + client.stream)">{{client.stream}}</a></td>
                            <td>{{client.publish| sc_filter_ctype}}</td>
                            <td>{{client.alive| sc_filter_time}}</td>
                            <td>{{client |sc_filter_streamURL}}</td>
                            <td>{{client.type}}</td>
                            <td>
                                <a ng-click="kickoff(client)" href="javascript:void(0)">踢Ta</a>
                            </td>
                        </tr>
                    </table>
                </div>
            </div>
        </div>
    </div>
</div>