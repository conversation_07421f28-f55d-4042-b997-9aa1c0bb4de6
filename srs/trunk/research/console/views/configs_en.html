<div>
    <div class="accordion">
        <div class="accordion-group" ng-show="!support_raw_api">
            <div class="accordion-heading" sc-collapse="in">
                <a class="accordion-toggle" href="javascript:void(0)">
                    HTTP RAW API
                </a>
            </div>
            <div class="accordion-body collapse">
                <div class="accordion-inner">
                    <div class="alert alert-block alert-danger">
                        HTTP RAW API is removed, please read <a href='https://github.com/ossrs/srs/issues/2653'>#2653</a>。
                    </div>
                    <table class="table table-striped table-hover table-bordered">
                        <tr>
                            <th>Key</th>
                            <th>Value</th>
                            <th>Description</th>
                            <th>Opt</th>
                        </tr>
                        <tr sc-pretty scp-key="http_api.enabled" scp-value="http_api.enabled" scp-bool="true"
                            scp-desc="Whether enable HTTP API. Default is {{false| sc_filter_enabled}}">
                        </tr>
                        <tr sc-pretty scp-key="http_api.listen" scp-value="http_api.listen"
                            scp-desc="The listen port for HTTP API, format is &lt;[address:]port&gt;. Default is 1985">
                        </tr>
                        <tr sc-pretty scp-key="http_api.crossdomain" scp-value="http_api.crossdomain" scp-bool="true"
                            scp-desc="Whether allow js CORS(JSONP). Default is {{true| sc_filter_enabled}}">
                        </tr>
                        <tr sc-pretty scp-key="http_api.raw_api.enabled" scp-value="http_api.raw_api.enabled" scp-bool="true"
                            scp-desc="Whether enable HTTP RAW API. Default is {{false| sc_filter_enabled}}">
                        </tr>
                        <tr sc-pretty scp-key="http_api.raw_api.allow_reload" scp-value="http_api.raw_api.allow_reload" scp-bool="true"
                            scp-desc="Whether allow HTTP RAW API to reload. Default is {{false| sc_filter_enabled}}">
                        </tr>
                        <tr sc-pretty scp-key="http_api.raw_api.allow_query" scp-value="http_api.raw_api.allow_query" scp-bool="true"
                            scp-desc="Whether allow HTTP RAW API to query. Default is {{false| sc_filter_enabled}}">
                        </tr>
                        <tr sc-pretty scp-key="http_api.raw_api.allow_update" scp-value="http_api.raw_api.allow_update" scp-bool="true"
                            scp-desc="Whether allow HTTP RAW API to update. Default is {{false| sc_filter_enabled}}">
                        </tr>
                    </table>
                </div>
            </div>
        </div>
    </div>
</div>