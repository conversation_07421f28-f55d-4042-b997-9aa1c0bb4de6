<div>
    <div class="accordion">
        <div class="accordion-group">
            <div class="accordion-heading" sc-collapse="in">
                <a class="accordion-toggle" href="javascript:void(0)">
                    System Streams
                </a>
            </div>
            <div id="collapseOne" class="accordion-body collapse">
                <div class="accordion-inner">
                    <table class="table table-striped table-bordered">
                        <tr>
                            <th>ID</th>
                            <th>Name</th>
                            <th>URL</th>
                            <th>Vhost</th>
                            <th>Status</th>
                            <th>Clients</th>
                            <th>Inbound</th>
                            <th>Outbound</th>
                            <th>Video</th>
                            <th>Audio</th>
                            <th>Manage</th>
                        </tr>
                        <tr ng-repeat="stream in streams">
                            <td><a href="javascript:void(0)" ng-click="gogogo('/streams/' + stream.id)">{{stream.id}}</a></td>
                            <td>{{stream.name| sc_filter_less}}</td>
                            <td>{{stream |sc_filter_streamURL}}</td>
                            <td><a href="javascript:void(0)" ng-click="gogogo('/vhosts/' + stream.vhost)">{{stream.owner.name}}</a></td>
                            <td>{{stream.publish.active| sc_filter_has_stream}}</td>
                            <td>{{stream.clients}}人</td>
                            <td>{{stream.kbps.recv_30s| sc_filter_bitrate_k}}</td>
                            <td>{{stream.kbps.send_30s| sc_filter_bitrate_k}}</td>
                            <td><span ng-if="stream.video">{{stream.video| sc_filter_video}}</span></td>
                            <td><span ng-if="stream.audio">{{stream.audio| sc_filter_audio}}</span></td>
                            <td>
                                <a ng-href="{{stream| sc_filter_preview_url}}" target="_blank">Preview</a>
                                <a ng-show="stream.publish.active" ng-click="kickoff(stream)" href="javascript:void(0)">Kickoff</a>
                                <span ng-show="support_raw_api">
                                    <a href="javascript:void(0)" ng-click="dvr(stream)">Record</a>
                                </span>
                            </td>
                        </tr>
                    </table>
                </div>
            </div>
        </div>
    </div>
</div>