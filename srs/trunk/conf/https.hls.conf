# the config for srs to delivery hls
# @see https://github.com/ossrs/srs/issues/1657#issuecomment-722971676
# @see full.conf for detail config.

listen              1935;
max_connections     1000;
daemon              off;
srs_log_tank        console;
http_server {
    enabled         on;
    listen          8080;
    dir             ./objs/nginx/html;
    https {
        enabled on;
        listen 8088;
        key ./conf/server.key;
        cert ./conf/server.crt;
    }
}
vhost __defaultVhost__ {
    hls {
        enabled         on;
        hls_fragment    10;
        hls_window      60;
        hls_path        ./objs/nginx/html;
        hls_m3u8_file   [app]/[stream].m3u8;
        hls_ts_file     [app]/[stream]-[seq].ts;
    }
}
