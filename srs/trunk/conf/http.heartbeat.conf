# the config for srs http heartbeat, report its info to api-server
# @see full.conf for detail config.

listen              1935
max_connections     1000;
daemon              off;
srs_log_tank        console;
heartbeat {
    enabled         on;
    interval        9.3;
    # for python api-server
    url             http://127.0.0.1:8085/api/v1/servers;
    device_id       "my-srs-device";
    # for ossrs.net monitor, device_id is the key genereated by bsm.
    #url             http://www.ossrs.net:1977/api/v1/robots/servers;
    #device_id       "35c9b402c12a7246868752e2878f7e0e";
    # with detail summaries
    summaries       on;
}
stats {
    network         0;
    disk            sda sdb xvda xvdb;
}
vhost __defaultVhost__ {
}
