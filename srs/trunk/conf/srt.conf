# SRT config.

listen              1935;
max_connections     1000;
daemon              off;
srs_log_tank        console;

http_api {
    enabled         on;
    listen          1985;
}

http_server {
    enabled         on;
    listen          8080;
    dir             ./objs/nginx/html;
}

srt_server {
    enabled on;
    listen 10080;
    maxbw 1000000000;
    connect_timeout 4000;
    peerlatency 0;
    recvlatency 0;
    latency 0;
    tsbpdmode off;
    tlpktdrop off;
    sendbuf 2000000;
    recvbuf 2000000;
}

# @doc https://github.com/ossrs/srs/issues/1147#issuecomment-577607026
vhost __defaultVhost__ {
    srt {
        enabled     on;
        srt_to_rtmp on;
    }

    http_remux {
        enabled     on;
        mount       [vhost]/[app]/[stream].flv;
    }
}

# For SRT to use vhost.
vhost srs.srt.com.cn {
}

stats {
    network         0;
    disk            sda sdb xvda xvdb;
}
