# the config for srs to dvr in session mode
# @see https://ossrs.net/lts/zh-cn/docs/v4/doc/dvr
# @see full.conf for detail config.

listen              1935;
max_connections     1000;
daemon              off;
srs_log_tank        console;
vhost __defaultVhost__ {
    dvr {
        enabled      on;
        dvr_path     ./objs/nginx/html/[app]/[stream].[timestamp].flv;
        dvr_plan     session;
    }
}
