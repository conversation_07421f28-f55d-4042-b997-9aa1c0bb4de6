# the config for srs to delivery dash
# @see https://ossrs.net/lts/zh-cn/docs/v4/doc/sample-dash
# @see full.conf for detail config.

listen              1935;
max_connections     1000;
daemon              off;
srs_log_tank        console;
http_server {
    enabled         on;
    listen          8080;
    dir             ./objs/nginx/html;
}
vhost __defaultVhost__ {
    dash {
        enabled         on;
        dash_fragment       10;
        dash_update_period  10;
        dash_window_size    3;
        dash_path           ./objs/nginx/html;
        dash_mpd_file       [app]/[stream].mpd;
    }
}
