
listen              1935;
max_connections     1000;

# For clion, use console as output.
daemon off; 
srs_log_tank console;

stream_caster {
    enabled on;
    caster gb28181;
    output rtmp://127.0.0.1/live/[stream];
    listen 9000;
    sip {
        enabled on;
        listen 5060;
        timeout 2.1;
        reinvite 1.2;
    }
}

http_server {
    enabled         on;
    listen          8080;
    dir             ./objs/nginx/html;
}

http_api {
    enabled         on;
    listen          1985;
}
stats {
    network         0;
}
rtc_server {
    enabled         on;
    listen          8000;
    candidate       $CANDIDATE;
}

vhost __defaultVhost__ {
    rtc {
        enabled     on;
        rtmp_to_rtc on;
        keep_bframe off;
        rtc_to_rtmp on;
    }
    play {
        atc         on;
    }
    http_remux {
        enabled     on;
        mount       [vhost]/[app]/[stream].flv;
        drop_if_not_match on;
    }
    ingest livestream {
        enabled on;
        input {
            type    file;
            url     ./doc/source.200kbps.768x320.flv;
        }
        ffmpeg      ./objs/ffmpeg/bin/ffmpeg;
        engine {
            enabled          off;
            output          rtmp://127.0.0.1:[port]/live/livestream;
        }
    }
}

