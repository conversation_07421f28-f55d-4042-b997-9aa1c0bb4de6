# the config for srs to remux rtmp to flv live stream.
# @see https://github.com/ossrs/srs/issues/1657#issuecomment-722971676
# @see full.conf for detail config.

listen              1935;
max_connections     1000;
daemon              off;
srs_log_tank        console;
http_server {
    enabled         on;
    listen          8080;
    dir             ./objs/nginx/html;
    https {
        enabled on;
        listen 8088;
        key ./conf/server.key;
        cert ./conf/server.crt;
    }
}
vhost __defaultVhost__ {
    http_remux {
        enabled     on;
        mount       [vhost]/[app]/[stream].flv;
    }
}
