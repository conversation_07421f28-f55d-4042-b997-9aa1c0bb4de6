# the config for srs to remux rtmp to mp3 live stream.
# @see https://ossrs.net/lts/zh-cn/docs/v4/doc/delivery-http-flv
# @see full.conf for detail config.

listen              1935;
max_connections     1000;
daemon              off;
srs_log_tank        console;
http_server {
    enabled         on;
    listen          8080;
    dir             ./objs/nginx/html;
}
vhost __defaultVhost__ {
    http_remux {
        enabled     on;
        fast_cache  30;
        mount       [vhost]/[app]/[stream].mp3;
    }
}
