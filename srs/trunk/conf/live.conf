# Live streaming config for SRS.
# @see full.conf for detail config.

listen              1935;
max_connections     1000;
srs_log_tank        console;
daemon              off;

http_api {
    enabled         on;
    listen          1985;
}

http_server {
    enabled         on;
    listen          8080;
    dir             ./objs/nginx/html;
}

vhost __defaultVhost__ {
    hls {
        enabled         on;
    }
    http_remux {
        enabled     on;
        mount       [vhost]/[app]/[stream].flv;
    }
}
