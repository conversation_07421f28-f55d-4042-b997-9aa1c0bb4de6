# use ffmpeg to ingest file/stream/device to SRS
# @see https://ossrs.net/lts/zh-cn/docs/v4/doc/sample-ingest
# @see full.conf for detail config.

listen              1935;
max_connections     1000;
daemon              off;
srs_log_tank        console;
vhost __defaultVhost__ {
    ingest livestream {
        enabled      on;
        input {
            type    file;
            url     ./doc/source.200kbps.768x320.flv;
        }
        ffmpeg      ./objs/ffmpeg/bin/ffmpeg;
        engine {
            enabled          off;
            output          rtmp://127.0.0.1:[port]/live/livestream?vhost=[vhost];
        }
    }
}
