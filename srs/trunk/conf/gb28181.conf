
listen 1935;
max_connections 1000;
daemon off;
srs_log_tank console;

stream_caster {
    enabled on;
    caster gb28181;
    output rtmp://127.0.0.1/live/[stream];
    listen 9000;
    sip {
        enabled on;
        listen 5060;
        # @see https://ossrs.net/lts/zh-cn/docs/v5/doc/gb28181#config-candidate
        candidate $CANDIDATE;
    }
}

http_server {
    enabled on;
    listen 8080;
    dir ./objs/nginx/html;
}

http_api {
    enabled on;
    listen 1985;
}
stats {
    network 0;
}
rtc_server {
    enabled on;
    listen 8000; # UDP port
    # @see https://ossrs.net/lts/zh-cn/docs/v4/doc/webrtc#config-candidate
    candidate $CANDIDATE;
}

vhost __defaultVhost__ {
    rtc {
        enabled on;
        # @see https://ossrs.net/lts/zh-cn/docs/v4/doc/webrtc#rtmp-to-rtc
        rtmp_to_rtc on;
        # @see https://ossrs.net/lts/zh-cn/docs/v4/doc/webrtc#rtc-to-rtmp
        rtc_to_rtmp on;
    }
    http_remux {
        enabled on;
        mount [vhost]/[app]/[stream].flv;
    }
    hls {
        enabled on;
    }
}

