listen 1935;
pid ./objs/srs.pid;
srs_log_tank console;
srs_log_level trace;
max_connections 1000;
daemon off;
http_api {
    enabled on;
    listen 1985;
    crossdomain on;
    raw_api {
        enabled on;
        allow_reload on;
        allow_query on;
        allow_update on;
    }
}
# for SRS1.
http_stream {
    enabled on;
    listen 8080;
    dir ./objs/nginx/html;
}
vhost __defaultVhost__ {
    # for SRS1.
    http {
        enabled on;
        mount [vhost]/hls;
        dir ./objs/nginx/html/hls;
    }

    # for SRS1.
    refer github.com github.io;
    refer_publish github.com github.io;
    refer_play github.com github.io;
    
    # for SRS2
    publish_1stpkt_timeout  20000;
    publish_normal_timeout  7000;
    
    # for SRS2
    mr {
        enabled off;
        latency 350;
    }
    
    # for SRS1
    mode remote;
    origin 127.0.0.1:1935 localhost:1935;
    
    token_traverse off;
    vhost same.edge.srs.com;

    debug_srs_upnode off;
    
    # for SRS1
    forward 127.0.0.1:1936 127.0.0.1:1937;
    
    # for SRS1
    time_jitter full;
    
    # for SRS2
    mix_correct off;

    #for SRS1
    atc on;
    atc_auto on;
    
    # for SRS2
    mw_latency 100;
    
    # for SRS1
    gop_cache off;
    queue_length 10;
    
    # for SRS2
    send_min_interval 10.0;
    reduce_sequence_header on;
}
