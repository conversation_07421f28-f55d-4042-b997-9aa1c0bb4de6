# the config for srs to remux rtmp to flv live stream.
# @see https://ossrs.net/lts/zh-cn/docs/v4/doc/delivery-http-flv
# @see full.conf for detail config.

listen              19352;
max_connections     1000;
pid                 objs/srs.flv.19352.pid;
daemon              off;
srs_log_tank        console;
http_server {
    enabled         on;
    listen          8082;
    dir             ./objs/nginx/html;
}
vhost __defaultVhost__ {
    cluster {
        mode remote;
        origin 127.0.0.1;
    }
    http_remux {
        enabled     on;
        mount       [vhost]/[app]/[stream].flv;
    }
}
