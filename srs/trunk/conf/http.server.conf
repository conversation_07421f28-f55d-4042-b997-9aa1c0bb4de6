# the config for srs to serve as http server
# @see full.conf for detail config.

listen              1935;
max_connections     1000;
daemon              off;
srs_log_tank        console;
http_server {
    enabled         on;
    listen          8080;
    dir             ./objs/nginx/html;
}
vhost ossrs.net {
    http_static {
        enabled     on;
        mount       [vhost]/;
        dir         ./objs/nginx/html;
    }
}
