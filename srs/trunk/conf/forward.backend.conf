# the config for srs to forward to slave service
# @see https://ossrs.net/lts/zh-cn/docs/v4/doc/sample-forward
# @see full.conf for detail config.

listen              1935;
max_connections     1000;
pid                 ./objs/srs.backend.pid;
daemon              off;
srs_log_tank        console;
vhost __defaultVhost__ {
    forward {
        enabled on;
        backend http://127.0.0.1:8085/api/v1/forward;
    }
}
