# push HTTP FLV to SRS.
# @see https://ossrs.net/lts/zh-cn/docs/v4/doc/streamer#push-http-flv-to-srs
# @see full.conf for detail config.

listen              1935;
max_connections     1000;
daemon              off;
srs_log_tank        console;
stream_caster {
    enabled         on;
    caster          flv;
    output          rtmp://127.0.0.1/[app]/[stream];
    listen          8936;
}
http_server {
    enabled         on;
    listen          8080;
    dir             ./objs/nginx/html;
}
http_api {
    enabled on;
    listen 1985;
}
rtc_server {
    enabled on;
    listen 8000; # UDP port
    # @see https://ossrs.net/lts/zh-cn/docs/v4/doc/webrtc#config-candidate
    candidate $CANDIDATE;
}
vhost __defaultVhost__ {
    rtc {
        enabled on;
        # @see https://ossrs.net/lts/zh-cn/docs/v4/doc/webrtc#rtmp-to-rtc
        rtmp_to_rtc on;
        # @see https://ossrs.net/lts/zh-cn/docs/v4/doc/webrtc#rtc-to-rtmp
        rtc_to_rtmp on;
    }
    http_remux {
        enabled on;
        mount [vhost]/[app]/[stream].flv;
    }
    hls {
        enabled on;
    }
}
