# the config for srs to delivery realtime RTMP stream
# @see https://ossrs.net/lts/zh-cn/docs/v4/doc/sample-realtime
# @see full.conf for detail config.

listen              1935;
max_connections     1000;
daemon              off;
srs_log_tank        console;
http_server {
    enabled         on;
    listen          8080;
    dir             ./objs/nginx/html;
}
vhost __defaultVhost__ {
    hls {
        enabled         on;
        hls_fragment    2;
        hls_window      10;
    }
}
