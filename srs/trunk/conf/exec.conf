# the config for srs to support nginx-rtmp exec.
# @see https://ossrs.net/lts/zh-cn/docs/v4/doc/nginx-exec
# @see full.conf for detail config.

listen              1935;
max_connections     1000;
daemon              off;
srs_log_tank        console;
vhost __defaultVhost__ {
    exec {
        enabled     on;
        publish     ./objs/ffmpeg/bin/ffmpeg -f flv -i [url] -c copy -y ./[stream].flv;
    }
}
