# the config for srs demo
# @see full.conf for detail config.

listen              1935;
max_connections     1000;
daemon              off;
srs_log_tank        console;
pid                 ./objs/srs.demo.pid;

chunk_size          60000;
max_connections     2000;

http_api {
    enabled         on;
    listen          1985;
}

http_server {
    enabled         on;
    listen          8080;
    dir             ./objs/nginx/html;
}

vhost __defaultVhost__ {
    enabled         on;
    gop_cache       on;
}

vhost demo.srs.com {
    chunk_size      60000;
    enabled         on;
    gop_cache       on;
    queue_length    30;
    forward {
        enabled on;
        destination 127.0.0.1:19350;
    }
    hls {
        enabled         on;
        hls_path        ./objs/nginx/html;
        hls_fragment    5;
        hls_window      30;
    }
    http_hooks {
        enabled         on;
        on_publish      http://127.0.0.1:8085/api/v1/streams;
        on_unpublish    http://127.0.0.1:8085/api/v1/streams;
        on_play         http://127.0.0.1:8085/api/v1/sessions;
        on_stop         http://127.0.0.1:8085/api/v1/sessions;
    }
    transcode {
        enabled         on;
        ffmpeg          ./objs/ffmpeg/bin/ffmpeg;
        engine ld {
            enabled         on;
            vfilter {
                i               ./doc/srs-logo.png;
                filter_complex      'overlay=10:10';
            }
            vcodec          libx264;
            vbitrate        300;
            vfps            20;
            vwidth          768;
            vheight         320;
            vthreads        1;
            vprofile        baseline;
            vpreset         superfast;
            vparams {
            }
            acodec          libfdk_aac;
            abitrate        45;
            asample_rate    44100;
            achannels       2;
            aparams {
            }
            output          rtmp://127.0.0.1:[port]/[app]/[stream]_[engine]?vhost=[vhost];
        }
        engine sd {
            enabled         on;
            vfilter {
                vf                  'split [main][tmp]; [tmp] crop=iw:ih/2:0:0, vflip [flip]; [main][flip] overlay=0:H/2';
            }
            vcodec          libx264;
            vbitrate        500;
            vfps            20;
            vwidth          768;
            vheight         320;
            vthreads        1;
            vprofile        main;
            vpreset         fast;
            vparams {
            }
            acodec          libfdk_aac;
            abitrate        40;
            asample_rate    44100;
            achannels       2;
            aparams {
            }
            output          rtmp://127.0.0.1:[port]/[app]/[stream]_[engine]?vhost=[vhost];
        }
    }
    ingest {
        enabled      on;
        input {
            type    file;
            url     ./doc/source.200kbps.768x320.flv;
        }
        ffmpeg      ./objs/ffmpeg/bin/ffmpeg;
        engine {
            enabled          off;
            output          rtmp://127.0.0.1:[port]/live/livestream?vhost=[vhost];
        }
    }
}

vhost players {
    enabled         on;
    gop_cache       on;
    transcode {
        enabled         on;
        ffmpeg          ./objs/ffmpeg/bin/ffmpeg;
        engine hls {
            enabled         on;
            vfilter {
                i               ./doc/srs-logo.png;
                filter_complex      'overlay=10:10';
            }
            vcodec          libx264;
            vbitrate        300;
            vfps            20;
            vwidth          768;
            vheight         320;
            vthreads        1;
            vprofile        baseline;
            vpreset         superfast;
            vparams {
                g           100;
            }
            acodec          libfdk_aac;
            abitrate        30;
            asample_rate    44100;
            achannels       2;
            aparams {
            }
            output          rtmp://127.0.0.1:[port]/[app]?vhost=players_pub/[stream];
        }
    }
    ingest {
        enabled      on;
        input {
            type    file;
            url     ./doc/source.200kbps.768x320.flv;
        }
        ffmpeg      ./objs/ffmpeg/bin/ffmpeg;
        engine {
            enabled          off;
            output          rtmp://127.0.0.1:[port]/live/demo?vhost=[vhost];
        }
    }
}
vhost players_pub {
    hls {
        enabled         on;
        hls_path        ./objs/nginx/html;
        hls_fragment    5;
        hls_window      30;
    }
}

vhost players_chat {
    gop_cache       off;
    hls {
        enabled         off;
    }
    transcode {
        enabled         on;
        ffmpeg          ./objs/ffmpeg/bin/ffmpeg;
        engine chat_hls {
            enabled         on;
            vfilter {
            }
            vcodec          libx264;
            vbitrate        150;
            vfps            15;
            vwidth          576;
            vheight         240;
            vthreads        1;
            vprofile        baseline;
            vpreset         ultrafast;
            vparams {
                g           30;
            }
            acodec          an;
            output          rtmp://127.0.0.1:[port]/[app]?vhost=chat_hls/[stream];
        }
    }
}
vhost chat_hls {
    hls {
        enabled         on;
        hls_path        ./objs/nginx/html;
        hls_fragment    2;
        hls_window      10;
    }
}
