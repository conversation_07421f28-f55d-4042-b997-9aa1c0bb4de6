# use ffmpeg to ingest file/stream/device to SRS
# @see https://ossrs.net/lts/zh-cn/docs/v4/doc/sample-ingest
# @see full.conf for detail config.

listen              1935;
max_connections     1000;
daemon              off;
srs_log_tank        console;
vhost __defaultVhost__ {
    ingest {
        enabled      on;
        input {
            type    stream;
            url     rtsp://admin:12345678@************:554/Streaming/Channels/501?transportmode=unicast;
        }
        ffmpeg      ./objs/ffmpeg/bin/ffmpeg;
        engine {
            enabled          on;
            perfile {
                rtsp_transport tcp;
            }
            vcodec copy;
            acodec copy;
            output          rtmp://127.0.0.1:[port]/live/livestream?vhost=[vhost];
        }
    }
}

