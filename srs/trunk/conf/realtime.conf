# the config for srs to delivery realtime RTMP stream
# @see https://ossrs.net/lts/zh-cn/docs/v4/doc/sample-realtime
# @see full.conf for detail config.

listen              1935;
max_connections     1000;
daemon              off;
srs_log_tank        console;
vhost __defaultVhost__ {
    tcp_nodelay     on;
    min_latency     on;
    
    play {
        gop_cache       off;
        queue_length    10;
        mw_latency      100;
    }
    
    publish {
        mr off;
    }
}
