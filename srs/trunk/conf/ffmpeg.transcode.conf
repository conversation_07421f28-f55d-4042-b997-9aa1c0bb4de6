# the config for srs use ffmpeg to transcode
# @see https://ossrs.net/lts/zh-cn/docs/v4/doc/sample-ffmpeg
# @see full.conf for detail config.

listen              1935;
max_connections     1000;
daemon              off;
srs_log_tank        console;
vhost __defaultVhost__ {
    transcode {
        enabled     on;
        ffmpeg      ./objs/ffmpeg/bin/ffmpeg;
        engine ff {
            enabled         on;
            vfilter {
            }
            vcodec          libx264;
            vthreads        4;
            vprofile        main;
            vpreset         medium;
            vparams {
            }
            acodec          libfdk_aac;
            aparams {
            }
            output          rtmp://127.0.0.1:[port]/[app]/[stream]_[engine]?vhost=[vhost];
        }
    }
}
