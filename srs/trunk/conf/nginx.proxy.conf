
# Note that this is for NGINX server, not SRS.

worker_processes  1;
events {
    worker_connections  1024;
}

http {
    include             /etc/nginx/mime.types;

    server {
        listen       80;
        listen       443 ssl http2;
        server_name  _;
        ssl_certificate      /usr/local/srs/conf/server.crt;
        ssl_certificate_key  /usr/local/srs/conf/server.key;

        # For SRS homepage, console and players
        #   http://r.ossrs.net/console/
        #   http://r.ossrs.net/players/
        location ~ ^/(console|players)/ {
           proxy_pass http://127.0.0.1:8080/$request_uri;
        }
        # For SRS streaming, for example:
        #   http://r.ossrs.net/live/livestream.flv
        #   http://r.ossrs.net/live/livestream.m3u8
        location ~ ^/.+/.*\.(flv|m3u8|ts|aac|mp3)$ {
           proxy_pass http://127.0.0.1:8080$request_uri;
        }
        # For SRS backend API for console.
        # For SRS WebRTC publish/play API.
        location ~ ^/(api|rtc)/ {
           proxy_pass http://127.0.0.1:1985$request_uri;
        }
    }
}


