# the config for isolated origin server to reuse port.
# @see https://ossrs.net/lts/zh-cn/docs/v4/doc/reuse-port
# @see full.conf for detail config.

listen              1935;
max_connections     1000;
pid                 ./objs/origin.hls.only1.pid;
daemon              off;
srs_log_tank        console;
http_server {
    enabled         on;
    listen          8080;
    dir             ./objs/nginx/html;
}
vhost __defaultVhost__ {
    hls {
        enabled         on;
        hls_path        ./objs/nginx/html;
        hls_fragment    10;
        hls_window      60;
    }
}
