# http-hooks or https-callbacks config for srs.
# @see https://github.com/ossrs/srs/issues/1657#issuecomment-720889906
# @see full.conf for detail config.

listen              1935;
max_connections     1000;
daemon              off;
srs_log_tank        console;
vhost __defaultVhost__ {
    http_hooks {
        enabled         on;
        on_publish      https://127.0.0.1:443/api/v1/streams;
        on_unpublish    https://127.0.0.1:443/api/v1/streams;
        on_play         https://127.0.0.1:443/api/v1/sessions;
        on_stop         https://127.0.0.1:443/api/v1/sessions;
    }
}
