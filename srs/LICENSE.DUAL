The MIT License (MIT)

Copyright (c) 2013-2024 The SRS Authors

Permission is hereby granted, free of charge, to any person obtaining a copy of
this software and associated documentation files (the "Software"), to deal in
the Software without restriction, including without limitation the rights to
use, copy, modify, merge, publish, distribute, sublicense, and/or sell copies of
the Software, and to permit persons to whom the Software is furnished to do so,
subject to the following conditions:

The above copyright notice and this permission notice shall be included in all
copies or substantial portions of the Software.

THE SOFTWARE IS PROVIDED "AS IS", WITHOUT WARRANTY OF ANY KIND, EXPRESS OR
IMPLIED, INCLUDING BUT NOT LIMITED TO THE WARRANTIES OF MERCHANTABILITY, FITNESS
FOR A PARTICULAR PURPOSE AND NONINFRINGEMENT. IN NO EVENT SHALL THE AUTHORS OR
COPYRIGHT HOLDERS BE LIABLE FOR ANY CLAIM, DAMAGES OR OTHER LIABILITY, WHETHER
IN AN ACTION OF CONTRACT, TORT OR OTHERWISE, ARISING FROM, OUT OF OR IN
CONNECTION WITH THE SOFTWARE OR THE USE OR OTHER DEALINGS IN THE SOFTWARE.

---------------------------------------------------------------------------
Note:
Individual files contain the following tag instead of the full license text.

	SPDX-License-Identifier: MIT or MulanPSL-2.0

This enables machine processing of license information based on the SPDX
License Identifiers that are here available: http://spdx.org/licenses/

For MIT, please read https://spdx.org/licenses/MIT.html

For MulanPSL-2.0, please read https://spdx.org/licenses/MulanPSL-2.0.html
and note that MulanPSL-2.0 is compatible with Apache-2.0, please see
https://www.apache.org/legal/resolved.html#category-a
