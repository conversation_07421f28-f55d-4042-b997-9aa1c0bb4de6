name: "Release"

# @see https://docs.github.com/en/actions/reference/workflow-syntax-for-github-actions#onpushpull_requestbranchestags
on:
  push:
    tags:
      - v5*

jobs:
  envs:
    name: envs
    steps:
      ##################################################################################################################
      # Git checkout
      - name: Checkout repository
        uses: actions/checkout@v3
      # The github.ref is, for example, refs/tags/v5.0.145 or refs/tags/v5.0-r8
      # Generate variables like:
      #   SRS_TAG=v5.0-r8
      #   SRS_TAG=v5.0.145
      #   SRS_VERSION=5.0.145
      #   SRS_VERSION=5.0-r8
      #   SRS_MAJOR=5
      #   SRS_XYZ=5.0.157
      # @see https://docs.github.com/en/actions/reference/workflow-commands-for-github-actions#setting-an-environment-variable
      - name: Generate varaiables
        run: |
          SRS_TAG=$(echo ${{ github.ref }}| awk -F '/' '{print $3}')
          echo "SRS_TAG=$SRS_TAG" >> $GITHUB_ENV
          SRS_VERSION=$(echo ${SRS_TAG}| sed 's/^v//g')
          echo "SRS_VERSION=$SRS_VERSION" >> $GITHUB_ENV
          SRS_MAJOR=$(echo $SRS_TAG| cut -c 2)
          echo "SRS_MAJOR=$SRS_MAJOR" >> $GITHUB_ENV
          VFILE="trunk/src/core/srs_core_version5.hpp"
          SRS_X=$(cat $VFILE |grep VERSION_MAJOR |awk '{print $3}')
          SRS_Y=$(cat $VFILE |grep VERSION_MINOR |awk '{print $3}')
          SRS_Z=$(cat $VFILE |grep VERSION_REVISION |awk '{print $3}')
          SRS_XYZ=$SRS_X.$SRS_Y.$SRS_Z
          echo "SRS_XYZ=$SRS_XYZ" >> $GITHUB_ENV
    # Map a step output to a job output, see https://docs.github.com/en/actions/using-jobs/defining-outputs-for-jobs
    outputs:
      SRS_TAG: ${{ env.SRS_TAG }}
      SRS_VERSION: ${{ env.SRS_VERSION }}
      SRS_MAJOR: ${{ env.SRS_MAJOR }}
      SRS_XYZ: ${{ env.SRS_XYZ }}
    runs-on: ubuntu-20.04

  test:
    name: test
    needs:
      - envs
    steps:
      ##################################################################################################################
      - name: Covert output to env
        run: |
          echo "SRS_TAG=${{ needs.envs.outputs.SRS_TAG }}" >> $GITHUB_ENV
          echo "SRS_VERSION=${{ needs.envs.outputs.SRS_VERSION }}" >> $GITHUB_ENV
          echo "SRS_MAJOR=${{ needs.envs.outputs.SRS_MAJOR }}" >> $GITHUB_ENV
      ##################################################################################################################
      # Git checkout
      - name: Checkout repository
        uses: actions/checkout@v3
      ##################################################################################################################
      # Tests
      - name: Build test image
        run: docker build --tag srs:test -f trunk/Dockerfile.test .
      # For utest
      - name: Run SRS utest
        run: docker run --rm srs:test bash -c 'make utest && ./objs/srs_utest'
      # For regression-test
      - name: Run SRS regression-test
        run: |
          docker run --rm srs:test bash -c 'make && \
            ./objs/srs -c conf/regression-test.conf && \
            cd 3rdparty/srs-bench && make && ./objs/srs_test -test.v'
    runs-on: ubuntu-20.04

  draft:
    name: draft
    needs:
      - envs
    steps:
      - name: Create release draft
        id: create_draft
        uses: ncipollo/release-action@v1
        env:
          GITHUB_TOKEN: ${{ secrets.GITHUB_TOKEN }}
        with:
          allowUpdates: true
          tag: ${{ github.ref }}
          draft: true
          prerelease: false
    # Map a step output to a job output, see https://docs.github.com/en/actions/using-jobs/defining-outputs-for-jobs
    outputs:
      SRS_RELEASE_ID: ${{ steps.create_draft.outputs.id }}
    runs-on: ubuntu-20.04

  cygwin64:
    name: cygwin64
    needs:
      - envs
      - draft
    steps:
      # See https://github.com/cygwin/cygwin-install-action#parameters
      # Note that https://github.com/egor-tensin/setup-cygwin fails to install packages.
      - name: Setup Cygwin
        uses: cygwin/cygwin-install-action@master
        with:
          platform: x86_64
          packages: bash make gcc-g++ cmake automake patch pkg-config tcl unzip
          install-dir: C:\cygwin64
      ##################################################################################################################
      - name: Checkout repository
        uses: actions/checkout@v3
      ##################################################################################################################
      - name: Covert output to env
        env:
          SHELLOPTS: igncr
        shell: C:\cygwin64\bin\bash.exe --login '{0}'
        run: |
          echo "SRS_TAG=${{ needs.envs.outputs.SRS_TAG }}" >> $GITHUB_ENV
          echo "SRS_VERSION=${{ needs.envs.outputs.SRS_VERSION }}" >> $GITHUB_ENV
          echo "SRS_MAJOR=${{ needs.envs.outputs.SRS_MAJOR }}" >> $GITHUB_ENV
          echo "SRS_RELEASE_ID=${{ needs.draft.outputs.SRS_RELEASE_ID }}" >> $GITHUB_ENV
      ##################################################################################################################
      - name: Build SRS
        env:
          SHELLOPTS: igncr
          SRS_WORKSPACE: ${{ github.workspace }}
        shell: C:\cygwin64\bin\bash.exe --login '{0}'
        run: |
          export PATH=/usr/bin:/usr/local/bin &&
          which make gcc g++ patch cmake pkg-config uname grep sed &&
          (make --version; gcc --version; patch --version; cmake --version; pkg-config --version) && 
          (aclocal --version; autoconf --version; automake --version; uname -a) &&
          cd $(cygpath -u $SRS_WORKSPACE)/trunk && ./configure --gb28181=on && make
      ##################################################################################################################
      - name: Package SRS
        env:
          SHELLOPTS: igncr
          SRS_WORKSPACE: ${{ github.workspace }}
        shell: C:\cygwin64\bin\bash.exe --login '{0}'
        run: |
          cd $(cygpath -u $SRS_WORKSPACE) &&
          if [[ $(echo $SRS_TAG |grep -qE '^v' && echo YES) != YES ]]; then 
              SRS_VERSION=$(./trunk/objs/srs -v 2>&1); echo "Change version to ${SRS_VERSION}"; 
          fi &&
          "/cygdrive/c/Program Files (x86)/NSIS/makensis.exe" /DSRS_VERSION=${SRS_VERSION} \
              /DCYGWIN_DIR="C:\cygwin64" trunk/packaging/nsis/srs.nsi &&
          mv trunk/packaging/nsis/SRS-Windows-x86_64-${SRS_VERSION}-setup.exe . && ls -lh *.exe &&
          echo "SRS_CYGWIN_TAR=SRS-Windows-x86_64-${SRS_VERSION}-setup.exe" >> $GITHUB_ENV &&
          echo "SRS_CYGWIN_MD5=$(md5sum SRS-Windows-x86_64-${SRS_VERSION}-setup.exe| awk '{print $1}')" >> $GITHUB_ENV
      ##################################################################################################################
      - name: Upload Release Assets Cygwin
        id: upload-release-assets-cygwin
        uses: dwenegar/upload-release-assets@v1
        env:
          GITHUB_TOKEN: ${{ secrets.GITHUB_TOKEN }}
        with:
          release_id: ${{ env.SRS_RELEASE_ID }}
          assets_path: ${{ env.SRS_CYGWIN_TAR }}
    # Map a step output to a job output, see https://docs.github.com/en/actions/using-jobs/defining-outputs-for-jobs
    outputs:
      SRS_CYGWIN_TAR: ${{ env.SRS_CYGWIN_TAR }}
      SRS_CYGWIN_MD5: ${{ env.SRS_CYGWIN_MD5 }}
    runs-on: windows-latest

  linux:
    name: linux
    needs:
      - envs
      - draft
    steps:
      ##################################################################################################################
      - name: Covert output to env
        run: |
          echo "SRS_TAG=${{ needs.envs.outputs.SRS_TAG }}" >> $GITHUB_ENV
          echo "SRS_VERSION=${{ needs.envs.outputs.SRS_VERSION }}" >> $GITHUB_ENV
          echo "SRS_MAJOR=${{ needs.envs.outputs.SRS_MAJOR }}" >> $GITHUB_ENV
          echo "SRS_RELEASE_ID=${{ needs.draft.outputs.SRS_RELEASE_ID }}" >> $GITHUB_ENV
      ##################################################################################################################
      # Git checkout
      - name: Checkout repository
        uses: actions/checkout@v3
      ##################################################################################################################
      # Create source tar for release. Note that it's for OpenWRT package srs-server, so the filename MUST be
      # srs-server-xxx.tar.gz, because the package is named srs-server.
      # Generate variables like:
      #   SRS_SOURCE_TAR=srs-server-5.0.145.tar.gz
      #   SRS_SOURCE_MD5=83e38700a80a26e30b2df054e69956e5
      - name: Create source tar.gz
        run: |
          DEST_DIR=srs-server-$SRS_VERSION && mkdir -p $DEST_DIR &&
          cp README.md $DEST_DIR && cp LICENSE $DEST_DIR && cp -R trunk $DEST_DIR/trunk &&
          (cd $DEST_DIR/trunk/3rdparty && rm -rf *.zip openssl-*.gz srs-bench) &&
          tar zcf ${DEST_DIR}.tar.gz ${DEST_DIR} && du -sh ${DEST_DIR}* && rm -rf ${DEST_DIR} &&
          echo "SRS_SOURCE_TAR=${DEST_DIR}.tar.gz" >> $GITHUB_ENV &&
          echo "SRS_SOURCE_MD5=$(md5sum ${DEST_DIR}.tar.gz| awk '{print $1}')" >> $GITHUB_ENV
      # Create package tar for release
      # Generate variables like:
      #   SRS_PACKAGE_ZIP=SRS-CentOS7-x86_64-5.0.145.zip
      #   SRS_PACKAGE_MD5=3880a26e30b283edf05700a4e69956e5
      - name: Create package zip
        env:
          PACKAGER: ${{ secrets.SRS_PACKAGER_BINARY }}
        run: |
          docker build --tag srs:pkg --build-arg version=$SRS_VERSION --build-arg SRS_AUTO_PACKAGER=$PACKAGER -f trunk/Dockerfile.pkg . &&
          SRS_PACKAGE_ZIP=SRS-CentOS7-x86_64-$SRS_VERSION.zip &&
          docker run --rm -v $(pwd):/output srs:pkg cp objs/$SRS_PACKAGE_ZIP /output/ &&
          du -sh $SRS_PACKAGE_ZIP &&
          echo "SRS_PACKAGE_ZIP=$SRS_PACKAGE_ZIP" >> $GITHUB_ENV &&
          echo "SRS_PACKAGE_MD5=$(md5sum $SRS_PACKAGE_ZIP| awk '{print $1}')" >> $GITHUB_ENV
      ##################################################################################################################
      - name: Upload Release Assets Packager
        id: upload-release-assets-packager
        uses: dwenegar/upload-release-assets@v1
        env:
          GITHUB_TOKEN: ${{ secrets.GITHUB_TOKEN }}
        with:
          release_id: ${{ env.SRS_RELEASE_ID }}
          assets_path: ${{ env.SRS_PACKAGE_ZIP }}
      - name: Upload Release Assets Source
        id: upload-release-assets-source
        uses: dwenegar/upload-release-assets@v1
        env:
          GITHUB_TOKEN: ${{ secrets.GITHUB_TOKEN }}
        with:
          release_id: ${{ env.SRS_RELEASE_ID }}
          assets_path: ${{ env.SRS_SOURCE_TAR }}
    # Map a step output to a job output, see https://docs.github.com/en/actions/using-jobs/defining-outputs-for-jobs
    outputs:
      SRS_PACKAGE_ZIP: ${{ env.SRS_PACKAGE_ZIP }}
      SRS_PACKAGE_MD5: ${{ env.SRS_PACKAGE_MD5 }}
      SRS_SOURCE_TAR: ${{ env.SRS_SOURCE_TAR }}
      SRS_SOURCE_MD5: ${{ env.SRS_SOURCE_MD5 }}
    runs-on: ubuntu-20.04

  docker-srs:
    name: docker-srs
    needs:
      - envs
    steps:
      ##################################################################################################################
      - name: Covert output to env
        run: |
          echo "SRS_TAG=${{ needs.envs.outputs.SRS_TAG }}" >> $GITHUB_ENV
          echo "SRS_VERSION=${{ needs.envs.outputs.SRS_VERSION }}" >> $GITHUB_ENV
          echo "SRS_MAJOR=${{ needs.envs.outputs.SRS_MAJOR }}" >> $GITHUB_ENV
          echo "SRS_XYZ=${{ needs.envs.outputs.SRS_XYZ }}" >> $GITHUB_ENV
      ##################################################################################################################
      # Git checkout
      - name: Checkout repository
        uses: actions/checkout@v3
      # See https://github.com/crazy-max/ghaction-docker-buildx#moved-to-docker-organization
      # https://github.com/docker/setup-qemu-action
      - name: Set up QEMU
        uses: docker/setup-qemu-action@v2
      # https://github.com/docker/setup-buildx-action
      - name: Set up Docker Buildx
        uses: docker/setup-buildx-action@v2
      ##################################################################################################################
      # Create main images for Docker
      - name: Login to docker hub
        uses: docker/login-action@v2
        with:
          username: "${{ secrets.DOCKER_USERNAME }}"
          password: "${{ secrets.DOCKER_PASSWORD }}"
      - name: Build and push images to Docker hub
        env:
          PACKAGER: ${{ secrets.SRS_PACKAGER_DOCKER }}
        run: |
          echo "Release ossrs/srs:$SRS_TAG"
          docker buildx build --platform linux/arm/v7,linux/arm64/v8,linux/amd64 \
            --output "type=image,push=true" \
            -t ossrs/srs:$SRS_TAG --build-arg SRS_AUTO_PACKAGER=$PACKAGER -f Dockerfile .
      # Docker alias images
      # TODO: FIXME: If stable, please set the latest from 4.0 to 5.0
      - name: Docker alias images for ossrs/srs
        uses: akhilerm/tag-push-action@v2.1.0
        with:
          src: ossrs/srs:${{ env.SRS_TAG }}
          dst: |
            ossrs/srs:${{ env.SRS_VERSION }}
            ossrs/srs:${{ env.SRS_MAJOR }}
            ossrs/srs:v${{ env.SRS_MAJOR }}
            ossrs/srs:${{ env.SRS_XYZ }}
            ossrs/srs:v${{ env.SRS_XYZ }}
    runs-on: ubuntu-20.04

  aliyun-srs:
    name: aliyun-srs
    needs:
      - envs
      - docker-srs
      - test
    steps:
      ##################################################################################################################
      - name: Covert output to env
        run: |
          echo "SRS_TAG=${{ needs.envs.outputs.SRS_TAG }}" >> $GITHUB_ENV
          echo "SRS_VERSION=${{ needs.envs.outputs.SRS_VERSION }}" >> $GITHUB_ENV
          echo "SRS_MAJOR=${{ needs.envs.outputs.SRS_MAJOR }}" >> $GITHUB_ENV
          echo "SRS_XYZ=${{ needs.envs.outputs.SRS_XYZ }}" >> $GITHUB_ENV
      # Aliyun ACR
      # TODO: FIXME: If stable, please set the latest from 4.0 to 5.0
      - name: Login aliyun hub
        uses: docker/login-action@v2
        with:
          registry: registry.cn-hangzhou.aliyuncs.com
          username: "${{ secrets.ACR_USERNAME }}"
          password: "${{ secrets.ACR_PASSWORD }}"
      - name: Push to Aliyun registry for ossrs/srs
        uses: akhilerm/tag-push-action@v2.1.0
        with:
          src: ossrs/srs:${{ env.SRS_TAG }}
          dst: |
            registry.cn-hangzhou.aliyuncs.com/ossrs/srs:${{ env.SRS_TAG }}
            registry.cn-hangzhou.aliyuncs.com/ossrs/srs:${{ env.SRS_VERSION }}
            registry.cn-hangzhou.aliyuncs.com/ossrs/srs:${{ env.SRS_MAJOR }}
            registry.cn-hangzhou.aliyuncs.com/ossrs/srs:v${{ env.SRS_MAJOR }}
            registry.cn-hangzhou.aliyuncs.com/ossrs/srs:${{ env.SRS_XYZ }}
            registry.cn-hangzhou.aliyuncs.com/ossrs/srs:v${{ env.SRS_XYZ }}
    runs-on: ubuntu-20.04

  update:
    name: update
    needs:
      - aliyun-srs
      - envs
    steps:
      ##################################################################################################################
      - name: Covert output to env
        run: |
          echo "SRS_TAG=${{ needs.envs.outputs.SRS_TAG }}" >> $GITHUB_ENV
          echo "SRS_VERSION=${{ needs.envs.outputs.SRS_VERSION }}" >> $GITHUB_ENV
          echo "SRS_MAJOR=${{ needs.envs.outputs.SRS_MAJOR }}" >> $GITHUB_ENV
      ##################################################################################################################
      # Git checkout
      - name: Checkout repository
        uses: actions/checkout@v3
      ##################################################################################################################
      # Generate variables like:
      #   SRS_R_OSSRS_NET=*******
      - name: Build variables for r.ossrs.net
        run: |
          SRS_R_OSSRS_NET=$(dig +short r.ossrs.net)
          echo "SRS_R_OSSRS_NET=$SRS_R_OSSRS_NET" >> $GITHUB_ENV
      - name: Release to r.ossrs.net
        uses: appleboy/ssh-action@master
        with:
          host: ${{ env.SRS_R_OSSRS_NET }}
          username: root
          key: ${{ secrets.DIGITALOCEAN_SSHKEY }}
          port: 22
          envs: SRS_MAJOR
          timeout: 60s
          command_timeout: 30m
          script: |
            echo "Update r.ossrs.net ok for SRS $SRS_MAJOR"
    runs-on: ubuntu-20.04

  release:
    name: release
    needs:
      - update
      - envs
      - draft
      - cygwin64
      - linux
    steps:
      ##################################################################################################################
      - name: Covert output to env
        run: |
          echo "SRS_TAG=${{ needs.envs.outputs.SRS_TAG }}" >> $GITHUB_ENV
          echo "SRS_VERSION=${{ needs.envs.outputs.SRS_VERSION }}" >> $GITHUB_ENV
          echo "SRS_MAJOR=${{ needs.envs.outputs.SRS_MAJOR }}" >> $GITHUB_ENV
          echo "SRS_XYZ=${{ needs.envs.outputs.SRS_XYZ }}" >> $GITHUB_ENV
          echo "SRS_RELEASE_ID=${{ needs.draft.outputs.SRS_RELEASE_ID }}" >> $GITHUB_ENV
          echo "SRS_PACKAGE_ZIP=${{ needs.linux.outputs.SRS_PACKAGE_ZIP }}" >> $GITHUB_ENV
          echo "SRS_PACKAGE_MD5=${{ needs.linux.outputs.SRS_PACKAGE_MD5 }}" >> $GITHUB_ENV
          echo "SRS_SOURCE_TAR=${{ needs.linux.outputs.SRS_SOURCE_TAR }}" >> $GITHUB_ENV
          echo "SRS_SOURCE_MD5=${{ needs.linux.outputs.SRS_SOURCE_MD5 }}" >> $GITHUB_ENV
          echo "SRS_CYGWIN_TAR=${{ needs.cygwin64.outputs.SRS_CYGWIN_TAR }}" >> $GITHUB_ENV
          echo "SRS_CYGWIN_MD5=${{ needs.cygwin64.outputs.SRS_CYGWIN_MD5 }}" >> $GITHUB_ENV
      ##################################################################################################################
      # Git checkout
      - name: Checkout repository
        uses: actions/checkout@v3
      # Create release.
      - name: Update release
        id: update_release
        uses: ncipollo/release-action@v1
        env:
          GITHUB_TOKEN: ${{ secrets.GITHUB_TOKEN }}
        with:
          allowUpdates: true
          tag: ${{ github.ref }}
          name: Release ${{ env.SRS_TAG }}
          body: |
            [${{ github.sha }}](https://github.com/ossrs/srs/commit/${{ github.sha }})
            ${{ github.event.head_commit.message }}
            
            ## Resource
            * Source: ${{ env.SRS_SOURCE_MD5 }} [${{ env.SRS_SOURCE_TAR }}](https://github.com/ossrs/srs/releases/download/${{ env.SRS_TAG }}/${{ env.SRS_SOURCE_TAR }})
            * Binary: ${{ env.SRS_PACKAGE_MD5 }} [${{ env.SRS_PACKAGE_ZIP }}](https://github.com/ossrs/srs/releases/download/${{ env.SRS_TAG }}/${{ env.SRS_PACKAGE_ZIP }})
            * Binary: ${{ env.SRS_CYGWIN_MD5 }} [${{ env.SRS_CYGWIN_TAR }}](https://github.com/ossrs/srs/releases/download/${{ env.SRS_TAG }}/${{ env.SRS_CYGWIN_TAR }})
            
            ## Resource Mirror: gitee.com
            * Source: ${{ env.SRS_SOURCE_MD5 }} [${{ env.SRS_SOURCE_TAR }}](https://gitee.com/ossrs/srs/releases/download/${{ env.SRS_TAG }}/${{ env.SRS_SOURCE_TAR }})
            * Binary: ${{ env.SRS_PACKAGE_MD5 }} [${{ env.SRS_PACKAGE_ZIP }}](https://gitee.com/ossrs/srs/releases/download/${{ env.SRS_TAG }}/${{ env.SRS_PACKAGE_ZIP }})
            * Binary: ${{ env.SRS_CYGWIN_MD5 }} [${{ env.SRS_CYGWIN_TAR }}](https://gitee.com/ossrs/srs/releases/download/${{ env.SRS_TAG }}/${{ env.SRS_CYGWIN_TAR }})
            
            ## Docker
            * [docker pull ossrs/srs:${{ env.SRS_MAJOR }}](https://ossrs.io/lts/en-us/docs/v5/doc/getting-started)
            * [docker pull ossrs/srs:${{ env.SRS_TAG }}](https://ossrs.io/lts/en-us/docs/v5/doc/getting-started)
            * [docker pull ossrs/srs:v${{ env.SRS_XYZ }}](https://ossrs.io/lts/en-us/docs/v5/doc/getting-started)
            
            ## Docker Mirror: aliyun.com
            * [docker pull registry.cn-hangzhou.aliyuncs.com/ossrs/srs:${{ env.SRS_MAJOR }}](https://ossrs.net/lts/zh-cn/docs/v5/doc/getting-started)
            * [docker pull registry.cn-hangzhou.aliyuncs.com/ossrs/srs:${{ env.SRS_TAG }}](https://ossrs.net/lts/zh-cn/docs/v5/doc/getting-started)
            * [docker pull registry.cn-hangzhou.aliyuncs.com/ossrs/srs:v${{ env.SRS_XYZ }}](https://ossrs.net/lts/zh-cn/docs/v5/doc/getting-started)
            
            ## Doc: ossrs.io
            * [Getting Started](https://ossrs.io/lts/en-us/docs/v5/doc/getting-started)
            * [Wiki home](https://ossrs.io/lts/en-us/docs/v5/doc/introduction)
            * [FAQ](https://ossrs.io/lts/en-us/faq), [Features](https://github.com/ossrs/srs/blob/${{ github.sha }}/trunk/doc/Features.md#features) or [ChangeLogs](https://github.com/ossrs/srs/blob/${{ github.sha }}/trunk/doc/CHANGELOG.md#changelog)
            
            ## Doc: ossrs.net
            * [快速入门](https://ossrs.net/lts/zh-cn/docs/v5/doc/getting-started)
            * [中文Wiki首页](https://ossrs.net/lts/zh-cn/docs/v5/doc/introduction)
            * [中文FAQ](https://ossrs.net/lts/zh-cn/faq), [功能列表](https://github.com/ossrs/srs/blob/${{ github.sha }}/trunk/doc/Features.md#features) 或 [修订历史](https://github.com/ossrs/srs/blob/${{ github.sha }}/trunk/doc/CHANGELOG.md#changelog)
          draft: false
          prerelease: false
          makeLatest: true
    runs-on: ubuntu-20.04

  done:
    name: done
    runs-on: ubuntu-20.04
    needs:
      - update
      - release
    steps:
      - run: echo 'All done'
