name: "CodeQL"

# @see https://docs.github.com/en/actions/reference/workflow-syntax-for-github-actions#onpushpull_requestbranchestags
on: [push, pull_request]

jobs:
  analyze:
    name: actions-codeql-analyze
    runs-on: ubuntu-20.04

    strategy:
      fail-fast: false
      matrix:
        language: [ 'cpp' ]

    steps:
      - name: Checkout repository
        uses: actions/checkout@v3

      # Initializes the CodeQL tools for scanning.
      - name: Initialize CodeQL
        uses: github/codeql-action/init@v2
        with:
          languages: ${{ matrix.language }}

      - name: Build SRS
        run: |
          echo "pwd: $(pwd), who: $(whoami)"
          docker run --rm -v $(pwd):$(pwd) -w $(pwd)/trunk ossrs/srs:ubuntu20-cache \
              bash -c "./configure && chmod 777 -R objs"
          cd trunk && ./configure && make

      - name: Perform CodeQL Analysis
        uses: github/codeql-action/analyze@v2
