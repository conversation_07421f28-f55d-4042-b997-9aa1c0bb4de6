name: "Test"

# @see https://docs.github.com/en/actions/reference/workflow-syntax-for-github-actions#onpushpull_requestbranchestags
on: [push, pull_request]

# The dependency graph:
# test(6m)
# multiple-arch-armv7(13m)
# multiple-arch-aarch64(7m)
# cygwin64-cache(1m)
#     cygwin64(6m) - Must depends on cygwin64-cache.
#     fast(0s) - To limit all fastly run jobs after slow jobs.
#         build-centos7(3m)
#         build-ubuntu16(3m)
#         build-ubuntu18(2m)
#         build-ubuntu20(2m)
#         build-cross-arm(3m)
#         build-cross-aarch64(3m)
#         multiple-arch-amd64(2m)
#         coverage(3m)

jobs:
  cygwin64-cache:
    name: cygwin64-cache
    steps:
      - name: Download Cache for Cygwin
        run: |
          echo "Generate convert.sh" &&
          echo "for file in \$(find objs -type l); do" > convert.sh &&
          echo "    REAL=\$(readlink -f \$file) &&" >> convert.sh &&
          echo "    echo \"convert \$file to \$REAL\" &&" >> convert.sh &&
          echo "    rm -rf \$file &&" >> convert.sh &&
          echo "    cp -r \$REAL \$file" >> convert.sh &&
          echo "done" >> convert.sh &&
          cat convert.sh &&
          docker run --rm -v $(pwd):/srs -w /usr/local/srs-cache/srs/trunk ossrs/srs:cygwin64-cache \
              bash -c "bash /srs/convert.sh && tar cf /srs/objs.tar.bz2 objs" &&
          pwd && du -sh *
      ##################################################################################################################
      - uses: actions/upload-artifact@v3
        with:
          name: srs-cache
          path: objs.tar.bz2
          retention-days: 1
    runs-on: ubuntu-20.04

  cygwin64:
    name: cygwin64
    needs:
      - cygwin64-cache
    steps:
      # See https://github.com/cygwin/cygwin-install-action#parameters
      # Note that https://github.com/egor-tensin/setup-cygwin fails to install packages.
      - name: Setup Cygwin
        uses: cygwin/cygwin-install-action@master
        with:
          platform: x86_64
          packages: bash make gcc-g++ cmake automake patch pkg-config tcl unzip
          install-dir: C:\cygwin64
      ##################################################################################################################
      - name: Checkout repository
        uses: actions/checkout@v3
      ##################################################################################################################
      # Note that we must download artifact after checkout code, because it will change the files in workspace.
      - uses: actions/download-artifact@v3
        with:
          name: srs-cache
      - uses: geekyeggo/delete-artifact@v2
        with:
          name: srs-cache
      ##################################################################################################################
      - name: Build and test SRS
        env:
          SHELLOPTS: igncr
          SRS_WORKSPACE: ${{ github.workspace }}
        shell: C:\cygwin64\bin\bash.exe --login '{0}'
        run: |
          WORKDIR=$(cygpath -u $SRS_WORKSPACE) && export PATH=/usr/bin:/usr/local/bin && cd ${WORKDIR} &&
          pwd && rm -rf /usr/local/srs-cache && mkdir -p /usr/local/srs-cache/srs/trunk && ls -lh &&
          tar xf objs.tar.bz2 -C /usr/local/srs-cache/srs/trunk/ && du -sh /usr/local/srs-cache/srs/trunk/* && 
          cd ${WORKDIR}/trunk && ./configure --gb28181=on --utest=on && ls -lh && du -sh * && du -sh objs/* &&
          cd ${WORKDIR}/trunk && make utest && ./objs/srs_utest
    runs-on: windows-latest

  build-centos7:
    name: build-centos7
    needs:
      - fast
    steps:
      - name: Checkout repository
        uses: actions/checkout@v3
      # Build for CentOS 7
      - name: Build on CentOS7, baseline
        run: DOCKER_BUILDKIT=1 docker build -f trunk/Dockerfile.builds --target centos7-baseline .
      - name: Build on CentOS7, with all features
        run: DOCKER_BUILDKIT=1 docker build -f trunk/Dockerfile.builds --target centos7-all .
      - name: Build on CentOS7, without WebRTC
        run: DOCKER_BUILDKIT=1 docker build -f trunk/Dockerfile.builds --target centos7-no-webrtc .
      - name: Build on CentOS7, without ASM
        run: DOCKER_BUILDKIT=1 docker build -f trunk/Dockerfile.builds --target centos7-no-asm .
      - name: Build on CentOS7, C++98, no FFmpeg
        run: DOCKER_BUILDKIT=1 docker build -f trunk/Dockerfile.builds --target centos7-ansi-no-ffmpeg .
    runs-on: ubuntu-20.04

  build-ubuntu16:
    name: build-ubuntu16
    needs:
      - fast
    steps:
      - name: Checkout repository
        uses: actions/checkout@v3
      # Build for Ubuntu16
      - name: Build on Ubuntu16, baseline
        run: DOCKER_BUILDKIT=1 docker build -f trunk/Dockerfile.builds --target ubuntu16-baseline .
      - name: Build on Ubuntu16, with all features
        run: DOCKER_BUILDKIT=1 docker build -f trunk/Dockerfile.builds --target ubuntu16-all .
    runs-on: ubuntu-20.04

  build-ubuntu18:
    name: build-ubuntu18
    needs:
      - fast
    steps:
      - name: Checkout repository
        uses: actions/checkout@v3
      # Build for Ubuntu18
      - name: Build on Ubuntu18, baseline
        run: DOCKER_BUILDKIT=1 docker build -f trunk/Dockerfile.builds --target ubuntu18-baseline .
      - name: Build on Ubuntu18, with all features
        run: DOCKER_BUILDKIT=1 docker build -f trunk/Dockerfile.builds --target ubuntu18-all .
    runs-on: ubuntu-20.04

  build-ubuntu20:
    name: build-ubuntu20
    needs:
      - fast
    steps:
      - name: Checkout repository
        uses: actions/checkout@v3
      # Build for Ubuntu20
      - name: Build on Ubuntu20, baseline
        run: DOCKER_BUILDKIT=1 docker build -f trunk/Dockerfile.builds --target ubuntu20-baseline .
      - name: Build on Ubuntu20, with all features
        run: DOCKER_BUILDKIT=1 docker build -f trunk/Dockerfile.builds --target ubuntu20-all .
    runs-on: ubuntu-20.04

  build-cross-arm:
    name: build-cross-arm
    needs:
      - fast
    steps:
      - name: Checkout repository
        uses: actions/checkout@v3
      - name: Cross Build for ARMv7 on Ubuntu16
        run: DOCKER_BUILDKIT=1 docker build -f trunk/Dockerfile.builds --target ubuntu16-cache-cross-armv7 .
      - name: Cross Build for ARMv7 on Ubuntu20
        run: DOCKER_BUILDKIT=1 docker build -f trunk/Dockerfile.builds --target ubuntu20-cache-cross-armv7 .
    runs-on: ubuntu-20.04

  build-cross-aarch64:
    name: build-cross-aarch64
    needs:
      - fast
    steps:
      - name: Checkout repository
        uses: actions/checkout@v3
      - name: Cross Build for AARCH64 on Ubuntu16
        run: DOCKER_BUILDKIT=1 docker build -f trunk/Dockerfile.builds --target ubuntu16-cache-cross-aarch64 .
      - name: Cross Build for AARCH64 on Ubuntu20
        run: DOCKER_BUILDKIT=1 docker build -f trunk/Dockerfile.builds --target ubuntu20-cache-cross-aarch64 .
    runs-on: ubuntu-20.04

  test:
    name: utest-regression-blackbox-test
    steps:
      - name: Checkout repository
        uses: actions/checkout@v3
      # Tests
      - name: Build test image
        run: docker build --tag srs:test --build-arg MAKEARGS='-j2' -f trunk/Dockerfile.test .
      # For blackbox-test
      - name: Run SRS blackbox-test
        run: |
          #docker run --rm -w /srs/trunk/3rdparty/srs-bench srs:test ./objs/srs_blackbox_test -test.v \
          #  -test.run 'TestFast_RtmpPublish_DvrFlv_Basic' -srs-log -srs-stdout srs-ffmpeg-stderr -srs-dvr-stderr \
          #  -srs-ffprobe-stdout
          docker run --rm -w /srs/trunk/3rdparty/srs-bench srs:test \
            ./objs/srs_blackbox_test -test.v -test.run '^TestFast' -test.parallel 64
          docker run --rm -w /srs/trunk/3rdparty/srs-bench srs:test \
            ./objs/srs_blackbox_test -test.v -test.run '^TestSlow' -test.parallel 4
      # For utest
      - name: Run SRS utest
        run: docker run --rm srs:test ./objs/srs_utest
      # For regression-test
      - name: Run SRS regression-test
        run: |
          docker run --rm srs:test bash -c './objs/srs -c conf/regression-test.conf && \
              cd 3rdparty/srs-bench && (./objs/srs_test -test.v || (cat ../../objs/srs.log && exit 1)) && \
              ./objs/srs_gb28181_test -test.v'
    runs-on: ubuntu-20.04

  coverage:
    name: coverage
    needs:
      - fast
    steps:
      - name: Checkout repository
        uses: actions/checkout@v3
      # Tests
      - name: Build coverage image
        run: docker build --tag srs:cov --build-arg MAKEARGS='-j2' -f trunk/Dockerfile.cov .
      # For coverage
      - name: Run SRS covergae
        if: ${{ startsWith(github.ref, 'refs/heads/') || startsWith(github.ref, 'refs/pull/') }}
        env:
          CODECOV_TOKEN: ${{ secrets.CODECOV_TOKEN }}
        run: |
          # The hash of commit.
          SRS_SHA=${{ github.sha }}
          # Note that the root of SRS, must contains .git, for report fixing.
          SRS_PROJECT=/srs
          # The github.ref is, for example, refs/heads/develop
          SRS_BRANCH=$(echo ${{ github.ref }}| awk -F 'refs/heads/' '{print $2}'| awk -F '/' '{print $1}')
          # The github.ref is, for example, refs/pull/2536/merge
          SRS_PR=$(echo ${{ github.ref }}| awk -F 'refs/pull/' '{print $2}'| awk -F '/' '{print $1}')
          #
          echo "For github.ref=${{ github.ref }}, github.sha=${{ github.sha }}"
          echo "SRS_BRANCH=$SRS_BRANCH, SRS_PR=$SRS_PR, SRS_SHA=$SRS_SHA, SRS_PROJECT=$SRS_PROJECT"
          docker run --rm --env CODECOV_TOKEN=$CODECOV_TOKEN --env SRS_BRANCH=$SRS_BRANCH \
            --env SRS_PR=$SRS_PR --env SRS_SHA=$SRS_SHA --env SRS_PROJECT=$SRS_PROJECT \
            srs:cov bash -c './objs/srs_utest && bash auto/codecov.sh'
          #
    runs-on: ubuntu-20.04

  multiple-arch-armv7:
    name: multiple-arch-armv7
    steps:
      - name: Checkout repository
        uses: actions/checkout@v3
      # See https://github.com/crazy-max/ghaction-docker-buildx#moved-to-docker-organization
      # https://github.com/docker/setup-qemu-action
      - name: Set up QEMU
        uses: docker/setup-qemu-action@v2
      # https://github.com/docker/setup-buildx-action
      - name: Set up Docker Buildx
        uses: docker/setup-buildx-action@v2
      - name: Build multiple archs image
        run: |
          docker buildx build --platform linux/arm/v7 \
            --output "type=image,push=false" \
            --build-arg IMAGE=ossrs/srs:ubuntu20-cache \
            --build-arg INSTALLDEPENDS="NO" \
            -f Dockerfile .
    runs-on: ubuntu-20.04

  multiple-arch-aarch64:
    name: multiple-arch-aarch64
    steps:
      - name: Checkout repository
        uses: actions/checkout@v3
      # See https://github.com/crazy-max/ghaction-docker-buildx#moved-to-docker-organization
      # https://github.com/docker/setup-qemu-action
      - name: Set up QEMU
        uses: docker/setup-qemu-action@v2
      # https://github.com/docker/setup-buildx-action
      - name: Set up Docker Buildx
        uses: docker/setup-buildx-action@v2
      - name: Build multiple archs image
        run: |
          docker buildx build --platform linux/arm64/v8 \
            --output "type=image,push=false" \
            --build-arg IMAGE=ossrs/srs:ubuntu20-cache \
            --build-arg INSTALLDEPENDS="NO" \
            -f Dockerfile .
    runs-on: ubuntu-20.04

  multiple-arch-amd64:
    name: multiple-arch-amd64
    needs:
      - fast
    steps:
      - name: Checkout repository
        uses: actions/checkout@v3
      # See https://github.com/crazy-max/ghaction-docker-buildx#moved-to-docker-organization
      # https://github.com/docker/setup-qemu-action
      - name: Set up QEMU
        uses: docker/setup-qemu-action@v2
      # https://github.com/docker/setup-buildx-action
      - name: Set up Docker Buildx
        uses: docker/setup-buildx-action@v2
      - name: Build multiple archs image
        run: |
          docker buildx build --platform linux/amd64 \
            --output "type=image,push=false" \
            --build-arg IMAGE=ossrs/srs:ubuntu20-cache \
            -f Dockerfile .
    runs-on: ubuntu-20.04

  fast:
    name: fast
    needs:
      - cygwin64-cache
    steps:
      - run: echo 'Start fast jobs'
    runs-on: ubuntu-20.04

  done:
    name: done
    needs:
      - cygwin64
      - coverage
      - test
      - build-centos7
      - build-ubuntu16
      - build-ubuntu18
      - build-ubuntu20
      - build-cross-arm
      - build-cross-aarch64
      - multiple-arch-armv7
      - multiple-arch-aarch64
      - multiple-arch-amd64
    runs-on: ubuntu-20.04
    steps:
      - run: echo 'All done'

